# Supplier Management System Documentation

## Overview

The Supplier Management System is a comprehensive solution for managing hotel booking platform suppliers, including hotels, service providers, transport providers, activity providers, and tour operators. This system has been refactored from the legacy vendor management system to use consistent "supplier" terminology throughout.

## Key Features

### 🏢 Supplier Directory
- Complete supplier profile management
- Business type categorization
- Contact information management
- Address and location tracking
- Verification status tracking

### 📦 Product & Service Catalog
- Product inventory management
- Service offerings with scheduling
- Dynamic pricing models
- Stock level monitoring
- Category-based organization

### 📋 Order Management
- End-to-end order processing
- Multi-item order support
- Real-time status tracking
- Delivery scheduling
- Financial reconciliation

### 📄 Contract & Document Management
- Contract lifecycle management
- Document repository
- Version control
- Approval workflows
- Compliance tracking

### 📊 Performance Analytics
- Supplier performance metrics
- Order completion rates
- Quality ratings
- Financial analytics
- Trend analysis

## Architecture

### Database Models

#### Core Supplier Models
- `Supplier` - Main supplier entity
- `SupplierProduct` - Product catalog
- `SupplierService` - Service offerings
- `SupplierOrder` - Order management
- `SupplierOrderItem` - Order line items

#### Supporting Models
- `SupplierContract` - Contract management
- `SupplierDocument` - Document repository
- `SupplierContactHistory` - Communication tracking
- `SupplierProductPricing` - Dynamic pricing
- `SupplierServicePricing` - Service pricing

### API Endpoints

#### Supplier Management
```
GET    /admin/supplier-management/suppliers
POST   /admin/supplier-management/suppliers
GET    /admin/supplier-management/suppliers/{id}
PUT    /admin/supplier-management/suppliers/{id}
DELETE /admin/supplier-management/suppliers/{id}
```

#### Product Management
```
GET    /admin/supplier-management/products
POST   /admin/supplier-management/products
GET    /admin/supplier-management/products/{id}
PUT    /admin/supplier-management/products/{id}
DELETE /admin/supplier-management/products/{id}
```

#### Service Management
```
GET    /admin/supplier-management/services
POST   /admin/supplier-management/services
GET    /admin/supplier-management/services/{id}
PUT    /admin/supplier-management/services/{id}
DELETE /admin/supplier-management/services/{id}
```

#### Order Management
```
GET    /admin/supplier-management/orders
POST   /admin/supplier-management/orders
GET    /admin/supplier-management/orders/{id}
PUT    /admin/supplier-management/orders/{id}
PATCH  /admin/supplier-management/orders/{id}/status
```

### Service Layer

#### SupplierModuleService
Primary service for all supplier operations:
- `createSupplier()` - Create new supplier
- `updateSupplier()` - Update supplier information
- `listSuppliers()` - List suppliers with filtering
- `retrieveSupplier()` - Get supplier details
- `deleteSupplier()` - Remove supplier

#### Product & Service Management
- `createSupplierProduct()` - Add product to catalog
- `updateSupplierProductStock()` - Update inventory
- `createSupplierService()` - Add service offering
- `updateSupplierServiceAvailability()` - Manage availability

#### Order Processing
- `createSupplierOrder()` - Create new order
- `updateSupplierOrderStatus()` - Update order status
- `addSupplierOrderItem()` - Add items to order
- `calculateOrderTotal()` - Calculate pricing

### Workflow System

#### Core Workflows
- `CreateSupplierWorkflow` - Supplier onboarding
- `UpdateSupplierWorkflow` - Information updates
- `DeleteSupplierWorkflow` - Safe removal
- `ActivateSupplierWorkflow` - Status management

#### Order Workflows
- `CreateSupplierOrderWorkflow` - Order processing
- `UpdateSupplierOrderWorkflow` - Order modifications
- `CancelSupplierOrderWorkflow` - Order cancellation

## Frontend Components

### Admin Interface
- Supplier Directory (`/admin/supplier-management/suppliers`)
- Product Catalog (`/admin/supplier-management/products`)
- Service Directory (`/admin/supplier-management/services`)
- Order Management (`/admin/supplier-management/orders`)
- Contract Management (`/admin/supplier-management/contracts`)

### React Hooks
- `useSuppliers()` - Supplier data management
- `useSupplierProducts()` - Product catalog
- `useSupplierServices()` - Service management
- `useSupplierOrders()` - Order operations

## Migration & Backward Compatibility

### Database Migration
The system includes a comprehensive migration that:
- Renames all vendor tables to supplier tables
- Preserves all existing data
- Maintains referential integrity
- Updates all indexes and constraints

### API Compatibility
- Legacy vendor APIs remain functional
- New supplier APIs are the recommended approach
- Gradual migration path available
- No breaking changes for existing integrations

### Frontend Migration
- New supplier components available
- Legacy vendor components maintained
- Shared hooks for both systems
- Consistent UI/UX experience

## Getting Started

### 1. Database Setup
Run the migration to create supplier tables:
```bash
npm run migration:run
```

### 2. API Usage
Create a new supplier:
```typescript
const supplier = await supplierService.createSupplier({
  name: "Hotel Partner ABC",
  type: "hotel_partner",
  primary_contact_name: "John Doe",
  primary_contact_email: "<EMAIL>",
  address_line_1: "123 Main St",
  city: "New York",
  state: "NY",
  postal_code: "10001",
  country: "US"
});
```

### 3. Frontend Integration
Use supplier hooks in React components:
```typescript
import { useSuppliers, useCreateSupplier } from '@/hooks/vendor-management';

const SupplierList = () => {
  const { data: suppliers } = useSuppliers();
  const createSupplier = useCreateSupplier();
  
  // Component implementation
};
```

## Best Practices

### Data Management
- Use proper validation schemas
- Implement error handling
- Maintain data consistency
- Regular backup procedures

### Performance
- Implement pagination for large datasets
- Use appropriate database indexes
- Cache frequently accessed data
- Monitor query performance

### Security
- Validate all input data
- Implement proper authentication
- Use role-based access control
- Audit sensitive operations

## Support

### Documentation
- API documentation via OpenAPI/Swagger
- Code examples in `/examples`
- Test cases for reference
- Inline code documentation

### Migration Support
- Legacy vendor system remains functional
- Gradual migration tools available
- Data integrity verification
- Rollback procedures documented

For technical support or questions, refer to the development team or create an issue in the project repository.
