# Translation System Guide

## Overview

This document explains the translation system implementation for the destination form, focusing on the `loadTranslations` function and its integration with Tolgee API.

## 🎯 Purpose of `loadTranslations`

The `loadTranslations` function is responsible for fetching existing translations from Tolgee when users switch to non-base languages in the destination form.

### When It's Called
```typescript
// Automatically triggered when user selects a non-base language
useEffect(() => {
  if (!isBaseLanguage && destinationId && destinationId !== 'new') {
    loadTranslations(selectedLanguage); // 👈 Called here
  }
}, [selectedLanguage, isBaseLanguage, destinationId]);
```

## 🌐 API Integration

### Tolgee Tree Translations Endpoint
```
GET https://app.tolgee.io/v2/projects/{projectId}/translations/{language}
```

### Query Parameters
- `filterKeyName`: Filter to get only keys matching a pattern (e.g., 'destination.123')

### Example Request
```
GET /v2/projects/19149/translations/ja?filterKeyName=destination.01JSBNCPRYENAAM64WY5QW8XS5
```

## 📋 Function Implementation

### 1. Input Validation
```typescript
const loadTranslations = useCallback(async (language: string) => {
  setLoading(true);
  setError(null);

  try {
    const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
    const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

    if (!apiKey || !projectId) {
      throw new Error('Tolgee API configuration missing');
    }
```

### 2. Filter Preparation
```typescript
    // Use destination ID as filter to get only destination-specific translations
    const filterKeyName = `destination.${destinationId}`;
    // Example: "destination.01JSBNCPRYENAAM64WY5QW8XS5"
```

### 3. API Request Construction
```typescript
    // Use the tree endpoint with filter for destination keys
    const url = `https://app.tolgee.io/v2/projects/${projectId}/translations/${language}?filterKeyName=${filterKeyName}`;
    // Example: /v2/projects/19149/translations/ja?filterKeyName=destination.01JSBNCPRYENAAM64WY5QW8XS5
```

### 4. HTTP Request
```typescript
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': apiKey,
      }
    });
```

### 5. Error Handling
```typescript
    if (!response.ok) {
      if (response.status === 404) {
        console.log(`No translations found for language ${language}`);
        // Set empty translations for this language and continue
        setTranslations(prev => ({ ...prev, [language]: {} }));
        return;
      }
      throw new Error(`Load failed: ${response.status}`);
    }
```

### 6. Response Processing
```typescript
    // Parse the tree response format
    const responseData: TolgeeTreeResponse = await response.json();

    // Extract translations from the nested tree structure
    const transformedData: Record<string, string> = {};

    // Navigate through: language -> destination -> destinationId -> field
    if (responseData[language]?.destination?.[destinationId]) {
      const destinationTranslations = responseData[language].destination[destinationId];

      // Convert nested structure to flat keys
      DESTINATION_TRANSLATABLE_FIELDS.forEach(fieldName => {
        if (destinationTranslations[fieldName]) {
          const fullKey = `destination.${destinationId}.${fieldName}`;
          transformedData[fullKey] = destinationTranslations[fieldName];
        }
      });
    }

    setTranslations(prev => ({
      ...prev,
      [language]: transformedData
    }));
```

## 🎬 Real-World Example

### Scenario: User switches from English to German

1. **User Action**: Selects "German (de)" in language selector
2. **Function Triggered**: `loadTranslations('de')`
3. **API Call Made**:
   ```
   GET /v2/projects/19149/translations?targetLanguage=de&keyNames[]=destination.dest_123.name&keyNames[]=destination.dest_123.description&keyNames[]=destination.dest_123.location
   ```

4. **Tolgee Response**:
   ```json
   {
     "ja": {
       "destination": {
         "01JSBNCPRYENAAM64WY5QW8XS5": {
           "name": "アローザ",
           "description": "グラウビュンデン州中心部の陽光降り注ぐ谷間に位置する魅力的な村...",
           "location": "ヨーロッパ"
         }
       }
     }
   }
   ```

5. **State Updated**:
   ```typescript
   translations = {
     de: {
       "destination.dest_123.name": "Schweizer Alpen",
       "destination.dest_123.description": "Wunderschöne Berglandschaft...",
       "destination.dest_123.location": "Zermatt, Wallis"
     }
   }
   ```

6. **UI Updates**: Form fields now display German translations

## 🔍 Why This API Approach?

### ✅ Advantages of Flat Translations Endpoint

1. **Efficient**: Only fetches the exact keys needed
2. **Fast**: No need to filter through thousands of translations
3. **Clean**: Returns simple array format
4. **Targeted**: Avoids downloading unnecessary data
5. **Scalable**: Works well with large translation datasets

### ❌ Alternative Approach (Less Efficient)

```typescript
// This would fetch ALL translations for the language
GET /v2/projects/19149/translations/de
// Returns: { "key1": "value1", "key2": "value2", ... thousands of keys }
// Then we'd have to filter for our destination keys
```

## 🛡️ Error Handling Strategy

### 404 Not Found
```typescript
if (response.status === 404) {
  // No translations exist for this language yet
  setTranslations(prev => ({ ...prev, [language]: {} }));
  return; // Graceful fallback to base language values
}
```

### Other Errors
```typescript
throw new Error(`Load failed: ${response.status}`);
// Caught by try-catch and stored in error state
```

## 🔄 Integration with Form

### Translation Loading Flow
1. User selects non-base language → `loadTranslations()` called
2. API fetches existing translations → State updated
3. Form fields display translated values → User can edit
4. Form submission → `saveTranslations()` called (separate function)

### Field Value Resolution
```typescript
// Helper function in destination form
const getFieldValue = (fieldName: string, baseValue: string): string => {
  if (isBaseLanguage || !isFieldTranslatable(fieldName)) {
    return baseValue;
  }
  
  // Use translated value if available, fallback to base value
  return translatedValues[fieldName] || baseValue;
};
```

## 🎯 Key Benefits

1. **Read-Only Operation**: Never modifies data, only fetches
2. **Language Isolation**: Each language stored separately in state
3. **Fallback Handling**: Gracefully handles missing translations
4. **Performance Optimized**: Memoized keys prevent unnecessary recalculations
5. **User Experience**: Smooth transitions between languages

## 🔧 Configuration Requirements

### Environment Variables
```env
VITE_TOLGEE_API_KEY=your-tolgee-api-key
VITE_TOLGEE_PROJECT_ID=19149
```

### Translatable Fields
```typescript
const DESTINATION_TRANSLATABLE_FIELDS = [
  'name',
  'description', 
  'location'
] as const;
```

## 💾 Save Translations API

### Tolgee Bulk Create or Update Endpoint
```
POST/PUT https://app.tolgee.io/v2/projects/{projectId}/translations
```

### Method Options
- **POST**: Upsert behavior (creates keys if they don't exist, updates if they do)
- **PUT**: Strict update only (will error if key doesn't exist)

### Example Save Request
```typescript
// Save each translation individually using correct API format
const payload = {
  key: "destination.01JSBNCPRYENAAM64WY5QW8XS5.name",
  translations: {
    "ja": "アローザ"
  }
};

const response = await fetch(`https://app.tolgee.io/v2/projects/19149/translations`, {
  method: 'POST', // or 'PUT' for strict mode
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-API-Key': apiKey,
  },
  body: JSON.stringify(payload)
});
```

### API Endpoints Summary
- **GET** `/v2/projects/{projectId}/translations/{language}?filterKeyName={filter}` - Load translations
- **POST** `/v2/projects/{projectId}/translations` - Create or update translations (upsert)
- **PUT** `/v2/projects/{projectId}/translations` - Update existing translations only (strict)

This function is the foundation of the translation system's "read" functionality, enabling users to view and edit existing translations in a seamless, efficient manner.
