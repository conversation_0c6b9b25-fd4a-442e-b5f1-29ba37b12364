# Direct User Creation Flow

This document describes the new direct user creation flow that bypasses Medusa's invite system to avoid invite URL expiry issues.

## Overview

The new user creation flow creates user accounts directly with dummy passwords and sends welcome emails with password reset instructions, eliminating the need for invite URLs that can expire.

## Architecture

### Components

1. **Admin Endpoint**: `/admin/users` (POST) - Creates users directly
2. **User Creation Workflow**: `CreateUserWorkflow` - Handles user creation with password generation
3. **Email Templates**: Welcome email with password reset instructions
4. **Password Reset Enforcement**: Middleware to force password reset on first login
5. **Admin UI**: Updated roles page to use direct user creation

### Flow Diagram

```
Admin creates user → Generate secure password → Create user account → Generate reset token → Send welcome email → User sets password → Login allowed
```

## API Endpoints

### POST /admin/users

Creates a new user directly with a dummy password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "role": "admin", // or role_id for custom roles
  "custom_permissions": [],
  "assigned_hotels": [],
  "metadata": {}
}
```

**Response:**
```json
{
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "created_at": "2024-01-01T00:00:00Z",
    "rbac": {
      "role": "admin",
      "is_active": true,
      "requires_password_reset": true
    }
  },
  "message": "User created successfully. Welcome email sent with password setup instructions."
}
```

### POST /admin/auth/password-reset-complete

Completes password reset and removes the password reset requirement.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "token": "reset_token_here",
  "password": "new_secure_password"
}
```

## Security Features

### Password Generation
- 16-character random passwords
- Includes uppercase, lowercase, numbers, and symbols
- Cryptographically secure random generation
- Passwords are hashed immediately by the auth provider

### Password Reset Enforcement
- Users with `requires_password_reset: true` are blocked from accessing admin routes
- Middleware checks user metadata on each request
- Only password reset and auth routes are allowed
- Flag is removed after successful password reset

### Validation
- Email format validation
- Duplicate email prevention
- Required field validation
- Role assignment validation

## Email Templates

### Welcome Email Template
- Event: `user.created_direct`
- Channel: `email`
- Contains password reset link with token
- Professional HTML template with company branding
- Clear instructions for password setup

### Template Variables
- `{{ user.email }}` - User's email address
- `{{ user.first_name }}` - User's first name
- `{{ user.last_name }}` - User's last name
- `{{ resetLink }}` - Password reset URL with token
- `{{ frontendURL }}` - Frontend application URL

## Admin UI Changes

### Roles Page Updates
1. **Form Fields**: Added first name and last name inputs
2. **Button Text**: Changed from "Send Invite" to "Create User"
3. **Success Message**: Updated to reflect direct user creation
4. **Validation**: Added validation for all required fields

### User Experience
1. Admin fills out user creation form
2. User receives welcome email immediately
3. User clicks password reset link
4. User sets their own password
5. User can log in normally

## Migration from Invite System

### Backward Compatibility
- Existing invite system remains functional
- New direct creation can be used alongside invites
- No breaking changes to existing functionality

### Recommended Migration Steps
1. Deploy the new user creation system
2. Update admin training to use new flow
3. Gradually phase out invite system usage
4. Monitor for any issues with the new flow

## Configuration

### Environment Variables
- `MEDUSA_BACKEND_URL` - Backend URL for password reset links
- `MEDUSA_STOREFRONT_URL` - Frontend URL for user interface

### Notification Templates
Run the seed script to create default templates:
```bash
npm run seed:notification-templates
```

## Testing

### Manual Testing
1. Create a user through the admin interface
2. Check that welcome email is sent
3. Use password reset link to set password
4. Verify user can log in normally

### Automated Testing
Run the test script:
```bash
npm run exec src/scripts/test-user-creation.ts
```

## Troubleshooting

### Common Issues

1. **Email not sent**: Check notification template configuration
2. **Password reset fails**: Verify token hasn't expired (24 hours)
3. **User creation fails**: Check for duplicate emails or validation errors
4. **Login blocked**: Ensure password reset was completed successfully

### Logs
- User creation: Check workflow execution logs
- Email sending: Check notification service logs
- Password reset: Check auth service logs

## Security Considerations

1. **Password Tokens**: Reset tokens expire in 24 hours
2. **Email Security**: Welcome emails contain sensitive reset links
3. **Access Control**: Only admins can create users
4. **Audit Trail**: All user creation is logged with creator information

## Future Enhancements

1. **Bulk User Creation**: Support for creating multiple users
2. **Custom Email Templates**: Per-organization email customization
3. **Role Templates**: Predefined role configurations
4. **User Import**: CSV/Excel import functionality
