# Supplier Management System Documentation

## Overview

The Supplier Management System (formerly Vendor Management System) is a comprehensive solution for managing hotel booking platform suppliers, including hotels, service providers, transport providers, activity providers, and tour operators. The system provides complete CRUD operations, relationship management, performance tracking, and communication history.

**Note**: This system has been refactored from "vendor" to "supplier" terminology for consistency. Both legacy vendor APIs and new supplier APIs are supported for backward compatibility.

## Architecture

### Core Components

1. **Database Models** (`src/modules/vendor_management/models/`)
   - `Supplier` - Core supplier information (new)
   - `Vendor` - Legacy vendor information (backward compatibility)
   - `SupplierProduct` - Product catalog management
   - `SupplierService` - Service offerings management
   - `SupplierOrder` - Order management system
   - `SupplierContract` - Contract lifecycle management
   - `SupplierDocument` - Document repository
   - `SupplierContactHistory` - Communication tracking

2. **Service Layer** (`src/modules/vendor_management/`)
   - `supplier-service.ts` - New supplier service (primary)
   - `service.ts` - Legacy vendor service (backward compatibility)
   - Comprehensive business logic
   - Data validation and error handling
   - Transaction management with atomic operations

3. **API Layer** (`src/api/admin/`)
   - `supplier-management/` - New supplier APIs (primary)
   - `vendor_management/` - Legacy vendor APIs (backward compatibility)
   - RESTful API endpoints
   - Input validation with Zod schemas
   - Comprehensive error handling

4. **Admin UI** (`src/admin/routes/vendor-management/`)
   - React-based admin interface
   - Form components for CRUD operations
   - Responsive design with @camped-ai/ui

5. **Workflows** (`src/workflows/vendor-management/`)
   - Vendor onboarding automation
   - Performance evaluation processes
   - Complex business logic orchestration

## Features

### Supplier Management
- **CRUD Operations**: Create, read, update, delete suppliers
- **Supplier Types**: Hotel partners, service providers, transport providers, activity providers, tour operators
- **Status Management**: Active, inactive, pending approval, suspended, terminated
- **Address Management**: Complete address information with validation
- **Metadata Support**: Flexible additional data storage
- **Backward Compatibility**: Legacy vendor APIs still supported

### Product & Service Management
- **Product Catalog**: Complete product inventory management
- **Service Offerings**: Service catalog with availability scheduling
- **Pricing Models**: Dynamic pricing with seasonal adjustments
- **Stock Management**: Inventory tracking and alerts

### Order Management
- **Order Processing**: Complete order lifecycle management
- **Multi-item Orders**: Support for mixed product/service orders
- **Order Tracking**: Real-time status updates
- **Financial Tracking**: Comprehensive pricing and payment tracking

### Contact Management
- **Multiple Contacts**: Support for multiple contacts per supplier
- **Role-based Contacts**: Primary, billing, operations, support, sales, technical, management
- **Contact Validation**: Email and phone number validation
- **Communication History**: Complete interaction tracking

### Contract Management
- **Contract Lifecycle**: Draft, active, expired, terminated, pending renewal
- **Financial Terms**: Commission rates, payment terms
- **Date Management**: Start and end dates with validation
- **Terms Storage**: Full terms and conditions storage

### Communication History
- **Communication Types**: Email, phone calls, meetings, video calls, chat, documents
- **Direction Tracking**: Inbound and outbound communications
- **Status Management**: Pending, completed, follow-up required, cancelled
- **Priority Levels**: Low, medium, high, urgent
- **Participant Tracking**: Internal and external participants
- **Attachment Support**: File attachments with metadata

### Performance Tracking
- **Metrics**: Booking fulfillment rate, response time, customer satisfaction, cancellation rate, revenue
- **Period-based**: Performance tracking over specific time periods
- **Automated Scoring**: Calculated performance scores with recommendations
- **Status Updates**: Automatic status updates based on performance

## API Endpoints

### Vendors
- `GET /admin/vendor-management/vendors` - List vendors with filtering and pagination
- `POST /admin/vendor-management/vendors` - Create new vendor
- `GET /admin/vendor-management/vendors/{id}` - Get vendor details
- `PUT /admin/vendor-management/vendors/{id}` - Update vendor
- `DELETE /admin/vendor-management/vendors/{id}` - Delete vendor

### Contacts
- `GET /admin/vendor-management/vendors/{id}/contacts` - List vendor contacts
- `POST /admin/vendor-management/vendors/{id}/contacts` - Create contact
- `GET /admin/vendor-management/vendors/{id}/contacts/{contactId}` - Get contact
- `PUT /admin/vendor-management/vendors/{id}/contacts/{contactId}` - Update contact
- `DELETE /admin/vendor-management/vendors/{id}/contacts/{contactId}` - Delete contact

### Contracts
- `GET /admin/vendor-management/vendors/{id}/contracts` - List vendor contracts
- `POST /admin/vendor-management/vendors/{id}/contracts` - Create contract
- `GET /admin/vendor-management/vendors/{id}/contracts/{contractId}` - Get contract
- `PUT /admin/vendor-management/vendors/{id}/contracts/{contractId}` - Update contract
- `DELETE /admin/vendor-management/vendors/{id}/contracts/{contractId}` - Delete contract

### Communications
- `GET /admin/vendor-management/vendors/{id}/communications` - List communications
- `POST /admin/vendor-management/vendors/{id}/communications` - Log communication

### Performance
- `GET /admin/vendor-management/vendors/{id}/performance` - Get performance metrics
- `POST /admin/vendor-management/vendors/{id}/performance` - Record performance metric

## Workflows

### Vendor Onboarding Workflow
Automates the vendor onboarding process:

1. **Vendor Creation**: Creates vendor with validation
2. **Communication Logging**: Logs initial onboarding communication
3. **Welcome Email**: Sends welcome email (configurable)
4. **Account Manager Assignment**: Assigns account manager

**Usage:**
```typescript
import { vendorOnboardingWorkflow } from './workflows/vendor-management';

const result = await vendorOnboardingWorkflow.run({
  vendor: {
    name: "New Hotel Partner",
    type: "hotel_partner",
    // ... other vendor data
  },
  contacts: [/* contact data */],
  contracts: [/* contract data */],
  send_welcome_email: true,
  assign_account_manager: "John Smith"
});
```

### Performance Evaluation Workflow
Automates performance evaluation:

1. **Validation**: Validates vendor and evaluation period
2. **Metrics Recording**: Records performance metrics
3. **Score Calculation**: Calculates overall performance score
4. **Status Update**: Updates vendor status based on performance
5. **Communication Logging**: Logs evaluation results

**Usage:**
```typescript
import { vendorPerformanceEvaluationWorkflow } from './workflows/vendor-management';

const result = await vendorPerformanceEvaluationWorkflow.run({
  vendor_id: "vendor-123",
  evaluation_period: {
    start_date: new Date("2024-01-01"),
    end_date: new Date("2024-01-31")
  },
  metrics: {
    booking_fulfillment_rate: 95.5,
    response_time: 2.5,
    customer_satisfaction: 4.2,
    cancellation_rate: 3.2,
    revenue_generated: 50000
  },
  evaluator: "Performance Manager",
  auto_update_status: true
});
```

## Validation and Error Handling

### Input Validation
- **Zod Schemas**: Comprehensive input validation using Zod
- **Business Rules**: Custom business logic validation
- **Type Safety**: Full TypeScript type safety

### Error Handling
- **Custom Error Classes**: Specific error types for different scenarios
- **Error Logging**: Comprehensive error logging with context
- **Performance Monitoring**: Operation performance tracking
- **Rate Limiting**: API rate limiting protection

### Logging
- **Structured Logging**: JSON-structured logs with context
- **Audit Trail**: Complete audit trail for all operations
- **Performance Metrics**: Operation timing and performance data
- **Error Tracking**: Detailed error tracking and reporting

## Testing

### Unit Tests
- **Service Tests**: Comprehensive service layer testing
- **Workflow Tests**: Workflow logic testing
- **Validation Tests**: Input validation testing

### Test Coverage
- Models and relationships
- Service methods and business logic
- API endpoints and error handling
- Workflow steps and error scenarios

**Running Tests:**
```bash
npm test src/modules/vendor-management
npm test src/workflows/vendor-management
```

## Database Schema

### Tables
- `vendor` - Core vendor information
- `vendor_contact` - Vendor contacts
- `vendor_contract` - Vendor contracts
- `vendor_performance` - Performance metrics
- `vendor_communication` - Communication history
- `vendor_hotel_mapping` - Hotel mappings

### Indexes
- Optimized indexes for common queries
- Foreign key constraints for data integrity
- Unique constraints for business rules

## Security Considerations

### Access Control
- Role-based access control
- API authentication and authorization
- Data privacy compliance

### Data Protection
- Input sanitization
- SQL injection prevention
- XSS protection

## Performance Optimization

### Database
- Optimized queries with proper indexing
- Connection pooling
- Query result caching

### API
- Pagination for large datasets
- Response compression
- Rate limiting

### UI
- Lazy loading for large lists
- Optimistic updates
- Error boundaries

## Deployment

### Environment Variables
```env
DATABASE_URL=postgresql://...
VENDOR_MANAGEMENT_LOG_LEVEL=info
VENDOR_MANAGEMENT_MAX_LOGS=1000
```

### Database Migration
```bash
npm run migration:run
```

### Health Checks
- Database connectivity
- Service availability
- Performance metrics

## Monitoring and Maintenance

### Metrics
- API response times
- Error rates
- Database performance
- Workflow execution times

### Alerts
- High error rates
- Slow response times
- Failed workflows
- Database issues

### Maintenance Tasks
- Regular performance reviews
- Data cleanup
- Index optimization
- Log rotation

## Future Enhancements

### Planned Features
- Advanced reporting and analytics
- Integration with external systems
- Mobile application support
- Advanced workflow automation

### Scalability Improvements
- Microservice architecture
- Event-driven architecture
- Caching layer optimization
- Database sharding

## Support and Documentation

### Getting Help
- Technical documentation in `/docs`
- API documentation via OpenAPI/Swagger
- Code comments and inline documentation
- Test examples for usage patterns

### Contributing
- Follow existing code patterns
- Add comprehensive tests
- Update documentation
- Follow TypeScript best practices
