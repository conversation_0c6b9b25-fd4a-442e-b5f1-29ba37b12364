# Extra Bed Setup Guide

This guide explains how to configure and use the extra bed functionality in the hotel booking system.

## Overview

The extra bed feature allows hotels to:
1. Configure maximum extra beds per room type
2. Set pricing for extra beds (per night, with seasonal variations)
3. Accept bookings with extra bed requests
4. Validate extra bed limits during booking

## Setup Steps

### 1. Configure Room Maximum Extra Beds

Each room configuration can specify the maximum number of extra beds allowed:

```typescript
// Room configuration metadata includes:
{
  max_extra_beds: 2, // Maximum 2 extra beds for this room type
  // ... other room config fields
}
```

### 2. Create Extra Bed Occupancy Configuration

For each hotel, create an `EXTRA_BED` occupancy configuration:

**API Endpoint:** `POST /admin/hotel-management/hotels/{hotel_id}/pricing/occupancy-config`

```json
{
  "name": "Extra Bed",
  "type": "EXTRA_BED",
  "min_age": 0,
  "max_age": 120,
  "is_default": false
}
```

### 3. Set Extra Bed Pricing

Create base price rules for the extra bed occupancy type:

**API Endpoint:** `POST /admin/hotel-management/room-configs/{room_config_id}/weekday-pricing/bulk`

```json
{
  "currency_code": "USD",
  "weekday_rules": [
    {
      "occupancy_type_id": "{extra_bed_occupancy_id}",
      "meal_plan_id": "{meal_plan_id}",
      "weekday_prices": {
        "mon": 2500,  // $25.00 per night
        "tue": 2500,
        "wed": 2500,
        "thu": 2500,
        "fri": 3000,  // $30.00 per night (weekend rate)
        "sat": 3000,
        "sun": 2500
      }
    }
  ]
}
```

### 4. Set Seasonal Pricing (Optional)

Create seasonal price overrides for extra beds:

**API Endpoint:** `POST /admin/hotel-management/room-configs/{room_config_id}/seasonal-pricing/bulk`

```json
{
  "currency_code": "USD",
  "name": "Summer 2024",
  "start_date": "2024-06-01",
  "end_date": "2024-08-31",
  "weekday_rules": [
    {
      "occupancy_type_id": "{extra_bed_occupancy_id}",
      "meal_plan_id": "{meal_plan_id}",
      "weekday_prices": {
        "mon": 3500,  // Higher summer rates
        "tue": 3500,
        "wed": 3500,
        "thu": 3500,
        "fri": 4000,
        "sat": 4000,
        "sun": 3500
      }
    }
  ]
}
```

## Usage

### 1. Check Availability with Extra Beds

**API Endpoint:** `GET /store/hotel-management/hotels/{hotel_id}/availability`

```
GET /store/hotel-management/hotels/hotel_123/availability?check_in=2024-03-15&check_out=2024-03-17&adults=2&children=1&extra_beds=1
```

### 2. Create Booking with Extra Beds

**API Endpoint:** `POST /store/hotel-management/cart`

```json
{
  "hotel_id": "hotel_123",
  "room_config_id": "room_cfg_456",
  "check_in_date": "2024-03-15",
  "check_out_date": "2024-03-17",
  "adults": 2,
  "children": 1,
  "infants": 0,
  "extra_beds": 1,
  "guest_email": "<EMAIL>",
  "total_amount": 15000,
  "currency_code": "USD",
  "region_id": "reg_us"
}
```

## Validation Rules

1. **Room Limits**: Extra beds requested cannot exceed `max_extra_beds` for the room configuration
2. **Pricing Required**: Extra bed occupancy configuration must have pricing rules configured
3. **Non-negative**: Extra beds must be 0 or positive integer

## Error Handling

### Common Error Responses

1. **Exceeds Room Limit**:
```json
{
  "message": "Maximum 2 extra beds allowed for room Deluxe Suite. Requested: 3",
  "max_extra_beds": 2,
  "requested_extra_beds": 3
}
```

2. **No Pricing Configuration**:
```
Extra beds requested but no pricing configured. Continuing without extra bed charges.
```

3. **No Occupancy Configuration**:
```
Extra beds requested but no extra bed occupancy configuration found. Continuing without extra bed charges.
```

## Price Calculation

Extra bed pricing follows the same day-wise calculation as other occupancy types:

1. **Base Pricing**: Uses weekday-specific prices from base price rules
2. **Seasonal Overrides**: Applies seasonal pricing when dates fall within seasonal periods
3. **Multiple Extra Beds**: Price is multiplied by the number of extra beds requested
4. **Currency**: Prices are stored in smallest currency unit (cents) and converted for display

## Integration Points

### Workflow Integration
- `CheckHotelAvailabilityInput` includes `extra_beds` parameter
- `CalculatePricesInput` includes `extra_beds` parameter
- Price calculation step validates and calculates extra bed pricing

### Cart Integration
- Cart schema validates `extra_beds` as non-negative integer
- Cart metadata includes extra bed information
- Room configuration validation occurs during cart creation

### Admin Interface
- Occupancy configuration management supports `EXTRA_BED` type
- Pricing configuration includes extra bed pricing rules
- Room configuration forms include `max_extra_beds` field

## Best Practices

1. **Set Realistic Limits**: Configure `max_extra_beds` based on actual room size and safety regulations
2. **Competitive Pricing**: Price extra beds competitively while covering costs
3. **Clear Communication**: Ensure extra bed policies are clearly communicated to guests
4. **Seasonal Adjustments**: Use seasonal pricing to optimize revenue during peak periods
5. **Regular Review**: Periodically review extra bed utilization and adjust pricing accordingly
