# Supplier Offerings Import/Export Documentation

## Overview

The Supplier Offerings Import/Export functionality allows users to bulk manage supplier offerings through Excel/CSV files. This feature supports template generation, data validation, and comprehensive import/export operations.

## Features

### 1. Template Generation
- **Export Template**: Downloads an Excel template with proper column headers and example data
- **Dynamic Fields**: Automatically includes category-specific dynamic fields based on existing categories
- **Human-Readable Format**: Uses supplier names, product/service names, and category names instead of IDs

### 2. Data Import
- **File Support**: Accepts both Excel (.xlsx) and CSV files
- **Validation**: Comprehensive validation including:
  - Required field validation
  - Data type validation
  - Dynamic field validation based on category schemas
  - Uniqueness validation using established business rules
- **Preview**: Shows data preview with validation errors before import
- **Progress Tracking**: Real-time progress indicators during import
- **Error Handling**: Detailed error messages with row numbers and field names

### 3. Data Export
- **Current Data Export**: Exports existing supplier offerings with all fields
- **Multiple Formats**: Supports both Excel and CSV export formats
- **Human-Readable**: Includes supplier names, product/service names, and category names

## Usage Guide

### Accessing Import/Export

1. Navigate to **Supplier Management > Supplier Offerings**
2. Click the **Export** dropdown button in the top-right corner
3. Choose from the following options:
   - **Export Template**: Download template for new imports
   - **Export Data**: Export current supplier offerings
   - **Import Data**: Import supplier offerings from file

### Import Process

#### Step 1: Download Template
1. Click **Export > Export Template**
2. An Excel file will be downloaded with:
   - Proper column headers
   - Example data for different categories
   - Dynamic fields based on your category schemas

#### Step 2: Prepare Your Data
1. Open the downloaded template
2. Replace example data with your actual supplier offerings
3. Ensure all required fields are filled
4. Use exact supplier names, product/service names, and category names as they appear in the system

#### Step 3: Import Data
1. Click **Export > Import Data**
2. Upload your completed Excel or CSV file
3. Review the data preview and validation results
4. Fix any validation errors if present
5. Click **Import** to proceed

### Field Mapping

#### Required Fields
- **supplier_name**: Name of the supplier (must match existing supplier)
- **product_service_name**: Name of the product/service (must match existing product/service)
- **category_name**: Category name (automatically populated based on product/service)

#### Optional Fields
- **active_from**: Start date (YYYY-MM-DD format)
- **active_to**: End date (YYYY-MM-DD format)
- **availability_notes**: Notes about availability
- **status**: Either "active" or "inactive" (defaults to "active")

#### Dynamic Fields
- **custom_field_[field_key]**: Category-specific fields
- Field validation depends on the field type defined in the category schema

### Validation Rules

#### Data Validation
- **Supplier Validation**: Supplier must exist in the system
- **Product/Service Validation**: Product/service must exist in the system
- **Date Validation**: Dates must be in valid format (YYYY-MM-DD)
- **Status Validation**: Must be either "active" or "inactive"

#### Dynamic Field Validation
- **Required Fields**: Must be filled if marked as required in category schema
- **Type Validation**: 
  - Number fields must contain valid numbers
  - Dropdown fields must contain valid options
  - Date fields must be in valid date format
  - Boolean fields accept: true/false, 1/0, yes/no

#### Uniqueness Validation
Supplier offerings are validated for uniqueness based on:
- Supplier ID
- Product/Service ID
- Mandatory dynamic field values
- Active date range

## Error Handling

### Common Import Errors

1. **"Supplier not found"**
   - Ensure supplier name exactly matches an existing supplier
   - Check for extra spaces or typos

2. **"Product/Service not found"**
   - Ensure product/service name exactly matches an existing product/service
   - Verify the product/service is active

3. **"Required field missing"**
   - Fill in all required dynamic fields for the category
   - Check category schema for required fields

4. **"Invalid date format"**
   - Use YYYY-MM-DD format for dates
   - Ensure dates are valid (e.g., not February 30th)

5. **"Duplicate offering"**
   - An offering with the same configuration already exists
   - Modify mandatory field values or date range to make it unique

### Troubleshooting

#### Template Issues
- If template download fails, ensure you have suppliers, products/services, and categories created
- Contact administrator if persistent issues occur

#### Import Issues
- Check file format (Excel .xlsx or CSV)
- Ensure file is not corrupted
- Verify all required data is present

#### Performance
- For large imports (>1000 records), consider splitting into smaller batches
- Import process may take several minutes for large datasets

## Technical Details

### API Endpoints
- `GET /admin/supplier-management/supplier-offerings/template` - Generate template
- `POST /admin/supplier-management/supplier-offerings/import` - Import data
- `GET /admin/supplier-management/supplier-offerings/export` - Export data

### File Formats
- **Excel**: .xlsx format with proper column headers
- **CSV**: Comma-separated values with UTF-8 encoding

### Limitations
- Maximum file size: 10MB
- Maximum records per import: 10,000
- Template generation requires existing suppliers, products/services, and categories

## Best Practices

1. **Always download a fresh template** before importing to ensure you have the latest field structure
2. **Test with small batches** before importing large datasets
3. **Keep backups** of your data before performing bulk imports
4. **Review validation errors carefully** before proceeding with import
5. **Use consistent naming** for suppliers, products/services, and categories
6. **Validate dates** to ensure they're in the correct format and logical ranges

## Support

For technical issues or questions about the import/export functionality, please contact the development team or refer to the system administrator.
