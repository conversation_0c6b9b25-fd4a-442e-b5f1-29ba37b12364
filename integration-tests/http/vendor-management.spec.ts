import { medusaIntegrationTestRunner } from "@camped-ai/test-utils";

jest.setTimeout(60 * 1000);

medusaIntegrationTestRunner({
  inApp: true,
  env: {},
  testSuite: ({ api }) => {
    describe("Vendor Management API", () => {
      let vendorId: string;
      let productId: string;
      let serviceId: string;
      let orderId: string;

      describe("Vendors", () => {
        it("should create a new vendor", async () => {
          const vendorData = {
            name: "Test Cleaning Service",
            business_type: "housekeeping",
            primary_contact_name: "<PERSON>",
            primary_contact_email: "<EMAIL>",
            primary_contact_phone: "+1234567890",
            address_line_1: "123 Main Street",
            city: "Test City",
            state: "Test State",
            postal_code: "12345",
            country: "USA",
            description: "Professional cleaning services for hotels",
            website: "https://testcleaning.com",
          };

          const response = await api.post("/admin/vendor_management/vendors", vendorData);

          expect(response.status).toBe(200);
          expect(response.data.vendor).toBeDefined();
          expect(response.data.vendor.name).toBe(vendorData.name);
          expect(response.data.vendor.business_type).toBe(vendorData.business_type);
          expect(response.data.vendor.handle).toBe("test-cleaning-service");
          expect(response.data.vendor.status).toBe("pending");
          expect(response.data.vendor.verification_status).toBe("unverified");

          vendorId = response.data.vendor.id;
        });

        it("should list vendors", async () => {
          const response = await api.get("/admin/vendor_management/vendors");

          expect(response.status).toBe(200);
          expect(response.data.vendors).toBeDefined();
          expect(Array.isArray(response.data.vendors)).toBe(true);
          expect(response.data.count).toBeGreaterThan(0);
          expect(response.data.vendors.some((v: any) => v.id === vendorId)).toBe(true);
        });

        it("should retrieve a specific vendor", async () => {
          const response = await api.get(`/admin/vendor_management/vendors/${vendorId}`);

          expect(response.status).toBe(200);
          expect(response.data.vendor).toBeDefined();
          expect(response.data.vendor.id).toBe(vendorId);
          expect(response.data.vendor.performance_metrics).toBeDefined();
        });

        it("should update vendor status", async () => {
          const response = await api.patch("/admin/vendor_management/vendors", {
            id: vendorId,
            activate: true,
          });

          expect(response.status).toBe(200);
          expect(response.data.vendor.status).toBe("active");
        });

        it("should update vendor information", async () => {
          const updateData = {
            id: vendorId,
            description: "Updated description for cleaning service",
            rating: 4.5,
          };

          const response = await api.put("/admin/vendor_management/vendors", updateData);

          expect(response.status).toBe(200);
          expect(response.data.vendor.description).toBe(updateData.description);
          expect(response.data.vendor.rating).toBe(updateData.rating);
        });

        it("should filter vendors by business type", async () => {
          const response = await api.get("/admin/vendor_management/vendors?business_type=housekeeping");

          expect(response.status).toBe(200);
          expect(response.data.vendors.every((v: any) => v.business_type === "housekeeping")).toBe(true);
        });

        it("should search vendors by name", async () => {
          const response = await api.get("/admin/vendor_management/vendors?search=Test Cleaning");

          expect(response.status).toBe(200);
          expect(response.data.vendors.some((v: any) => v.name.includes("Test Cleaning"))).toBe(true);
        });
      });

      describe("Products", () => {
        it("should create a product for the vendor", async () => {
          const productData = {
            vendor_id: vendorId,
            name: "All-Purpose Cleaner",
            description: "Professional grade all-purpose cleaning solution",
            category: "cleaning-supplies",
            unit_of_measure: "bottle",
            current_stock: 100,
            minimum_stock: 10,
            maximum_stock: 500,
            sku: "APC-001",
            specifications: {
              volume: "1L",
              concentration: "concentrated",
              eco_friendly: true,
            },
          };

          const response = await api.post(`/admin/vendor_management/vendors/${vendorId}/products`, productData);

          expect(response.status).toBe(200);
          expect(response.data.product).toBeDefined();
          expect(response.data.product.name).toBe(productData.name);
          expect(response.data.product.vendor_id).toBe(vendorId);
          expect(response.data.product.sku).toBe(productData.sku);

          productId = response.data.product.id;
        });

        it("should list products for the vendor", async () => {
          const response = await api.get(`/admin/vendor_management/vendors/${vendorId}/products`);

          expect(response.status).toBe(200);
          expect(response.data.products).toBeDefined();
          expect(Array.isArray(response.data.products)).toBe(true);
          expect(response.data.products.some((p: any) => p.id === productId)).toBe(true);
        });

        it("should list all products across vendors", async () => {
          const response = await api.get("/admin/vendor_management/products");

          expect(response.status).toBe(200);
          expect(response.data.products).toBeDefined();
          expect(response.data.products.some((p: any) => p.id === productId)).toBe(true);
        });

        it("should filter products by category", async () => {
          const response = await api.get(`/admin/vendor_management/vendors/${vendorId}/products?category=cleaning-supplies`);

          expect(response.status).toBe(200);
          expect(response.data.products.every((p: any) => p.category === "cleaning-supplies")).toBe(true);
        });
      });

      describe("Services", () => {
        it("should create a service for the vendor", async () => {
          const serviceData = {
            vendor_id: vendorId,
            name: "Deep Room Cleaning",
            description: "Comprehensive deep cleaning service for hotel rooms",
            category: "room-cleaning",
            duration_minutes: 90,
            capacity: 5,
            requirements: {
              equipment_needed: ["vacuum", "cleaning_cart"],
              staff_count: 2,
              advance_notice_hours: 24,
            },
          };

          const response = await api.post(`/admin/vendor_management/vendors/${vendorId}/services`, serviceData);

          expect(response.status).toBe(200);
          expect(response.data.service).toBeDefined();
          expect(response.data.service.name).toBe(serviceData.name);
          expect(response.data.service.vendor_id).toBe(vendorId);
          expect(response.data.service.duration_minutes).toBe(serviceData.duration_minutes);

          serviceId = response.data.service.id;
        });

        it("should list services for the vendor", async () => {
          const response = await api.get(`/admin/vendor_management/vendors/${vendorId}/services`);

          expect(response.status).toBe(200);
          expect(response.data.services).toBeDefined();
          expect(Array.isArray(response.data.services)).toBe(true);
          expect(response.data.services.some((s: any) => s.id === serviceId)).toBe(true);
        });

        it("should list all services across vendors", async () => {
          const response = await api.get("/admin/vendor_management/services");

          expect(response.status).toBe(200);
          expect(response.data.services).toBeDefined();
          expect(response.data.services.some((s: any) => s.id === serviceId)).toBe(true);
        });
      });

      describe("Orders", () => {
        it("should create an order for the vendor", async () => {
          const orderData = {
            vendor_id: vendorId,
            order_type: "mixed",
            items: [
              {
                item_type: "product",
                item_id: productId,
                item_name: "All-Purpose Cleaner",
                quantity: 10,
                unit_price: 15.99,
                total_price: 159.90,
                product_sku: "APC-001",
              },
              {
                item_type: "service",
                item_id: serviceId,
                item_name: "Deep Room Cleaning",
                quantity: 3,
                unit_price: 75.00,
                total_price: 225.00,
                service_date: new Date("2024-02-15T10:00:00Z").toISOString(),
                service_duration_minutes: 90,
              },
            ],
            subtotal: 384.90,
            tax_amount: 38.49,
            total_amount: 423.39,
            currency_code: "USD",
            customer_name: "Hotel Manager",
            customer_email: "<EMAIL>",
            requested_delivery_date: new Date("2024-02-14T09:00:00Z").toISOString(),
            notes: "Urgent order for weekend cleaning",
          };

          const response = await api.post(`/admin/vendor_management/vendors/${vendorId}/orders`, orderData);

          expect(response.status).toBe(200);
          expect(response.data.order).toBeDefined();
          expect(response.data.items).toBeDefined();
          expect(response.data.order.vendor_id).toBe(vendorId);
          expect(response.data.order.total_amount).toBe(orderData.total_amount);
          expect(response.data.items).toHaveLength(2);

          orderId = response.data.order.id;
        });

        it("should list orders for the vendor", async () => {
          const response = await api.get(`/admin/vendor_management/vendors/${vendorId}/orders`);

          expect(response.status).toBe(200);
          expect(response.data.orders).toBeDefined();
          expect(Array.isArray(response.data.orders)).toBe(true);
          expect(response.data.orders.some((o: any) => o.id === orderId)).toBe(true);
        });

        it("should list all orders across vendors", async () => {
          const response = await api.get("/admin/vendor_management/orders");

          expect(response.status).toBe(200);
          expect(response.data.orders).toBeDefined();
          expect(response.data.orders.some((o: any) => o.id === orderId)).toBe(true);
        });

        it("should filter orders by status", async () => {
          const response = await api.get(`/admin/vendor_management/vendors/${vendorId}/orders?status=pending`);

          expect(response.status).toBe(200);
          expect(response.data.orders.every((o: any) => o.status === "pending")).toBe(true);
        });
      });

      describe("Error Handling", () => {
        it("should return 404 for non-existent vendor", async () => {
          const response = await api.get("/admin/vendor_management/vendors/vendor_nonexistent");

          expect(response.status).toBe(404);
          expect(response.data.type).toBe("not_found");
        });

        it("should return 400 for invalid vendor data", async () => {
          const invalidData = {
            name: "", // Required field is empty
            business_type: "invalid_type",
          };

          const response = await api.post("/admin/vendor_management/vendors", invalidData);

          expect(response.status).toBe(400);
          expect(response.data.type).toBe("invalid_data");
        });

        it("should return 400 for duplicate vendor handle", async () => {
          const duplicateData = {
            name: "Another Test Service",
            handle: "test-cleaning-service", // This handle already exists
            business_type: "maintenance",
            primary_contact_name: "Jane Doe",
            primary_contact_email: "<EMAIL>",
            address_line_1: "456 Oak St",
            city: "Another City",
            state: "Another State",
            postal_code: "54321",
            country: "USA",
          };

          const response = await api.post("/admin/vendor_management/vendors", duplicateData);

          expect(response.status).toBe(400);
          expect(response.data.message).toContain("already exists");
        });
      });

      describe("Cleanup", () => {
        it("should delete the test vendor", async () => {
          const response = await api.delete("/admin/vendor_management/vendors", {
            ids: vendorId,
          });

          expect(response.status).toBe(200);
          expect(response.data.results).toBeDefined();
        });
      });
    });
  },
});
