import { medusaIntegrationTestRunner } from "@camped-ai/framework/test-utils";

jest.setTimeout(30000);

medusaIntegrationTestRunner({
  testSuite: ({ api }) => {
    describe("Vendor Management HTTP API Tests", () => {
      let createdVendorId: string;

      const testVendorData = {
        name: "Test API Hotel Partner",
        type: "hotel_partner",
        status: "active",
        description: "A test hotel partner for HTTP API testing",
        website: "https://test-api-hotel.com",
        tax_id: "API123456",
        registration_number: "APIREG789012",
        address: {
          street: "456 API Test Street",
          city: "API Test City",
          state: "API Test State",
          country: "API Test Country",
          postal_code: "54321"
        },
        metadata: {
          test: true,
          api_test: "vendor-management"
        },
        contacts: [
          {
            name: "<PERSON>",
            email: "<EMAIL>",
            phone: "+0987654321",
            role: "API Manager",
            is_primary: true,
            metadata: {
              department: "API Operations"
            }
          }
        ],
        contracts: [
          {
            contract_number: "API-CONTRACT-001",
            title: "Test API Partnership Agreement",
            description: "Test API contract for integration testing",
            start_date: "2024-01-01T00:00:00.000Z",
            end_date: "2024-12-31T23:59:59.999Z",
            status: "active",
            terms_and_conditions: "Test API terms and conditions",
            commission_rate: 12.5,
            payment_terms: "Net 15",
            metadata: {
              test_api_contract: true
            }
          }
        ]
      };

      afterAll(async () => {
        // Clean up created vendor
        if (createdVendorId) {
          try {
            await api.delete(`/admin/vendor-management/vendors/${createdVendorId}`);
          } catch (error) {
            console.log("HTTP cleanup error:", error);
          }
        }
      });

      describe("POST /admin/vendor-management/vendors", () => {
        it("should create a vendor successfully", async () => {
          const response = await api.post("/admin/vendor-management/vendors", testVendorData);
          
          expect(response.status).toBe(201);
          expect(response.data).toBeDefined();
          expect(response.data.vendor).toBeDefined();
          expect(response.data.message).toBe("Vendor created successfully");
          
          const vendor = response.data.vendor;
          expect(vendor.id).toBeDefined();
          expect(vendor.name).toBe(testVendorData.name);
          expect(vendor.type).toBe(testVendorData.type);
          expect(vendor.status).toBe(testVendorData.status);
          expect(vendor.description).toBe(testVendorData.description);
          expect(vendor.website).toBe(testVendorData.website);
          expect(vendor.tax_id).toBe(testVendorData.tax_id);
          expect(vendor.registration_number).toBe(testVendorData.registration_number);
          expect(vendor.address).toEqual(testVendorData.address);
          expect(vendor.metadata).toEqual(testVendorData.metadata);
          
          // Store the ID for further tests
          createdVendorId = vendor.id;
        });

        it("should return 400 for invalid vendor data", async () => {
          const invalidData = {
            name: "", // Invalid: empty name
            type: "invalid_type", // Invalid: not in enum
            status: "invalid_status" // Invalid: not in enum
          };
          
          const response = await api.post("/admin/vendor-management/vendors", invalidData);
          
          expect(response.status).toBe(400);
          expect(response.data.error).toBeDefined();
        });
      });

      describe("GET /admin/vendor-management/vendors", () => {
        it("should list vendors with default pagination", async () => {
          const response = await api.get("/admin/vendor-management/vendors");
          
          expect(response.status).toBe(200);
          expect(response.data).toBeDefined();
          expect(response.data.vendors).toBeDefined();
          expect(Array.isArray(response.data.vendors)).toBe(true);
          expect(response.data.count).toBeDefined();
          expect(typeof response.data.count).toBe("number");
          expect(response.data.limit).toBeDefined();
          expect(response.data.offset).toBeDefined();
          expect(response.data.message).toBe("Vendors retrieved successfully");
          
          // Should include our created vendor
          const ourVendor = response.data.vendors.find((v: any) => v.id === createdVendorId);
          expect(ourVendor).toBeDefined();
        });

        it("should list vendors with custom pagination", async () => {
          const response = await api.get("/admin/vendor-management/vendors?limit=5&offset=0");
          
          expect(response.status).toBe(200);
          expect(response.data.limit).toBe(5);
          expect(response.data.offset).toBe(0);
          expect(response.data.vendors.length).toBeLessThanOrEqual(5);
        });

        it("should filter vendors by type", async () => {
          const response = await api.get("/admin/vendor-management/vendors?type=hotel_partner");
          
          expect(response.status).toBe(200);
          expect(response.data.vendors).toBeDefined();
          
          // All returned vendors should be hotel partners
          response.data.vendors.forEach((vendor: any) => {
            expect(vendor.type).toBe("hotel_partner");
          });
        });

        it("should filter vendors by status", async () => {
          const response = await api.get("/admin/vendor-management/vendors?status=active");
          
          expect(response.status).toBe(200);
          expect(response.data.vendors).toBeDefined();
          
          // All returned vendors should be active
          response.data.vendors.forEach((vendor: any) => {
            expect(vendor.status).toBe("active");
          });
        });

        it("should search vendors by name", async () => {
          const response = await api.get("/admin/vendor-management/vendors?search=Test API Hotel");
          
          expect(response.status).toBe(200);
          expect(response.data.vendors).toBeDefined();
          
          // Should find our test vendor
          const ourVendor = response.data.vendors.find((v: any) => v.id === createdVendorId);
          expect(ourVendor).toBeDefined();
        });
      });

      describe("GET /admin/vendor-management/vendors/:id", () => {
        it("should retrieve a specific vendor", async () => {
          expect(createdVendorId).toBeDefined();
          
          const response = await api.get(`/admin/vendor-management/vendors/${createdVendorId}`);
          
          expect(response.status).toBe(200);
          expect(response.data).toBeDefined();
          expect(response.data.vendor).toBeDefined();
          expect(response.data.message).toBe("Vendor retrieved successfully");
          
          const vendor = response.data.vendor;
          expect(vendor.id).toBe(createdVendorId);
          expect(vendor.name).toBe(testVendorData.name);
          expect(vendor.type).toBe(testVendorData.type);
        });

        it("should return 404 for non-existent vendor", async () => {
          const response = await api.get("/admin/vendor-management/vendors/non-existent-id");
          
          expect(response.status).toBe(404);
          expect(response.data.error).toBeDefined();
        });

        it("should return 400 for missing vendor ID", async () => {
          const response = await api.get("/admin/vendor-management/vendors/");
          
          expect(response.status).toBe(400);
        });
      });

      describe("PUT /admin/vendor-management/vendors/:id", () => {
        it("should update a vendor successfully", async () => {
          expect(createdVendorId).toBeDefined();
          
          const updateData = {
            name: "Updated API Test Hotel Partner",
            description: "Updated description for API testing",
            status: "inactive"
          };
          
          const response = await api.put(`/admin/vendor-management/vendors/${createdVendorId}`, updateData);
          
          expect(response.status).toBe(200);
          expect(response.data).toBeDefined();
          expect(response.data.vendor).toBeDefined();
          expect(response.data.message).toBe("Vendor updated successfully");
          
          const vendor = response.data.vendor;
          expect(vendor.id).toBe(createdVendorId);
          expect(vendor.name).toBe(updateData.name);
          expect(vendor.description).toBe(updateData.description);
          expect(vendor.status).toBe(updateData.status);
        });

        it("should return 404 for non-existent vendor", async () => {
          const response = await api.put("/admin/vendor-management/vendors/non-existent-id", {
            name: "Test Update"
          });
          
          expect(response.status).toBe(404);
          expect(response.data.error).toBeDefined();
        });

        it("should return 400 for invalid update data", async () => {
          expect(createdVendorId).toBeDefined();
          
          const invalidData = {
            type: "invalid_type",
            status: "invalid_status"
          };
          
          const response = await api.put(`/admin/vendor-management/vendors/${createdVendorId}`, invalidData);
          
          expect(response.status).toBe(400);
          expect(response.data.error).toBeDefined();
        });
      });

      describe("DELETE /admin/vendor-management/vendors/:id", () => {
        it("should delete a vendor successfully", async () => {
          expect(createdVendorId).toBeDefined();
          
          const response = await api.delete(`/admin/vendor-management/vendors/${createdVendorId}`);
          
          expect(response.status).toBe(200);
          expect(response.data).toBeDefined();
          expect(response.data.message).toBe("Vendor deleted successfully");
          expect(response.data.id).toBe(createdVendorId);
          
          // Verify it's deleted by trying to retrieve it
          const getResponse = await api.get(`/admin/vendor-management/vendors/${createdVendorId}`);
          expect(getResponse.status).toBe(404);
          
          // Clear the ID so cleanup doesn't try to delete again
          createdVendorId = "";
        });

        it("should return 404 for non-existent vendor", async () => {
          const response = await api.delete("/admin/vendor-management/vendors/non-existent-id");
          
          expect(response.status).toBe(404);
          expect(response.data.error).toBeDefined();
        });
      });
    });
  }
});
