name: Medusa Backend Deployment

# Trigger the workflow on any push to the 'main' branch
on:
  push:
    branches:
      - powderbyrne
      - perfect-piste-qa

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: powderbyrne-dev
    steps:
      # Step 1: Checkout code from the repository
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Configure NPM for GitHub Packages
        run: |
          echo "@camped-ai:registry=https://npm.pkg.github.com/" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.GIT_TOKEN }}" >> .npmrc

      # Step 2: Log in to Azure Container Registry (ACR)
      - name: Log in to Azure Container Registry
        uses: azure/docker-login@v1
        with:
          login-server: ${{ vars.POWDERBYRNE_ACR_SERVER }}
          username: ${{ vars.POWDERBYRNE_ACR_USERNAME }}
          password: ${{ secrets.POWDERBYRNE_ACR_PASSWORD }}

      # Step 3: Log in to Azure
      - name: Login to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      # Step 4: Set up Node.js environment
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      # Step 5: Build the Docker image for the Medusa backend
      - name: Build Docker image
        run: |
          docker build -t ${{ vars.ACR_DOCKER_IMAGE }} .

      # Step 6: Tag the Docker image for ACR
      - name: Tag Docker image for ACR
        run: |
          docker tag ${{ vars.ACR_DOCKER_IMAGE }}:latest ${{ vars.ACR_DOCKER_IMAGE_URL }}

      # Step 7: Push the Docker image to ACR
      - name: Push Docker image to ACR
        run: |
          docker push ${{ vars.ACR_DOCKER_IMAGE_URL }}

      - name: Set up Kubectl
        uses: azure/setup-kubectl@v1
        with:
          version: 'latest' # Or specify a specific version

      - name: Get AKS Credentials
        run: |
          az aks get-credentials --resource-group camped-medusa --name shop-test --admin --overwrite-existing

      - name: Inject Vars into Kubernetes Secrets YAML
        run: |
          cat <<EOF > k8s/config.yaml
          apiVersion: v1
          kind: ConfigMap
          metadata:
            name: shop-powderbyrne-config
          data:
            DATABASE_URL: "${{ vars.DATABASE_URL }}"
            ADMIN_CORS: "${{ vars.ADMIN_CORS }}"
            AUTH_CORS: "${{ vars.AUTH_CORS }}"
            MEDUSA_ADMIN_BACKEND_URL: "${{ vars.MEDUSA_ADMIN_BACKEND_URL }}"
            MEDUSA_BACKEND_URL: "${{ vars.MEDUSA_BACKEND_URL }}"
            MEDUSA_ADMIN_ONBOARDING_NEXTJS_DIRECTORY: "${{ vars.MEDUSA_ADMIN_ONBOARDING_NEXTJS_DIRECTORY }}"
            STORE_CORS: "${{ vars.STORE_CORS }}"
            JWT_SECRET: "${{ vars.JWT_SECRET }}"
            COOKIE_SECRET: "${{ vars.COOKIE_SECRET }}"
            PROJECT_NAME: "${{ vars.PROJECT_NAME }}"
            VITE_VERCEL_DEPLOY_HOOK_URL: "${{ vars.VITE_VERCEL_DEPLOY_HOOK_URL }}"
            MAIL_URL: "${{ vars.MAIL_URL }}"
            RAZORPAY_ID: "${{ vars.RAZORPAY_ID }}"
            RAZORPAY_ACCOUNT: "${{ vars.RAZORPAY_ACCOUNT }}"
            S3_FILE_URL: "${{ vars.S3_FILE_URL }}"
            S3_REGION: "${{ vars.S3_REGION }}"
            S3_BUCKET: "${{ vars.S3_BUCKET }}"
            S3_ENDPOINT: "${{ vars.S3_ENDPOINT }}"
            MEDUSA_STOREFRONT_URL: "${{ vars.MEDUSA_STOREFRONT_URL }}"
            VITE_MEDUSA_BACKEND_URL: "${{ vars.VITE_MEDUSA_BACKEND_URL }}"
            VITE_USER_MANUAL_URL: "${{ vars.VITE_USER_MANUAL_URL }}"
            GA4_PROPERTY_ID: "${{ vars.GA4_PROPERTY_ID }}"
            GA4_TYPE: "${{ vars.GA4_TYPE }}"
            GA4_PROJECT_ID: "${{ vars.GA4_PROJECT_ID }}"
            GA4_PRIVATE_KEY_ID: "${{ vars.GA4_PRIVATE_KEY_ID }}"
            GA4_CLIENT_EMAIL: "${{ vars.GA4_CLIENT_EMAIL }}"
            GA4_CLIENT_ID: "${{ vars.GA4_CLIENT_ID }}"
            GA4_AUTH_URI: "${{ vars.GA4_AUTH_URI }}"
            GA4_TOKEN_URI: "${{ vars.GA4_TOKEN_URI }}"
            WHATSAPP_PHONE_NUMBER_ID: "${{ vars.WHATSAPP_PHONE_NUMBER_ID }}"
            WHATSAPP_ACCESS_TOKEN: "${{ vars.WHATSAPP_ACCESS_TOKEN }}"
            WHATSAPP_BUSINESS_ACCOUNT_ID: "${{ vars.WHATSAPP_BUSINESS_ACCOUNT_ID }}"
            WHATSAPP_WEBHOOK_VERIFY_TOKEN: "${{ vars.WHATSAPP_WEBHOOK_VERIFY_TOKEN }}"
            WHATSAPP_WEBHOOK_SECRET: "${{ vars.WHATSAPP_WEBHOOK_SECRET }}"
            AZURE_OPENAI_DEPLOYMENT: "${{ vars.AZURE_OPENAI_DEPLOYMENT }}"
            AZURE_OPENAI_API_VERSION: "${{ vars.AZURE_OPENAI_API_VERSION }}"
            AZURE_OPENAI_API_ENDPOINT: "${{ vars.AZURE_OPENAI_API_ENDPOINT }}"
            AZURE_OPENAI_MODEL: "${{ vars.AZURE_OPENAI_MODEL }}"
            VITE_TOLGEE_BASE_URL: "${{ vars.VITE_TOLGEE_BASE_URL }}"
            VITE_TOLGEE_PROJECT_ID: "${{ vars.VITE_TOLGEE_PROJECT_ID }}"
          EOF

      - name: Inject Secrets into Kubernetes Secrets YAML
        run: |
          cat <<EOF > k8s/secrets.yaml
          apiVersion: v1
          kind: Secret
          metadata:
            name: shop-powderbyrne-secrets
          type: Opaque
          stringData:
            GA4_PRIVATE_KEY: ${{ secrets.GA4_PRIVATE_KEY }}
            SMTP_PASS: ${{ secrets.SMTP_PASS }}
            RAZORPAY_SECRET: ${{ secrets.RAZORPAY_SECRET }}
            RAZORPAY_WEBHOOK_SECRET: ${{ secrets.RAZORPAY_WEBHOOK_SECRET }}
            S3_ACCESS_KEY_ID: ${{ secrets.S3_ACCESS_KEY_ID }}
            S3_SECRET_ACCESS_KEY: ${{ secrets.S3_SECRET_ACCESS_KEY }}
            STRIPE_API_KEY: ${{ secrets.POWDERBYRNE_DEV_STRIPE_API_KEY }}
            STRIPE_WEBHOOK_SECRET_KEY: ${{ secrets.POWDERBYRNE_DEV_STRIPE_WEBHOOK_SECRET_KEY }}
            AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
            VITE_TOLGEE_API_KEY: ${{ secrets.VITE_TOLGEE_API_KEY }}
          EOF

      - name: Deploy to AKS
        run: |
          kubectl apply -f k8s/config.yaml
          kubectl apply -f k8s/secrets.yaml
          kubectl apply -f k8s/deployment.yaml
          kubectl apply -f k8s/service.yaml
          kubectl patch deployment powderbyrne-backend-v2 -p "{\"spec\": {\"template\": {\"metadata\": { \"labels\": {  \"date\": \"$(date +'%s')\"}}}}}"
          kubectl rollout restart deployment/medusa-backend

      - name: Finalize and Verify Deployment
        run: |
          echo "Deployment to AKS completed successfully!"
