apiVersion: apps/v1
kind: Deployment
metadata:
  name: powderbyrne-backend-v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: powderbyrne-backend-v2
  template:
    metadata:
      labels:
        app: powderbyrne-backend-v2
    spec:
      containers:
        - name: powderbyrne-backend-v2
          image: powderbyrne.azurecr.io/powderbyrne-backend:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 9000
          env:
            - name: DATABASE_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: DATABASE_URL
            - name: PORT
              value: "9000"
            - name: ADMIN_CORS
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: ADMIN_CORS
            - name: AUTH_CORS
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: AUTH_CORS
            - name: MEDUSA_ADMIN_BACKEND_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: MEDUSA_ADMIN_BACKEND_URL
            - name: MEDUSA_BACKEND_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: MEDUSA_BACKEND_URL
            - name: MEDUSA_ADMIN_ONBOARDING_TYPE
              value: "nextjs"
            - name: MEDUSA_ADMIN_ONBOARDING_NEXTJS_DIRECTORY
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: MEDUSA_ADMIN_ONBOARDING_NEXTJS_DIRECTORY
            - name: STORE_CORS
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: STORE_CORS
            - name: NODE_ENV
              value: "production"
            - name: NPM_CONFIG_PRODUCTION
              value: "false"
            - name: JWT_SECRET
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: JWT_SECRET
            - name: COOKIE_SECRET
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: COOKIE_SECRET
            - name: PROJECT_NAME
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: PROJECT_NAME
            - name: VITE_VERCEL_DEPLOY_HOOK_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: VITE_VERCEL_DEPLOY_HOOK_URL
            - name: SMTP_PASS
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: SMTP_PASS
            - name: MAIL_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: MAIL_URL
            - name: RAZORPAY_ID
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: RAZORPAY_ID
            - name: RAZORPAY_SECRET
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: RAZORPAY_SECRET
            - name: RAZORPAY_ACCOUNT
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: RAZORPAY_ACCOUNT
            - name: RAZORPAY_WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: RAZORPAY_WEBHOOK_SECRET
            - name: S3_FILE_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: S3_FILE_URL
            - name: S3_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: S3_ACCESS_KEY_ID
            - name: S3_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: S3_SECRET_ACCESS_KEY
            - name: S3_REGION
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: S3_REGION
            - name: S3_BUCKET
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: S3_BUCKET
            - name: S3_ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: S3_ENDPOINT
            - name: MEDUSA_STOREFRONT_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: MEDUSA_STOREFRONT_URL
            - name: STRIPE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: STRIPE_API_KEY
            - name: STRIPE_WEBHOOK_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: STRIPE_WEBHOOK_SECRET_KEY
            - name: VITE_MEDUSA_BACKEND_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: VITE_MEDUSA_BACKEND_URL
            - name: VITE_USER_MANUAL_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: VITE_USER_MANUAL_URL
            - name: GA4_PROPERTY_ID
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: GA4_PROPERTY_ID
            - name: GA4_TYPE
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: GA4_TYPE
            - name: GA4_PROJECT_ID
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: GA4_PROJECT_ID
            - name: GA4_PRIVATE_KEY_ID
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: GA4_PRIVATE_KEY_ID
            - name: GA4_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: GA4_PRIVATE_KEY
            - name: GA4_CLIENT_EMAIL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: GA4_CLIENT_EMAIL
            - name: GA4_CLIENT_ID
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: GA4_CLIENT_ID
            - name: GA4_AUTH_URI
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: GA4_AUTH_URI
            - name: GA4_TOKEN_URI
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: MEDUSA_STOREFRONT_URL
            - name: WHATSAPP_PHONE_NUMBER_ID
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: WHATSAPP_PHONE_NUMBER_ID
            - name: WHATSAPP_ACCESS_TOKEN
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: WHATSAPP_ACCESS_TOKEN
            - name: WHATSAPP_BUSINESS_ACCOUNT_ID
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: WHATSAPP_BUSINESS_ACCOUNT_ID
            - name: WHATSAPP_WEBHOOK_VERIFY_TOKEN
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: WHATSAPP_WEBHOOK_VERIFY_TOKEN
            - name: WHATSAPP_WEBHOOK_SECRET
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: WHATSAPP_WEBHOOK_SECRET
            - name: AZURE_OPENAI_DEPLOYMENT
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: AZURE_OPENAI_DEPLOYMENT
            - name: AZURE_OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: AZURE_OPENAI_API_KEY
            - name: AZURE_OPENAI_API_VERSION
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: AZURE_OPENAI_API_VERSION
            - name: AZURE_OPENAI_API_ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: AZURE_OPENAI_API_ENDPOINT
            - name: AZURE_OPENAI_MODEL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: AZURE_OPENAI_MODEL
            - name: VITE_TOLGEE_BASE_URL
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: VITE_TOLGEE_BASE_URL
            - name: VITE_TOLGEE_PROJECT_ID
              valueFrom:
                configMapKeyRef:
                  name: shop-powderbyrne-config
                  key: VITE_TOLGEE_PROJECT_ID
            - name: VITE_TOLGEE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrne-secrets
                  key: VITE_TOLGEE_API_KEY

          readinessProbe:
            httpGet:
              path: /health
              port: 9000
            initialDelaySeconds: 60
            periodSeconds: 15
            failureThreshold: 5
            timeoutSeconds: 5