import Stripe from "stripe";

export interface PaymentLinkOptions {
  amount: number;
  currency: string;
  orderId: string;
  description?: string;
  customerEmail?: string; // Will be stored in metadata, not used for prefilling
  expiresInHours?: number;
  metadata?: Record<string, any>;
}

export interface PaymentLinkResponse {
  id: string;
  url: string;
  expires_at?: string | null;
  status?: string;
  created_at?: string;
}

class StripePaymentLinkService {
  private stripe: Stripe;

  constructor(container) {
    try {
      // Initialize Stripe with API key from environment variables
      const apiKey = process.env.STRIPE_API_KEY;

      if (!apiKey) {
        throw new Error(
          "STRIPE_API_KEY is not defined in environment variables"
        );
      }

      this.stripe = new Stripe(apiKey, {
        apiVersion: "2024-04-10", // Use the latest API version
      });

      console.log("Stripe payment link service initialized successfully");
    } catch (error) {
      console.error("Failed to initialize Stripe Payment Link Service:", error);
      throw error;
    }
  }

  /**
   * Generate a payment link for an order
   * @param options - Payment link options
   * @returns The created payment link
   *
   * Note: Stripe Payment Links don't support prefilling customer email.
   * The customerEmail will be stored in metadata for reference.
   * Customers will need to enter their email during checkout.
   */
  async generatePaymentLink(options: PaymentLinkOptions) {
    const {
      amount,
      currency,
      orderId,
      description,
      customerEmail,
      expiresInHours = 24,
      metadata = {},
    } = options;

    try {
      const price = await this.stripe.prices.create({
        unit_amount: Math.round(amount * 100), // amount in cents
        currency: currency,
        product_data: {
          name: description || `Booking #${orderId}`,
        },
      });

      const paymentLink = await this.stripe.paymentLinks.create({
        line_items: [
          {
            price: price.id, // Use the Price ID here
            quantity: 1,
          },
        ],
        metadata: {
          order_id: orderId,
          ...metadata,
          // Store customer email in metadata since Payment Links don't support prefilling email
          ...(customerEmail && { customer_email: customerEmail }),
        },
        after_completion: {
          type: "redirect",
          redirect: {
            url: `${process.env.MEDUSA_BACKEND_URL}/hotel-management/bookings/${orderId}`,
          },
        },
        // Use customer_creation to control customer creation behavior
        // 'if_required' will create a customer only if needed (e.g., for subscriptions)
        // 'always' will always create a customer
        customer_creation: "if_required",
      });

      // Log the created payment link for debugging
      console.log(
        `Created payment link for order ${orderId}:`,
        JSON.stringify(paymentLink, null, 2)
      );

      return paymentLink;
    } catch (error) {
      console.error(
        `Failed to generate payment link for order ${orderId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Retrieve a payment link by ID
   * @param paymentLinkId - The payment link ID
   * @returns The payment link details
   */
  async retrievePaymentLink(paymentLinkId: string) {
    try {
      const paymentLink = await this.stripe.paymentLinks.retrieve(
        paymentLinkId
      );

      // Log the payment link details for debugging
      console.log(
        `Retrieved payment link ${paymentLinkId}:`,
        JSON.stringify(paymentLink, null, 2)
      );

      return paymentLink;
    } catch (error) {
      console.error(`Failed to retrieve payment link ${paymentLinkId}:`, error);
      throw error;
    }
  }
}

export default StripePaymentLinkService;
