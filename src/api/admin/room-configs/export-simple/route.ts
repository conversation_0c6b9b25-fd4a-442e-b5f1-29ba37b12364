import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import * as ExcelJS from 'exceljs';

/**
 * GET endpoint to export room configurations data with multi-sheet support
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log('Room config export request received');

    // Parse query parameters
    const { hotel_id, type, is_active, format = 'xlsx' } = req.query;

    console.log(`Exporting room configs with filters:`, { hotel_id, type, is_active, format });

    if (!hotel_id || hotel_id === 'all') {
      console.log('Warning: No specific hotel_id provided, exporting all room configurations');
    } else {
      console.log(`Filtering room configurations for hotel_id: ${hotel_id}`);
    }

    // Get product service using the correct module resolution
    const productService = req.scope.resolve(Modules.PRODUCT);
    if (!productService) {
      throw new Error('Product module service not found');
    }

    // Fetch all products
    console.log('Fetching all products...');
    const products = await productService.listProducts({});
    console.log(`Found ${products.length} total products`);

    // Filter for room configurations based on the provided filters
    const roomConfigs = products.filter(product => {
      // Check if it has metadata and is a room configuration (has type)
      if (!product.metadata || !product.metadata.type) return false;

      // Apply hotel_id filter if provided
      if (hotel_id && hotel_id !== 'all' && product.metadata.hotel_id !== hotel_id) {
        return false;
      }

      // Apply type filter if provided
      if (type && type !== 'all' && product.metadata.type !== type) {
        return false;
      }

      // Apply is_active filter if provided
      if (is_active && is_active !== 'all') {
        const isActiveValue = is_active === 'true';
        if (product.status === 'draft' && isActiveValue) return false;
        if (product.status === 'published' && !isActiveValue) return false;
      }

      return true;
    });

    console.log(`Filtered to ${roomConfigs.length} room configurations`);

    // Fetch room variants (individual rooms) for the filtered room configurations
    console.log('Fetching room variants...');
    const roomVariants = [];

    for (const roomConfig of roomConfigs) {
      try {
        const variants = await productService.listProductVariants({
          product_id: roomConfig.id
        });

        // Filter variants that represent individual rooms (have room_number in metadata)
        const roomVariantsForConfig = variants.filter(variant =>
          variant.metadata && variant.metadata.room_number
        );

        roomVariants.push(...roomVariantsForConfig.map(variant => ({
          ...variant,
          room_config_name: roomConfig.title,
          room_config_id: roomConfig.id
        })));
      } catch (error) {
        console.error(`Error fetching variants for room config ${roomConfig.id}:`, error);
      }
    }

    console.log(`Found ${roomVariants.length} individual rooms`);

    // Process room configurations data
    const processedRoomConfigs = roomConfigs.map(config => {
      const metadata = config.metadata || {};

      // Convert metadata to proper types with defaults
      const max_adults = metadata.max_adults ? Number(metadata.max_adults) : 1;
      const max_children = metadata.max_children ? Number(metadata.max_children) : 0;
      const max_infants = metadata.max_infants ? Number(metadata.max_infants) : 0;
      const max_extra_beds = metadata.max_extra_beds ? Number(metadata.max_extra_beds) : 0;
      const max_cots = metadata.max_cots ? Number(metadata.max_cots) : 0;
      const max_adults_beyond_capacity = metadata.max_adults_beyond_capacity ? Number(metadata.max_adults_beyond_capacity) : 0;

      // Handle amenities array
      let amenitiesStr = '';
      if (metadata.amenities) {
        if (Array.isArray(metadata.amenities)) {
          amenitiesStr = metadata.amenities.join(', ');
        } else if (typeof metadata.amenities === 'string') {
          amenitiesStr = metadata.amenities;
        } else {
          try {
            amenitiesStr = JSON.stringify(metadata.amenities);
          } catch (e) {
            amenitiesStr = '';
          }
        }
      }

      return {
        id: config.id,
        name: config.title || '',
        description: config.description || '',
        max_adults,
        max_children,
        max_infants,
        max_extra_beds,
        max_cots,
        max_adults_beyond_capacity,
        room_size: metadata.room_size || '',
        bed_configuration: metadata.bed_type || '',
        amenities: amenitiesStr
      };
    });

    // Process rooms data
    const processedRooms = roomVariants.map(room => {
      const metadata = room.metadata || {};

      return {
        id: room.id,
        room_number: metadata.room_number || '',
        name: room.title || `Room ${metadata.room_number || room.id.slice(-4)}`,
        room_config_name: room.room_config_name || '',
        room_config_id: room.room_config_id || '',
        floor: metadata.floor || '',
        status: metadata.status || 'available',
        is_active: metadata.is_active !== false,
        notes: metadata.notes || '',
        left_room: metadata.left_room || '',
        right_room: metadata.right_room || '',
        opposite_room: metadata.opposite_room || '',
        connected_room: metadata.connected_room || '',
        created_at: room.created_at ? new Date(room.created_at).toISOString().split('T')[0] : '',
        updated_at: room.updated_at ? new Date(room.updated_at).toISOString().split('T')[0] : ''
      };
    });

    // Create Excel workbook with multi-sheet support
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Room Configurations
    const roomConfigSheet = workbook.addWorksheet('Room Configurations');

    // Define columns for room configurations to match the required export format
    roomConfigSheet.columns = [
      { header: 'ID', key: 'id', width: 30 },
      { header: 'Name*', key: 'name', width: 30 },
      { header: 'Description', key: 'description', width: 50 },
      { header: 'Max Adults*', key: 'max_adults', width: 15 },
      { header: 'Max Children', key: 'max_children', width: 15 },
      { header: 'Max Infants', key: 'max_infants', width: 15 },
      { header: 'Max Extra Beds', key: 'max_extra_beds', width: 15 },
      { header: 'Max Cots', key: 'max_cots', width: 15 },
      { header: 'Max Adults Beyond Capacity', key: 'max_adults_beyond_capacity', width: 25 },
      { header: 'Room Size', key: 'room_size', width: 15 },
      { header: 'Bed Configuration', key: 'bed_configuration', width: 20 },
      { header: 'Amenities (comma separated)', key: 'amenities', width: 40 }
    ];

    // Style room config header row
    roomConfigSheet.getRow(1).font = { bold: true };
    roomConfigSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add room configuration data rows
    processedRoomConfigs.forEach(data => {
      roomConfigSheet.addRow(data);
    });

    // Sheet 2: Rooms
    const roomsSheet = workbook.addWorksheet('Rooms');

    // Define columns for rooms to match the import template format
    roomsSheet.columns = [
      { header: 'ID', key: 'id', width: 30 },
      { header: 'Room Number*', key: 'room_number', width: 15 },
      { header: 'Name', key: 'name', width: 30 },
      { header: 'Room Config Name*', key: 'room_config_name', width: 25 },
      { header: 'Room Config ID', key: 'room_config_id', width: 30 },
      { header: 'Floor', key: 'floor', width: 10 },
      { header: 'Status', key: 'status', width: 15 },
      { header: 'Active', key: 'is_active', width: 10 },
      { header: 'Notes', key: 'notes', width: 40 },
      { header: 'Left Room', key: 'left_room', width: 15 },
      { header: 'Right Room', key: 'right_room', width: 15 },
      { header: 'Opposite Room', key: 'opposite_room', width: 15 },
      { header: 'Connected Room', key: 'connected_room', width: 15 },
      { header: 'Created At', key: 'created_at', width: 15 },
      { header: 'Updated At', key: 'updated_at', width: 15 }
    ];

    // Style rooms header row
    roomsSheet.getRow(1).font = { bold: true };
    roomsSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD0F0D0' }
    };

    // Add rooms data rows
    processedRooms.forEach(data => {
      roomsSheet.addRow(data);
    });

    // Generate filename based on filters
    let filename = 'room-configs-and-rooms-export';
    if (hotel_id && hotel_id !== 'all') {
      // Try to get hotel name for better filename
      try {
        const { HOTEL_MODULE } = require("../../../../modules/hotel-management/hotel");
        const hotelService = req.scope.resolve(HOTEL_MODULE);
        if (hotelService) {
          const hotel = await hotelService.retrieveHotel(hotel_id);
          if (hotel && hotel.name) {
            const sanitizedHotelName = hotel.name.replace(/[^a-zA-Z0-9]/g, '-');
            filename = `room-configs-and-rooms-${sanitizedHotelName}`;
          }
        }
      } catch (error) {
        console.log('Could not get hotel name for filename, using default');
      }
    }

    const currentDate = new Date().toISOString().split('T')[0];
    filename += `-${currentDate}.xlsx`;

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=${filename}`);

    // Write workbook to response
    await workbook.xlsx.write(res);
    console.log(`Export completed successfully: ${processedRoomConfigs.length} room configurations and ${processedRooms.length} rooms exported`);

  } catch (error) {
    console.error('Error exporting room configurations and rooms:', error);
    console.error('Error stack:', error.stack);

    res.status(500).json({
      message: 'Error exporting room configurations and rooms',
      error: error.message
    });
  }
};
