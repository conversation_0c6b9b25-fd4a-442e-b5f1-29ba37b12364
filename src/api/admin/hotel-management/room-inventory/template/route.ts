import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from "exceljs";
import { format, addDays } from "date-fns";
import { Modules } from "@camped-ai/framework/utils";
import { IProductModuleService } from "@camped-ai/framework/types";

/**
 * GET endpoint to download a template for bulk room inventory import
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get hotel_id from query parameters
    const { hotel_id } = req.query;
    console.log("Template generation requested for hotel_id:", hotel_id);

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Add a worksheet for room inventory
    const worksheet = workbook.addWorksheet("Room Inventory");

    // Define columns - inventory_item_id as first column to match export template
    worksheet.columns = [
      { header: "inventory_item_id", key: "inventory_item_id", width: 40 },
      { header: "hotel_id", key: "hotel_id", width: 40 },
      { header: "room_number", key: "room_number", width: 15 },
      { header: "room_config_name", key: "room_config_name", width: 25 },
      { header: "from_date", key: "from_date", width: 15 },
      { header: "to_date", key: "to_date", width: 15 },
      { header: "available_quantity", key: "available_quantity", width: 15 },
      { header: "status", key: "status", width: 15 },
      { header: "notes", key: "notes", width: 40 },
      { header: "check_in_time", key: "check_in_time", width: 15 },
      { header: "check_out_time", key: "check_out_time", width: 15 },
      { header: "is_noon_to_noon", key: "is_noon_to_noon", width: 15 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Initialize variables
    let rooms = [];
    let inventoryItems = [];
    let resolvedHotelId = null;
    let hotelData = null;

    // Get hotel-specific rooms and their inventory items
    try {
      const query = req.scope.resolve("query");

      // First, resolve hotel_id if it's provided (could be slug or ID)
      if (hotel_id && hotel_id !== "all") {
        console.log(`Resolving hotel identifier: ${hotel_id}`);

        // Try to find hotel by ID first
        let { data: hotels } = await query.graph({
          entity: "hotel",
          filters: { id: hotel_id as string },
          fields: ["id", "name", "handle", "category_id"],
        });

        // If not found by ID, try by handle/slug
        if (!hotels || hotels.length === 0) {
          console.log(`Hotel not found by ID, trying handle: ${hotel_id}`);
          const handleResult = await query.graph({
            entity: "hotel",
            filters: { handle: hotel_id as string },
            fields: ["id", "name", "handle", "category_id"],
          });
          hotels = handleResult.data;
        }

        if (hotels && hotels.length > 0) {
          hotelData = hotels[0];
          resolvedHotelId = hotelData.id;
          console.log(`✅ Resolved hotel: ${hotelData.name} (ID: ${resolvedHotelId})`);
        } else {
          console.log(`❌ No hotel found with identifier: ${hotel_id}`);
        }
      }

      // Get rooms based on hotel filter
      if (resolvedHotelId) {
        console.log(`Fetching rooms for hotel: ${resolvedHotelId}`);

        // Get room variants filtered by hotel
        const { data: hotelRooms } = await query.graph({
          entity: "product_variant",
          filters: {
            "product.metadata.hotel_id": resolvedHotelId,
          },
          fields: [
            "id",
            "title",
            "metadata",
            "product.id",
            "product.title",
            "product.metadata",
          ],
        });

        rooms = hotelRooms || [];
        console.log(`Found ${rooms.length} rooms for hotel ${resolvedHotelId}`);

        // Get inventory items for these specific rooms
        if (rooms.length > 0) {
          const roomIds = rooms.map(room => room.id);
          const { data: inventoryItemsData } = await query.graph({
            entity: "inventory_item",
            filters: { variant_id: roomIds },
            fields: ["id", "variant_id"],
          });
          inventoryItems = inventoryItemsData || [];
          console.log(`Found ${inventoryItems.length} inventory items for hotel rooms`);
        }
      } else {
        console.log("No hotel filter provided, fetching all rooms");
        // Fallback to all rooms if no hotel specified
        const productModuleService: IProductModuleService = req.scope.resolve(
          Modules.PRODUCT
        );
        const variantsResult = await productModuleService.listProductVariants({});
        rooms = variantsResult;

        // Get all inventory items
        const { data: inventoryItemsData } = await query.graph({
          entity: "inventory_item",
          filters: {},
          fields: ["id", "variant_id"],
        });
        inventoryItems = inventoryItemsData || [];
      }
    } catch (error) {
      console.error("Error fetching hotel-specific data:", error);
    }

    // Add a reference sheet for rooms
    const roomSheet = workbook.addWorksheet("Rooms Reference");

    // Define columns for the room reference sheet - match export template structure
    roomSheet.columns = [
      { header: "inventory_item_id", key: "inventory_item_id", width: 40 },
      { header: "room_name", key: "room_name", width: 25 },
      { header: "room_number", key: "room_number", width: 15 },
      { header: "room_config_name", key: "room_config_name", width: 25 },
      { header: "hotel_id", key: "hotel_id", width: 40 },
      { header: "floor", key: "floor", width: 10 },
    ];

    // Style the header row
    roomSheet.getRow(1).font = { bold: true };
    roomSheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Add room data to the reference sheet - directly use all rooms without checking for inventory items
    let roomsAdded = 0;

    // Populate room reference sheet with hotel-specific data
    for (const room of rooms) {
      // Find the corresponding inventory item for this room
      const inventoryItem = inventoryItems.find(item => item.variant_id === room.id);
      const actualInventoryId = inventoryItem ? inventoryItem.id : room.id;

      roomSheet.addRow({
        inventory_item_id: actualInventoryId, // Use actual inventory item ID
        room_name: room.title || room.product?.title || `Room ${roomsAdded + 1}`,
        room_number: String(room.metadata?.room_number || room.title || `${roomsAdded + 1}`), // Ensure string format
        room_config_name: room.product?.title || room.metadata?.room_config_name || room.metadata?.room_type || "Standard Room",
        hotel_id: resolvedHotelId || room.metadata?.hotel_id || room.product?.metadata?.hotel_id || "",
        floor: room.metadata?.floor || "",
      });
      roomsAdded++;
    }

    console.log(`Added ${roomsAdded} rooms to the reference sheet`);

    // Format room_number column in room reference sheet as text
    const roomRefRoomNumberColumn = roomSheet.getColumn('C'); // room_number is the 3rd column in room reference
    roomRefRoomNumberColumn.numFmt = '@'; // Text format

    // If no rooms were found, add a sample one with hotel-specific data if available
    if (roomsAdded === 0) {
      const sampleRoomId = "sample-room-id";
      const sampleHotelId = resolvedHotelId || "sample-hotel-id";

      roomSheet.addRow({
        inventory_item_id: sampleRoomId,
        room_name: hotelData ? `${hotelData.name} - Standard Room 101` : "Standard Room 101",
        room_number: "101",
        room_config_name: "Standard Room",
        hotel_id: sampleHotelId,
        floor: "1",
      });
      console.log(`Added a sample room for hotel: ${sampleHotelId}`);
    }

    // Add status dropdown validation to the main inventory sheet
    console.log("Adding status dropdown validation to main inventory sheet");

    // Define status options (matching export functionality)
    const statusOptions = [
      "available",
      "booked",
      "reserved",
      "maintenance",
      "cleaning",
      "unavailable",
      "reserved_unassigned",
      "cart_reserved",
      "on_demand"
    ];

    // Create data validation for status column (column H, index 8)
    const statusColumnLetter = 'H'; // status is the 8th column (A=1, B=2, ..., H=8)

    // Apply validation to a reasonable range (rows 2-1000)
    worksheet.dataValidations.add(`${statusColumnLetter}2:${statusColumnLetter}1000`, {
      type: 'list',
      allowBlank: true,
      formulae: [`"${statusOptions.join(',')}"`],
      showErrorMessage: true,
      errorStyle: 'error',
      errorTitle: 'Invalid Status',
      error: `Please select a valid status from the dropdown: ${statusOptions.join(', ')}`
    });

    // Format room_number column as text to prevent Excel from treating it as a number
    // This prevents validation errors where room_number is expected to be a string
    const roomNumberColumnLetter = 'C'; // room_number is the 3rd column

    // Set the entire room_number column to text format
    const roomNumberColumn = worksheet.getColumn(roomNumberColumnLetter);
    roomNumberColumn.numFmt = '@'; // '@' is Excel's text format

    console.log("Applied text formatting to room_number column to prevent type conversion issues");

    // Add multiple sample rows to the room inventory sheet with real hotel-specific data
    const today = new Date();
    const tomorrow = addDays(today, 1);
    const nextWeek = addDays(today, 7);

    console.log(`Creating sample rows for ${Math.min(rooms.length, 5)} rooms`);

    if (rooms.length > 0) {
      // Create sample rows for up to 5 rooms to give users multiple examples
      const sampleRoomsCount = Math.min(rooms.length, 5);

      for (let i = 0; i < sampleRoomsCount; i++) {
        const room = rooms[i];
        const inventoryItem = inventoryItems.find(item => item.variant_id === room.id);

        const sampleInventoryId = inventoryItem ? inventoryItem.id : room.id;
        // Ensure room_number is always a string to prevent validation errors
        const sampleRoomNumber = String(room.metadata?.room_number || room.title || `${i + 1}01`);
        const sampleRoomConfigName = room.product?.title || room.metadata?.room_config_name || room.metadata?.room_type || "Standard Room";
        const sampleHotelId = resolvedHotelId || room.metadata?.hotel_id || room.product?.metadata?.hotel_id || "sample-hotel-id";

        // Vary the sample data to show different scenarios
        const sampleStatuses = ["available", "maintenance", "available", "cleaning", "available"];
        const sampleNotes = [
          `Sample record for ${room.title || `Room ${sampleRoomNumber}`}`,
          "Room under maintenance - sample",
          "Available for booking - sample",
          "Cleaning in progress - sample",
          "Ready for guests - sample"
        ];

        worksheet.addRow({
          inventory_item_id: sampleInventoryId,
          hotel_id: sampleHotelId,
          room_number: sampleRoomNumber,
          room_config_name: sampleRoomConfigName,
          from_date: format(addDays(today, i), "yyyy-MM-dd"), // Stagger dates
          to_date: format(addDays(tomorrow, i), "yyyy-MM-dd"),
          available_quantity: 1,
          status: sampleStatuses[i],
          notes: sampleNotes[i],
          check_in_time: "14:00",
          check_out_time: "11:00",
          is_noon_to_noon: "false",
        });

        console.log(`Added sample row ${i + 1}: Room ${sampleRoomNumber}, Inventory ID: ${sampleInventoryId}`);
      }
    } else {
      // Fallback: create one sample row with generic data
      console.log("No rooms found, creating generic sample data");

      worksheet.addRow({
        inventory_item_id: "sample-inventory-item-id",
        hotel_id: resolvedHotelId || "sample-hotel-id",
        room_number: "101", // Already a string
        room_config_name: "Standard Room",
        from_date: format(today, "yyyy-MM-dd"),
        to_date: format(tomorrow, "yyyy-MM-dd"),
        available_quantity: 1,
        status: "available",
        notes: hotelData ? `Sample record for ${hotelData.name}` : "Sample inventory record",
        check_in_time: "14:00",
        check_out_time: "11:00",
        is_noon_to_noon: "false",
      });
    }

    // Add instructions sheet
    const instructionsSheet = workbook.addWorksheet("Instructions");
    instructionsSheet.columns = [
      { header: "Field", key: "field", width: 20 },
      { header: "Description", key: "description", width: 60 },
      { header: "Required", key: "required", width: 15 },
      { header: "Format", key: "format", width: 30 },
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true };
    instructionsSheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Add instructions for each field - include new fields
    const instructions = [
      {
        field: "inventory_item_id",
        description: "The unique inventory item ID for the room (required for updates)",
        required: "Yes",
        format: "Text (UUID)",
      },
      {
        field: "hotel_id",
        description: "The hotel ID where the room is located",
        required: "Yes",
        format: "Text (UUID)",
      },
      {
        field: "room_number",
        description: "The room number (e.g., 101, 202). Must be text format - do not let Excel convert to numbers.",
        required: "Yes",
        format: "Text (formatted as text to prevent Excel number conversion)",
      },
      {
        field: "room_config_name",
        description: "The room configuration/type name (e.g., Standard Room, Deluxe Suite)",
        required: "No",
        format: "Text",
      },
      {
        field: "from_date",
        description:
          "The start date of the inventory period. Must be in ISO format.",
        required: "Yes",
        format: "YYYY-MM-DD (e.g., 2023-12-31)",
      },
      {
        field: "to_date",
        description:
          "The end date of the inventory period. Must be in ISO format.",
        required: "Yes",
        format: "YYYY-MM-DD (e.g., 2024-01-01)",
      },
      {
        field: "available_quantity",
        description:
          "The number of rooms available (usually 0 or 1 for individual rooms)",
        required: "Yes",
        format: "Number",
      },
      {
        field: "status",
        description: "The status of the room during this period. Use the dropdown to select from valid options.",
        required: "No",
        format: "Dropdown: available, booked, reserved, maintenance, cleaning, unavailable, reserved_unassigned, cart_reserved, on_demand",
      },
      {
        field: "notes",
        description: "Additional notes about the inventory period",
        required: "No",
        format: "Text",
      },
      {
        field: "check_in_time",
        description: "The check-in time for this period",
        required: "No",
        format: "HH:MM (24-hour format)",
      },
      {
        field: "check_out_time",
        description: "The check-out time for this period",
        required: "No",
        format: "HH:MM (24-hour format)",
      },
      {
        field: "is_noon_to_noon",
        description: "Whether the check-in/check-out is noon to noon",
        required: "No",
        format: "true or false",
      },
    ];

    // Add important notes
    instructionsSheet.addRow({
      field: "IMPORTANT NOTES",
      description:
        'Date fields must be in YYYY-MM-DD format (e.g., 2023-12-31). Excel may automatically format dates differently when you edit them. To ensure proper formatting, you may need to format the date cells as "Text" before entering dates, or use custom date formatting in Excel.',
      required: "",
      format: "",
    });

    instructionsSheet.addRow({
      field: "ROOM REFERENCE",
      description:
        'Use the "Rooms Reference" sheet to find the correct inventory_item_id for each room. This sheet contains all rooms for the selected hotel with their corresponding IDs, room numbers, and configuration names.',
      required: "",
      format: "",
    });

    instructionsSheet.addRow({
      field: "STATUS DROPDOWN",
      description:
        'The status column has dropdown validation. Click on any status cell to see the available options. Invalid status values will show an error message.',
      required: "",
      format: "",
    });

    instructionsSheet.addRow({
      field: "ROOM NUMBER FORMAT",
      description:
        'The room_number column is pre-formatted as text to prevent Excel from converting room numbers to numeric values. This prevents import validation errors. If you add new rows, ensure room numbers remain as text.',
      required: "",
      format: "",
    });

    instructions.forEach((instruction) => {
      instructionsSheet.addRow(instruction);
    });

    // Set content type and headers for Excel file download
    // Create hotel-specific filename if hotel data is available
    const filename = hotelData
      ? `room-inventory-import-template-${hotelData.handle || hotelData.name.toLowerCase().replace(/\s+/g, '-')}.xlsx`
      : "room-inventory-import-template.xlsx";

    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}`
    );

    // Write the workbook to the response
    await workbook.xlsx.write(res);
  } catch (error) {
    console.error("Error generating template:", error);
    res.status(500).json({ message: "Error generating template" });
  }
};
