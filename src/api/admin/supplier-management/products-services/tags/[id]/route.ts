import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { UpdateTagWorkflow } from "src/workflows/supplier-products-services/update-tag";
import { DeleteTagWorkflow } from "src/workflows/supplier-products-services/delete-tag";
import { PostAdminUpdateTag } from "../../validators";

type PostAdminUpdateTagType = z.infer<typeof PostAdminUpdateTag>;

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        message: "Tag ID is required",
      });
    }

    const supplierProductsServicesService: SupplierProductsServicesModuleService = 
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const tag = await supplierProductsServicesService.retrieveTag(id);

    res.json({ tag });
  } catch (error) {
    if (error.message.includes("not found")) {
      return res.status(404).json({
        message: error.message,
      });
    }

    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to retrieve tag",
    });
  }
};

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateTagType>,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        message: "Tag ID is required",
      });
    }

    // Use validatedBody from middleware - this is already parsed and validated
    const inputData = req.validatedBody;

    if (!inputData) {
      return res.status(400).json({
        message: "Invalid request body",
      });
    }

    const { result } = await UpdateTagWorkflow(req.scope).run({
      input: {
        id,
        data: inputData,
      },
    });

    res.json({ tag: result });
  } catch (error) {
    console.error("❌ Error updating tag:", error);
    if (error.message.includes("not found")) {
      return res.status(404).json({
        message: error.message,
      });
    }

    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update tag",
    });
  }
};

export const DELETE = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        message: "Tag ID is required",
      });
    }


    await DeleteTagWorkflow(req.scope).run({
      input: { id },
    });

    res.json({
      deleted: true,
      id,
      message: "Tag deleted successfully",
    });
  } catch (error) {
    console.error("❌ Error deleting tag:", error);
    if (error.message && error.message.includes("not found")) {
      return res.status(404).json({
        message: error.message,
      });
    }

    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete tag",
    });
  }
};
