import { AuthenticatedMedusaRequest, MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { CreateTagWorkflow } from "src/workflows/supplier-products-services/create-tag";
import {
  PostAdminCreateTag,
  GetAdminTagsQuery,
} from "../validators";

type PostAdminCreateTagType = z.infer<typeof PostAdminCreateTag>;
type GetAdminTagsQueryType = z.infer<typeof GetAdminTagsQuery>;

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateTagType>,
  res: MedusaResponse
) => {
  try {
    console.log("🔍 POST /admin/supplier-management/products-services/tags");
    console.log("Request body:", req.body);
    console.log("Validated body:", req.validatedBody);

    // Use validatedBody from middleware - this is already parsed and validated
    const inputData = req.validatedBody;

    if (!inputData) {
      return res.status(400).json({
        message: "Invalid request body",
      });
    }

    const { result } = await CreateTagWorkflow(req.scope).run({
      input: inputData,
    });

    console.log("Tag created successfully via workflow:", result);
    res.status(201).json({ tag: result });
  } catch (error) {
    console.error("❌ Error creating tag:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create tag",
    });
  }
};

export const GET = async (
  req: MedusaRequest<{}, GetAdminTagsQueryType>,
  res: MedusaResponse
) => {
  try {
    console.log("🔍 GET /admin/supplier-management/products-services/tags");
    console.log("Query params:", req.query);

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Parse query parameters
    const limit = parseInt(req.query.limit as string) || 25;
    const offset = parseInt(req.query.offset as string) || 0;

    // Build filters
    const filters: any = {};

    if (req.query.name) {
      filters.name = req.query.name;
    }

    if (req.query.is_active !== undefined) {
      filters.is_active = req.query.is_active === "true";
    }

    console.log("Listing tags with filters:", filters, "options:", { skip: offset, take: limit });

    const result = await supplierProductsServicesService.listTags(
      filters,
      { skip: offset, take: limit }
    );

    console.log("Raw result from listTags:", result);
    console.log("Result type:", typeof result, "Is array:", Array.isArray(result));

    // Handle different return formats from generated method
    let tags, count;
    if (Array.isArray(result)) {
      // Direct array format (what the generated method returns)
      tags = result;
      count = result.length;
    } else if (result && result.data) {
      // Object with data property (custom format)
      tags = result.data;
      count = result.count || result.data.length;
    } else {
      tags = [];
      count = 0;
    }

    console.log("✅ Tags listed successfully:", {
      count: count,
      tagsLength: tags.length,
      limit,
      offset,
    });

    res.json({
      tags: tags,
      count: count,
      limit: limit,
      offset: offset,
    });
  } catch (error) {
    console.error("❌ Error listing tags:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to list tags",
    });
  }
};
