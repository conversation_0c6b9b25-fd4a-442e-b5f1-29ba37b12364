import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import * as ExcelJS from 'exceljs';

// Query validation schema
const GetAdminExportProductServicesQuery = z.object({
  format: z.enum(["excel", "csv"]).optional().default("excel"),
  fields: z.string().optional(), // Comma-separated list of fields to include in export
  category_id: z.string().min(1, "Category selection is required"), // Required category filter
  status: z.enum(["active", "inactive"]).optional(),
  type: z.enum(["Product", "Service"]).optional(),
});

type GetAdminExportProductServicesQueryType = z.infer<typeof GetAdminExportProductServicesQuery>;

/**
 * GET /admin/supplier-management/products-services/export
 * Export products/services data in Excel format matching import template structure exactly
 */
export const GET = async (
  req: MedusaRequest<{}, GetAdminExportProductServicesQueryType>,
  res: MedusaResponse
) => {
  try {

    // Validate query parameters
    const validatedQuery = GetAdminExportProductServicesQuery.parse(req.query);
    const { format, fields, category_id, status, type } = validatedQuery;

    // Parse selected fields if provided
    const selectedFields = fields ? fields.split(',').map(f => f.trim()).filter(Boolean) : null;

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Build filters for products/services - category is mandatory
    const filters: any = {
      category_id: category_id, // Required filter
    };

    if (status) filters.status = status;
    if (type) filters.type = type;


    // Fetch products/services with filters
    const result = await supplierProductsServicesService.listProductServicesWithFiltersAndCount(
      filters,
      { skip: 0, take: 10000 } // Large limit to get all data for export
    );

    const productServices = result.data || [];

    if (productServices.length === 0) {
      return res.status(404).json({
        type: "no_data",
        message: "No products/services found matching the specified criteria",
      });
    }


    // Fetch required data for template generation (exactly like import template)
    const [categoriesResult, unitTypesResult, tagsResult, hotelsResult, destinationsResult] : any[] = await Promise.all([
      // Fetch the specific category with full details
      supplierProductsServicesService.retrieveCategory(category_id as string).then(cat => [cat]),
      // Fetch unit types from the same API endpoint used by the config page
      supplierProductsServicesService.listUnitTypes({ is_active: true }, { skip: 0, take: 100 }),
      // Fetch tags from the same API endpoint used by the config page
      supplierProductsServicesService.listTags({ is_active: true }, { skip: 0, take: 100 }),
      // Fetch hotels data directly from database using query service
      (async () => {
        try {
          const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
          const result = await query.graph({
            entity: "hotel",
            fields: ["id", "name", "destination_id", "is_active"],
            filters: { is_active: true },
            pagination: { skip: 0, take: 100 }
          });
          return { hotels: result.data || [] };
        } catch (err) {
          return { hotels: [] };
        }
      })(),
      // Fetch destinations data directly from database using query service
      (async () => {
        try {
          const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
          const result = await query.graph({
            entity: "destination",
            fields: ["id", "name", "country", "is_active"],
            filters: { is_active: true },
            pagination: { skip: 0, take: 100 }
          });
          return { destinations: result.data || [] };
        } catch (err) {
          return { destinations: [] };
        }
      })()
    ]);

    const categories = Array.isArray(categoriesResult) ? categoriesResult : [];
    const unitTypes = unitTypesResult?.data || unitTypesResult || [];
    const tags = tagsResult?.data || tagsResult || [];
    const hotels = hotelsResult?.hotels || [];
    const destinations = destinationsResult?.destinations || [];

    if (categories.length === 0) {
      return res.status(400).json({
        type: "category_not_found",
        message: `Category with ID ${category_id} not found`,
      });
    }

    const category = categories[0];

    // Create lookup maps for efficient data transformation (used by both CSV and Excel)
    const unitTypeMap = new Map();
    unitTypes.forEach((ut: any) => {
      unitTypeMap.set(ut.id, ut.name || ut);
    });

    const tagMap = new Map();
    tags.forEach((tag: any) => {
      tagMap.set(tag.id, tag.name || tag);
    });

    // Handle CSV export (simple format)
    if (format === "csv") {
      // Define all available fields for CSV export
      const allAvailableFields = {
        name: (ps: any) => ps.name,
        type: (ps: any) => ps.type,
        description: (ps: any) => ps.description || '',
        status: (ps: any) => ps.status,
        category_name: () => category.name,
        unit_type_name: (ps: any) => unitTypeMap.get(ps.unit_type_id) || ps.unit_type_id || '',
        tag_names: (ps: any) => {
          if (ps.tag_ids && Array.isArray(ps.tag_ids)) {
            return ps.tag_ids.map((tagId: string) => tagMap.get(tagId)).filter(Boolean).join(', ');
          }
          return '';
        },
        base_cost: (ps: any) => ps.base_cost || '',
        created_at: (ps: any) => ps.created_at,
        updated_at: (ps: any) => ps.updated_at,
      };

      // Determine which fields to include
      const fieldsToInclude = selectedFields && selectedFields.length > 0
        ? selectedFields.filter(field => field in allAvailableFields)
        : Object.keys(allAvailableFields);

      if (fieldsToInclude.length === 0) {
        return res.status(400).json({
          type: "invalid_fields",
          message: "No valid fields selected for export",
        });
      }

      // Generate CSV data with only selected fields
      const csvData = productServices.map(ps => {
        const row: Record<string, any> = {};
        fieldsToInclude.forEach(field => {
          row[field] = allAvailableFields[field](ps);
        });
        return row;
      });

      const headers = fieldsToInclude;
      const csvContent = [
        headers.join(','),
        ...csvData.map(row =>
          headers.map(header => {
            const value = row[header];
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value || '';
          }).join(',')
        )
      ].join('\n');

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `products_services_${category.name.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.csv`;

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      return res.send(csvContent);
    }

    // Excel export - use exact template structure with actual data
    // Get dynamic field schema for this category (only fields used in products)
    const allDynamicFields = (category.dynamic_field_schema as unknown as any[]) || [];
    const dynamicFieldSchema = allDynamicFields.filter((field: any) =>
      field.used_in_product !== false // Show field if used_in_product is true or undefined (backward compatibility)
    );


    // Base columns that match the import template exactly
    const baseColumns = [
      'category_name',          // Category name (for easy identification and parsing)
      'type',                   // Type dropdown (Product/Service)
      'description',            // Description textarea
      'unit_type_name',         // Unit Type name (user-friendly dropdown)
      'tag_names',              // Tag names (user-friendly, comma-separated)
      'base_cost',              // Base Cost input
      'status'                  // Status (active/inactive)
    ];

    // Add dynamic field columns based on category schema (only fields used in products)
    const dynamicColumns: string[] = [];
    dynamicFieldSchema.forEach((field: any) => {
      const fieldKey = field.key || field.field_name;
      if (field.type === 'number-range') {
        // For number-range fields, create separate from and to columns
        dynamicColumns.push(`${fieldKey}_from`);
        dynamicColumns.push(`${fieldKey}_to`);
      } else {
        dynamicColumns.push(fieldKey);
      }
    });

    // Determine which columns to include based on field selection
    let columnsToInclude = [...baseColumns, ...dynamicColumns];
    if (selectedFields && selectedFields.length > 0) {
      // Filter columns to only include selected fields
      columnsToInclude = columnsToInclude.filter(col => selectedFields.includes(col));

      // If no valid columns selected, return error
      if (columnsToInclude.length === 0) {
        return res.status(400).json({
          type: "invalid_fields",
          message: "No valid fields selected for export",
        });
      }
    }

    const allColumns = columnsToInclude;

    // Create additional lookup maps for Excel export
    const hotelMap = new Map();
    hotels.forEach((hotel: any) => {
      hotelMap.set(hotel.id, hotel.name);
    });

    const destinationMap = new Map();
    destinations.forEach((destination: any) => {
      destinationMap.set(destination.id, destination.name);
    });

    // Transform products/services data to match import template format exactly
    const exportData = productServices.map(ps => {
      const baseData: any = {
        category_name: category.name,  // Add category name for easy parsing
        type: ps.type,
        description: ps.description || '',
        unit_type_name: unitTypeMap.get(ps.unit_type_id) || ps.unit_type_id || '',
        tag_names: '', // Will be populated below
        base_cost: ps.base_cost || '',
        status: ps.status
      };

      // Handle tag names - extract from populated tags relationship
      if (ps.tags && Array.isArray(ps.tags)) {
        const tagNames = ps.tags
          .map((tag: any) => tag.name)
          .filter((name: string) => name)
          .join(',');
        baseData.tag_names = tagNames;
      }

      // Add custom fields if they exist
      if (ps.custom_fields && typeof ps.custom_fields === 'object') {
        dynamicFieldSchema.forEach((field: any) => {
          const fieldKey = field.key || field.field_name;
          const value = ps.custom_fields[fieldKey];

          if (field.type === 'number-range' && value) {

            if (typeof value === 'object' && value !== null) {
              // Handle object format {from: x, to: y} or {min: x, max: y}
              const fromValue = value.from !== undefined ? value.from : value.min;
              const toValue = value.to !== undefined ? value.to : value.max;
              baseData[`${fieldKey}_from`] = fromValue !== undefined && fromValue !== null && fromValue !== '' ? fromValue : '';
              baseData[`${fieldKey}_to`] = toValue !== undefined && toValue !== null && toValue !== '' ? toValue : '';
            } else if (typeof value === 'string' && value.includes('-')) {
              // Handle string format "x-y"
              const [fromValue, toValue] = value.split('-').map(v => v.trim());
              baseData[`${fieldKey}_from`] = fromValue || '';
              baseData[`${fieldKey}_to`] = toValue || '';
            } else {
              // Handle single value or unexpected format
              baseData[`${fieldKey}_from`] = value || '';
              baseData[`${fieldKey}_to`] = '';
            }
          } else if (field.type === 'hotels' && value) {
            // Convert hotel IDs to names
            if (Array.isArray(value)) {
              const hotelNames = value
                .map(hotelId => hotelMap.get(hotelId))
                .filter(name => name)
                .join(',');
              baseData[fieldKey] = hotelNames;
            } else {
              baseData[fieldKey] = value;
            }
          } else if (field.type === 'destinations' && value) {
            // Convert destination IDs to names
            if (Array.isArray(value)) {
              const destinationNames = value
                .map(destId => destinationMap.get(destId))
                .filter(name => name)
                .join(',');
              baseData[fieldKey] = destinationNames;
            } else {
              baseData[fieldKey] = value;
            }
          } else if (field.type === 'multi-select' && Array.isArray(value)) {
            // Handle multi-select fields as comma-separated values
            baseData[fieldKey] = value.join(',');
          } else {
            // Handle all other field types
            baseData[fieldKey] = value || '';
          }
        });
      }

      // Ensure all columns are present in the data
      allColumns.forEach(col => {
        if (!(col in baseData)) {
          baseData[col] = '';
        }
      });

      return baseData;
    });

    // Create workbook using ExcelJS (exactly like import template)
    const workbook = new ExcelJS.Workbook();

    // Create main data sheet with category name
    const sheetName = category.name.substring(0, 31); // Excel sheet name limit
    const worksheet = workbook.addWorksheet(sheetName);

    // Set up headers with proper formatting (exactly like import template)
    const headerRow = worksheet.addRow(allColumns);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "4472C4" },
    };

    // Set column widths
    allColumns.forEach((col, index) => {
      worksheet.getColumn(index + 1).width = Math.max(col.length, 20);
    });

    // Add all the actual data rows
    exportData.forEach(item => {
      const values = allColumns.map(col => item[col] || '');
      const dataRow = worksheet.addRow(values);
      dataRow.font = { color: { argb: "000000" } };
    });


    // Add dropdown validations using ExcelJS approach (exactly like import template)
    const dropdownOptions = {
      type: ['Product', 'Service'],
      status: ['active', 'inactive'],
      unit_type_name: unitTypes.map((ut: any) => ut.name || ut)
    };

    // Apply dropdown validation for base fields
    Object.entries(dropdownOptions).forEach(([fieldName, options]) => {
      const columnIndex = allColumns.indexOf(fieldName) + 1;
      if (columnIndex > 0 && options.length > 0) {
        const cleanOptions = options
          .filter((option: any) => option && typeof option === 'string')
          .map((option: any) => option.toString().trim())
          .filter((option: string) => option.length > 0)
          .slice(0, 50);

        if (cleanOptions.length === 0) return;

        const optionsString = cleanOptions.join(',');
        if (optionsString.length <= 255) {
          for (let rowIndex = 2; rowIndex <= Math.max(1000, exportData.length + 100); rowIndex++) {
            const cell = worksheet.getCell(rowIndex, columnIndex);
            try {
              cell.dataValidation = {
                type: 'list',
                allowBlank: true,
                formulae: [`"${cleanOptions.join(',')}"`],
                showErrorMessage: true,
                errorStyle: 'error',
                errorTitle: 'Invalid Value',
                error: `Please select a value from the dropdown list`,
                showInputMessage: true,
                promptTitle: `Select ${fieldName.replace(/_/g, ' ')}`,
                prompt: `Choose from available options`
              };
            } catch (validationError) {
            }
          }
        }
      }
    });

    // Custom field dropdown validations (dynamic from category schema)
    dynamicFieldSchema.forEach((field: any) => {
      const fieldKey = field.key || field.field_name;
      const columnIndex = allColumns.indexOf(fieldKey) + 1;

      if (columnIndex > 0) {
        let options: string[] = [];
        let useDropdown = true;

        if (field.type === 'dropdown' && field.options && field.options.length > 0) {
          options = field.options;
          useDropdown = true; // Single select - use dropdown
        } else if (field.type === 'multi-select' && field.options && field.options.length > 0) {
          options = field.options;
          useDropdown = false; // Multi-select - no dropdown, use reference sheet
        } else if (field.type === 'hotels' && hotels.length > 0) {
          options = hotels.map((hotel: any) => hotel.name);
          useDropdown = false; // Hotels - multi-select, no dropdown, use reference sheet
        } else if (field.type === 'destinations' && destinations.length > 0) {
          options = destinations.map((destination: any) => destination.name);
          useDropdown = false; // Destinations - multi-select, no dropdown, use reference sheet
        } else if (field.type === 'boolean') {
          options = ['true', 'false'];
          useDropdown = true; // Single select - use dropdown
        }

        // Only add dropdown validation for single-select fields
        if (options.length > 0 && useDropdown) {
          const cleanOptions = options
            .filter(option => option && typeof option === 'string')
            .map(option => option.toString().trim())
            .filter(option => option.length > 0)
            .slice(0, 50);

          if (cleanOptions.length > 0) {
            const optionsString = cleanOptions.join(',');
            if (optionsString.length <= 255) {
              for (let rowIndex = 2; rowIndex <= Math.max(1000, exportData.length + 100); rowIndex++) {
                const cell = worksheet.getCell(rowIndex, columnIndex);
                try {
                  cell.dataValidation = {
                    type: 'list',
                    allowBlank: true,
                    formulae: [`"${cleanOptions.join(',')}"`],
                    showErrorMessage: true,
                    errorStyle: 'error',
                    errorTitle: 'Invalid Value',
                    error: `Please select a value from the dropdown list`,
                    showInputMessage: true,
                    promptTitle: `Select ${fieldKey.replace(/_/g, ' ')}`,
                    prompt: `Choose from available options`
                  };
                } catch (validationError) {
                }
              }
            }
          }
        }
      }
    });

    // Add tags reference sheet for easy lookup (exactly like import template)
    if (tags.length > 0) {
      const tagsSheet = workbook.addWorksheet('Tags_Reference');
      const tagHeaders = ['name', 'description'];
      const tagHeaderRow = tagsSheet.addRow(tagHeaders);
      tagHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } };
      tagHeaderRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "4472C4" },
      };

      // Add instruction row for tags usage
      const instructionRow = tagsSheet.addRow(['Available tag names for reference (use comma-separated for multiple tags)']);
      instructionRow.font = { italic: true, color: { argb: '666666' } };
      tagsSheet.mergeCells('A2:B2');

      // Add empty row for spacing
      tagsSheet.addRow(['']);

      tags.forEach((tag: any) => {
        const tagName = tag.name || tag;
        tagsSheet.addRow([
          tagName,
          `Tag: ${tagName}`
        ]);
      });

      tagsSheet.getColumn(1).width = 25;
      tagsSheet.getColumn(2).width = 30;
    }

    // Add reference sheets for multi-select custom fields (exactly like import template)
    const multiSelectFields = new Map<string, string[]>();

    // Collect all multi-select fields from the category
    dynamicFieldSchema.forEach((field: any) => {
      const fieldKey = field.key || field.field_name;

      // Create reference sheets for multi-select fields
      if (field.type === 'multi-select' && field.options && field.options.length > 0) {
        multiSelectFields.set(`${fieldKey}_options`, field.options);
      } else if (field.type === 'hotels' && hotels.length > 0) {
        multiSelectFields.set(`${fieldKey}_hotels`, hotels.map((hotel: any) => hotel.name));
      } else if (field.type === 'destinations' && destinations.length > 0) {
        multiSelectFields.set(`${fieldKey}_destinations`, destinations.map((destination: any) => destination.name));
      }
    });

    // Create reference sheets for multi-select fields
    multiSelectFields.forEach((options, fieldKey) => {
      if (options.length > 0) {
        // Ensure sheet name doesn't exceed Excel's 31 character limit
        const sheetName = `${fieldKey.replace(/_/g, '_').substring(0, 27)}_Ref`;
        const referenceSheet = workbook.addWorksheet(sheetName);

        const headers = ['name', 'description'];
        const headerRow = referenceSheet.addRow(headers);
        headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
        headerRow.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "4472C4" },
        };

        // Add instruction row
        const instructionRow = referenceSheet.addRow([`Available ${fieldKey.replace(/_/g, ' ')} options (use comma-separated for multiple)`]);
        instructionRow.font = { italic: true, color: { argb: '666666' } };
        referenceSheet.mergeCells('A2:B2');

        // Add empty row for spacing
        referenceSheet.addRow(['']);

        // Add options
        options.forEach((option: string) => {
          referenceSheet.addRow([
            option,
            `Option: ${option}`
          ]);
        });

        referenceSheet.getColumn(1).width = 25;
        referenceSheet.getColumn(2).width = 30;
      }
    });

    // Create comprehensive instructions sheet with dynamic data (exactly like import template)
    const instructionsData = [
      { Field: 'category_name', Description: 'Category name (auto-filled, do not modify)', Required: 'Yes', 'Valid Values': 'Pre-filled category name' },
      { Field: 'type', Description: 'Product or Service', Required: 'Yes', 'Valid Values': 'Product, Service (dropdown available)' },
      { Field: 'description', Description: 'Product/Service description', Required: 'No', 'Valid Values': 'Any text' },
      {
        Field: 'unit_type_name',
        Description: 'Unit type name',
        Required: 'Yes',
        'Valid Values': `Available options: ${unitTypes.map((ut: any) => ut.name || ut).join(', ')} (dropdown available)`
      },
      {
        Field: 'tag_names',
        Description: 'Tag names (comma-separated for multiple tags)',
        Required: 'No',
        'Valid Values': `Available options: ${tags.map((tag: any) => tag.name || tag).join(', ')} (no dropdown - refer to Tags_Reference sheet, enter comma-separated values)`
      },
      { Field: 'base_cost', Description: 'Base cost in CHF', Required: 'No', 'Valid Values': 'Positive number' },
      { Field: 'status', Description: 'Product/Service status', Required: 'No', 'Valid Values': 'active, inactive (dropdown available)' }
    ];

    // Add custom field instructions with dropdown information
    const categoryFields = (category.dynamic_field_schema as unknown as any[]) || [];
    const productFields = categoryFields.filter((field: any) => field.used_in_product !== false);

    if (productFields.length > 0) {
      // Add separator for custom fields
      instructionsData.push({
        Field: '--- CUSTOM FIELDS ---',
        Description: `Category-specific fields for ${category.name}`,
        Required: '',
        'Valid Values': ''
      });
    }

    productFields.forEach((field: any) => {
      const fieldKey = field.key || field.field_name;
      let validValues = 'Any text';

      if (field.type === 'dropdown' && field.options) {
        validValues = `${field.options.join(', ')} (dropdown available)`;
      } else if (field.type === 'multi-select' && field.options) {
        const fieldKey = field.key || field.field_name;
        validValues = `Comma-separated from: ${field.options.join(', ')} (no dropdown - refer to ${fieldKey}_options_Ref sheet)`;
      } else if (field.type === 'boolean') {
        validValues = 'true, false (dropdown available)';
      } else if (field.type === 'number') {
        validValues = 'Number';
      } else if (field.type === 'date') {
        validValues = 'YYYY-MM-DD format';
      } else if (field.type === 'number-range') {
        // For number-range fields, add instructions for both from and to columns
        instructionsData.push({
          Field: `${fieldKey}_from`,
          Description: `${field.label || fieldKey} - From Value (Custom Field)`,
          Required: field.required ? 'Yes' : 'No',
          'Valid Values': 'Number (minimum value of range)'
        });
        instructionsData.push({
          Field: `${fieldKey}_to`,
          Description: `${field.label || fieldKey} - To Value (Custom Field)`,
          Required: field.required ? 'Yes' : 'No',
          'Valid Values': 'Number (maximum value of range)'
        });
        return; // Skip the default instruction addition below
      } else if (field.type === 'hotels') {
        const fieldKey = field.key || field.field_name;
        validValues = hotels.length > 0
          ? `Available hotels: ${hotels.map((h: any) => h.name).join(', ')} (no dropdown - refer to ${fieldKey}_hotels_Ref sheet, comma-separated for multiple)`
          : 'Comma-separated hotel names (no hotels available)';
      } else if (field.type === 'destinations') {
        const fieldKey = field.key || field.field_name;
        validValues = destinations.length > 0
          ? `Available destinations: ${destinations.map((d: any) => d.name).join(', ')} (no dropdown - refer to ${fieldKey}_destinations_Ref sheet, comma-separated for multiple)`
          : 'Comma-separated destination names (no destinations available)';
      }

      instructionsData.push({
        Field: fieldKey,
        Description: `${field.label || fieldKey} (Custom Field)`,
        Required: field.required ? 'Yes' : 'No',
        'Valid Values': validValues
      });
    });

    // Create instructions sheet using ExcelJS
    const instructionsSheet = workbook.addWorksheet('Instructions');
    const instructionHeaders = ['Field', 'Description', 'Required', 'Valid Values'];
    const instructionHeaderRow = instructionsSheet.addRow(instructionHeaders);
    instructionHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } };
    instructionHeaderRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "4472C4" },
    };

    instructionsData.forEach((instruction: any) => {
      instructionsSheet.addRow([
        instruction.Field,
        instruction.Description,
        instruction.Required,
        instruction['Valid Values']
      ]);
    });

    instructionsSheet.getColumn(1).width = 20;
    instructionsSheet.getColumn(2).width = 40;
    instructionsSheet.getColumn(3).width = 10;
    instructionsSheet.getColumn(4).width = 50;

    // Set response headers for file download
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `products_services_export_${category.name.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.xlsx`;

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Write the workbook to the response using ExcelJS
    try {
      await workbook.xlsx.write(res);
    } catch (writeError) {
      throw new Error('Failed to write Excel export file');
    }

  } catch (error) {

    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid query parameters",
        details: error.errors,
      });
    }

    return res.status(500).json({
      type: "internal_error",
      message: "An error occurred while exporting products/services",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
