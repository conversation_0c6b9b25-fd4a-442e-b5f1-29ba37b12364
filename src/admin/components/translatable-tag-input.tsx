import React, { useState } from "react";
import { Input, Badge, Label, Text, Tooltip, Button } from "@camped-ai/ui";
import { X, Info, Globe, Trash2, Sparkles, Languages } from "lucide-react";
import { useAITranslation } from "../hooks/useAITranslation";
import "../styles/theme-variables.css";

export type ColorScheme =
  | "blue"
  | "green"
  | "orange"
  | "red"
  | "purple"
  | "indigo";

export interface TranslatableTagInputProps {
  label: string;
  baseTags: string[];
  translatedTags: string[];
  onBaseTagsChange: (tags: string[]) => void;
  onTranslatedTagsChange: (tags: string[]) => void;
  isBaseLanguage: boolean;
  disabled?: boolean;
  placeholder?: string;
  helpText?: string;
  currentLanguage?: string;
  hasUnsavedChanges?: boolean;
  colorScheme?: ColorScheme;
  // AI Translation props
  fieldType?: "tags" | "amenities" | "rules" | "safety_measures";
  entityType?: "hotel" | "destination";
  entityName?: string;
  entityLocation?: string;
  enableAITranslation?: boolean;
}

const TranslatableTagInput: React.FC<TranslatableTagInputProps> = ({
  label,
  baseTags,
  translatedTags,
  onBaseTagsChange,
  onTranslatedTagsChange,
  isBaseLanguage,
  disabled = false,
  placeholder = "Type a tag and press Enter",
  helpText,
  currentLanguage = "en",
  hasUnsavedChanges = false,
  colorScheme = "blue",
  fieldType = "tags",
  entityType = "hotel",
  entityName,
  entityLocation,
  enableAITranslation = true,
}) => {
  const [tagInput, setTagInput] = useState("");

  // Color scheme mapping with theme-aware colors
  const getColorClasses = (scheme: ColorScheme, isBase: boolean) => {
    const colorMap = {
      blue: {
        base: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
        translated:
          "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400",
        hover: "hover:bg-blue-200 dark:hover:bg-blue-800/40",
      },
      green: {
        base: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
        translated:
          "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400",
        hover: "hover:bg-green-200 dark:hover:bg-green-800/40",
      },
      orange: {
        base: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
        translated:
          "bg-orange-50 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400",
        hover: "hover:bg-orange-200 dark:hover:bg-orange-800/40",
      },
      red: {
        base: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
        translated:
          "bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400",
        hover: "hover:bg-red-200 dark:hover:bg-red-800/40",
      },
      purple: {
        base: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
        translated:
          "bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400",
        hover: "hover:bg-purple-200 dark:hover:bg-purple-800/40",
      },
      indigo: {
        base: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",
        translated:
          "bg-indigo-50 text-indigo-700 dark:bg-indigo-900/20 dark:text-indigo-400",
        hover: "hover:bg-indigo-200 dark:hover:bg-indigo-800/40",
      },
    };

    const colors = colorMap[scheme];
    return {
      badge: isBase ? colors.base : colors.translated,
      hover: colors.hover,
    };
  };

  // AI Translation functionality
  const {
    translateValues,
    isTranslating,
    error: translationError,
  } = useAITranslation({
    onSuccess: (translatedValues) => {
      console.log("🎉 AI translation completed:", translatedValues);
      onTranslatedTagsChange(translatedValues);
    },
    onError: (error) => {
      console.error("❌ AI translation failed:", error);
    },
  });

  // Check if AI translation should be available
  const shouldShowAITranslation =
    enableAITranslation &&
    !isBaseLanguage &&
    baseTags.length > 0 &&
    (translatedTags.length === 0 || translatedTags.length < baseTags.length) &&
    !disabled;

  // Handle AI translation
  const handleAITranslation = async () => {
    if (!shouldShowAITranslation || isTranslating) return;

    await translateValues({
      values: baseTags,
      targetLanguage: currentLanguage,
      fieldType,
      context: {
        entityType,
        entityName,
        location: entityLocation,
      },
    });
  };

  // Determine which tags to display and which handler to use
  const displayTags = isBaseLanguage ? baseTags : translatedTags;
  const handleTagsChange = isBaseLanguage
    ? onBaseTagsChange
    : onTranslatedTagsChange;

  // Helper functions for tag management
  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !displayTags.includes(trimmedTag)) {
      const newTags = [...displayTags, trimmedTag];
      handleTagsChange(newTags);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string, indexToRemove?: number) => {
    if (indexToRemove !== undefined) {
      // Remove by specific index (for duplicate tags)
      const newTags = displayTags.filter((_, index) => index !== indexToRemove);
      handleTagsChange(newTags);
    } else {
      // Remove by value (fallback for backward compatibility)
      const newTags = displayTags.filter((tag) => tag !== tagToRemove);
      handleTagsChange(newTags);
    }
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addTag(tagInput);
    } else if (
      e.key === "Backspace" &&
      tagInput === "" &&
      displayTags.length > 0
    ) {
      // Remove last tag when backspace is pressed on empty input
      removeTag(displayTags[displayTags.length - 1]);
    }
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Handle comma-separated input
    if (value.includes(",")) {
      const newTags = value
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag);
      newTags.forEach((tag) => {
        if (!displayTags.includes(tag)) {
          addTag(tag);
        }
      });
      setTagInput("");
    } else {
      setTagInput(value);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Label htmlFor="tags" className="font-medium flex items-center gap-1">
            {label}
            {/* {!isBaseLanguage && <Globe size={14} className="text-blue-500" />} */}
            {hasUnsavedChanges && (
              <span className="text-orange-500 dark:text-orange-400 text-xs">
                (unsaved)
              </span>
            )}
            <Tooltip
              content={
                isBaseLanguage
                  ? "Add tags to categorize this destination"
                  : `Translate tags for ${currentLanguage} language. Changes will be saved when you submit the form.`
              }
            >
              <Info size={14} className="text-muted-foreground" />
            </Tooltip>
          </Label>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          {/* AI Translation Button - Icon Only */}
          {shouldShowAITranslation && (
            <Tooltip
              content={isTranslating ? "Translating..." : "Translate with AI"}
            >
              <Button
                type="button"
                variant="transparent"
                size="small"
                onClick={handleAITranslation}
                disabled={isTranslating || disabled}
                className="p-1 h-6 w-6 text-primary hover:bg-primary/10 disabled:opacity-50 transition-colors"
              >
                {isTranslating ? (
                  <Sparkles size={14} className="animate-pulse text-primary" />
                ) : (
                  <Languages size={14} />
                )}
              </Button>
            </Tooltip>
          )}

          {/* Clear All Button - Icon Only */}
          {!isBaseLanguage && displayTags.length > 0 && (
            <Tooltip content="Clear all tags">
              <Button
                type="button"
                variant="transparent"
                size="small"
                onClick={() => handleTagsChange([])}
                className="p-1 h-6 w-6 text-destructive hover:bg-destructive/10 transition-colors"
                disabled={disabled}
              >
                <Trash2 size={14} />
              </Button>
            </Tooltip>
          )}
        </div>
      </div>

      {/* Base Language Reference (shown for non-base languages) */}
      {!isBaseLanguage && baseTags.length > 0 && (
        <div className="p-2 bg-muted/50 rounded-md border border-border">
          <Text className="text-xs text-muted-foreground mb-1 font-medium">
            Original (English):
          </Text>
          <div className="flex flex-wrap gap-1">
            {baseTags.map((tag, index) => (
              <Badge
                key={index}
                className="inline-flex items-center px-2 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-full"
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Translated Tags Display */}
      {displayTags.length > 0 ? (
        <div className="flex flex-wrap gap-2 mb-2 p-2 border border-border rounded-md bg-muted/30">
          {displayTags.map((tag, index) => {
            const colors = getColorClasses(colorScheme, isBaseLanguage);
            return (
              <Badge
                key={index}
                className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full ${colors.badge}`}
              >
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(tag, index)}
                  className={`ml-1 rounded-full p-0.5 transition-colors ${colors.hover}`}
                  aria-label={`Remove ${tag} tag`}
                  disabled={disabled}
                >
                  <X size={12} />
                </button>
              </Badge>
            );
          })}
        </div>
      ) : (
        /* Empty state for non-base languages */
        !isBaseLanguage && (
          <div className="mb-2 p-3 border border-dashed border-border rounded-md bg-muted/20 text-center">
            <Text className="text-sm text-muted-foreground mb-2">
              No {currentLanguage} translations yet.
            </Text>
            {baseTags.length > 0 && (
              <Button
                type="button"
                variant="secondary"
                size="small"
                onClick={() => handleTagsChange([...baseTags])}
                className="text-xs px-3 py-1 h-6"
                disabled={disabled}
              >
                Copy English tags as starting point
              </Button>
            )}
          </div>
        )
      )}

      {/* Input Field */}
      <Input
        id="tags"
        value={tagInput}
        onChange={handleTagInputChange}
        onKeyDown={handleTagInputKeyDown}
        placeholder={placeholder}
        className="w-full"
        disabled={disabled}
      />

      {/* Help Text */}
      {helpText && (
        <Text className="text-xs text-muted-foreground">{helpText}</Text>
      )}

      {/* AI Translation Error */}
      {translationError && (
        <Text className="text-xs text-destructive bg-destructive/10 p-2 rounded border border-destructive/20">
          Translation failed: {translationError}
        </Text>
      )}

      {/* Translation Status */}
      {!isBaseLanguage && (
        <Text className="text-xs text-muted-foreground">
          {displayTags.length === 0
            ? `Add tags above to create ${currentLanguage} translations. You can type new tags or copy English tags as a starting point.`
            : `${displayTags.length} tag${
                displayTags.length !== 1 ? "s" : ""
              } translated for ${currentLanguage}${
                hasUnsavedChanges ? " (unsaved)" : ""
              }`}
        </Text>
      )}
    </div>
  );
};

export default TranslatableTagInput;
