import React from "react";
import {
  Container,
  Heading,
  Text,
  Badge,
} from "@camped-ai/ui";
import {
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

interface VendorAnalyticsProps {
  vendorId?: string;
  className?: string;
}

interface AnalyticsMetric {
  label: string;
  value: string | number;
  change?: number;
  trend?: "up" | "down" | "neutral";
  icon: React.ReactNode;
  color: "green" | "red" | "blue" | "orange" | "purple";
}

const VendorAnalyticsDashboard: React.FC<VendorAnalyticsProps> = ({
  vendorId,
  className = "",
}) => {
  // Mock analytics data - replace with real API calls
  const analyticsData: AnalyticsMetric[] = [
    {
      label: "Total Orders",
      value: 156,
      change: 12,
      trend: "up",
      icon: <ShoppingCart className="h-5 w-5" />,
      color: "blue",
    },
    {
      label: "Completed Orders",
      value: 152,
      change: 8,
      trend: "up",
      icon: <CheckCircle className="h-5 w-5" />,
      color: "green",
    },
    {
      label: "Total Revenue",
      value: "$24,580",
      change: 15,
      trend: "up",
      icon: <DollarSign className="h-5 w-5" />,
      color: "green",
    },
    {
      label: "Avg. Order Value",
      value: "$162",
      change: -3,
      trend: "down",
      icon: <TrendingUp className="h-5 w-5" />,
      color: "orange",
    },
    {
      label: "On-Time Delivery",
      value: "94%",
      change: 2,
      trend: "up",
      icon: <Clock className="h-5 w-5" />,
      color: "green",
    },
    {
      label: "Customer Rating",
      value: "4.8",
      change: 0.2,
      trend: "up",
      icon: <Users className="h-5 w-5" />,
      color: "purple",
    },
  ];

  const getTrendIcon = (trend?: "up" | "down" | "neutral") => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-3 w-3 text-green-500" />;
      case "down":
        return <TrendingDown className="h-3 w-3 text-red-500" />;
      default:
        return null;
    }
  };

  const getTrendColor = (trend?: "up" | "down" | "neutral") => {
    switch (trend) {
      case "up":
        return "text-green-600";
      case "down":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getCardBorderColor = (color: string) => {
    const colorMap = {
      green: "border-l-green-500",
      red: "border-l-red-500",
      blue: "border-l-blue-500",
      orange: "border-l-orange-500",
      purple: "border-l-purple-500",
    };
    return colorMap[color as keyof typeof colorMap] || "border-l-gray-500";
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <Heading level="h3" className="mb-2">
          Performance Analytics
        </Heading>
        <Text className="text-ui-fg-subtle">
          Key metrics and performance indicators
        </Text>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {analyticsData.map((metric, index) => (
          <Container
            key={index}
            className={`p-4 border-l-4 ${getCardBorderColor(metric.color)}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg bg-${metric.color}-50`}>
                  {metric.icon}
                </div>
                <div>
                  <Text size="small" className="text-ui-fg-subtle">
                    {metric.label}
                  </Text>
                  <div className="flex items-center space-x-2">
                    <Text size="large" weight="plus">
                      {metric.value}
                    </Text>
                    {metric.change !== undefined && (
                      <div className="flex items-center space-x-1">
                        {getTrendIcon(metric.trend)}
                        <Text
                          size="small"
                          className={getTrendColor(metric.trend)}
                        >
                          {metric.change > 0 ? "+" : ""}
                          {metric.change}%
                        </Text>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Container>
        ))}
      </div>

      {/* Recent Activity */}
      <Container className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Heading level="h4">Recent Activity</Heading>
          <Badge variant="secondary">Last 30 days</Badge>
        </div>
        
        <div className="space-y-3">
          {[
            {
              action: "Order completed",
              details: "Order #VO-2024-001 - Room cleaning supplies",
              time: "2 hours ago",
              status: "success",
            },
            {
              action: "New order received",
              details: "Order #VO-2024-002 - Maintenance services",
              time: "5 hours ago",
              status: "info",
            },
            {
              action: "Payment processed",
              details: "$1,250 payment for Order #VO-2024-001",
              time: "1 day ago",
              status: "success",
            },
            {
              action: "Order delayed",
              details: "Order #VO-2023-998 - Delivery postponed",
              time: "2 days ago",
              status: "warning",
            },
          ].map((activity, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 rounded-lg bg-ui-bg-subtle">
              <div className="flex-shrink-0 mt-1">
                {activity.status === "success" && (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
                {activity.status === "info" && (
                  <ShoppingCart className="h-4 w-4 text-blue-500" />
                )}
                {activity.status === "warning" && (
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <Text size="small" weight="plus">
                  {activity.action}
                </Text>
                <Text size="small" className="text-ui-fg-subtle">
                  {activity.details}
                </Text>
              </div>
              <Text size="small" className="text-ui-fg-muted flex-shrink-0">
                {activity.time}
              </Text>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default VendorAnalyticsDashboard;
