import React, { useState, useCallback } from "react";
import { Button, Text, FocusModal, Badge, toast, Prompt } from "@camped-ai/ui";
import {
  useSupplierDocuments,
  useUploadSupplierDocuments,
  useDeleteSupplierDocument,
} from "../../hooks/vendor-management/use-suppliers";
import {
  Upload,
  File,
  X,
  CheckCircle,
  AlertCircle,
  Download,
  Trash2,
} from "lucide-react";

interface DocumentUploadProps {
  supplierId: string;
  onUploadComplete?: () => void;
}

interface UploadedFile {
  file: File;
  id: string;
  progress: number;
  status: "pending" | "uploading" | "success" | "error";
  error?: string;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  supplierId,
  onUploadComplete,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // Use hooks for data fetching and mutations
  const { data: documentsData, isLoading: isLoadingDocuments } =
    useSupplierDocuments(supplierId);
  const uploadDocuments = useUploadSupplierDocuments();
  const deleteDocument = useDeleteSupplierDocument();

  const existingDocuments = documentsData?.supplier_documents || [];

  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ];

  // Delete a document using the hook
  const handleDeleteDocument = (documentId: string, documentName: string) => {
    setDocumentToDelete({ id: documentId, name: documentName });
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteDocument = () => {
    if (!documentToDelete) return;

    deleteDocument.mutate(
      {
        documentId: documentToDelete.id,
        supplierId,
        documentName: documentToDelete.name
      },
      {
        onSuccess: () => {
          // Only update UI state after successful deletion
          // Toast message is handled by the hook
          setDeleteConfirmOpen(false);
          setDocumentToDelete(null);
        },
        onError: () => {
          // Error toast is handled by the hook
          // Just reset the UI state
          setDeleteConfirmOpen(false);
          setDocumentToDelete(null);
        },
      }
    );
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleFiles = (fileList: File[]) => {
    const newFiles: UploadedFile[] = [];

    fileList.forEach((file) => {
      // Validate file type
      if (!allowedTypes.includes(file.type)) {
        toast.error(`File type not supported: ${file.name}`);
        return;
      }

      // Validate file size
      if (file.size > maxFileSize) {
        toast.error(`File too large: ${file.name} (max 10MB)`);
        return;
      }

      newFiles.push({
        file,
        id: Math.random().toString(36).substring(2, 11),
        progress: 0,
        status: "pending",
      });
    });

    setFiles((prev) => [...prev, ...newFiles]);
  };

  const removeFile = (id: string) => {
    setFiles((prev) => prev.filter((f) => f.id !== id));
  };

  // Upload files using the hook
  const handleUploadAll = async () => {
    if (files.length === 0) {
      toast.error("Please select files to upload");
      return;
    }

    // Update all files to uploading status
    setFiles((prev) =>
      prev.map((f) => ({ ...f, status: "uploading", progress: 0 }))
    );

    const filesToUpload = files.map((f) => f.file);

    uploadDocuments.mutate(
      { supplierId, files: filesToUpload },
      {
        onSuccess: () => {
          // Update all files to success status
          setFiles((prev) =>
            prev.map((f) => ({ ...f, status: "success", progress: 100 }))
          );

          // Close the modal after a brief delay to show success
          setTimeout(() => {
            setIsOpen(false);
            setFiles([]);
            if (onUploadComplete) {
              onUploadComplete();
            }
          }, 1000);
        },
        onError: () => {
          // Update all files to error status
          setFiles((prev) =>
            prev.map((f) => ({ ...f, status: "error", error: "Upload failed" }))
          );
        },
      }
    );
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "uploading":
        return (
          <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <File className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Existing Documents */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <Text weight="plus">Supplier Documents</Text>
          <Button
            variant="secondary"
            size="small"
            type="button"
            onClick={() => setIsOpen(true)}
          >
            <Upload className="h-4 w-4" />
            Upload Documents
          </Button>
        </div>

        {isLoadingDocuments ? (
          <Text>Loading documents...</Text>
        ) : existingDocuments.length > 0 ? (
          <div className="grid grid-cols-1 gap-3">
            {existingDocuments.map((doc) => (
              <div
                key={doc.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <File className="h-4 w-4 text-gray-500" />
                  <div>
                    <Text size="small" weight="plus">
                      {doc.name}
                    </Text>
                    <Text size="small" className="text-gray-500">
                      Uploaded {new Date(doc.created_at).toLocaleDateString()}
                    </Text>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="transparent"
                    size="small"
                    type="button"
                    onClick={() => window.open(doc.url, "_blank")}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="transparent"
                    size="small"
                    type="button"
                    onClick={() => handleDeleteDocument(doc.id, doc.name)}
                    disabled={deleteDocument.isPending && documentToDelete?.id === doc.id}
                  >
                    {deleteDocument.isPending && documentToDelete?.id === doc.id ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent" />
                    ) : (
                      <Trash2 className="h-4 w-4 text-red-500" />
                    )}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Text className="text-gray-500">No documents uploaded yet.</Text>
        )}
      </div>

      <FocusModal open={isOpen} onOpenChange={setIsOpen}>
        <FocusModal.Content className="p-4">
          <FocusModal.Header>
            <FocusModal.Title>Upload Supplier Documents</FocusModal.Title>
          </FocusModal.Header>

          <FocusModal.Body className="space-y-6">
            {/* Simplified upload - no complex fields needed */}

            {/* File Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-300 hover:border-gray-400"
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <Text className="mb-2">
                Drag and drop files here, or{" "}
                <label className="text-blue-600 hover:text-blue-700 cursor-pointer">
                  browse
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    onChange={handleFileInput}
                    className="hidden"
                  />
                </label>
              </Text>
              <Text size="small" className="text-gray-500">
                Supports: PDF, JPG, PNG, DOC, DOCX (max 10MB each)
              </Text>
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="space-y-2">
                <Text weight="plus">Selected Files</Text>
                {files.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(file.status)}
                      <div>
                        <Text size="small" weight="plus">
                          {file.file.name}
                        </Text>
                        <Text size="small" className="text-gray-500">
                          {formatFileSize(file.file.size)}
                        </Text>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Badge>{file.status}</Badge>
                      {file.status === "pending" && (
                        <Button
                          variant="transparent"
                          size="small"
                          type="button"
                          onClick={() => removeFile(file.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </FocusModal.Body>

          <FocusModal.Footer>
            <div className="flex justify-end space-x-2">
              <Button
                variant="secondary"
                onClick={() => setIsOpen(false)}
                type="button"
              >
                Cancel
              </Button>
              <Button
                onClick={handleUploadAll}
                disabled={files.length === 0}
                type="button"
              >
                Upload All Files
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Document</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{documentToDelete?.name}"? This
              action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel
              onClick={() => {
                setDeleteConfirmOpen(false);
                setDocumentToDelete(null);
              }}
            >
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={confirmDeleteDocument}
              disabled={deleteDocument.isPending}
            >
              {deleteDocument.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </div>
  );
};

export default DocumentUpload;
