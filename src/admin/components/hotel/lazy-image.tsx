import React, { useState, useRef, useEffect } from "react";

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  onLoad?: () => void;
  onError?: () => void;
  placeholder?: React.ReactNode;
  eager?: boolean; // For carousel main images that should load immediately
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = "",
  onLoad,
  onError,
  placeholder,
  eager = false,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(eager); // Start as true if eager loading
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(src);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Reset states when src changes
  useEffect(() => {
    if (currentSrc !== src) {
      setIsLoaded(false);
      setHasError(false);
      setCurrentSrc(src);
      if (!eager) {
        setIsInView(false);
      }
    }
  }, [src, currentSrc, eager]);

  useEffect(() => {
    if (eager) {
      setIsInView(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.01, // More sensitive for better UX
        rootMargin: "100px", // Larger margin for earlier loading
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [eager, currentSrc]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {isInView && !hasError && (
        <img
          ref={imgRef}
          src={currentSrc}
          alt={alt}
          className={`transition-opacity duration-200 ${
            isLoaded ? "opacity-100" : "opacity-0"
          } w-full h-full object-cover`}
          onLoad={handleLoad}
          onError={handleError}
          loading={eager ? "eager" : "lazy"}
        />
      )}

      {(!isInView || !isLoaded || hasError) && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
          {hasError ? (
            <div className="text-center">
              <div className="w-8 h-8 bg-muted-foreground/20 rounded mx-auto mb-2"></div>
              <span className="text-xs text-muted-foreground">
                Failed to load
              </span>
            </div>
          ) : placeholder ? (
            placeholder
          ) : (
            <div className="text-center">
              <div className="w-8 h-8 bg-muted-foreground/20 rounded animate-pulse mx-auto mb-2"></div>
              <span className="text-xs text-muted-foreground">Loading...</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LazyImage;
