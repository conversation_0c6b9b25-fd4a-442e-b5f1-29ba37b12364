/* Simple Room Calendar Styles */

/* Make booking cells clickable */
.booking-cell-clickable {
  cursor: pointer !important;
  pointer-events: auto !important;
  user-select: none;
  -webkit-user-select: none;
}

.booking-cell-clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
  z-index: 40 !important;
}

.booking-cell-clickable:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.calendar-container {
  width: 100%;
  padding-bottom: 24px;
  font-family: "Inter", system-ui, -apple-system, sans-serif;
  position: relative;
}

.calendar-inner {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.scroll-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
}

.scrollable-content {
  width: max-content;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  background-color: hsl(var(--card));
  border-bottom: 1px solid hsl(var(--border));
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.calendar-grid {
  display: grid;
  border-bottom: 1px solid hsl(var(--border));
  position: relative;
  background-color: hsl(var(--muted));
}

.fixed-column {
  position: sticky;
  left: 0;
  z-index: 20;
  background-color: hsl(var(--card));
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.first-column {
  position: sticky;
  left: 0;
  z-index: 20;
  background-color: hsl(var(--card));
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.calendar-cell {
  padding: 8px;
  border-right: 1px solid hsl(var(--border));
  transition: background-color 0.2s ease;
  position: relative;
}

.calendar-cell.header {
  background-color: hsl(var(--muted));
  font-weight: 600;
  text-align: center;
  padding: 10px 8px;
  font-size: 0.85rem;
  color: hsl(var(--muted-foreground));
}

.calendar-cell.today {
  background-color: hsl(var(--accent));
  font-weight: bold;
}

.room-group-header {
  grid-column: 1 / -1;
  background-color: hsl(var(--muted));
  padding: 8px 12px;
  font-weight: 600;
  border-bottom: 1px solid hsl(var(--border));
  position: sticky;
  left: 0;
  z-index: 4;
  font-size: 0.9rem;
  color: hsl(var(--foreground));
}

.summary-row {
  grid-column: 1 / -1;
  background-color: hsl(var(--muted));
  border-bottom: 1px solid hsl(var(--border));
  border-top: 1px solid hsl(var(--border));
  font-weight: 500;
}

.summary-cell {
  padding: 8px;
  border-right: 1px solid hsl(var(--border));
  text-align: center;
  font-size: 0.8rem;
  color: hsl(var(--muted-foreground));
}

.summary-label {
  font-weight: 600;
  padding: 8px 12px;
  border-right: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

.room-cell {
  padding: 8px 12px;
  border-right: 1px solid hsl(var(--border));
  border-bottom: 1px solid hsl(var(--border));
  font-weight: 500;
  color: hsl(var(--foreground));
  font-size: 0.85rem;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.status-cell {
  padding: 4px;
  text-align: center;
  font-size: 0.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 28px;
  position: relative;
  transition: all 0.2s ease;
  border-radius: 0;
  box-sizing: border-box;
  overflow: visible;
  border-left-width: 2px !important;
  border-right-width: 2px !important;
}

.status-cell:hover {
  box-shadow: inset 0 0 0 2px #3b82f6;
  z-index: 5;
}

.maintenance-span {
  background-color: #fee2e2;
  border: 1px solid #f87171;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-radius: 0;
  border-left-color: #f97316 !important;
  border-right-color: #f97316 !important;
}

.maintenance-span:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-available {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--card));
  display: flex;
  justify-content: center;
  align-items: center;
  border-left-color: hsl(var(--card)) !important;
  border-right-color: hsl(var(--card)) !important;
}

.status-booked {
  background-color: #dcfce7;
  background-image: linear-gradient(to bottom, #dcfce7, #d1fae5);
  border: 1px solid #4ade80;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left-color: #22c55e !important;
  border-right-color: #22c55e !important;
}

.status-maintenance {
  background-color: hsl(var(--card));
  border: 1px solid #f87171;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-reserved {
  background-color: hsl(var(--card));
  border: 1px solid #34d399;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left-color: #34d399 !important;
  border-right-color: #34d399 !important;
}

.status-unavailable {
  background-color: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-on_demand {
  background-color: #fef3c7;
  background-image: linear-gradient(to bottom, #fef9c3, #fef3c7);
  border: 1px solid #fbbf24;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left-color: #f59e0b !important;
  border-right-color: #f59e0b !important;
}

.booking-cell {
  padding: 4px 10px;
  font-size: 0.8rem;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 30 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border-radius: 4px;
  box-sizing: border-box;
  overflow: visible;
  background-color: #ffb8b8;
  border: 1px solid #ff8080;
  cursor: pointer !important;
  pointer-events: auto !important;
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  margin: 0;
  font-family: inherit;
}

.booking-cell:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  z-index: 40 !important;
}

.booking-cell:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.selected-booking {
  background-color: #3b82f6 !important;
  color: white !important;
  border: 2px solid #2563eb !important;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
}

/* Status-specific styling */
.booking-confirmed {
  background-color: #ffb8b8;
  border-color: #ff8080;
}

.booking-checked_in {
  background-color: #bfdbfe;
  border-color: #93c5fd;
}

.booking-checked_out {
  background-color: #d1fae5;
  border-color: #6ee7b7;
}

.booking-canceled {
  background-color: #e5e7eb;
  border-color: #d1d5db;
  color: #6b7280;
}

.booking-confirmed {
  background-color: #dbeafe;
  background-image: linear-gradient(to bottom, #eff6ff, #dbeafe);
  border: 1px solid #93c5fd;
}

.booking-pending {
  background-color: #fef3c7;
  background-image: linear-gradient(to bottom, #fef9c3, #fef3c7);
  border: 1px solid #fcd34d;
}

.booking-cancelled {
  background-color: #fecaca;
  background-image: linear-gradient(to bottom, #fee2e2, #fecaca);
  border: 1px solid #f87171;
}

.booking-completed {
  background-color: #d1fae5;
  background-image: linear-gradient(to bottom, #ecfdf5, #d1fae5);
  border: 1px solid #6ee7b7;
}

/* Legend styles */
.calendar-legend {
  padding: 8px 12px;
  border-bottom: 1px solid hsl(var(--border));
  display: flex;
  gap: 16px;
  background-color: hsl(var(--card));
  margin-bottom: 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-item span {
  color: hsl(var(--foreground));
}
