import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { Button, Text } from "@camped-ai/ui";
import { ChevronLeft, ChevronRight, X, ExternalLink } from "lucide-react";

interface ImageData {
  id?: string;
  url: string;
  isThumbnail?: boolean;
}

interface OptimizedImageCarouselProps {
  images: ImageData[];
  initialIndex?: number;
  onClose: () => void;
  hotelName?: string;
}

// Simplified main image component without persistent loading states
const MainImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
}> = React.memo(({ src, alt, className }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Reset states when src changes
  useEffect(() => {
    setIsLoaded(false);
    setHasError(false);
  }, [src]);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    setHasError(false);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoaded(false);
  }, []);

  return (
    <div className={`relative ${className}`}>
      <img
        src={src}
        alt={alt}
        className={`w-full h-full object-contain transition-opacity duration-300 ${
          isLoaded ? "opacity-100" : "opacity-0"
        }`}
        onLoad={handleLoad}
        onError={handleError}
        loading="lazy"
        decoding="async"
        style={{
          imageRendering: "auto",
        }}
      />

      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/20">
          <div className="text-center">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <span className="text-xs text-muted-foreground">
              Loading image...
            </span>
          </div>
        </div>
      )}

      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/20">
          <div className="text-center">
            <div className="w-6 h-6 bg-muted-foreground/30 rounded mx-auto mb-2"></div>
            <span className="text-xs text-muted-foreground">
              Failed to load
            </span>
          </div>
        </div>
      )}
    </div>
  );
});

MainImage.displayName = "MainImage";

const OptimizedImageCarousel: React.FC<OptimizedImageCarouselProps> = ({
  images,
  initialIndex = 0,
  onClose,
  hotelName,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const preloadedRef = useRef<Set<string>>(new Set());
  const preloadTimeoutRef = useRef<NodeJS.Timeout>();
  const activeImagesRef = useRef<HTMLImageElement[]>([]);

  // Cleanup on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      // Cancel all pending timeouts
      if (preloadTimeoutRef.current) {
        clearTimeout(preloadTimeoutRef.current);
      }

      // Clear preloaded images cache
      preloadedRef.current.clear();

      // Cleanup active images
      activeImagesRef.current.forEach((img) => {
        img.onload = null;
        img.onerror = null;
        img.src = "";
      });
      activeImagesRef.current = [];
    };
  }, []);

  // Ultra-conservative preloading to prevent page freezing
  useEffect(() => {
    let isActive = true;

    const preloadWithDelay = (url: string, delay: number = 0) => {
      if (!preloadedRef.current.has(url) && isActive) {
        setTimeout(() => {
          if (!isActive) return;

          // Use requestIdleCallback to avoid blocking main thread
          const loadImage = () => {
            const img = new Image();
            img.decoding = "async";
            img.loading = "lazy";

            // Track active images for cleanup
            activeImagesRef.current.push(img);

            img.onload = () => {
              if (isActive) {
                preloadedRef.current.add(url);
              }
              // Remove from active images when done
              const index = activeImagesRef.current.indexOf(img);
              if (index > -1) {
                activeImagesRef.current.splice(index, 1);
              }
            };
            img.onerror = () => {
              if (isActive) {
                preloadedRef.current.add(url);
              }
              // Remove from active images when done
              const index = activeImagesRef.current.indexOf(img);
              if (index > -1) {
                activeImagesRef.current.splice(index, 1);
              }
            };

            // Start loading
            img.src = url;
          };

          // Use requestIdleCallback if available, otherwise setTimeout
          if ("requestIdleCallback" in window) {
            requestIdleCallback(loadImage, { timeout: 1000 });
          } else {
            setTimeout(loadImage, 16); // Next frame
          }
        }, delay);
      }
    };

    // Only preload current image immediately
    const currentImage = images[currentIndex];
    if (currentImage) {
      preloadWithDelay(currentImage.url, 0);
    }

    // Clear any existing timeout
    if (preloadTimeoutRef.current) {
      clearTimeout(preloadTimeoutRef.current);
    }

    // Preload next image only after user stops navigating for 1 second
    preloadTimeoutRef.current = setTimeout(() => {
      if (!isActive) return;

      const nextIndex = (currentIndex + 1) % images.length;
      if (images[nextIndex]) {
        preloadWithDelay(images[nextIndex].url, 500);
      }
    }, 1000);

    return () => {
      isActive = false;
      if (preloadTimeoutRef.current) {
        clearTimeout(preloadTimeoutRef.current);
      }
    };
  }, [currentIndex, images]);

  // Debounced navigation to prevent freezing
  const navigationTimeoutRef = useRef<NodeJS.Timeout>();

  const goToPrevious = useCallback(() => {
    // Clear any pending navigation
    if (navigationTimeoutRef.current) {
      clearTimeout(navigationTimeoutRef.current);
    }

    // Use requestAnimationFrame for smooth state updates
    requestAnimationFrame(() => {
      setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
    });
  }, [images.length]);

  const goToNext = useCallback(() => {
    // Clear any pending navigation
    if (navigationTimeoutRef.current) {
      clearTimeout(navigationTimeoutRef.current);
    }

    // Use requestAnimationFrame for smooth state updates
    requestAnimationFrame(() => {
      setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    });
  }, [images.length]);

  const openFullSize = useCallback(() => {
    const currentImage = images[currentIndex];
    if (currentImage?.url) {
      window.open(currentImage.url, "_blank");
    }
  }, [images, currentIndex]);

  // Optimized keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case "ArrowLeft":
          event.preventDefault();
          goToPrevious();
          break;
        case "ArrowRight":
          event.preventDefault();
          goToNext();
          break;
        case "Escape":
          event.preventDefault();
          onClose();
          break;
        case "Enter":
        case " ":
          event.preventDefault();
          openFullSize();
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown, { passive: false });
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [goToPrevious, goToNext, onClose, openFullSize]);

  const currentImage = images[currentIndex];

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-background">
        <div className="flex items-center gap-3">
          <Text className="font-medium text-foreground">
            {hotelName ? `${hotelName} - Gallery` : "Image Gallery"}
          </Text>
          <Text className="text-sm text-muted-foreground">
            {currentIndex + 1} of {images.length}
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="small"
            onClick={openFullSize}
            className="text-muted-foreground hover:text-foreground"
          >
            <ExternalLink className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="small"
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Main Image Display */}
      <div className="flex-1 relative bg-black/5 dark:bg-black/20 min-h-[60vh]">
        <div className="absolute inset-0 flex items-center justify-center p-4">
          <div className="relative max-w-full max-h-full">
            <MainImage
              src={currentImage.url}
              alt={`Hotel image ${currentIndex + 1}`}
              className="max-w-full max-h-[60vh] rounded-lg shadow-lg"
            />

            {/* Featured Image Badge */}
            {currentImage.isThumbnail && (
              <div className="absolute top-4 right-4 bg-green-500 dark:bg-green-600 text-white text-sm px-3 py-1 rounded-full shadow-lg">
                Featured Image
              </div>
            )}
          </div>
        </div>

        {/* Navigation Buttons */}
        {images.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="small"
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-0 w-10 h-10 p-0 transition-all duration-150"
            >
              <ChevronLeft className="w-5 h-5" />
            </Button>
            <Button
              variant="ghost"
              size="small"
              onClick={goToNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-0 w-10 h-10 p-0 transition-all duration-150"
            >
              <ChevronRight className="w-5 h-5" />
            </Button>
          </>
        )}
      </div>

      {/* Simplified Navigation Strip - No Thumbnails to Prevent Freezing */}
      {images.length > 1 && (
        <div className="p-4 border-t border-border bg-background">
          <div className="flex items-center justify-center gap-4">
            <Button
              variant="ghost"
              size="small"
              onClick={goToPrevious}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>

            <div className="flex items-center gap-2">
              <Text className="text-sm text-muted-foreground">
                {currentIndex + 1} of {images.length}
              </Text>
              {currentImage.isThumbnail && (
                <div
                  className="w-2 h-2 bg-green-500 rounded-full"
                  title="Featured Image"
                ></div>
              )}
            </div>

            <Button
              variant="ghost"
              size="small"
              onClick={goToNext}
              className="flex items-center gap-2"
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          {/* Progress indicator */}
          <div className="mt-3 w-full bg-muted/30 rounded-full h-1">
            <div
              className="bg-primary h-1 rounded-full transition-all duration-300"
              style={{
                width: `${((currentIndex + 1) / images.length) * 100}%`,
              }}
            />
          </div>
        </div>
      )}

      {/* Instructions with connection info */}
      <div className="px-4 py-2 bg-muted/30 border-t border-border">
        <Text className="text-xs text-muted-foreground text-center">
          Use arrow keys to navigate • Press Enter or Space to view full size •
          Press Esc to close
          {(() => {
            const connection = (navigator as any).connection;
            const isSlowConnection =
              connection &&
              (connection.effectiveType === "slow-2g" ||
                connection.effectiveType === "2g" ||
                connection.saveData);
            return isSlowConnection ? " • Optimized for slow connection" : "";
          })()}
        </Text>
      </div>
    </div>
  );
};

export default OptimizedImageCarousel;
