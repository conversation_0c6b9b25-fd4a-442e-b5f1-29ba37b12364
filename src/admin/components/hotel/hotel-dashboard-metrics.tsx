import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, Head<PERSON>, Button } from "@camped-ai/ui";
import { Bed, LogIn, LogOut, Percent, RefreshCw } from "lucide-react";
import { format } from "date-fns";

interface HotelDashboardMetricsProps {
  hotelId: string;
}

interface DashboardMetrics {
  occupancyRate: number;
  availableRooms: number;
  totalRooms: number;
  todayCheckIns: number;
  todayCheckOuts: number;
}

const HotelDashboardMetrics: React.FC<HotelDashboardMetricsProps> = ({
  hotelId,
}) => {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    occupancyRate: 0,
    availableRooms: 0,
    totalRooms: 0,
    todayCheckIns: 0,
    todayCheckOuts: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isMockData, setIsMockData] = useState<boolean>(false);

  const fetchMetrics = useCallback(async () => {
    if (!hotelId) {
      console.log("No hotel ID provided, cannot fetch metrics");
      return;
    }

    console.log(`Fetching dashboard metrics for hotel ID: ${hotelId}`);
    setIsLoading(true);

    try {
      // Use the real endpoint
      let url = `/admin/hotel-management/hotels/${hotelId}/dashboard-metrics`;
      console.log(`Making API request to: ${url}`);
      setIsMockData(false);

      const response = await fetch(url);
      console.log(`API response status: ${response.status}`);

      if (!response.ok) {
        throw new Error(
          `Failed to fetch dashboard metrics: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      console.log("Received dashboard metrics data:", data);

      // Validate the data
      if (!data || typeof data !== "object") {
        throw new Error("Invalid data format received from API");
      }

      // Update metrics state with data from API
      // Make sure we're not using the fallback values (65%, 15/45, 7, 9)
      // This helps us confirm we're getting real data
      const isDefaultData =
        data.occupancyRate === 65 &&
        data.availableRooms === 15 &&
        data.totalRooms === 45 &&
        data.todayCheckIns === 7 &&
        data.todayCheckOuts === 9;

      if (isDefaultData) {
        console.warn(
          "Received default fallback data from API, trying mock endpoint instead"
        );
        throw new Error("Received default fallback data");
      }

      const newMetrics = {
        occupancyRate: data.occupancyRate ?? 0,
        availableRooms: data.availableRooms ?? 0,
        totalRooms: data.totalRooms ?? 0,
        todayCheckIns: data.todayCheckIns ?? 0,
        todayCheckOuts: data.todayCheckOuts ?? 0,
      };

      console.log("Setting metrics state to:", newMetrics);
      setMetrics(newMetrics);
    } catch (error) {
      console.error("Error fetching dashboard metrics:", error);

      // Try the mock endpoint as a fallback
      try {
        console.log("Trying mock endpoint as fallback");
        const mockUrl = `/admin/hotel-management/hotels/${hotelId}/dashboard-metrics/mock`;
        const mockResponse = await fetch(mockUrl);

        if (mockResponse.ok) {
          const mockData = await mockResponse.json();
          console.log("Received mock data:", mockData);

          // Set the mock data
          setMetrics({
            occupancyRate: mockData.occupancyRate ?? 0,
            availableRooms: mockData.availableRooms ?? 0,
            totalRooms: mockData.totalRooms ?? 0,
            todayCheckIns: mockData.todayCheckIns ?? 0,
            todayCheckOuts: mockData.todayCheckOuts ?? 0,
          });

          // Indicate we're using mock data
          setIsMockData(true);
          return;
        }
      } catch (mockError) {
        console.error("Error fetching mock data:", mockError);
      }

      // If both real and mock endpoints fail, use hardcoded fallback
      const fallbackMetrics = {
        occupancyRate: 0,
        availableRooms: 0,
        totalRooms: 0,
        todayCheckIns: 0,
        todayCheckOuts: 0,
      };
      console.log("Using hardcoded fallback metrics:", fallbackMetrics);
      setMetrics(fallbackMetrics);
      setIsMockData(false);
    } finally {
      setIsLoading(false);
    }
  }, [hotelId]);

  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        {isMockData && (
          <div className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200 rounded-md text-xs font-medium">
            Using mock data
          </div>
        )}
        <Button
          variant="secondary"
          size="small"
          onClick={fetchMetrics}
          disabled={isLoading}
        >
          <RefreshCw
            className={`w-4 h-4 mr-1 ${isLoading ? "animate-spin" : ""}`}
          />
          {isLoading ? "Refreshing..." : "Refresh Metrics"}
        </Button>
      </div>
      <div className="grid grid-cols-2 gap-4">
        {/* Occupancy Rate */}
        <div className="flex flex-col p-4 border border-border rounded-lg bg-blue-50/30 dark:bg-blue-950/30 hover:shadow-sm transition-shadow duration-200">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center flex-shrink-0">
              <Percent className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <Text className="text-sm text-muted-foreground">
              Occupancy Rate
            </Text>
          </div>
          <Text className="text-2xl font-semibold text-blue-700 dark:text-blue-300 mt-1">
            {isLoading ? "Loading..." : `${metrics.occupancyRate}%`}
          </Text>
        </div>

        {/* Available Rooms */}
        <div className="flex flex-col p-4 border border-border rounded-lg bg-green-50/30 dark:bg-green-950/30 hover:shadow-sm transition-shadow duration-200">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center flex-shrink-0">
              <Bed className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <Text className="text-sm text-muted-foreground">
              Available Rooms
            </Text>
          </div>
          <Text className="text-2xl font-semibold text-green-700 dark:text-green-300 mt-1">
            {isLoading
              ? "Loading..."
              : `${metrics.availableRooms} / ${metrics.totalRooms}`}
          </Text>
        </div>

        {/* Today's Check-ins */}
        <div className="flex flex-col p-4 border border-border rounded-lg bg-indigo-50/30 dark:bg-indigo-950/30 hover:shadow-sm transition-shadow duration-200">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center flex-shrink-0">
              <LogIn className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
            </div>
            <Text className="text-sm text-muted-foreground">
              Today's Check-ins
            </Text>
          </div>
          <Text className="text-2xl font-semibold text-indigo-700 dark:text-indigo-300 mt-1">
            {isLoading ? "Loading..." : metrics.todayCheckIns}
          </Text>
        </div>

        {/* Today's Check-outs */}
        <div className="flex flex-col p-4 border border-border rounded-lg bg-amber-50/30 dark:bg-amber-950/30 hover:shadow-sm transition-shadow duration-200">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-8 h-8 rounded-full bg-amber-100 dark:bg-amber-900/50 flex items-center justify-center flex-shrink-0">
              <LogOut className="w-4 h-4 text-amber-600 dark:text-amber-400" />
            </div>
            <Text className="text-sm text-muted-foreground">
              Today's Check-outs
            </Text>
          </div>
          <Text className="text-2xl font-semibold text-amber-700 dark:text-amber-300 mt-1">
            {isLoading ? "Loading..." : metrics.todayCheckOuts}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default HotelDashboardMetrics;
