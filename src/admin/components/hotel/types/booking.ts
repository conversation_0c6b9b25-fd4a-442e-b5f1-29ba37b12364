/**
 * Room Inventory Status Enum
 * Defines all possible statuses for room availability
 * Based on actual API response values
 */
export enum RoomInventoryStatus {
  AVAILABLE = "available",
  RESERVED = "reserved",
  BOOKED = "booked",
  MAINTENANCE = "maintenance",
  CLEANING = "cleaning",
  UNAVAILABLE = "unavailable",
  RESERVED_UNASSIGNED = "reserved_unassigned",
  CART_RESERVED = "cart_reserved",
  ON_DEMAND = "on_demand",
}

/**
 * Booking interface for room availability - derived from availability data
 */
export interface Booking {
  id: string;
  room_id: string;
  guestName?: string;
  checkIn: Date;
  checkOut: Date;
  status: RoomInventoryStatus;
  notes?: string;
  order_id?: string;
  cart_id?: string;
  confirmationNumber?: string;
  metadata?: Record<string, any>;
}

/**
 * Room availability data interface - matches API response structure
 */
export interface RoomAvailability {
  room_id: string;
  room_number: string;
  room_name: string;
  config_name: string;
  from_date: string;
  to_date: string;
  status: string;
  quantity: number;
  dynamic_price?: number | null;
  notes: string;
  order_id: string;
}

/**
 * Complete API response interface
 */
export interface HotelAvailabilityAPIResponse {
  room_configs: RoomConfig[];
  rooms: Room[];
  availability: RoomAvailability[];
}

/**
 * Room configuration interface - matches API response structure
 */
export interface RoomConfig {
  id: string;
  title: string;
  handle: string;
  subtitle?: string | null;
  description: string;
  is_giftcard: boolean;
  status: string;
  thumbnail?: string | null;
  weight?: number | null;
  length?: number | null;
  height?: number | null;
  width?: number | null;
  origin_country?: string | null;
  hs_code?: string | null;
  mid_code?: string | null;
  material?: string | null;
  discountable: boolean;
  external_id?: string | null;
  metadata: {
    type: string;
    bed_type: string;
    hotel_id: string;
    max_cots: number;
    amenities: string[];
    room_size: string;
    max_adults: number;
    max_infants: number;
    max_children: number;
    price_set_id: string;
    max_occupancy: number;
    max_extra_beds: number;
    max_adults_beyond_capacity: number;
  };
  type_id?: string | null;
  type?: any | null;
  collection_id?: string | null;
  collection?: any | null;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
  variants: Room[];
}

/**
 * Individual room interface - matches API response structure
 */
export interface Room {
  id: string;
  title: string;
  sku?: string | null;
  barcode?: string | null;
  ean?: string | null;
  upc?: string | null;
  allow_backorder: boolean;
  manage_inventory: boolean;
  hs_code?: string | null;
  origin_country?: string | null;
  mid_code?: string | null;
  material?: string | null;
  weight?: number | null;
  length?: number | null;
  height?: number | null;
  width?: number | null;
  metadata: {
    floor: string;
    notes: string;
    status: string;
    is_active: boolean;
    left_room: string;
    right_room: string;
    room_number: string;
    opposite_room: string;
    connected_room: string;
    availability_type?: string | null;
  };
  variant_rank: number;
  product_id: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
  room_config_id: string;
  config_name: string;
  room_number: string;
  name: string;
}

/**
 * Filter options for room availability
 */
export interface AvailabilityFilters {
  selectedRoomType: string;
  selectedStatuses: RoomInventoryStatus[];
  dateRange: {
    start: Date;
    end: Date;
  };
}

/**
 * Booking block for timeline view
 */
export interface BookingBlock {
  id: string;
  room_id: string;
  start_time: Date;
  end_time: Date;
  status: RoomInventoryStatus;
  guest_name?: string;
  booking_id?: string;
  order_id?: string;
  notes?: string;
}

/**
 * Props for BookingBlock component
 */
export interface BookingBlockProps {
  booking: Booking;
  left: number;
  width: number;
  height: number;
  room: Room;
  isHighlighted?: boolean;
}

/**
 * Timeline position interface
 */
export interface TimelinePosition {
  left: number;
  width: number;
  height: number;
  top?: number;
}

/**
 * Drag and drop state interface
 */
export interface DragState {
  isDragging: boolean;
  dragStartX: number;
  dragStartY: number;
  originalLeft: number;
  originalWidth: number;
}

/**
 * Tooltip data interface
 */
export interface TooltipData {
  visible: boolean;
  position: {
    x: number;
    y: number;
  };
  content: {
    status: RoomInventoryStatus;
    guestName?: string;
    checkIn: Date;
    checkOut: Date;
    duration: number;
    confirmationNumber?: string;
    notes?: string;
  };
}
