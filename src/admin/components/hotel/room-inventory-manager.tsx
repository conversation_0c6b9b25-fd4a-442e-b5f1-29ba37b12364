import React, { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  Input,
  Badge,
  Select,
} from "@camped-ai/ui";
import { format, addDays, startOfDay, endOfDay } from "date-fns";
import Spinner from "../shared/spinner";
import {
  Calendar,
  Bed,
  Building2,
  Clock,
  ClipboardList,
  Tag,
} from "lucide-react";

type RoomConfig = {
  id: string;
  name: string;
  title?: string;
  type?: string;
  description?: string;
  hotel_id?: string;
};

type Room = {
  id: string;
  name: string;
  title?: string;
  room_number?: string;
  status?: string;
  floor?: string;
  room_config_id?: string;
};

type RoomInventoryManagerProps = {
  hotelId: string;
  roomId?: string;
  roomConfigId?: string;
  onInventoryUpdated?: () => void;
};

const RoomInventoryManager: React.FC<RoomInventoryManagerProps> = ({
  hotelId,
  roomId,
  roomConfigId,
  onInventoryUpdated,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [startDate, setStartDate] = useState(startOfDay(new Date()));
  const [endDate, setEndDate] = useState(endOfDay(addDays(new Date(), 7)));
  const [status, setStatus] = useState("available");
  const [notes, setNotes] = useState<string>("");
  const [roomConfigs, setRoomConfigs] = useState<RoomConfig[]>([]);
  const [selectedConfigId, setSelectedConfigId] = useState(roomConfigId || "");
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedRoomId, setSelectedRoomId] = useState(roomId || "");
  const [updateType, setUpdateType] = useState(
    roomId ? "room" : roomConfigId ? "config" : "room"
  );

  // Fetch room configurations for this hotel
  useEffect(() => {
    const fetchRoomConfigs = async () => {
      if (!hotelId) return;

      try {
        if (isInitialLoad) {
          setIsLoading(true);
        }
        console.log("Fetching room configurations for hotel:", hotelId);

        // Try different endpoints to fetch room configurations
        const endpoints = [
          `/admin/direct-room-configs?hotel_id=${hotelId}`,
          `/admin/hotel-room-configs?hotel_id=${hotelId}`,
          `/admin/simple-room-configs?hotel_id=${hotelId}`,
        ];

        let roomConfigsData = [];
        let success = false;

        for (const endpoint of endpoints) {
          try {
            console.log(`Trying to fetch room configurations from ${endpoint}`);
            const response = await fetch(endpoint);

            if (response.ok) {
              const data = await response.json();

              // Check different possible response formats
              if (data.roomConfigs && data.roomConfigs.length > 0) {
                roomConfigsData = data.roomConfigs;
                success = true;
                console.log(
                  `Found ${roomConfigsData.length} room configurations from ${endpoint}`
                );
                break;
              } else if (data.room_configs && data.room_configs.length > 0) {
                roomConfigsData = data.room_configs;
                success = true;
                console.log(
                  `Found ${roomConfigsData.length} room configurations from ${endpoint}`
                );
                break;
              } else if (Array.isArray(data) && data.length > 0) {
                roomConfigsData = data;
                success = true;
                console.log(
                  `Found ${roomConfigsData.length} room configurations from ${endpoint}`
                );
                break;
              }
            }
          } catch (error) {
            console.log(`Error fetching from ${endpoint}:`, error);
          }
        }

        if (success && roomConfigsData.length > 0) {
          setRoomConfigs(roomConfigsData);

          // If no config is selected, select the first one
          if (!selectedConfigId && !roomConfigId) {
            setSelectedConfigId(roomConfigsData[0].id);
          }
        } else {
          console.error(
            "Could not fetch room configurations from any endpoint"
          );
          toast.error("Error", {
            description:
              "Failed to fetch room configurations. Please check the server logs.",
          });
          setRoomConfigs([]);
        }
      } catch (error) {
        console.error("Error fetching room configurations:", error);
        toast.error("Error", {
          description:
            error instanceof Error
              ? error.message
              : "Failed to fetch room configurations",
        });
      } finally {
        if (isInitialLoad) {
          setIsLoading(false);
          setIsInitialLoad(false);
        }
      }
    };

    fetchRoomConfigs();
  }, [hotelId, roomConfigId, selectedConfigId, isInitialLoad]);

  // Fetch rooms for the selected room configuration
  useEffect(() => {
    const fetchRooms = async () => {
      if (!selectedConfigId) return;

      try {
        // Don't show loading for subsequent room fetches
        console.log("Fetching rooms for room configuration:", selectedConfigId);

        // Try different endpoints to fetch rooms
        const endpoints = [
          `/admin/room-configs/${selectedConfigId}/rooms`,
          `/admin/hotel-management/room-configs/${selectedConfigId}/rooms`,
          `/admin/products/${selectedConfigId}/variants`,
        ];

        let roomsData = [];
        let success = false;

        for (const endpoint of endpoints) {
          try {
            console.log(`Trying to fetch rooms from ${endpoint}`);
            const response = await fetch(endpoint);

            if (response.ok) {
              const data = await response.json();

              // Check different possible response formats
              if (data.rooms && data.rooms.length > 0) {
                roomsData = data.rooms;
                success = true;
                console.log(`Found ${roomsData.length} rooms from ${endpoint}`);
                break;
              } else if (data.variants && data.variants.length > 0) {
                // Transform variants to room format
                roomsData = data.variants.map((variant: any) => ({
                  id: variant.id,
                  name: variant.title || `Room ${variant.id.slice(-4)}`,
                  title: variant.title,
                  room_number: variant.metadata?.room_number || variant.title,
                  status: variant.metadata?.status || "available",
                  floor: variant.metadata?.floor || "",
                  room_config_id: selectedConfigId,
                }));
                success = true;
                console.log(
                  `Found ${roomsData.length} variants/rooms from ${endpoint}`
                );
                break;
              } else if (Array.isArray(data) && data.length > 0) {
                roomsData = data;
                success = true;
                console.log(`Found ${roomsData.length} rooms from ${endpoint}`);
                break;
              }
            }
          } catch (error) {
            console.log(`Error fetching from ${endpoint}:`, error);
          }
        }

        if (success && roomsData.length > 0) {
          setRooms(roomsData);

          // If no room is selected, select the first one
          if (!selectedRoomId && !roomId && updateType === "room") {
            setSelectedRoomId(roomsData[0].id);
          }
        } else {
          console.error("Could not fetch rooms from any endpoint");
          toast.error("Error", {
            description: "Failed to fetch rooms. Please check the server logs.",
          });
          setRooms([]);
        }
      } catch (error) {
        console.error("Error fetching rooms:", error);
        toast.error("Error", {
          description:
            error instanceof Error ? error.message : "Failed to fetch rooms",
        });
      } finally {
        // No loading state change for room fetches
      }
    };

    fetchRooms();
  }, [selectedConfigId, roomId, selectedRoomId, updateType]);

  // Handle date change
  const handleDateChange = (type: "start" | "end", date: string) => {
    if (type === "start") {
      setStartDate(startOfDay(new Date(date)));
    } else {
      setEndDate(endOfDay(new Date(date)));
    }
  };

  // Handle update type change
  const handleUpdateTypeChange = (type: "room" | "config") => {
    setUpdateType(type);

    if (type === "config") {
      setSelectedRoomId("");
    }
  };

  // Handle inventory update
  const handleInventoryUpdate = async () => {
    try {
      setIsLoading(true);

      // Validate inputs
      if (!startDate || !endDate) {
        throw new Error("Start date and end date are required");
      }

      if (startDate > endDate) {
        throw new Error("Start date must be before end date");
      }

      if (!status) {
        throw new Error("Please select a status");
      }

      if (updateType === "room" && !selectedRoomId) {
        throw new Error("Please select a room");
      }

      if (updateType === "config" && !selectedConfigId) {
        throw new Error("Please select a room configuration");
      }

      const updateData = {
        update_type: updateType,
        room_id: updateType === "room" ? selectedRoomId : undefined,
        room_config_id: updateType === "config" ? selectedConfigId : undefined,
        start_date: format(startDate, "yyyy-MM-dd"),
        end_date: format(endDate, "yyyy-MM-dd"),
        status: status,

        metadata: {
          notes: notes || undefined,
        },
      };

      console.log("Updating inventory with the following data:", updateData);

      // Use the most reliable endpoint directly
      const endpoint = "/admin/hotel-management/room-inventory/bulk-update";
      let success = false;
      let responseData = null;

      // Add auth header to the request
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
      };

      // Get auth token from localStorage if available
      const token = localStorage.getItem("_medusa_jwt");
      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
        console.log("Using auth token:", token.substring(0, 10) + "...");
      } else {
        console.warn("No auth token found in localStorage");
      }

      try {
        console.log(`Updating inventory using ${endpoint}`);
        console.log("Request data:", updateData);

        // Send update directly to the API
        const response = await fetch(endpoint, {
          method: "POST",
          headers,
          body: JSON.stringify(updateData),
          credentials: "include", // Include cookies for session-based auth
        });

        // Check if the response is OK
        if (response.ok) {
          responseData = await response.json();
          success = true;
          console.log(`Successfully updated inventory:`, responseData);
        } else {
          // If the response is not OK, try to parse the error
          try {
            const errorData = await response.json();
            console.error(`Error updating inventory:`, errorData);
            responseData = errorData;
          } catch (jsonError) {
            console.error(`Error parsing error response:`, jsonError);
            responseData = {
              success: false,
              message: "Failed to parse server response",
              error: "Unknown error",
            };
          }
        }
      } catch (error: any) {
        console.error(`Error with inventory update:`, error);
        responseData = {
          success: false,
          message: "Network error. Please check your connection and try again.",
          error: error?.message || "Unknown error",
        };
      }

      if (success) {
        toast.success("Inventory updated successfully");

        // Call the callback if provided
        if (onInventoryUpdated) {
          onInventoryUpdated();
        }
      } else {
        if (responseData?.message) {
          toast.error("Failed to update inventory", {
            description: responseData.message,
          });
        } else {
          toast.error("Failed to update inventory", {
            description:
              "Could not update inventory. Please check your connection and try again.",
          });
        }
      }
    } catch (error) {
      console.error("Error updating inventory:", error);
      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to update inventory",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container className="p-0 border border-gray-200 rounded-lg bg-white shadow-sm overflow-hidden">
      <Toaster />
      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Spinner size="medium" />
          <div className="ml-4">Loading...</div>
        </div>
      ) : (
        <div className="p-6 bg-ui-bg-subtle">
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Tag className="w-4 h-4 text-ui-fg-subtle" />
                <Heading level="h3" className="text-md">
                  Update Type
                </Heading>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={updateType === "room" ? "primary" : "secondary"}
                  size="small"
                  onClick={() => handleUpdateTypeChange("room")}
                  className="flex items-center gap-2"
                >
                  <Bed className="w-4 h-4" />
                  Single Room
                </Button>
                <Button
                  variant={updateType === "config" ? "primary" : "secondary"}
                  size="small"
                  onClick={() => handleUpdateTypeChange("config")}
                  className="flex items-center gap-2"
                >
                  <Building2 className="w-4 h-4" />
                  All Rooms in Configuration
                </Button>
              </div>
            </div>

            <div className="pt-4 border-t border-ui-border-base">
              <div className="flex items-center gap-2 mb-4">
                <Building2 className="w-4 h-4 text-ui-fg-subtle" />
                <Heading level="h3" className="text-md">
                  Room Selection
                </Heading>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Text className="text-ui-fg-subtle mb-2 flex items-center gap-1">
                    Room Configuration
                  </Text>
                  <Select
                    value={selectedConfigId}
                    onValueChange={setSelectedConfigId}
                    disabled={!!roomConfigId}
                  >
                    <Select.Trigger className="w-full">
                      <Select.Value placeholder="Select a configuration" />
                    </Select.Trigger>
                    <Select.Content>
                      {roomConfigs.map((config) => (
                        <Select.Item key={config.id} value={config.id}>
                          {config.title || config.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>

                {updateType === "room" && (
                  <div>
                    <Text className="text-ui-fg-subtle mb-2 flex items-center gap-1">
                      Room
                    </Text>
                    <Select
                      value={selectedRoomId}
                      onValueChange={setSelectedRoomId}
                      disabled={!!roomId}
                    >
                      <Select.Trigger className="w-full">
                        <Select.Value placeholder="Select a room" />
                      </Select.Trigger>
                      <Select.Content>
                        {rooms.map((room) => (
                          <Select.Item key={room.id} value={room.id}>
                            {room.title ||
                              room.name ||
                              `Room ${room.id.slice(-4)}`}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  </div>
                )}
              </div>
            </div>

            <div className="pt-4 border-t border-ui-border-base">
              <div className="flex items-center gap-2 mb-4">
                <Calendar className="w-4 h-4 text-ui-fg-subtle" />
                <Heading level="h3" className="text-md">
                  Date Range
                </Heading>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Text className="text-ui-fg-subtle mb-2 flex items-center gap-1">
                    Start Date
                  </Text>
                  <Input
                    type="date"
                    value={format(startDate, "yyyy-MM-dd")}
                    onChange={(e) => handleDateChange("start", e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <Text className="text-ui-fg-subtle mb-2 flex items-center gap-1">
                    End Date
                  </Text>
                  <Input
                    type="date"
                    value={format(endDate, "yyyy-MM-dd")}
                    onChange={(e) => handleDateChange("end", e.target.value)}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            <div className="pt-4 border-t border-ui-border-base">
              <div className="flex items-center gap-2 mb-4">
                <Clock className="w-4 h-4 text-ui-fg-subtle" />
                <Heading level="h3" className="text-md">
                  Status
                </Heading>
              </div>
              <div>
                <Text className="text-ui-fg-subtle mb-3">
                  Select the status for this date range
                </Text>
                <div className="flex flex-wrap gap-3">
                  <Badge
                    color="green"
                    className={`cursor-pointer px-3 py-1 ${
                      status === "available" ? "ring-2 ring-green-500" : ""
                    }`}
                    onClick={() => setStatus("available")}
                  >
                    Available
                  </Badge>
                  <Badge
                    color="blue"
                    className={`cursor-pointer px-3 py-1 ${
                      status === "booked" ? "ring-2 ring-blue-500" : ""
                    }`}
                    onClick={() => setStatus("booked")}
                  >
                    Booked
                  </Badge>
                  <Badge
                    color="orange"
                    className={`cursor-pointer px-3 py-1 ${
                      status === "maintenance" ? "ring-2 ring-orange-500" : ""
                    }`}
                    onClick={() => setStatus("maintenance")}
                  >
                    Maintenance
                  </Badge>
                  <Badge
                    color="purple"
                    className={`cursor-pointer px-3 py-1 ${
                      status === "reserved" ? "ring-2 ring-purple-500" : ""
                    }`}
                    onClick={() => setStatus("reserved")}
                  >
                    Reserved
                  </Badge>
                  <Badge
                    color="yellow"
                    className={`cursor-pointer px-3 py-1 ${
                      status === "on_demand" ? "ring-2 ring-yellow-500" : ""
                    }`}
                    onClick={() => setStatus("on_demand")}
                  >
                    On Request
                  </Badge>
                  <Badge
                    color="grey"
                    className={`cursor-pointer px-3 py-1 ${
                      status === "unavailable" ? "ring-2 ring-gray-500" : ""
                    }`}
                    onClick={() => setStatus("unavailable")}
                  >
                    Unavailable
                  </Badge>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t border-ui-border-base">
              <div className="flex items-center gap-2 mb-4">
                <ClipboardList className="w-4 h-4 text-ui-fg-subtle" />
                <Heading level="h3" className="text-md">
                  Notes (Optional)
                </Heading>
              </div>
              <div>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="w-full rounded-md border border-ui-border-base px-4 py-2 min-h-[80px]"
                  placeholder="Add notes about this inventory entry (e.g., reason for maintenance, special instructions)"
                />
                <Text className="text-xs text-ui-fg-subtle mt-2">
                  These notes will be visible in the calendar view when hovering
                  over the dates
                </Text>
              </div>
            </div>

            <div className="flex justify-end pt-4 border-t border-ui-border-base">
              <Button
                variant="primary"
                onClick={handleInventoryUpdate}
                disabled={isLoading}
                className="px-6"
                size="large"
              >
                {isLoading ? "Updating..." : "Update Inventory"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};

export default RoomInventoryManager;
