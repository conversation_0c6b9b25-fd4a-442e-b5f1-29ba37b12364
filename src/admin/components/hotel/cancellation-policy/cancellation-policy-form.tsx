import React, { useState, useEffect, useRef } from "react";
import {
  But<PERSON>,
  Text,
  Heading,
  Input,
  Checkbox,
  Textarea,
  InlineTip,
  Select,
  toast,
  FocusModal,
} from "@camped-ai/ui";
import { CancellationPolicyData } from "../../../types";
import LanguageSelector from "../../language-selector";
import { useProjectLanguages } from "../../../hooks/languages/useProjectLanguages";
import { useCancellationPolicyTranslations } from "../../../hooks/translations/useCancellationPolicyTranslations";
import AITranslateButton from "../../ai-translate-button";

// Define which fields are translatable
const TRANSLATABLE_FIELDS = ["name", "description"] as const;

// Helper function to check if a field is translatable
const isFieldTranslatable = (fieldName: string): boolean => {
  return TRANSLATABLE_FIELDS.includes(fieldName as any);
};

// Helper function to determine if a field should be enabled based on language selection
const isFieldEnabled = (
  fieldName: string,
  isBaseLanguage: boolean
): boolean => {
  // If it's the base language, all fields are enabled
  if (isBaseLanguage) return true;

  // For non-base languages, only translatable fields are enabled
  return isFieldTranslatable(fieldName);
};

type CancellationPolicyFormProps = {
  initialData?: Partial<CancellationPolicyData>;
  hotelId: string;
  onSubmit: (data: Partial<CancellationPolicyData>) => Promise<void>;
  onCancel: () => void;
  selectedPolicy?: CancellationPolicyData;
};

const CancellationPolicyForm: React.FC<CancellationPolicyFormProps> = ({
  initialData,
  hotelId,
  onSubmit,
  onCancel,
}) => {
  const [formData, setFormData] = useState<Partial<CancellationPolicyData>>({
    name: "",
    description: "",
    hotel_id: hotelId,
    days_before_checkin: 0,
    refund_type: "percentage",
    refund_amount: 0,
    is_active: true,
    ...initialData,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSavingTranslations, setIsSavingTranslations] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("en");

  // State to track translated values for ALL languages (not just current)
  const [allTranslatedValues, setAllTranslatedValues] = useState<
    Record<string, Record<string, string>>
  >({});

  // Track which languages have user input to prevent overwrites
  const userInputLanguages = useRef<Set<string>>(new Set());

  // Get translated values for the current language
  const translatedValues = allTranslatedValues[selectedLanguage] || {};

  // Get available languages to determine base language
  const { languages: tolgeeLanguages } = useProjectLanguages();

  // Determine if current language is base language
  const isBaseLanguage =
    selectedLanguage === "en" ||
    tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)?.base ===
    true;

  // Translation management
  const policyId = initialData?.id || "new";
  const { saveTranslations, loadTranslations } =
    useCancellationPolicyTranslations(policyId);

  // Load translations when language changes
  useEffect(() => {
    const loadTranslationsForLanguage = async () => {
      console.log(`🔄 Language change effect triggered:`, {
        selectedLanguage,
        isBaseLanguage,
        policyId,
        hasUserInput: userInputLanguages.current.has(selectedLanguage),
      });

      if (!isBaseLanguage && policyId && policyId !== "new") {
        console.log(
          `🔄 Loading translations for ${selectedLanguage} (policy: ${policyId})`
        );

        try {
          const initialTranslations = await loadTranslations(selectedLanguage);
          console.log(`📥 Loaded translations:`, initialTranslations);

          if (Object.keys(initialTranslations).length > 0) {
            console.log(
              `🔧 Setting initial translations for ${selectedLanguage}:`,
              initialTranslations
            );

            // Convert from full keys to field names for the form state
            const fieldTranslations: Record<string, string> = {};
            TRANSLATABLE_FIELDS.forEach((fieldName) => {
              const key = `cancellation_policy.${policyId}.${fieldName}`;
              if (initialTranslations[key]) {
                fieldTranslations[fieldName] = initialTranslations[key];
                console.log(
                  `📝 Mapped ${key} -> ${fieldName}: ${initialTranslations[key]}`
                );
              }
            });

            if (Object.keys(fieldTranslations).length > 0) {
              // Only update if we don't already have user input for this language
              const hasUserInput = userInputLanguages.current.has(selectedLanguage);
              const existingTranslations = allTranslatedValues[selectedLanguage] || {};
              const hasExistingUserTranslations = Object.keys(existingTranslations).length > 0;

              if (!hasUserInput && !hasExistingUserTranslations) {
                setAllTranslatedValues((prev) => ({
                  ...prev,
                  [selectedLanguage]: {
                    ...prev[selectedLanguage],
                    ...fieldTranslations,
                  },
                }));
                console.log(
                  `✅ Updated form state with translations for ${selectedLanguage}`
                );
              } else {
                console.log(
                  `⚠️ Skipping API translation load for ${selectedLanguage} - user input or existing translations exist`,
                  { hasUserInput, hasExistingUserTranslations, existingTranslations }
                );
              }
            }
          } else {
            console.log(`📭 No translations found for ${selectedLanguage}`);
          }
        } catch (error) {
          console.error(
            `❌ Error loading translations for ${selectedLanguage}:`,
            error
          );
        }
      } else if (isBaseLanguage) {
        console.log(
          "📍 Base language selected - keeping translated values for later use"
        );
      } else {
        console.log(
          `⏭️ Skipping translation load - policyId: ${policyId}, isBaseLanguage: ${isBaseLanguage}`
        );
      }
    };

    loadTranslationsForLanguage();
  }, [selectedLanguage, isBaseLanguage, policyId, loadTranslations]);

  // Helper function to get field value based on selected language
  const getFieldValue = (fieldName: string, baseValue: string): string => {
    if (isBaseLanguage || !isFieldTranslatable(fieldName)) {
      return baseValue;
    }

    // For non-base languages, get the most up-to-date translated value
    const currentLanguageTranslations =
      allTranslatedValues[selectedLanguage] || {};
    const translatedValue = currentLanguageTranslations[fieldName];
    const finalValue =
      translatedValue !== undefined ? translatedValue : baseValue;

    console.log(
      `getFieldValue(${fieldName}): base="${baseValue}", translated="${translatedValue}", final="${finalValue}", lang="${selectedLanguage}"`
    );
    return finalValue;
  };

  // Helper function to handle field changes
  const handleFieldChange = (fieldName: string, value: string) => {
    console.log(
      `🎯 handleFieldChange called: ${fieldName} = "${value}" (lang: ${selectedLanguage})`
    );

    if (isBaseLanguage) {
      // Update the base form data
      console.log(`📝 Updating base form data for ${fieldName}`);
      const updated = { ...formData, [fieldName]: value };
      console.log(`📝 Base form data updated:`, updated);
      setFormData(updated);
    } else if (isFieldTranslatable(fieldName)) {
      // Update the translated values for the current language in multi-language state
      console.log(
        `🌐 Updating translation for ${fieldName} in ${selectedLanguage}`
      );

      // Mark this language as having user input
      userInputLanguages.current.add(selectedLanguage);
      console.log(`👤 Marked ${selectedLanguage} as having user input`);

      setAllTranslatedValues((prev) => {
        const updated = {
          ...prev,
          [selectedLanguage]: {
            ...prev[selectedLanguage],
            [fieldName]: value,
          },
        };
        console.log(`🔄 Updated allTranslatedValues:`, updated);
        return updated;
      });
    }
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const target = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: target.checked,
      });
    } else if (name === "days_before_checkin" || name === "refund_amount") {
      setFormData({
        ...formData,
        [name]: parseFloat(value),
      });
    } else if (isFieldTranslatable(name)) {
      // Handle translatable fields using the translation logic
      handleFieldChange(name, value);
    } else {
      // Handle non-translatable fields normally
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare form data with translation handling
      let updatedFormData = { ...formData };

      // If we're in base language, include any translated values in the base form
      if (isBaseLanguage) {
        // For base language, use form data as-is
        console.log("📝 Submitting base language form data:", updatedFormData);
      }

      // Submit the form data to the parent component
      await onSubmit(updatedFormData);

      // For existing policies, save translations for ALL languages that have translated content
      // This should happen regardless of which language is currently selected
      if (policyId && policyId !== "new") {
        console.log('🔄 Processing translations for existing cancellation policy:', policyId);

        // Get all languages that have any kind of translations
        const allLanguagesWithTranslations = new Set([
          ...Object.keys(allTranslatedValues).filter(lang => {
            const langTranslations = allTranslatedValues[lang] || {};
            return Object.keys(langTranslations).length > 0;
          })
        ]);

        console.log('🌍 Languages with translations for existing policy:', Array.from(allLanguagesWithTranslations));

        // Set translation saving loading state if we have translations to save
        if (allLanguagesWithTranslations.size > 0) {
          setIsSavingTranslations(true);
        }

        try {
          // Save translations for each language that has translated content
          for (const languageCode of allLanguagesWithTranslations) {
            if (languageCode === 'en') continue; // Skip base language

            console.log(`💾 Processing ${languageCode} translations for existing policy...`);

            const languageTranslations = allTranslatedValues[languageCode] || {};
            const translationsToSave: Record<string, string> = {};

            TRANSLATABLE_FIELDS.forEach(fieldName => {
              const value = languageTranslations[fieldName];
              if (value && typeof value === 'string') {
                const key = `cancellation_policy.${policyId}.${fieldName}`;
                translationsToSave[key] = value;
                console.log(`✅ Added ${languageCode} translation: ${key} = ${value}`);
              }
            });

            // Save translations for this language
            if (Object.keys(translationsToSave).length > 0) {
              await saveTranslations(languageCode, translationsToSave);
              console.log(`✅ Successfully saved ${languageCode} field translations`);
            }
          }
        } catch (error) {
          console.error('❌ Error saving cancellation policy translations:', error);
          // Continue with form submission even if translation saving fails
          toast.error("Warning", {
            description: "Policy saved but translations could not be saved",
          });
        } finally {
          // Clear translation saving loading state
          setIsSavingTranslations(false);
        }

        console.log('🎉 Completed saving translations for all languages');
      }
    } catch (error) {
      console.error("Error saving cancellation policy:", error);
      toast.error("Error", {
        description: "Failed to save cancellation policy",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <FocusModal.Header className="flex justify-between items-center w-full">
        <div className="flex justify-between items-center w-full">
          <Heading level="h2" className="text-l font-semibold">
            {initialData?.id
              ? "Edit Cancellation Policy"
              : "Create Cancellation Policy"}
          </Heading>
          <LanguageSelector
            selectedLanguage={selectedLanguage}
            onLanguageChange={setSelectedLanguage}
            className="w-auto"
          />
        </div>
      </FocusModal.Header>

      <div className="flex flex-col overflow-y-auto  flex-1 px-6 pb-4 w-full">
        <div className="w-full">
          <div className="space-y-6 mt-4">
            {/* Translation Info Tip */}
            {!isBaseLanguage && (
              <div className="mb-6">
                <InlineTip label="Translation Mode">
                  You are editing in{" "}
                  {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)
                    ?.name || selectedLanguage}{" "}
                  language. Only translatable fields (name, description) can be
                  edited. Other fields show base language values and are
                  read-only.
                </InlineTip>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <Text className="font-medium">Policy Name</Text>
                  {!isBaseLanguage && (
                    <AITranslateButton
                      sourceText={formData.name || ""}
                      targetLanguage={selectedLanguage}
                      fieldType="name"
                      onTranslate={(translatedText) =>
                        handleFieldChange("name", translatedText)
                      }
                      context={{
                        entityType: "cancellation_policy",
                        entityName: formData.name || "New Policy",
                      }}
                      disabled={!isFieldEnabled("name", isBaseLanguage)}
                      allowEmptySource={true}
                    />
                  )}
                </div>
                <Input
                  name="name"
                  key={`name-${selectedLanguage}`}
                  value={getFieldValue("name", formData.name || "")}
                  onChange={handleChange}
                  placeholder="e.g., Standard Cancellation Policy"
                  disabled={!isFieldEnabled("name", isBaseLanguage)}
                  required
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <Text className="font-medium">Description</Text>
                  {!isBaseLanguage && (
                    <AITranslateButton
                      sourceText={formData.description || ""}
                      targetLanguage={selectedLanguage}
                      fieldType="description"
                      onTranslate={(translatedText) =>
                        handleFieldChange("description", translatedText)
                      }
                      context={{
                        entityType: "cancellation_policy",
                        entityName: formData.name || "New Policy",
                      }}
                      disabled={!isFieldEnabled("description", isBaseLanguage)}
                      allowEmptySource={true}
                    />
                  )}
                </div>
                <Textarea
                  name="description"
                  key={`description-${selectedLanguage}`}
                  value={getFieldValue(
                    "description",
                    formData.description || ""
                  )}
                  onChange={handleChange}
                  placeholder="Describe the cancellation policy details"
                  disabled={!isFieldEnabled("description", isBaseLanguage)}
                  rows={3}
                />
              </div>

              <div>
                <Text className="font-medium mb-1">Days Before Check-in</Text>
                <Input
                  type="number"
                  name="days_before_checkin"
                  value={formData.days_before_checkin || 0}
                  onChange={handleChange}
                  disabled={
                    !isFieldEnabled("days_before_checkin", isBaseLanguage)
                  }
                  min={0}
                  required
                />
                <Text size="small" className="text-gray-500 mt-1">
                  Number of days before check-in when this policy applies
                </Text>
              </div>

              <div>
                <Text className="font-medium mb-1">Refund Type</Text>
                <Select
                  value={formData.refund_type || "percentage"}
                  onValueChange={(value) => {
                    setFormData({
                      ...formData,
                      refund_type: value as
                        | "percentage"
                        | "fixed"
                        | "no_refund",
                    });
                  }}
                  disabled={!isFieldEnabled("refund_type", isBaseLanguage)}
                >
                  <Select.Trigger className="w-full">
                    <Select.Value placeholder="Select refund type" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="percentage">Percentage</Select.Item>
                    <Select.Item value="fixed">Fixed Amount</Select.Item>
                    <Select.Item value="no_refund">No Refund</Select.Item>
                  </Select.Content>
                </Select>
              </div>

              {formData.refund_type !== "no_refund" && (
                <div>
                  <Text className="font-medium mb-1">
                    {formData.refund_type === "percentage"
                      ? "Refund Percentage"
                      : "Refund Amount"}
                  </Text>
                  <Input
                    type="number"
                    name="refund_amount"
                    value={formData.refund_amount || 0}
                    onChange={handleChange}
                    disabled={!isFieldEnabled("refund_amount", isBaseLanguage)}
                    min={0}
                    max={
                      formData.refund_type === "percentage" ? 100 : undefined
                    }
                    required
                  />
                  {formData.refund_type === "percentage" && (
                    <Text size="small" className="text-gray-500 mt-1">
                      Percentage of the booking amount to refund (0-100)
                    </Text>
                  )}
                </div>
              )}

              <div className="flex items-center">
                <Checkbox
                  name="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      is_active: checked === true,
                    })
                  }
                  disabled={!isFieldEnabled("is_active", isBaseLanguage)}
                  id="is_active"
                />
                <label htmlFor="is_active" className="ml-2 cursor-pointer">
                  <Text>Active</Text>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-2 p-4 border-t mt-5">
        <Button variant="secondary" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={isSubmitting}
          isLoading={isSubmitting}
        >
          {isSubmitting ? (
            isSavingTranslations ?
              "Saving Translations..." :
              (initialData?.id ? "Updating..." : "Creating...")
          ) : (
            initialData?.id ? "Update Policy" : "Create Policy"
          )}
        </Button>
      </div>
    </>
  );
};

export default CancellationPolicyForm;
