import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Container,
  Text,
  Heading,
  Table,
  FocusModal,
  Prompt,
  toast,
  Toaster,
} from "@camped-ai/ui";
import { PlusMini, Pencil, Trash } from "@camped-ai/icons";
import { CancellationPolicyData } from "../../../types";
import CancellationPolicyForm from "./cancellation-policy-form";
import "../../../styles/cancellation-policy-modal.css";
// Login helper removed as it's not needed

type CancellationPolicyListProps = {
  hotelId: string;
};

const CancellationPolicyList: React.FC<CancellationPolicyListProps> = ({
  hotelId,
}) => {
  const [policies, setPolicies] = useState<CancellationPolicyData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] =
    useState<CancellationPolicyData | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [policyToDelete, setPolicyToDelete] =
    useState<CancellationPolicyData | null>(null);

  const fetchPolicies = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/admin/hotel-management/cancellation-policies?hotel_id=${hotelId}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch policies: ${response.statusText}`);
      }

      const data = await response.json();
      // User is already authenticated
      setPolicies(data.cancellation_policies || []);
    } catch (error) {
      console.error("Error fetching cancellation policies:", error);
      toast.error("Error", {
        description: "Failed to load cancellation policies",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (hotelId) {
      fetchPolicies();
    }
  }, [hotelId]);

  const handleCreatePolicy = () => {
    setSelectedPolicy(null);
    setIsModalOpen(true);
  };

  const handleEditPolicy = (policy: CancellationPolicyData) => {
    setSelectedPolicy(policy);
    setIsModalOpen(true);
  };

  const handleDeletePolicy = (policy: CancellationPolicyData) => {
    setPolicyToDelete(policy);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeletePolicy = async () => {
    if (!policyToDelete) return;

    setIsDeleting(true);
    try {
      const response = await fetch(
        `/admin/hotel-management/cancellation-policies/${policyToDelete.id}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to delete policy: ${response.statusText}`);
      }

      // Refresh the policies list after deletion
      await fetchPolicies();

      toast.success("Success", {
        description: "Cancellation policy deleted successfully",
      });

      // Close dialog and reset state
      setIsDeleteDialogOpen(false);
      setPolicyToDelete(null);
    } catch (error) {
      console.error("Error deleting cancellation policy:", error);
      toast.error("Error", {
        description: "Failed to delete cancellation policy",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleSubmitPolicy = async (data: Partial<CancellationPolicyData>) => {
    try {
      let response: Response;
      let responseData: { cancellation_policy: CancellationPolicyData };

      if (selectedPolicy?.id) {
        // Update existing policy - only send the fields that are being updated
        const updateData: any = {};

        // Only include fields that are explicitly provided and needed for update
        if (data.name !== undefined) updateData.name = data.name;
        if (data.description !== undefined)
          updateData.description = data.description;
        if (data.days_before_checkin !== undefined)
          updateData.days_before_checkin = data.days_before_checkin;
        if (data.refund_type !== undefined)
          updateData.refund_type = data.refund_type;
        if (data.refund_amount !== undefined)
          updateData.refund_amount = data.refund_amount;
        if (data.is_active !== undefined) updateData.is_active = data.is_active;

        console.log("Updating policy with data:", updateData);

        // Use the proper update endpoint
        response = await fetch(
          `/admin/hotel-management/cancellation-policies/${selectedPolicy.id}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(updateData),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to update policy: ${errorText}`);
        }

        responseData = await response.json();

        // After successful update, refresh the policies list
        // The backend creates a new policy with a new ID and deletes the old one
        // so we need to fetch the latest list
        // Wait a bit to ensure the backend has completed the operation
        setTimeout(async () => {
          await fetchPolicies();
        }, 500);

        toast.success("Success", {
          description: "Cancellation policy updated successfully",
        });
      } else {
        // Create new policy
        const createData = {
          ...data,
          hotel_id: hotelId,
        };

        response = await fetch(
          "/admin/hotel-management/cancellation-policies",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(createData),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to create policy: ${errorText}`);
        }

        responseData = await response.json();

        // Add the new policy to local state
        setPolicies([...policies, responseData.cancellation_policy]);

        toast.success("Success", {
          description: "Cancellation policy created successfully",
        });
      }

      // Close modal
      setIsModalOpen(false);
    } catch (error) {
      console.error("Error saving cancellation policy:", error);
      toast.error("Error", {
        description:
          "Failed to save cancellation policy: " +
          (error instanceof Error ? error.message : String(error)),
      });
    }
  };

  const getRefundText = (policy: CancellationPolicyData) => {
    switch (policy.refund_type) {
      case "percentage":
        return `${policy.refund_amount}% refund`;
      case "fixed":
        return `${policy.refund_amount} fixed refund`;
      case "no_refund":
        return "No refund";
      default:
        return "Unknown";
    }
  };

  // Login is handled by the admin framework

  return (
    <Container className="mb-8">
      <Toaster />

      <div className="flex justify-between items-center mb-4">
        <div>
          <Heading level="h3">Cancellation Policies</Heading>
          <Text size="small" className="text-gray-500">
            Define when and how guests can cancel their bookings
          </Text>
        </div>
        <Button
          variant="secondary"
          onClick={handleCreatePolicy}
          className="flex items-center justify-center"
        >
          <PlusMini className="w-5 h-5 mt-1" />
          Add Policy
        </Button>
      </div>

      {isLoading ? (
        <Text>Loading cancellation policies...</Text>
      ) : policies.length === 0 ? (
        <div className="bg-gray-50 p-8 rounded-lg text-center">
          <Text className="text-gray-500">
            No cancellation policies defined yet. Create your first policy to
            define how cancellations are handled.
          </Text>
          <Button
            variant="secondary"
            onClick={handleCreatePolicy}
            className="mt-4"
          >
            Create Cancellation Policy
          </Button>
        </div>
      ) : (
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Name</Table.HeaderCell>
              <Table.HeaderCell>Days Before Check-in</Table.HeaderCell>
              <Table.HeaderCell>Refund</Table.HeaderCell>
              <Table.HeaderCell>Status</Table.HeaderCell>
              <Table.HeaderCell>Actions</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {policies.map((policy) => (
              <Table.Row key={policy.id}>
                <Table.Cell>
                  <div className="my-3">
                    <Text className="font-medium">{policy.name}</Text>
                    {policy.description && (
                      <Text size="small" className="text-gray-500">
                        {policy.description}
                      </Text>
                    )}
                  </div>
                </Table.Cell>
                <Table.Cell>{policy.days_before_checkin}</Table.Cell>
                <Table.Cell>{getRefundText(policy)}</Table.Cell>
                <Table.Cell>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      policy.is_active
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {policy.is_active ? "Active" : "Inactive"}
                  </span>
                </Table.Cell>
                <Table.Cell>
                  <div className="flex space-x-2">
                    <Button
                      variant="transparent"
                      size="small"
                      onClick={() => handleEditPolicy(policy)}
                    >
                      <Pencil className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="transparent"
                      size="small"
                      onClick={() => handleDeletePolicy(policy)}
                      disabled={isDeleting}
                    >
                      <Trash className="w-4 h-4 text-red-500" />
                    </Button>
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      )}

      <FocusModal open={isModalOpen} onOpenChange={setIsModalOpen}>
        <FocusModal.Content className="cancellation-policy-modal fixed right-0 top-0 w-[600px] max-w-[90vw] h-full bg-white shadow-2xl overflow-hidden m-0 rounded-none z-[100]">
          <CancellationPolicyForm
            initialData={selectedPolicy || undefined}
            hotelId={hotelId}
            onSubmit={handleSubmitPolicy}
            onCancel={() => setIsModalOpen(false)}
            selectedPolicy={selectedPolicy}
          />
        </FocusModal.Content>
      </FocusModal>

      {/* Delete Confirmation Dialog */}
      <Prompt open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Cancellation Policy</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete the cancellation policy "
              {policyToDelete?.name}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setPolicyToDelete(null);
              }}
              disabled={isDeleting}
            >
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={confirmDeletePolicy} disabled={isDeleting}>
              {isDeleting ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </Container>
  );
};

export default CancellationPolicyList;
