import React, { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  Input,
  Badge,
  Tooltip,
} from "@camped-ai/ui";
import CustomSelect from "../../custom-select";
import { ChevronLeft, ChevronRight, Filter, Calendar } from "@camped-ai/icons";
import { Search } from "lucide-react";
import {
  format,
  addMonths,
  subMonths,
  addDays,
  isSameDay,
  parseISO,
  isWithinInterval,
} from "date-fns";

// Types
type Room = {
  id: string;
  name: string;
  room_number: string;
  floor?: string;
  status?: string;
  room_config_id: string;
};

type RoomConfig = {
  id: string;
  name: string;
  type?: string;
};

type Booking = {
  id: string;
  room_id: string;
  guest_name: string;
  check_in: string;
  check_out: string;
  status: "confirmed" | "pending" | "cancelled" | "completed";
};

type RoomAvailability = {
  room_id: string;
  date: string;
  status:
    | "available"
    | "booked"
    | "maintenance"
    | "reserved"
    | "reserved_unassigned"
    | "cart_reserved"
    | "unavailable"
    | "on_demand";
  quantity: number;
  dynamic_price?: number;
};

type RoomInventoryCalendarViewProps = {
  hotelId: string;
};

// Status colors
const statusColors = {
  available: "bg-green-100 text-green-800",
  booked: "bg-white text-blue-800",
  maintenance: "bg-white text-orange-800",
  reserved: "bg-white text-red-800",
  reserved_unassigned: "bg-white text-red-800", // Same as reserved
  cart_reserved: "bg-white text-red-800", // Same as reserved
  unavailable: "bg-gray-100 text-gray-800",
  on_demand: "bg-yellow-100 text-yellow-800",
  pending: "bg-yellow-100 text-yellow-800",
  confirmed: "bg-blue-100 text-blue-800",
  cancelled: "bg-red-100 text-red-800",
  completed: "bg-green-100 text-green-800",
};

// Status icons
const statusIcons = {
  booked: "👤",
  maintenance: "🔧",
  reserved: "⛔",
  reserved_unassigned: "⛔",
  cart_reserved: "⛔",
  on_demand: "📞",
};

const RoomInventoryCalendarView: React.FC<RoomInventoryCalendarViewProps> = ({
  hotelId,
}) => {
  // State
  const [currentDate, setCurrentDate] = useState(new Date());
  const [roomConfigs, setRoomConfigs] = useState<RoomConfig[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [availability, setAvailability] = useState<RoomAvailability[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFloor, setSelectedFloor] = useState<string>("all_floors");
  const [selectedRoomType, setSelectedRoomType] = useState<string>("all_types");
  const [floors, setFloors] = useState<string[]>([]);
  const [roomTypes, setRoomTypes] = useState<string[]>([]);

  // Calculate dates for the current view
  const startOfMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    1
  );
  const daysInView = 14; // Show 2 weeks at a time
  const dates = Array.from({ length: daysInView }, (_, i) =>
    addDays(startOfMonth, i)
  );

  // Fetch data
  useEffect(() => {
    if (!hotelId) return;

    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Format dates for API
        const startDate = format(dates[0], "yyyy-MM-dd");
        const endDate = format(dates[dates.length - 1], "yyyy-MM-dd");

        // Fetch hotel availability data
        const response = await fetch(
          `/admin/hotel-management/availability?hotel_id=${hotelId}&start_date=${startDate}&end_date=${endDate}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch availability data");
        }

        const data = await response.json();

        // Set room configs and rooms
        setRoomConfigs(data.room_configs || []);
        setRooms(data.rooms || []);
        setAvailability(data.availability || []);

        // Extract unique floors and room types for filtering
        const uniqueFloors = [
          ...new Set(data.rooms.map((room) => room.floor).filter(Boolean)),
        ];
        setFloors(uniqueFloors);

        const uniqueRoomTypes = [
          ...new Set(
            data.room_configs.map((config) => config.type).filter(Boolean)
          ),
        ];
        setRoomTypes(uniqueRoomTypes);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Error", {
          description: "Failed to load availability data",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [hotelId, currentDate]);

  // Filter rooms based on search and filters
  const filteredRooms = rooms.filter((room) => {
    const matchesSearch = searchQuery
      ? room.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        room.room_number.toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    const matchesFloor =
      selectedFloor === "all_floors" ? true : room.floor === selectedFloor;

    const roomConfig = roomConfigs.find(
      (config) => config.id === room.room_config_id
    );
    const matchesRoomType =
      selectedRoomType === "all_types"
        ? true
        : roomConfig?.type === selectedRoomType;

    return matchesSearch && matchesFloor && matchesRoomType;
  });

  // Group rooms by room config
  const roomsByConfig = roomConfigs
    .map((config) => ({
      config,
      rooms: filteredRooms.filter((room) => room.room_config_id === config.id),
    }))
    .filter((group) => group.rooms.length > 0);

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(subMonths(currentDate, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(addMonths(currentDate, 1));
  };

  // Get room status for a specific date
  const getRoomStatus = (roomId: string, date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd");
    const roomAvailability = availability.find(
      (item) => item.room_id === roomId && item.date === dateStr
    );

    return roomAvailability?.status || "available";
  };

  // Get booking for a room on a specific date
  const getBooking = (roomId: string, date: Date) => {
    return bookings.find((booking) => {
      const checkIn = parseISO(booking.check_in);
      const checkOut = parseISO(booking.check_out);

      return (
        booking.room_id === roomId &&
        isWithinInterval(date, { start: checkIn, end: checkOut })
      );
    });
  };

  // Calculate booking span (how many days it covers in the current view)
  const getBookingSpan = (booking: Booking) => {
    const checkIn = parseISO(booking.check_in);
    const checkOut = parseISO(booking.check_out);

    // Find the first day in our view that the booking covers
    const firstDayIndex = dates.findIndex(
      (date) =>
        isWithinInterval(date, { start: checkIn, end: checkOut }) ||
        isSameDay(date, checkIn)
    );

    if (firstDayIndex === -1) return { start: 0, span: 0 };

    // Calculate how many days the booking spans in our current view
    let span = 0;
    for (let i = firstDayIndex; i < dates.length; i++) {
      if (
        isWithinInterval(dates[i], { start: checkIn, end: checkOut }) ||
        isSameDay(dates[i], checkIn) ||
        isSameDay(dates[i], checkOut)
      ) {
        span++;
      } else {
        break;
      }
    }

    return { start: firstDayIndex, span };
  };

  // Reset filters
  const resetFilters = () => {
    setSearchQuery("");
    setSelectedFloor("all_floors");
    setSelectedRoomType("all_types");
  };

  // Function to check if a date is a weekend (Saturday or Sunday)
  const isWeekend = (date: Date) => {
    const day = date.getDay();
    return day === 0 || day === 6; // 0 is Sunday, 6 is Saturday
  };

  return (
    <div style={{ width: "2000px", minWidth: "100%" }}>
      <Toaster />

      {/* Calendar header with navigation and filters */}
      <div className="flex justify-between items-center p-4 bg-white border-b sticky top-0 z-10">
        <div className="flex items-center gap-2">
          <Button variant="secondary" size="small" onClick={goToPreviousMonth}>
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <Heading level="h3" className="text-lg">
            {format(currentDate, "MMMM yyyy")}
          </Heading>

          <Button variant="secondary" size="small" onClick={goToNextMonth}>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative">
            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400">
              <Search size={16} />
            </div>
            <Input
              placeholder="Search rooms..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 h-9"
            />
          </div>

          <CustomSelect
            value={selectedFloor}
            onChange={setSelectedFloor}
            placeholder="Floor"
            className="w-32 h-9"
            options={[
              { value: "all_floors", label: "All Floors" },
              ...floors.map((floor) => ({
                value: floor,
                label: `Floor ${floor}`,
              })),
            ]}
          />

          <CustomSelect
            value={selectedRoomType}
            onChange={setSelectedRoomType}
            placeholder="Room Type"
            className="w-40 h-9"
            options={[
              { value: "all_types", label: "All Room Types" },
              ...roomTypes.map((type) => ({
                value: type,
                label: type,
              })),
            ]}
          />

          {(searchQuery ||
            selectedFloor !== "all_floors" ||
            selectedRoomType !== "all_types") && (
            <Button variant="secondary" size="small" onClick={resetFilters}>
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      {/* Calendar view with improved scrolling */}
      <div style={{ width: "100%" }}>
        {/* Days header */}
        <div className="grid grid-cols-[200px_repeat(14,1fr)] border-b">
          <div className="p-2 font-medium border-r bg-gray-50">Rooms</div>
          {dates.map((date, index) => (
            <div
              key={index}
              className={`p-2 text-center border-r ${
                isSameDay(date, new Date())
                  ? "bg-blue-50 font-bold"
                  : isWeekend(date)
                  ? "bg-gray-100"
                  : "bg-gray-50"
              }`}
            >
              <div className="text-xs font-medium">{format(date, "EEE")}</div>
              <div className="text-sm">{format(date, "MMM dd")}</div>
            </div>
          ))}
        </div>

        {/* Room groups */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Text>Loading...</Text>
          </div>
        ) : roomsByConfig.length === 0 ? (
          <div className="flex justify-center items-center h-64">
            <Text>No rooms found</Text>
          </div>
        ) : (
          roomsByConfig.map(({ config, rooms }) => (
            <div key={config.id}>
              {/* Room type header */}
              <div className="grid grid-cols-[200px_repeat(14,1fr)] border-b bg-gray-100">
                <div className="p-2 font-medium border-r">
                  {config.name} ({rooms.length})
                </div>
                {/* Room type availability summary */}
                {dates.map((date, dateIndex) => {
                  const availableCount = rooms.filter(
                    (room) => getRoomStatus(room.id, date) === "available"
                  ).length;

                  return (
                    <div key={dateIndex} className="p-2 text-center border-r">
                      <Badge variant={availableCount > 0 ? "green" : "red"}>
                        {availableCount} / {rooms.length}
                      </Badge>
                    </div>
                  );
                })}
              </div>

              {/* Rooms in this group */}
              {rooms.map((room) => (
                <div
                  key={room.id}
                  className="grid grid-cols-[200px_repeat(14,1fr)] border-b hover:bg-gray-50"
                >
                  {/* Room info */}
                  <div className="p-2 border-r flex flex-col">
                    <Text className="font-medium">{room.room_number}</Text>
                    <Text className="text-xs text-gray-500">
                      {room.name} {room.floor && `• Floor ${room.floor}`}
                    </Text>
                  </div>

                  {/* Room availability cells */}
                  {dates.map((date, dateIndex) => {
                    const status = getRoomStatus(room.id, date);
                    const booking = getBooking(room.id, date);

                    // If this cell is part of a booking that started earlier, don't render it
                    if (booking) {
                      const { start, span } = getBookingSpan(booking);

                      // Only render the booking on its first day in our view
                      if (dateIndex === start) {
                        return (
                          <div
                            key={dateIndex}
                            className={`border-r ${
                              statusColors[booking.status]
                            } p-1 ${isWeekend(date) ? "bg-opacity-90" : ""}`}
                            style={{
                              gridColumn: `${dateIndex + 2} / span ${span}`,
                            }}
                          >
                            <Tooltip
                              content={`${booking.status} • ${format(
                                parseISO(booking.check_in),
                                "MMM dd"
                              )} - ${format(
                                parseISO(booking.check_out),
                                "MMM dd"
                              )}`}
                            >
                              <div className="h-full flex items-center text-xs">
                                {booking.guest_name}
                              </div>
                            </Tooltip>
                          </div>
                        );
                      } else if (
                        dateIndex > start &&
                        dateIndex < start + span
                      ) {
                        // Skip cells that are covered by the booking span
                        return null;
                      }
                    }

                    return (
                      <div
                        key={dateIndex}
                        className={`border-r p-1 ${statusColors[status]} ${
                          isWeekend(date) ? "bg-opacity-90" : ""
                        }`}
                        style={
                          isWeekend(date)
                            ? { backgroundColor: "rgba(243, 244, 246, 0.2)" }
                            : {}
                        }
                      >
                        <div className="h-full flex items-center justify-center">
                          {status === "booked" && (
                            <span title="Booked" style={{ fontSize: "1.2rem" }}>
                              {statusIcons.booked}
                            </span>
                          )}
                          {status === "maintenance" && (
                            <span
                              title="Maintenance"
                              style={{ fontSize: "1.2rem" }}
                            >
                              {statusIcons.maintenance}
                            </span>
                          )}
                          {status === "reserved" && (
                            <span
                              title="Reserved"
                              style={{ fontSize: "1.2rem" }}
                            >
                              {statusIcons.reserved}
                            </span>
                          )}
                          {status === "cart_reserved" && (
                            <span
                              title="Cart Reserved"
                              style={{ fontSize: "1.2rem" }}
                            >
                              {statusIcons.cart_reserved}
                            </span>
                          )}
                          {status === "on_demand" && (
                            <span
                              title="On Request"
                              style={{ fontSize: "1.2rem" }}
                            >
                              {statusIcons.on_demand}
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default RoomInventoryCalendarView;
