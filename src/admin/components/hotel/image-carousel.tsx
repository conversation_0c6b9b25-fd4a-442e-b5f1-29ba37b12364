import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { Button, Text } from "@camped-ai/ui";
import { ChevronLeft, ChevronRight, X, ExternalLink } from "lucide-react";

interface ImageData {
  id?: string;
  url: string;
  isThumbnail?: boolean;
}

interface ImageCarouselProps {
  images: ImageData[];
  initialIndex?: number;
  onClose: () => void;
  hotelName?: string;
}

// High-performance image component for carousel
const CarouselImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  onLoad?: () => void;
  onError?: () => void;
  isActive: boolean;
}> = ({ src, alt, className, onLoad, onError, isActive }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setHasError(true);
    onError?.();
  }, [onError]);

  // Reset states when src changes
  useEffect(() => {
    setIsLoaded(false);
    setHasError(false);
  }, [src]);

  return (
    <div className={`relative ${className}`}>
      <img
        ref={imgRef}
        src={src}
        alt={alt}
        className={`w-full h-full object-contain transition-opacity duration-150 ${
          isLoaded && !hasError ? "opacity-100" : "opacity-0"
        }`}
        onLoad={handleLoad}
        onError={handleError}
        loading="eager"
        decoding="async"
      />

      {(!isLoaded || hasError) && isActive && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/10">
          {hasError ? (
            <div className="text-center">
              <div className="w-8 h-8 bg-muted-foreground/20 rounded mx-auto mb-2"></div>
              <span className="text-xs text-muted-foreground">
                Failed to load
              </span>
            </div>
          ) : (
            <div className="text-center">
              <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
              <span className="text-xs text-muted-foreground">Loading...</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const ImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  initialIndex = 0,
  onClose,
  hotelName,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const preloadCacheRef = useRef<Set<string>>(new Set());

  // Optimized preloading - runs once and uses ref to avoid re-renders
  const preloadImages = useCallback(() => {
    const preloadImage = (src: string) => {
      if (!preloadCacheRef.current.has(src)) {
        const img = new Image();
        img.src = src;
        img.onload = () => preloadCacheRef.current.add(src);
        img.onerror = () => preloadCacheRef.current.add(src); // Mark as attempted
      }
    };

    // Preload all images for instant navigation
    images.forEach((image) => preloadImage(image.url));
  }, [images]);

  // Preload images on mount
  useEffect(() => {
    preloadImages();
  }, [preloadImages]);

  // Fast, unthrottled navigation
  const goToPrevious = useCallback(() => {
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  }, [images.length]);

  const goToNext = useCallback(() => {
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  }, [images.length]);

  const openFullSize = useCallback(() => {
    if (images[currentIndex]?.url) {
      window.open(images[currentIndex].url, "_blank");
    }
  }, [images, currentIndex]);

  // Optimized keyboard navigation with memoized handler
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      switch (event.key) {
        case "ArrowLeft":
          event.preventDefault();
          goToPrevious();
          break;
        case "ArrowRight":
          event.preventDefault();
          goToNext();
          break;
        case "Escape":
          event.preventDefault();
          onClose();
          break;
        case "Enter":
        case " ":
          event.preventDefault();
          openFullSize();
          break;
      }
    },
    [goToPrevious, goToNext, onClose, openFullSize]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  // Memoize current image to prevent unnecessary re-renders
  const currentImage = useMemo(
    () => images[currentIndex],
    [images, currentIndex]
  );

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-background">
        <div className="flex items-center gap-3">
          <Text className="font-medium text-foreground">
            {hotelName ? `${hotelName} - Gallery` : "Image Gallery"}
          </Text>
          <Text className="text-sm text-muted-foreground">
            {currentIndex + 1} of {images.length}
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="small"
            onClick={openFullSize}
            className="text-muted-foreground hover:text-foreground"
          >
            <ExternalLink className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="small"
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Main Image Display */}
      <div className="flex-1 relative bg-black/5 dark:bg-black/20 min-h-[60vh]">
        <div className="absolute inset-0 flex items-center justify-center p-4">
          <div className="relative max-w-full max-h-full">
            <CarouselImage
              src={currentImage.url}
              alt={`Hotel image ${currentIndex + 1}`}
              className="max-w-full max-h-[60vh] rounded-lg shadow-lg"
              isActive={true}
            />

            {/* Featured Image Badge */}
            {currentImage.isThumbnail && (
              <div className="absolute top-4 right-4 bg-green-500 dark:bg-green-600 text-white text-sm px-3 py-1 rounded-full shadow-lg">
                Featured Image
              </div>
            )}
          </div>
        </div>

        {/* Navigation Buttons */}
        {images.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="small"
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-0 w-10 h-10 p-0"
            >
              <ChevronLeft className="w-5 h-5" />
            </Button>
            <Button
              variant="ghost"
              size="small"
              onClick={goToNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-0 w-10 h-10 p-0"
            >
              <ChevronRight className="w-5 h-5" />
            </Button>
          </>
        )}
      </div>

      {/* Optimized Thumbnail Strip */}
      {images.length > 1 && (
        <div className="p-4 border-t border-border bg-background">
          <div className="flex gap-2 overflow-x-auto pb-2">
            {images.map((image, index) => {
              const isActive = index === currentIndex;
              return (
                <button
                  key={image.id || `thumb-${index}`}
                  onClick={() => setCurrentIndex(index)}
                  className={`relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-150 hover:scale-105 ${
                    isActive
                      ? "border-primary shadow-md scale-105"
                      : "border-border hover:border-muted-foreground"
                  }`}
                  title={`View image ${index + 1}${
                    image.isThumbnail ? " (Featured)" : ""
                  }`}
                >
                  <img
                    src={image.url}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                    decoding="async"
                  />
                  {image.isThumbnail && (
                    <div className="absolute top-0 right-0 w-2 h-2 bg-green-500 rounded-full border border-white"></div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="px-4 py-2 bg-muted/30 border-t border-border">
        <Text className="text-xs text-muted-foreground text-center">
          Use arrow keys to navigate • Press Enter or Space to view full size •
          Press Esc to close
        </Text>
      </div>
    </div>
  );
};

export default ImageCarousel;
