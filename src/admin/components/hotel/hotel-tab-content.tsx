import React from "react";
import { clx } from "@camped-ai/ui";
import { HotelTabId } from "./hotel-tabs";

interface HotelTabContentProps {
  activeTab: HotelTabId;
  children: React.ReactNode;
  tabId: HotelTabId;
  className?: string;
}

const HotelTabContent: React.FC<HotelTabContentProps> = ({
  activeTab,
  children,
  tabId,
  className,
}) => {
  const isActive = activeTab === tabId;

  if (!isActive) {
    return null;
  }

  return (
    <div
      role="tabpanel"
      id={`${tabId}-panel`}
      aria-labelledby={`${tabId}-tab`}
      className={clx(
        "w-full animate-in fade-in-0 duration-200",
        className
      )}
    >
      {children}
    </div>
  );
};

export default HotelTabContent;
