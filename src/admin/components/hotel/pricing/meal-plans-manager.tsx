import React, { useState } from "react";
import {
  Button,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Drawer,
  Prompt,
  Input,
  Label,
  Select,
} from "@camped-ai/ui";
import {
  useAdminHotelMealPlans,
  useAdminCreateHotelMealPlan,
  useAdminUpdateHotelMealPlan,
  useAdminDeleteHotelMealPlan,
  MealPlan,
} from "../../../hooks/hotel/use-admin-hotel-meal-plans";
import { useAdminHotelOccupancyConfigs } from "../../../hooks/hotel/use-admin-hotel-occupancy-configs";
import { MultiSelect } from "../../common/MultiSelect";
import { PlusCircle, Edit, Trash } from "lucide-react";

enum MealPlanTypeEnum {
  NONE = "none",
  BED_BREAKFAST = "bb",
  HALF_BOARD = "hb",
  FULL_BOARD = "fb",
}

type MealPlansManagerProps = {
  hotelId: string;
  canEdit?: boolean;
  canCreate?: boolean;
  canDelete?: boolean;
};

const MealPlansManager: React.FC<MealPlansManagerProps> = ({
  hotelId,
  canEdit = false,
  canCreate = false,
  canDelete = false,
}) => {
  const {
    mealPlans = [],
    isLoading,
    refetch,
  } = useAdminHotelMealPlans(hotelId);
  const { occupancyConfigs } = useAdminHotelOccupancyConfigs(hotelId);
  const { createMealPlan } = useAdminCreateHotelMealPlan();
  const { updateMealPlan } = useAdminUpdateHotelMealPlan();
  const { deleteMealPlan } = useAdminDeleteHotelMealPlan();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDefaultChangeDialogOpen, setIsDefaultChangeDialogOpen] =
    useState(false);
  const [currentMealPlan, setCurrentMealPlan] = useState<MealPlan | null>(null);
  const [previousDefaultMealPlan, setPreviousDefaultMealPlan] =
    useState<MealPlan | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    type: MealPlanTypeEnum.NONE as string, // Allow any string for custom types
    is_default: false,
    applicable_occupancy_types: [] as string[], // Array of occupancy type IDs
  });
  const [showCustomTypeInput, setShowCustomTypeInput] = useState(false);
  const [customTypeValue, setCustomTypeValue] = useState("");

  // Helper function to generate slug from name (only alphanumeric and hyphens)
  const generateSlugFromName = (name: string) => {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s]/g, "") // Remove ALL special characters, keep only letters, numbers, and spaces
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  };

  const handleOpenDialog = (mealPlan?: any) => {
    if (mealPlan) {
      setCurrentMealPlan(mealPlan);
      // Check if the type is a custom type (not in the enum)
      const isCustomType = !Object.values(MealPlanTypeEnum).includes(
        mealPlan.type as MealPlanTypeEnum
      );

      if (isCustomType) {
        setShowCustomTypeInput(true);
        setCustomTypeValue(mealPlan.type);
        setFormData({
          name: mealPlan.name,
          type: "custom",
          is_default: mealPlan.is_default,
          applicable_occupancy_types:
            mealPlan.metadata?.applicable_occupancy_types || [],
        });
      } else {
        setShowCustomTypeInput(false);
        setCustomTypeValue("");
        setFormData({
          name: mealPlan.name,
          type: mealPlan.type,
          is_default: mealPlan.is_default,
          applicable_occupancy_types:
            mealPlan.metadata?.applicable_occupancy_types || [],
        });
      }
    } else {
      setCurrentMealPlan(null);
      setShowCustomTypeInput(false);
      setCustomTypeValue("");
      setFormData({
        name: "",
        type: MealPlanTypeEnum.NONE,
        is_default: false,
        applicable_occupancy_types: [],
      });
    }
    setIsDialogOpen(true);
  };

  const handleOpenDeleteDialog = (mealPlan: any) => {
    setCurrentMealPlan(mealPlan);
    setIsDeleteDialogOpen(true);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    const newFormData = {
      ...formData,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    };

    setFormData(newFormData);

    // If name field changed and custom type is selected, update custom type value
    if (name === "name" && formData.type === "custom" && showCustomTypeInput) {
      const slug = generateSlugFromName(value);
      setCustomTypeValue(slug);
    }
  };

  const handleDefaultChange = (checked: boolean) => {
    if (checked) {
      // Find the current default meal plan
      const currentDefault = mealPlans.find((mp) => mp.is_default);
      if (currentDefault && currentDefault.id !== currentMealPlan?.id) {
        setPreviousDefaultMealPlan(currentDefault);
        setIsDefaultChangeDialogOpen(true);
        return;
      }
    }

    setFormData({
      ...formData,
      is_default: checked,
    });
  };

  const handleConfirmDefaultChange = () => {
    setFormData({
      ...formData,
      is_default: true,
    });
    setIsDefaultChangeDialogOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Determine the actual type to send to the API
      const actualType =
        formData.type === "custom" ? customTypeValue : formData.type;

      // Validate custom type
      if (formData.type === "custom" && !customTypeValue.trim()) {
        toast.error("Error", {
          description: "Please enter a custom meal plan type",
        });
        return;
      }

      const requestData = {
        name: formData.name,
        type: actualType,
        is_default: formData.is_default,
        applicable_occupancy_types: formData.applicable_occupancy_types,
      };

      console.log("Submitting meal plan data:", requestData);
      console.log("Current meal plan:", currentMealPlan);

      if (currentMealPlan) {
        // Update existing meal plan
        const result = await updateMealPlan(
          hotelId,
          currentMealPlan.id,
          requestData
        );
        console.log("Update result:", result);

        toast.success("Success", {
          description: "Meal plan updated successfully",
        });
      } else {
        // Create new meal plan
        const result = await createMealPlan(hotelId, requestData);
        console.log("Create result:", result);

        toast.success("Success", {
          description: "Meal plan created successfully",
        });
      }

      setIsDialogOpen(false);
      refetch(); // Refresh the data
    } catch (error) {
      console.error("Error saving meal plan:", error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        response: (error as any)?.response?.data || (error as any)?.response,
      });

      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to save meal plan",
      });
    }
  };

  const handleDelete = async () => {
    if (!currentMealPlan) return;

    try {
      console.log("Deleting meal plan:", currentMealPlan.id);
      const result = await deleteMealPlan(hotelId, currentMealPlan.id);
      console.log("Delete result:", result);

      toast.success("Success", {
        description: "Meal plan deleted successfully",
      });

      setIsDeleteDialogOpen(false);
      refetch(); // Refresh the data
    } catch (error) {
      console.error("Error deleting meal plan:", error);
      toast.error("Error", {
        description: "Failed to delete meal plan",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded-md mb-4 w-1/3"></div>
          <div className="h-64 bg-muted/50 rounded-md"></div>
        </div>
      </div>
    );
  }

  return (
    <Container>
      <Toaster />
      <div className="flex flex-row justify-between items-center mb-6">
        <div className="">
          <Heading level="h2" className="mb-1">
            Meal Plans
          </Heading>
          <Text className="text-muted-foreground text-sm">
            Manage meal plans for your hotel
          </Text>
        </div>

        {canCreate && (
          <Button
            variant="primary"
            onClick={() => handleOpenDialog()}
            className="flex items-center gap-2"
          >
            <PlusCircle className="w-4 h-4" />
            Add Meal Plan
          </Button>
        )}
      </div>
      <div className="mb-6">
        <Text className="text-muted-foreground"></Text>
      </div>
      <div className="overflow-x-auto border border-border rounded-lg">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Default
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-background divide-y divide-border">
            {mealPlans.map((mealPlan) => (
              <tr key={mealPlan.id} className="hover:bg-muted/50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-foreground">
                  {mealPlan.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                  {mealPlan.type}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                  {mealPlan.is_default ? "Yes" : "No"}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end gap-2">
                    {canEdit && (
                      <Button
                        variant="secondary"
                        onClick={() => handleOpenDialog(mealPlan)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    )}
                    {canDelete && (
                      <Button
                        variant="danger"
                        onClick={() => handleOpenDeleteDialog(mealPlan)}
                      >
                        <Trash className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Create/Edit Dialog */}
      <Drawer open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <Drawer.Content className="flex flex-col">
          <Drawer.Header className="flex-shrink-0">
            <Heading level="h2" className="text-l font-semibold">
              {currentMealPlan ? "Edit Meal Plan" : "Add Meal Plan"}
            </Heading>
          </Drawer.Header>
          <form onSubmit={handleSubmit} className="flex flex-col h-full">
            <Drawer.Body className="flex-1 overflow-y-auto">
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="type">Type</Label>
                    <div className="grid grid-cols-1 gap-2">
                      <Select
                        value={formData.type}
                        onValueChange={(value) => {
                          if (value === "custom") {
                            setShowCustomTypeInput(true);
                            // Auto-populate custom type with slug from name
                            const slug = generateSlugFromName(formData.name);
                            setCustomTypeValue(slug);
                          } else {
                            setShowCustomTypeInput(false);
                            setCustomTypeValue("");
                          }
                          setFormData({
                            ...formData,
                            type: value,
                          });
                        }}
                        disabled={!!currentMealPlan}
                      >
                        <Select.Trigger className="w-full">
                          <Select.Value placeholder="Select meal plan type" />
                        </Select.Trigger>
                        <Select.Content>
                          <Select.Item value={MealPlanTypeEnum.NONE}>
                            None (Room Only)
                          </Select.Item>
                          <Select.Item value={MealPlanTypeEnum.BED_BREAKFAST}>
                            Bed & Breakfast
                          </Select.Item>
                          <Select.Item value={MealPlanTypeEnum.HALF_BOARD}>
                            Half Board
                          </Select.Item>
                          <Select.Item value={MealPlanTypeEnum.FULL_BOARD}>
                            Full Board
                          </Select.Item>
                          <Select.Item value="custom">
                            Custom Type...
                          </Select.Item>
                        </Select.Content>
                      </Select>

                      {showCustomTypeInput && (
                        <div className="space-y-2">
                          <Input
                            id="type-custom"
                            name="type-custom"
                            placeholder="Auto-generated from name (editable)"
                            value={customTypeValue}
                            onChange={(e) => {
                              setCustomTypeValue(e.target.value);
                            }}
                            disabled={!!currentMealPlan}
                            required
                          />
                          <Text className="text-xs text-muted-foreground">
                            Type is auto-generated from the meal plan name as a
                            slug. You can edit it manually if needed.
                          </Text>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Applicable Occupancy Types */}
                  <div>
                    <Label htmlFor="applicable_occupancy_types">
                      Applicable Occupancy Types
                    </Label>
                    <MultiSelect
                      options={occupancyConfigs.map((occupancy) => ({
                        value: occupancy.id,
                        label: `${occupancy.name} (${occupancy.type})`,
                      }))}
                      selectedValues={formData.applicable_occupancy_types}
                      onChange={(selectedValues) => {
                        setFormData((prev) => ({
                          ...prev,
                          applicable_occupancy_types: selectedValues,
                        }));
                      }}
                      placeholder="Select occupancy types..."
                      showSelectAll={true}
                      showSelectedTags={true}
                      // maxHeight="max-h-32"
                    />
                    <Label className="text-xs text-muted-foreground">
                      Select which occupancy types can use this meal plan. Leave
                      empty for all types.
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_default"
                      name="is_default"
                      checked={formData.is_default}
                      onChange={(e) => handleDefaultChange(e.target.checked)}
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 border-input rounded"
                    />
                    <Label htmlFor="is_default">Default</Label>
                  </div>
                </div>
              </div>
            </Drawer.Body>

            {/* Fixed Footer with CTAs */}
            <div className="flex-shrink-0 border-t border-border bg-card p-4">
              <div className="flex justify-end gap-4">
                <Button
                  variant="secondary"
                  type="button"
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1 sm:flex-none"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  type="submit"
                  className="flex-1 sm:flex-none"
                >
                  {currentMealPlan ? "Update" : "Create"}
                </Button>
              </div>
            </div>
          </form>
        </Drawer.Content>
      </Drawer>

      {/* Delete Confirmation Dialog */}
      <Prompt open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Meal Plan</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete the meal plan "
              {currentMealPlan?.name}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleDelete}>Delete</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      {/* Default Change Confirmation Dialog */}
      <Prompt
        open={isDefaultChangeDialogOpen}
        onOpenChange={setIsDefaultChangeDialogOpen}
      >
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Change Default Meal Plan</Prompt.Title>
            <Prompt.Description>
              The meal plan "{previousDefaultMealPlan?.name}" is currently set
              as the default. Setting "{formData.name || "this meal plan"}" as
              default will remove the default status from "
              {previousDefaultMealPlan?.name}".
              <br />
              <br />
              This will be the new default option shown to customers when making
              bookings.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setIsDefaultChangeDialogOpen(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleConfirmDefaultChange}>
              Set as Default
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </Container>
  );
};

export default MealPlansManager;
