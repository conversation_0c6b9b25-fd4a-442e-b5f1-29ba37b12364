import React from "react";
import { Container, Heading, Text, Badge } from "@camped-ai/ui";
import { useAdminHotelMealPlans } from "../../../hooks/hotel/use-admin-hotel-meal-plans";
import { useAdminHotelOccupancyConfigs } from "../../../hooks/hotel/use-admin-hotel-occupancy-configs";

type MealPlanAvailabilityTestProps = {
  hotelId: string;
};

/**
 * Test component to verify meal plan availability functionality
 * This component shows which meal plans are available for each occupancy type
 */
const MealPlanAvailabilityTest: React.FC<MealPlanAvailabilityTestProps> = ({ hotelId }) => {
  const { mealPlans, isLoading: mealPlansLoading } = useAdminHotelMealPlans(hotelId);
  const { occupancyConfigs, isLoading: occupancyLoading } = useAdminHotelOccupancyConfigs(hotelId);

  const isLoading = mealPlansLoading || occupancyLoading;

  // Helper function to get available meal plans for an occupancy type (same logic as pricing table)
  const getAvailableMealPlans = (occupancyTypeId: string) => {
    const occupancy = occupancyConfigs.find(oc => oc.id === occupancyTypeId);
    if (!occupancy) return [];

    // Legacy logic for special accommodations (backward compatibility)
    const isExtraBed = (occupancy as any).type === "EXTRA_BED" || 
                      occupancy.name?.toLowerCase().includes("extra bed");
    const isCot = (occupancy as any).type === "COT" || 
                 occupancy.name?.toLowerCase().includes("cot");
    
    if (isExtraBed || isCot) {
      return [{ id: null, name: "N/A" }];
    }

    // Filter meal plans based on their applicable_occupancy_types metadata
    const availableMealPlans = mealPlans.filter(mealPlan => {
      const applicableTypes = mealPlan.metadata?.applicable_occupancy_types;
      
      // If no applicable types specified, meal plan is available for all occupancy types
      if (!applicableTypes || applicableTypes.length === 0) {
        return true;
      }
      
      // Check if this occupancy type is in the applicable types
      return applicableTypes.includes(occupancyTypeId);
    });

    // If no meal plans are available, show N/A
    if (availableMealPlans.length === 0) {
      return [{ id: null, name: "N/A" }];
    }

    return availableMealPlans;
  };

  if (isLoading) {
    return (
      <Container>
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded-md mb-4 w-1/3"></div>
          <div className="h-4 bg-muted rounded-md mb-6 w-1/2"></div>
          <div className="h-64 bg-muted rounded-md"></div>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <div className="mb-6">
        <Heading level="h2" className="mb-1">
          Meal Plan Availability Test
        </Heading>
        <Text className="text-sm text-muted-foreground">
          This shows which meal plans are available for each occupancy type based on the new configuration system.
        </Text>
      </div>

      <div className="space-y-6">
        {occupancyConfigs.map((occupancy) => {
          const availableMealPlans = getAvailableMealPlans(occupancy.id);
          
          return (
            <div key={occupancy.id} className="border border-border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <Heading level="h3" className="text-lg">
                  {occupancy.name}
                </Heading>
                <Badge variant="outline" className="text-xs">
                  {(occupancy as any).type || "CUSTOM"}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <Text className="text-sm font-medium text-muted-foreground">
                  Available Meal Plans:
                </Text>
                <div className="flex flex-wrap gap-2">
                  {availableMealPlans.map((mealPlan, index) => (
                    <Badge 
                      key={mealPlan.id || index} 
                      variant={mealPlan.id === null ? "secondary" : "default"}
                      className="text-sm"
                    >
                      {mealPlan.name}
                      {mealPlan.id === null && " (No meal plan)"}
                    </Badge>
                  ))}
                </div>
                
                {/* Show configuration details */}
                <div className="mt-2 text-xs text-muted-foreground">
                  {availableMealPlans.length === 1 && availableMealPlans[0].id === null ? (
                    <span>Special accommodation - no meal plans available</span>
                  ) : availableMealPlans.length === mealPlans.length ? (
                    <span>All meal plans available (no restrictions configured)</span>
                  ) : (
                    <span>Restricted meal plans based on configuration</span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary */}
      <div className="mt-8 p-4 bg-muted/50 rounded-lg">
        <Heading level="h4" className="mb-2">Summary</Heading>
        <div className="space-y-1 text-sm">
          <div>Total Occupancy Types: {occupancyConfigs.length}</div>
          <div>Total Meal Plans: {mealPlans.length}</div>
          <div>
            Meal Plans with Restrictions: {
              mealPlans.filter(mp => 
                mp.metadata?.applicable_occupancy_types && 
                mp.metadata.applicable_occupancy_types.length > 0
              ).length
            }
          </div>
        </div>
      </div>
    </Container>
  );
};

export default MealPlanAvailabilityTest;
