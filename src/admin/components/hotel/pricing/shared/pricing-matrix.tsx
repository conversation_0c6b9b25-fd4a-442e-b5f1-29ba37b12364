import React from "react";

// Define types
type RateType = "base" | "extra_adult" | "extra_child" | "infant";
type MealPlanType = "bb" | "hb" | "fb" | "none";

type AgeRange = {
  min: number;
  max: number;
};

export type Rate = {
  id?: string;
  occupancy_type: RateType;
  meal_plan?: MealPlanType;
  amount: number;
  min_occupancy: number;
  max_occupancy: number;
  age_range?: AgeRange;
};

type OccupancyType = {
  id: string;
  type: string;
  config: {
    occupancy_type: RateType;
    min_occupancy: number;
    max_occupancy: number;
    age_range?: AgeRange;
  };
};

type MealPlan = {
  id: MealPlanType;
  label: string;
};

interface PricingMatrixProps {
  rates: Rate[];
  onRateChange: (updatedRates: Rate[]) => void;
  currencyCode: string;
  childAgeRange: AgeRange;
  infantAgeRange: AgeRange;
}

const PricingMatrix: React.FC<PricingMatrixProps> = ({
  rates,
  onRateChange,
  currencyCode,
  childAgeRange,
  infantAgeRange,
}) => {
  // Define meal plans
  const mealPlans: MealPlan[] = [
    { id: "none", label: "Room Only" },
    { id: "bb", label: "Bed & Breakfast" },
    { id: "hb", label: "Half Board" },
    { id: "fb", label: "Full Board" },
  ];

  // Define occupancy types
  const occupancyTypes: OccupancyType[] = [
    {
      id: "1person",
      type: "1 Person",
      config: {
        occupancy_type: "base" as RateType,
        min_occupancy: 1,
        max_occupancy: 1,
      },
    },
    {
      id: "2persons",
      type: "2 Persons",
      config: {
        occupancy_type: "base" as RateType,
        min_occupancy: 2,
        max_occupancy: 2,
      },
    },
    {
      id: "extra_adult",
      type: "Extra Adult",
      config: {
        occupancy_type: "extra_adult" as RateType,
        min_occupancy: 1,
        max_occupancy: 1,
      },
    },
    {
      id: "child",
      type: `Child (${childAgeRange.min}-${childAgeRange.max} years)`,
      config: {
        occupancy_type: "extra_child" as RateType,
        min_occupancy: 1,
        max_occupancy: 1,
        age_range: childAgeRange,
      },
    },
    {
      id: "infant",
      type: `Infant (0-${infantAgeRange.max} years)`,
      config: {
        occupancy_type: "infant" as RateType,
        min_occupancy: 1,
        max_occupancy: 1,
        age_range: infantAgeRange,
      },
    },
  ];

  // Currency symbols mapping
  const currencySymbols: Record<string, string> = {
    USD: "$",
    EUR: "€",
    GBP: "£",
    JPY: "¥",
    // Add more currencies as needed
  };

  // Get rate amount for a specific occupancy and meal plan
  const getRateAmount = (occupancyId: string, mealPlanId: string): number => {
    const occupancyConfig = occupancyTypes.find(
      (o) => o.id === occupancyId
    )?.config;
    if (!occupancyConfig) return 0;

    const rate = rates.find(
      (r) =>
        r.occupancy_type === occupancyConfig.occupancy_type &&
        r.meal_plan === mealPlanId &&
        r.min_occupancy === occupancyConfig.min_occupancy &&
        r.max_occupancy === occupancyConfig.max_occupancy
    );

    return rate?.amount || 0;
  };

  // Handle price change
  const handlePriceChange = (
    occupancyId: string,
    mealPlanId: string,
    value: number
  ) => {
    const occupancyConfig = occupancyTypes.find(
      (o) => o.id === occupancyId
    )?.config;
    if (!occupancyConfig) return;

    // Check if rate already exists
    const existingRateIndex = rates.findIndex(
      (r) =>
        r.occupancy_type === occupancyConfig.occupancy_type &&
        r.meal_plan === mealPlanId &&
        r.min_occupancy === occupancyConfig.min_occupancy &&
        r.max_occupancy === occupancyConfig.max_occupancy
    );

    const newRates = [...rates];

    if (existingRateIndex >= 0) {
      // Update existing rate
      newRates[existingRateIndex] = {
        ...newRates[existingRateIndex],
        amount: value,
      };
    } else {
      // Create new rate
      newRates.push({
        occupancy_type: occupancyConfig.occupancy_type,
        meal_plan: mealPlanId as MealPlanType,
        amount: value,
        min_occupancy: occupancyConfig.min_occupancy,
        max_occupancy: occupancyConfig.max_occupancy,
        age_range: occupancyConfig.age_range,
      });
    }

    onRateChange(newRates);
  };

  return (
    <div className="mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Room Pricing</h3>

      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-200">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-3 border-b border-r border-gray-200 text-left text-sm font-medium text-gray-500">
                Occupancy
              </th>
              {mealPlans.map((plan) => (
                <th
                  key={plan.id}
                  className="p-3 border-b border-r border-gray-200 text-center text-sm font-medium text-gray-500"
                >
                  {plan.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {occupancyTypes.map((occupancy) => (
              <tr key={occupancy.id}>
                <td className="p-3 border-b border-r border-gray-200 font-medium">
                  {occupancy.type}
                </td>
                {mealPlans.map((plan) => (
                  <td
                    key={`${occupancy.id}-${plan.id}`}
                    className="p-3 border-b border-r border-gray-200"
                  >
                    <div className="flex items-center justify-center">
                      <div className="flex items-center">
                        <span className="mr-1">
                          {currencySymbols[currencyCode] || currencyCode}
                        </span>
                        <input
                          type="number"
                          className="w-24 p-2 border border-gray-300 rounded-md"
                          value={getRateAmount(occupancy.id, plan.id)}
                          onChange={(e) =>
                            handlePriceChange(
                              occupancy.id,
                              plan.id,
                              Number(e.target.value)
                            )
                          }
                          placeholder="0"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PricingMatrix;
