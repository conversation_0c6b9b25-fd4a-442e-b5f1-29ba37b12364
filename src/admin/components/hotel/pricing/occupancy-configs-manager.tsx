import React, { useState, useEffect } from "react";
import {
  Button,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Drawer,
  Prompt,
  Input,
  Label,
  Select,
} from "@camped-ai/ui";
import {
  useAdminHotelOccupancyConfigs,
  useAdminCreateHotelOccupancyConfig,
  useAdminUpdateHotelOccupancyConfig,
  useAdminDeleteHotelOccupancyConfig,
} from "../../../hooks/hotel/use-admin-hotel-occupancy-configs";
import { PlusCircle, Edit, Trash } from "lucide-react";

type OccupancyConfig = {
  id: string;
  name: string;
  type: string;
  min_age: number | null;
  max_age: number | null;
  min_occupancy: number;
  max_occupancy: number;
  is_default: boolean;
};

type OccupancyConfigsManagerProps = {
  hotelId: string;
  canEdit?: boolean;
  canCreate?: boolean;
  canDelete?: boolean;
};

const OccupancyConfigsManager: React.FC<OccupancyConfigsManagerProps> = ({
  hotelId,
  canEdit = false,
  canCreate = false,
  canDelete = false,
}) => {
  const { occupancyConfigs, isLoading, refetch } =
    useAdminHotelOccupancyConfigs(hotelId);
  const { createOccupancyConfig } = useAdminCreateHotelOccupancyConfig();
  const { updateOccupancyConfig } = useAdminUpdateHotelOccupancyConfig();
  const { deleteOccupancyConfig } = useAdminDeleteHotelOccupancyConfig();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentOccupancyConfig, setCurrentOccupancyConfig] =
    useState<OccupancyConfig | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    type: "EXTRA_ADULT",
    min_age: 0,
    max_age: 99,
    min_occupancy: 1,
    max_occupancy: 1,
    is_default: false,
  });

  const handleOpenDialog = (occupancyConfig?: OccupancyConfig) => {
    if (occupancyConfig) {
      setCurrentOccupancyConfig(occupancyConfig);
      setFormData({
        name: occupancyConfig.name,
        type: occupancyConfig.type,
        min_age: occupancyConfig.min_age || 0,
        max_age: occupancyConfig.max_age || 99,
        min_occupancy: occupancyConfig.min_occupancy,
        max_occupancy: occupancyConfig.max_occupancy,
        is_default: occupancyConfig.is_default,
      });
    } else {
      setCurrentOccupancyConfig(null);
      setFormData({
        name: "",
        type: "EXTRA_ADULT",
        min_age: 0,
        max_age: 99,
        min_occupancy: 1,
        max_occupancy: 1,
        is_default: false,
      });
    }
    setIsDialogOpen(true);
  };

  const handleOpenDeleteDialog = (occupancyConfig: OccupancyConfig) => {
    setCurrentOccupancyConfig(occupancyConfig);
    setIsDeleteDialogOpen(true);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]:
        type === "checkbox"
          ? (e.target as HTMLInputElement).checked
          : type === "number"
          ? Number(value)
          : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (currentOccupancyConfig) {
        // Update existing occupancy config
        await updateOccupancyConfig(
          hotelId,
          currentOccupancyConfig.id,
          formData
        );
        toast.success("Success", {
          description: "Occupancy configuration updated successfully",
        });
      } else {
        // Create new occupancy config
        await createOccupancyConfig(hotelId, formData);
        toast.success("Success", {
          description: "Occupancy configuration created successfully",
        });
      }

      setIsDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Error saving occupancy configuration:", error);
      toast.error("Error", {
        description: "Failed to save occupancy configuration",
      });
    }
  };

  const handleDelete = async () => {
    if (!currentOccupancyConfig) return;

    try {
      console.log("Deleting occupancy config:", currentOccupancyConfig.id);
      const result = await deleteOccupancyConfig(
        hotelId,
        currentOccupancyConfig.id
      );
      console.log("Delete result:", result);

      toast.success("Success", {
        description: "Occupancy configuration deleted successfully",
      });

      setIsDeleteDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Error deleting occupancy configuration:", error);
      toast.error("Error", {
        description: "Failed to delete occupancy configuration",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded-md mb-4 w-1/3"></div>
          <div className="h-64 bg-muted/50 rounded-md"></div>
        </div>
      </div>
    );
  }

  return (
    <Container>
      <Toaster />
      <div className="flex flex-row justify-between items-center mb-6">
        <div className="">
          <Heading level="h2" className="mb-1">
            Occupancy Configurations
          </Heading>
          <Text className="text-muted-foreground text-sm">
            Manage occupancy configurations for your hotel
          </Text>
        </div>

        {canCreate && (
          <Button
            variant="primary"
            onClick={() => handleOpenDialog()}
            className="flex items-center gap-2"
          >
            <PlusCircle className="w-4 h-4" />
            Add Occupancy Configuration
          </Button>
        )}
      </div>
      <div className="overflow-x-auto border border-border rounded-lg">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Age Range
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Occupancy
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Default
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-background divide-y divide-border">
            {occupancyConfigs.map((occupancyConfig) => (
              <tr key={occupancyConfig.id} className="hover:bg-muted/50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-foreground">
                  {occupancyConfig.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                  {occupancyConfig.type}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                  {occupancyConfig.min_age !== null &&
                  occupancyConfig.max_age !== null
                    ? `${occupancyConfig.min_age} - ${occupancyConfig.max_age} years`
                    : "N/A"}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                  {occupancyConfig.min_occupancy} -{" "}
                  {occupancyConfig.max_occupancy} persons
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                  {occupancyConfig.is_default ? "Yes" : "No"}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end gap-2">
                    {canEdit && (
                      <Button
                        variant="secondary"
                        onClick={() => handleOpenDialog(occupancyConfig)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    )}
                    {canDelete && (
                      <Button
                        variant="danger"
                        onClick={() => handleOpenDeleteDialog(occupancyConfig)}
                      >
                        <Trash className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Create/Edit Dialog */}
      <Drawer open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <Drawer.Content className="flex flex-col">
          <Drawer.Header className="flex-shrink-0">
            <Heading level="h2" className="text-l font-semibold">
              {currentOccupancyConfig
                ? "Edit Occupancy Configuration"
                : "Add Occupancy Configuration"}
            </Heading>
          </Drawer.Header>
          <form onSubmit={handleSubmit} className="flex flex-col h-full">
            <Drawer.Body className="flex-1 overflow-y-auto">
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="type">Type</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) => {
                        setFormData({
                          ...formData,
                          type: value,
                        });
                      }}
                      disabled={!!currentOccupancyConfig}
                    >
                      <Select.Trigger className="w-full">
                        <Select.Value placeholder="Select type" />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="BASE_1">
                          1 Person (Base)
                        </Select.Item>
                        <Select.Item value="BASE_2">
                          2 Persons (Base)
                        </Select.Item>
                        <Select.Item value="EXTRA_ADULT">
                          Extra Adult
                        </Select.Item>
                        <Select.Item value="EXTRA_ADULT_BEYOND_CAPACITY">
                          Extra Adult Beyond Capacity
                        </Select.Item>
                        <Select.Item value="CHILD">Child</Select.Item>
                        <Select.Item value="INFANT">Infant</Select.Item>
                        <Select.Item value="EXTRA_BED">Extra Bed</Select.Item>
                        <Select.Item value="COT">Cot</Select.Item>
                        <Select.Item value="CUSTOM">Custom</Select.Item>
                      </Select.Content>
                    </Select>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="min_age">Min Age</Label>
                      <Input
                        id="min_age"
                        name="min_age"
                        type="number"
                        min="0"
                        value={formData.min_age}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div>
                      <Label htmlFor="max_age">Max Age</Label>
                      <Input
                        id="max_age"
                        name="max_age"
                        type="number"
                        min="0"
                        value={formData.max_age}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="min_occupancy">Min Occupancy</Label>
                      <Input
                        id="min_occupancy"
                        name="min_occupancy"
                        type="number"
                        min="1"
                        value={formData.min_occupancy}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div>
                      <Label htmlFor="max_occupancy">Max Occupancy</Label>
                      <Input
                        id="max_occupancy"
                        name="max_occupancy"
                        type="number"
                        min="1"
                        value={formData.max_occupancy}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_default"
                      name="is_default"
                      checked={formData.is_default}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 border-input rounded"
                    />
                    <Label htmlFor="is_default">Default</Label>
                  </div>
                </div>
              </div>
            </Drawer.Body>

            {/* Fixed Footer with CTAs */}
            <div className="flex-shrink-0 border-t border-border bg-card p-4">
              <div className="flex justify-end gap-4">
                <Button
                  variant="secondary"
                  type="button"
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1 sm:flex-none"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  type="submit"
                  className="flex-1 sm:flex-none"
                >
                  {currentOccupancyConfig ? "Update" : "Create"}
                </Button>
              </div>
            </div>
          </form>
        </Drawer.Content>
      </Drawer>

      {/* Delete Confirmation Dialog */}
      <Prompt open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Occupancy Configuration</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete the occupancy configuration "
              {currentOccupancyConfig?.name}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleDelete}>Delete</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </Container>
  );
};

export default OccupancyConfigsManager;
