import React, { useState, useEffect } from "react";
import { But<PERSON>, toast, Badge } from "@camped-ai/ui";
import CotOccupancyConfig from "./cot-occupancy-config";
import CotPricingConfig from "./cot-pricing-config";

interface CotSetupWizardProps {
  hotelId: string;
  onComplete: (success: boolean) => void;
}

interface SetupStep {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in-progress" | "completed" | "error";
  component?: React.ReactNode;
}

const CotSetupWizard: React.FC<CotSetupWizardProps> = ({
  hotelId,
  onComplete,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [cotConfigId, setCotConfigId] = useState<string | null>(null);
  const [roomConfigs, setRoomConfigs] = useState<any[]>([]);
  const [selectedRoomConfigs, setSelectedRoomConfigs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [steps, setSteps] = useState<SetupStep[]>([
    {
      id: "occupancy",
      title: "Create Cot Configuration",
      description: "Set up the basic cot occupancy configuration",
      status: "in-progress",
    },
    {
      id: "rooms",
      title: "Select Room Types",
      description: "Choose which room types will support cots",
      status: "pending",
    },
    {
      id: "pricing",
      title: "Configure Pricing",
      description: "Set up pricing for cots in each room type",
      status: "pending",
    },
    {
      id: "complete",
      title: "Setup Complete",
      description: "Review and finalize cot configuration",
      status: "pending",
    },
  ]);

  useEffect(() => {
    fetchRoomConfigs();
  }, [hotelId]);

  const fetchRoomConfigs = async () => {
    try {
      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}/room-configs`);
      if (response.ok) {
        const data = await response.json();
        setRoomConfigs(data.room_configs || []);
      }
    } catch (error) {
      console.error("Error fetching room configs:", error);
    }
  };

  const updateStepStatus = (stepIndex: number, status: SetupStep["status"]) => {
    setSteps(prev => prev.map((step, index) => 
      index === stepIndex ? { ...step, status } : step
    ));
  };

  const handleOccupancyConfigComplete = (success: boolean, configId?: string) => {
    if (success && configId) {
      setCotConfigId(configId);
      updateStepStatus(0, "completed");
      updateStepStatus(1, "in-progress");
      setCurrentStep(1);
      toast.success("Cot configuration created successfully!");
    } else {
      updateStepStatus(0, "error");
      toast.error("Failed to create cot configuration");
    }
  };

  const handleRoomSelection = () => {
    if (selectedRoomConfigs.length === 0) {
      toast.error("Please select at least one room type");
      return;
    }
    updateStepStatus(1, "completed");
    updateStepStatus(2, "in-progress");
    setCurrentStep(2);
  };

  const handlePricingComplete = (success: boolean) => {
    if (success) {
      // Check if all selected room configs have pricing configured
      // For now, we'll assume success means all are configured
      updateStepStatus(2, "completed");
      updateStepStatus(3, "in-progress");
      setCurrentStep(3);
    } else {
      updateStepStatus(2, "error");
    }
  };

  const handleFinalComplete = () => {
    updateStepStatus(3, "completed");
    toast.success("Cot setup completed successfully!");
    onComplete(true);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <CotOccupancyConfig
            hotelId={hotelId}
            onComplete={handleOccupancyConfigComplete}
          />
        );

      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Select Room Types for Cots</h3>
              <p className="text-sm text-gray-600 mb-6">
                Choose which room types will support cots. You can add max_cots limits to room configurations later.
              </p>
            </div>

            <div className="space-y-3">
              {roomConfigs.map((room) => (
                <div
                  key={room.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedRoomConfigs.includes(room.id)
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={() => {
                    setSelectedRoomConfigs(prev =>
                      prev.includes(room.id)
                        ? prev.filter(id => id !== room.id)
                        : [...prev, room.id]
                    );
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{room.title}</h4>
                      <p className="text-sm text-gray-600">{room.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>Max Adults: {room.max_adults}</span>
                        <span>Max Children: {room.max_children}</span>
                        <span>Current Max Cots: {room.max_cots || 0}</span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {selectedRoomConfigs.includes(room.id) && (
                        <Badge variant="secondary">Selected</Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                variant="secondary"
                onClick={() => setCurrentStep(0)}
              >
                Back
              </Button>
              <Button
                onClick={handleRoomSelection}
                disabled={selectedRoomConfigs.length === 0}
              >
                Continue to Pricing ({selectedRoomConfigs.length} rooms selected)
              </Button>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Configure Cot Pricing</h3>
              <p className="text-sm text-gray-600 mb-6">
                Set up pricing for cots in the selected room types. You can configure different prices for each room type.
              </p>
            </div>

            {selectedRoomConfigs.length > 0 && cotConfigId && (
              <CotPricingConfig
                hotelId={hotelId}
                roomConfigId={selectedRoomConfigs[0]} // Start with first room
                cotConfigId={cotConfigId}
                onComplete={handlePricingComplete}
              />
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Setup Complete!</h3>
              <p className="text-sm text-gray-600 mb-6">
                Your cot configuration has been set up successfully. Here's a summary of what was configured:
              </p>
            </div>

            <div className="bg-green-50 p-6 rounded-lg">
              <h4 className="font-medium text-green-900 mb-4">Configuration Summary</h4>
              <div className="space-y-3 text-sm text-green-800">
                <div className="flex justify-between">
                  <span>Cot Configuration:</span>
                  <span>✓ Created</span>
                </div>
                <div className="flex justify-between">
                  <span>Room Types Configured:</span>
                  <span>✓ {selectedRoomConfigs.length} rooms</span>
                </div>
                <div className="flex justify-between">
                  <span>Pricing Rules:</span>
                  <span>✓ Configured</span>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-md">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Next Steps</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Update room configurations to set max_cots limits</li>
                <li>• Test cot booking through the store API</li>
                <li>• Configure seasonal pricing if needed</li>
                <li>• Train staff on cot availability and pricing</li>
              </ul>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                onClick={handleFinalComplete}
                className="bg-green-600 hover:bg-green-700"
              >
                Complete Setup
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                step.status === "completed" ? "bg-green-500 text-white" :
                step.status === "in-progress" ? "bg-blue-500 text-white" :
                step.status === "error" ? "bg-red-500 text-white" :
                "bg-gray-200 text-gray-600"
              }`}>
                {step.status === "completed" ? "✓" : index + 1}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{step.title}</p>
                <p className="text-xs text-gray-500">{step.description}</p>
              </div>
              {index < steps.length - 1 && (
                <div className="flex-1 mx-4 h-px bg-gray-200"></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="bg-white rounded-lg border p-6">
        {renderStepContent()}
      </div>
    </div>
  );
};

export default CotSetupWizard;
