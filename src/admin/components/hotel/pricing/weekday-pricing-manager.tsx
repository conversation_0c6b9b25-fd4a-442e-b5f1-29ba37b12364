import React, { useState } from "react";
import {
  Button,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  FocusModal,
} from "@camped-ai/ui";
import { PlusCircle, Edit, Trash } from "lucide-react";
import EnhancedWeekdayPricing from "./enhanced-weekday-pricing";
import {
  useAdminRoomConfigWeekdayRules,
  useAdminBulkUpsertWeekdayRules,
  useAdminDeleteWeekdayRule,
} from "../../../hooks/hotel/use-admin-hotel-enhanced-weekday-pricing";

type WeekdayPricingManagerProps = {
  hotelId: string;
  roomConfigId: string;
};

const WeekdayPricingManager: React.FC<WeekdayPricingManagerProps> = ({
  hotelId,
  roomConfigId,
}) => {
  const { weekdayRules, currencyCode, isLoading, refetch } =
    useAdminRoomConfigWeekdayRules(roomConfigId);
  const { bulkUpsertWeekdayRules, isLoading: isSaving } =
    useAdminBulkUpsertWeekdayRules();
  const { deleteWeekdayRule } = useAdminDeleteWeekdayRule();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const handleOpenModal = () => {
    setIsEditing(true);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setIsEditing(false);
  };

  const handleSave = async (data: any) => {
    try {
      await bulkUpsertWeekdayRules({
        roomConfigId,
        data,
      });

      toast.success("Success", {
        description: "Weekday pricing saved successfully",
      });

      handleCloseModal();
      refetch();
    } catch (error) {
      console.error("Error saving weekday pricing:", error);
      toast.error("Error", {
        description: "Failed to save weekday pricing",
      });
    }
  };

  const handleDelete = async (ruleId: string) => {
    if (
      !confirm("Are you sure you want to delete this weekday pricing rule?")
    ) {
      return;
    }

    try {
      await deleteWeekdayRule({
        roomConfigId,
        ruleId,
      });

      toast.success("Success", {
        description: "Weekday pricing rule deleted successfully",
      });

      refetch();
    } catch (error) {
      console.error("Error deleting weekday pricing rule:", error);
      toast.error("Error", {
        description: "Failed to delete weekday pricing rule",
      });
    }
  };

  const formatPrice = (amount: number, currencyCode: string) => {
    const currencySymbols: Record<string, string> = {
      USD: "$",
      EUR: "€",
      GBP: "£",
    };

    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      minimumFractionDigits: 2,
    }).format(amount / 100); // Convert from cents
  };

  const getOccupancyTypeName = (rule: any) => {
    return rule.occupancy_type?.name || "Unknown";
  };

  const getMealPlanName = (rule: any) => {
    return rule.meal_plan?.name || "Unknown";
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded-md mb-4 w-1/3"></div>
          <div className="h-64 bg-muted rounded-md"></div>
        </div>
      </div>
    );
  }

  return (
    <Container>
      <Toaster />
      <div className="mb-6">
        <Heading level="h2">Weekday Pricing</Heading>
        <Text className="text-gray-600">
          Set different rates for each day of the week
        </Text>
      </div>

      <div className="flex justify-end mb-4">
        <Button
          variant="primary"
          onClick={handleOpenModal}
          className="flex items-center gap-2"
        >
          <PlusCircle className="w-4 h-4" />
          {weekdayRules.length > 0
            ? "Edit Weekday Pricing"
            : "Add Weekday Pricing"}
        </Button>
      </div>

      {weekdayRules.length === 0 ? (
        <div className="p-6 text-center">
          <Text className="text-gray-500">
            No weekday pricing has been set yet.
          </Text>
          <Button variant="primary" onClick={handleOpenModal} className="mt-4">
            <PlusCircle className="w-4 h-4 mr-2" />
            Add Weekday Pricing
          </Button>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Occupancy Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Meal Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mon
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Wed
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thu
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fri
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sat
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sun
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {weekdayRules.map((rule) => (
                <tr key={rule.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {getOccupancyTypeName(rule)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {getMealPlanName(rule)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatPrice(rule.weekday_prices.mon, rule.currency_code)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatPrice(rule.weekday_prices.tue, rule.currency_code)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatPrice(rule.weekday_prices.wed, rule.currency_code)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatPrice(rule.weekday_prices.thu, rule.currency_code)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatPrice(rule.weekday_prices.fri, rule.currency_code)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatPrice(rule.weekday_prices.sat, rule.currency_code)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatPrice(rule.weekday_prices.sun, rule.currency_code)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <FocusModal open={isModalOpen} onOpenChange={setIsModalOpen}>
        <FocusModal.Content>
          <FocusModal.Header>
            <FocusModal.Title>
              {isEditing ? "Edit Weekday Pricing" : "Add Weekday Pricing"}
            </FocusModal.Title>
          </FocusModal.Header>
          <FocusModal.Body>
            <EnhancedWeekdayPricing
              hotelId={hotelId}
              roomConfigId={roomConfigId}
              initialData={{
                weekday_rules: weekdayRules,
                currency_code: currencyCode,
              }}
              onSave={handleSave}
              onCancel={handleCloseModal}
            />
          </FocusModal.Body>
        </FocusModal.Content>
      </FocusModal>
    </Container>
  );
};

export default WeekdayPricingManager;
