import React, { useState } from "react";
import { Button, Input, Textarea, toast } from "@camped-ai/ui";

interface CotOccupancyConfigProps {
  hotelId: string;
  onComplete: (success: boolean) => void;
  initialData?: any;
  isEdit?: boolean;
}

const CotOccupancyConfig: React.FC<CotOccupancyConfigProps> = ({
  hotelId,
  onComplete,
  initialData,
  isEdit = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: initialData?.name || "Baby Cot",
    type: "COT",
    description: initialData?.description || "Baby cot for infants and toddlers",
    min_age: initialData?.min_age || 0,
    max_age: initialData?.max_age || 3,
    capacity: initialData?.capacity || 1,
    hotel_id: hotelId,
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const endpoint = isEdit
        ? `/admin/hotel-management/hotels/${hotelId}/occupancy-configs/${initialData.id}`
        : `/admin/hotel-management/hotels/${hotelId}/occupancy-configs`;
      const method = isEdit ? "PUT" : "POST";

      const response = await fetch(endpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to save cot configuration");
      }

      toast.success(
        isEdit
          ? "Cot configuration updated successfully"
          : "Cot configuration created successfully"
      );
      onComplete(true);
    } catch (error) {
      console.error("Error saving cot configuration:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to save cot configuration"
      );
      onComplete(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">
          {isEdit ? "Edit" : "Create"} Cot Configuration
        </h3>
        <p className="text-sm text-gray-600 mb-6">
          Configure cot availability and settings for this hotel. Cots are typically used for infants and toddlers.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Configuration Name *
          </label>
          <Input
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="e.g., Baby Cot, Infant Cot"
            className="w-full"
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            A descriptive name for this cot configuration
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <Textarea
            value={formData.description}
            onChange={(e) => handleInputChange("description", e.target.value)}
            placeholder="Describe the cot type and its features..."
            rows={3}
            className="w-full"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Minimum Age (years)
            </label>
            <Input
              type="number"
              min="0"
              max="10"
              value={formData.min_age.toString()}
              onChange={(e) =>
                handleInputChange("min_age", parseInt(e.target.value) || 0)
              }
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-1">
              Minimum age for cot usage
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Maximum Age (years)
            </label>
            <Input
              type="number"
              min="0"
              max="10"
              value={formData.max_age.toString()}
              onChange={(e) =>
                handleInputChange("max_age", parseInt(e.target.value) || 3)
              }
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-1">
              Maximum age for cot usage
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Capacity
            </label>
            <Input
              type="number"
              min="1"
              max="2"
              value={formData.capacity.toString()}
              onChange={(e) =>
                handleInputChange("capacity", parseInt(e.target.value) || 1)
              }
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-1">
              Number of children per cot
            </p>
          </div>
        </div>

        <div className="bg-blue-50 p-4 rounded-md">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Configuration Summary</h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p><strong>Type:</strong> COT (Cot/Baby Bed)</p>
            <p><strong>Age Range:</strong> {formData.min_age} - {formData.max_age} years</p>
            <p><strong>Capacity:</strong> {formData.capacity} child{formData.capacity > 1 ? 'ren' : ''} per cot</p>
            <p><strong>Hotel:</strong> {hotelId}</p>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button
          variant="secondary"
          onClick={() => onComplete(false)}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
          loading={isSubmitting}
        >
          {isEdit ? "Update" : "Create"} Cot Configuration
        </Button>
      </div>
    </div>
  );
};

export default CotOccupancyConfig;
