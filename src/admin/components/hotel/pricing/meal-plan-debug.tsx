import React from "react";
import { Container, Heading, Text, Badge } from "@camped-ai/ui";
import { useAdminHotelMealPlans } from "../../../hooks/hotel/use-admin-hotel-meal-plans";
import { useAdminHotelOccupancyConfigs } from "../../../hooks/hotel/use-admin-hotel-occupancy-configs";

type MealPlanDebugProps = {
  hotelId: string;
};

/**
 * Debug component to verify meal plan data and filtering logic
 */
const MealPlanDebug: React.FC<MealPlanDebugProps> = ({ hotelId }) => {
  const { mealPlans, isLoading: mealPlansLoading } = useAdminHotelMealPlans(hotelId);
  const { occupancyConfigs, isLoading: occupancyLoading } = useAdminHotelOccupancyConfigs(hotelId);

  const isLoading = mealPlansLoading || occupancyLoading;

  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Helper function to get available meal plans for an occupancy type
  const getAvailableMealPlans = (occupancyTypeId: string) => {
    const occupancy = occupancyConfigs.find(oc => oc.id === occupancyTypeId);
    if (!occupancy) return [];

    // Legacy logic for special accommodations
    const isExtraBed = (occupancy as any).type === "EXTRA_BED" || 
                      occupancy.name?.toLowerCase().includes("extra bed");
    const isCot = (occupancy as any).type === "COT" || 
                 occupancy.name?.toLowerCase().includes("cot");
    
    if (isExtraBed || isCot) {
      return [{ id: null, name: "N/A" }];
    }

    // Filter meal plans based on their applicable_occupancy_types metadata
    const availableMealPlans = mealPlans.filter(mealPlan => {
      const applicableTypes = mealPlan.metadata?.applicable_occupancy_types;
      
      console.log(`Checking meal plan "${mealPlan.name}" for occupancy "${occupancy.name}":`, {
        mealPlanId: mealPlan.id,
        applicableTypes,
        occupancyTypeId,
        isApplicable: !applicableTypes || applicableTypes.length === 0 || applicableTypes.includes(occupancyTypeId)
      });
      
      // If no applicable types specified, meal plan is available for all occupancy types
      if (!applicableTypes || applicableTypes.length === 0) {
        return true;
      }
      
      // Check if this occupancy type is in the applicable types
      return applicableTypes.includes(occupancyTypeId);
    });

    return availableMealPlans.length > 0 ? availableMealPlans : [{ id: null, name: "N/A" }];
  };

  return (
    <Container>
      <Heading level="h2" className="mb-4">Meal Plan Debug Information</Heading>
      
      {/* Raw Data */}
      <div className="mb-8">
        <Heading level="h3" className="mb-2">Raw Meal Plans Data</Heading>
        <div className="space-y-2">
          {mealPlans.map((mealPlan) => (
            <div key={mealPlan.id} className="p-3 border rounded">
              <div className="font-medium">{mealPlan.name} (ID: {mealPlan.id})</div>
              <div className="text-sm text-gray-600">Type: {mealPlan.type}</div>
              <div className="text-sm text-gray-600">
                Applicable Occupancy Types: {
                  mealPlan.metadata?.applicable_occupancy_types 
                    ? JSON.stringify(mealPlan.metadata.applicable_occupancy_types)
                    : "null (all types)"
                }
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Full metadata: {JSON.stringify(mealPlan.metadata || {})}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Occupancy Configs */}
      <div className="mb-8">
        <Heading level="h3" className="mb-2">Occupancy Configurations</Heading>
        <div className="space-y-2">
          {occupancyConfigs.map((occupancy) => (
            <div key={occupancy.id} className="p-3 border rounded">
              <div className="font-medium">{occupancy.name} (ID: {occupancy.id})</div>
              <div className="text-sm text-gray-600">Type: {(occupancy as any).type || "CUSTOM"}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Filtering Results */}
      <div className="mb-8">
        <Heading level="h3" className="mb-2">Filtering Results</Heading>
        <div className="space-y-4">
          {occupancyConfigs.map((occupancy) => {
            const availableMealPlans = getAvailableMealPlans(occupancy.id);
            
            return (
              <div key={occupancy.id} className="p-4 border rounded">
                <div className="font-medium mb-2">
                  {occupancy.name} ({(occupancy as any).type || "CUSTOM"})
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  Available meal plans: {availableMealPlans.length}
                </div>
                <div className="flex flex-wrap gap-2">
                  {availableMealPlans.map((mealPlan, index) => (
                    <Badge 
                      key={mealPlan.id || index}
                      variant={mealPlan.id === null ? "secondary" : "default"}
                    >
                      {mealPlan.name}
                    </Badge>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Test Specific Meal Plan */}
      <div className="mb-8">
        <Heading level="h3" className="mb-2">Test "Kid foods" Meal Plan</Heading>
        {(() => {
          const kidFoodsMealPlan = mealPlans.find(mp => mp.name === "Kid foods");
          if (!kidFoodsMealPlan) {
            return <Text>❌ "Kid foods" meal plan not found</Text>;
          }

          const applicableTypes = kidFoodsMealPlan.metadata?.applicable_occupancy_types;
          const targetOccupancyId = "occ_01JY0TXRBH29W5SZBG78336VAG";
          const targetOccupancy = occupancyConfigs.find(oc => oc.id === targetOccupancyId);

          return (
            <div className="space-y-2">
              <Text>✅ "Kid foods" meal plan found</Text>
              <Text>📋 Applicable occupancy types: {JSON.stringify(applicableTypes)}</Text>
              <Text>🎯 Target occupancy ID: {targetOccupancyId}</Text>
              <Text>👤 Target occupancy name: {targetOccupancy?.name || "NOT FOUND"}</Text>
              <Text>
                🔍 Should be available for target occupancy: {
                  applicableTypes?.includes(targetOccupancyId) ? "✅ YES" : "❌ NO"
                }
              </Text>
              
              {targetOccupancy && (
                <div className="mt-4 p-3 bg-gray-50 rounded">
                  <Text className="font-medium">Available meal plans for {targetOccupancy.name}:</Text>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {getAvailableMealPlans(targetOccupancyId).map((mp, idx) => (
                      <Badge key={mp.id || idx} variant={mp.name === "Kid foods" ? "default" : "secondary"}>
                        {mp.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })()}
      </div>
    </Container>
  );
};

export default MealPlanDebug;
