import React, { useState, useEffect } from "react";
import { Button, Input, toast } from "@camped-ai/ui";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import { CurrencySelector } from "../../common/currency-selector";
import { getCurrencySymbol } from "../../../utils/currency-utils";

interface CotPricingConfigProps {
  hotelId: string;
  roomConfigId: string;
  cotConfigId: string;
  onComplete: (success: boolean) => void;
  initialData?: any;
  isEdit?: boolean;
}

const CotPricingConfig: React.FC<CotPricingConfigProps> = ({
  hotelId,
  roomConfigId,
  cotConfigId,
  onComplete,
  initialData,
  isEdit = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { defaultCurrency } = useAdminCurrencies();
  const [formData, setFormData] = useState({
    room_config_id: roomConfigId,
    occupancy_type_id: cotConfigId,
    currency_code: initialData?.currency_code || defaultCurrency?.currency_code || "USD",
    amount: initialData?.amount || 1000, // Default $10.00 in cents
    mon_price: initialData?.mon_price || 1000,
    tue_price: initialData?.tue_price || 1000,
    wed_price: initialData?.wed_price || 1000,
    thu_price: initialData?.thu_price || 1000,
    fri_price: initialData?.fri_price || 1000,
    sat_price: initialData?.sat_price || 1500, // Weekend premium
    sun_price: initialData?.sun_price || 1500, // Weekend premium
  });

  const [roomConfigName, setRoomConfigName] = useState("");
  const [cotConfigName, setCotConfigName] = useState("");

  useEffect(() => {
    // Fetch room config and cot config names for display
    fetchConfigNames();
  }, [roomConfigId, cotConfigId]);

  const fetchConfigNames = async () => {
    try {
      // Fetch room config name
      const roomResponse = await fetch(`/admin/hotel-management/room-configs/${roomConfigId}`);
      if (roomResponse.ok) {
        const roomData = await roomResponse.json();
        setRoomConfigName(roomData.room_config?.title || "Unknown Room");
      }

      // Fetch cot config name
      const cotResponse = await fetch(`/admin/hotel-management/hotels/${hotelId}/occupancy-configs`);
      if (cotResponse.ok) {
        const cotData = await cotResponse.json();
        const cotConfig = cotData.occupancy_configs?.find((config: any) => config.id === cotConfigId);
        setCotConfigName(cotConfig?.name || "Unknown Cot");
      }
    } catch (error) {
      console.error("Error fetching config names:", error);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value });
  };

  const handlePriceChange = (field: string, value: string) => {
    // Convert dollars to cents
    const cents = Math.round(parseFloat(value || "0") * 100);
    setFormData({ ...formData, [field]: cents });
  };

  const formatPrice = (cents: number) => {
    return (cents / 100).toFixed(2);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const endpoint = isEdit
        ? `/admin/hotel-management/hotels/${hotelId}/pricing/base-rules/${initialData.id}`
        : `/admin/hotel-management/hotels/${hotelId}/pricing/base-rules`;
      const method = isEdit ? "PUT" : "POST";

      const response = await fetch(endpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to save cot pricing");
      }

      toast.success(
        isEdit
          ? "Cot pricing updated successfully"
          : "Cot pricing created successfully"
      );
      onComplete(true);
    } catch (error) {
      console.error("Error saving cot pricing:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to save cot pricing"
      );
      onComplete(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">
          {isEdit ? "Edit" : "Create"} Cot Pricing
        </h3>
        <div className="bg-gray-50 p-4 rounded-md mb-4">
          <div className="text-sm space-y-1">
            <p><strong>Room:</strong> {roomConfigName}</p>
            <p><strong>Cot Type:</strong> {cotConfigName}</p>
            <p><strong>Currency:</strong> {formData.currency_code}</p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <CurrencySelector
            value={formData.currency_code}
            onChange={(currencyCode) => setFormData({ ...formData, currency_code: currencyCode })}
            label="Currency"
            className="mb-4"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Base Price (per cot per night)
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              {getCurrencySymbol(formData.currency_code)}
            </span>
            <Input
              type="number"
              step="0.01"
              min="0"
              value={formatPrice(formData.amount)}
              onChange={(e) => handlePriceChange("amount", e.target.value)}
              className="pl-8"
              placeholder="10.00"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Default price used when specific weekday prices are not set
          </p>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Weekday-Specific Pricing (optional)
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
            {[
              { key: "mon_price", label: "Mon" },
              { key: "tue_price", label: "Tue" },
              { key: "wed_price", label: "Wed" },
              { key: "thu_price", label: "Thu" },
              { key: "fri_price", label: "Fri" },
              { key: "sat_price", label: "Sat" },
              { key: "sun_price", label: "Sun" },
            ].map(({ key, label }) => (
              <div key={key}>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {label}
                </label>
                <div className="relative">
                  <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs">
                    {getCurrencySymbol(formData.currency_code)}
                  </span>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formatPrice(formData[key as keyof typeof formData] as number)}
                    onChange={(e) => handlePriceChange(key, e.target.value)}
                    className="pl-6 text-sm h-8"
                    placeholder="10.00"
                  />
                </div>
              </div>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Set different prices for each day of the week. Weekend prices are typically higher.
          </p>
        </div>

        <div className="bg-blue-50 p-4 rounded-md">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Pricing Summary</h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p><strong>Base Price:</strong> {getCurrencySymbol(formData.currency_code)}{formatPrice(formData.amount)} per cot per night</p>
            <p><strong>Weekend Price:</strong> {getCurrencySymbol(formData.currency_code)}{formatPrice(formData.sat_price)} (Sat/Sun)</p>
            <p><strong>Example:</strong> 2 cots × 3 nights = {getCurrencySymbol(formData.currency_code)}{(formatPrice(formData.amount * 2 * 3))}</p>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button
          variant="secondary"
          onClick={() => onComplete(false)}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
          loading={isSubmitting}
        >
          {isEdit ? "Update" : "Create"} Cot Pricing
        </Button>
      </div>
    </div>
  );
};

export default CotPricingConfig;
