import React, { useState } from "react";
import { Container, Heading, Text, Button, toast } from "@camped-ai/ui";
import { Sparkles } from "lucide-react";
import { HotelData } from "../../../types";
import RichTextEditor from "../../rich-text-editor";

interface BaileyAITabProps {
  hotel: HotelData | null;
  hasEditPermission: boolean;
}

const BaileyAITab: React.FC<BaileyAITabProps> = ({
  hotel,
  hasEditPermission,
}) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [aiContent, setAiContent] = useState(hotel?.ai_content || "");

  // Update local state when hotel changes
  React.useEffect(() => {
    setAiContent(hotel?.ai_content || "");
  }, [hotel?.ai_content]);

  const handleSyncWithBaileyAI = async () => {
    if (!hotel?.id) {
      toast.error("Error", {
        description: "Hotel ID is required for Bailey AI sync",
      });
      return;
    }

    setIsSyncing(true);

    try {
      console.log("🤖 Syncing with Bailey AI for hotel:", hotel.id);

      // First, save current AI content to database using the correct endpoint
      if (aiContent !== hotel.ai_content) {
        console.log("📝 Saving current AI content changes...");
        const saveResponse = await fetch(`/admin/hotel-management/hotels`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            id: hotel.id,
            ai_content: aiContent,
          }),
        });

        if (!saveResponse.ok) {
          const saveError = await saveResponse.json().catch(() => ({}));
          console.error("❌ Failed to save AI content:", saveError);
          throw new Error("Failed to save current AI content");
        }
        console.log("✅ AI content saved successfully");
      }

      // Use the dedicated Bailey AI sync endpoint for hotels
      const response = await fetch(`/admin/hotel-management/hotels/${hotel.id}/bailey-ai-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Bailey AI sync successful:", result);

        toast.success("Success", {
          description: "Successfully synced with Bailey AI",
        });

        // Update the AI content if the API returns updated content
        if (result.hotel?.ai_content) {
          setAiContent(result.hotel.ai_content);
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error("❌ Bailey AI sync failed:", response.status, errorData);

        // Check if it's a partial success (database updated but external API failed)
        if (response.status === 207 && errorData.hotel) {
          console.log("⚠️ Database updated but external API failed");
          toast.error("Partial Success", {
            description: "Content saved but Bailey AI sync failed. Please try again.",
          });

          if (errorData.hotel.ai_content) {
            setAiContent(errorData.hotel.ai_content);
          }
        } else {
          toast.error("Error", {
            description: errorData.message || "Failed to sync with Bailey AI. Please try again.",
          });
        }

        throw new Error(errorData.message || "Bailey AI sync failed");
      }
    } catch (error) {
      console.error("❌ Error during Bailey AI sync:", error);
      
      if (error instanceof Error && !error.message.includes("Bailey AI sync failed")) {
        toast.error("Error", {
          description: "Network error occurred. Please check your connection and try again.",
        });
      }
    } finally {
      setIsSyncing(false);
    }
  };



  return (
    <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
      {/* Header matching description container style */}
      <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
        <div className="flex items-center gap-3">
          <Sparkles className="w-5 h-5 text-muted-foreground flex-shrink-0" />
          <Heading
            level="h2"
            className="text-lg font-medium text-foreground"
          >
            Bailey AI Content
          </Heading>
        </div>

        {hasEditPermission && (
          <Button
            onClick={handleSyncWithBaileyAI}
            disabled={isSyncing || !hotel?.id}
            size="small"
            className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 hover:from-purple-700 hover:via-blue-700 hover:to-indigo-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group overflow-hidden"
            title={!hotel?.id ? "Save hotel first to sync with Bailey AI" : "Sync hotel content with Bailey AI"}
          >
            {/* Animated background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

            {/* Content */}
            <div className="relative flex items-center gap-2">
              <Sparkles className={`w-4 h-4 ${isSyncing ? 'animate-spin' : 'animate-pulse'}`} />
              <span>{isSyncing ? "Syncing..." : "Sync with Bailey AI"}</span>
            </div>
          </Button>
        )}
      </div>

      <div className="p-6">
        <RichTextEditor
          value={aiContent}
          onChange={setAiContent}
          placeholder="Enter hotel details to guide Bailey AI's conversations…"
          height="400px"
          helpText="This content powers Bailey AI. Add rich, engaging details about the hotel to enhance AI-guided conversations and personalized guest recommendations."
          disabled={!hasEditPermission}
        />

        {hotel?.id && (
          <div className="mt-4 p-3 bg-muted rounded-lg">
            <Text className="text-sm text-muted-foreground">
              💡 <strong>Tip:</strong> Use the "Sync with Bailey AI" button to update Bailey AI with the latest hotel content and ensure optimal AI-powered guest interactions.
            </Text>
          </div>
        )}
      </div>
    </Container>
  );
};

export default BaileyAITab;
