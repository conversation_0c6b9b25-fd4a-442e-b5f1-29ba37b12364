import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Drawer, Text, toast } from "@camped-ai/ui";
import { format, addDays } from "date-fns";
import { Booking, Room } from "../types";
import { Calendar } from "lucide-react";

interface SplitBookingModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  booking: Booking | null;
  room: Room | null;
  onSplitSuccess?: () => void;
}

const SplitBookingModal: React.FC<SplitBookingModalProps> = ({
  isOpen,
  onOpenChange,
  booking,
  room,
  onSplitSuccess,
}) => {
  const [splitDate, setSplitDate] = useState<Date | null>(null);
  const [keepFirstPart, setKeepFirstPart] = useState(true);
  const [isSplitting, setIsSplitting] = useState(false);

  // Initialize split date when modal opens
  useEffect(() => {
    if (isOpen && booking) {
      // Calculate booking duration
      const fromDate = new Date(booking.checkIn);
      const toDate = new Date(booking.checkOut);
      const duration = Math.ceil(
        (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (duration > 1) {
        // For multi-day bookings, set split date to a day after the start date
        const middleDate = addDays(fromDate, 1);
        setSplitDate(middleDate);
      } else {
        // If booking is only 1 day, set a valid date but this shouldn't happen
        setSplitDate(addDays(fromDate, 1));
      }
    }
  }, [isOpen, booking]);

  // Validate if booking can be split
  const canSplit = () => {
    if (!booking) return false;

    const fromDate = new Date(booking.checkIn);
    const toDate = new Date(booking.checkOut);
    const duration = Math.ceil(
      (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    return duration > 1;
  };

  // Handle split booking
  const handleSplitBooking = async () => {
    if (!booking || !splitDate || !room) return;

    const bookingId = booking.order_id || booking.id;
    if (!bookingId) {
      toast.error("Error", {
        description: "No booking ID found for this booking",
      });
      return;
    }

    // Validate split date
    const fromDate = new Date(booking.checkIn);
    const toDate = new Date(booking.checkOut);
    const duration = Math.ceil(
      (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (duration <= 1) {
      toast.error("Error", {
        description: "Booking must span multiple days to be split",
      });
      return;
    }

    // Verify the split date is between the booking dates
    const splitDateTime = splitDate.getTime();
    const minDate = addDays(fromDate, 1).getTime();
    const maxDate = addDays(toDate, -1).getTime();

    if (splitDateTime < minDate) {
      toast.error("Error", {
        description: "Split date must be at least one day after check-in date",
      });
      return;
    }

    if (splitDateTime > maxDate) {
      toast.error("Error", {
        description:
          "Split date must be at least one day before check-out date",
      });
      return;
    }

    const formattedSplitDate = format(splitDate, "yyyy-MM-dd");
    setIsSplitting(true);

    try {
      // Call the API to split the booking
      const response = await fetch(
        `/admin/hotel-management/orders/${bookingId}/split`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            split_date: formattedSplitDate,
            keep_first_part: keepFirstPart,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to split booking");
      }

      const result = await response.json();
      console.log("Split booking API response:", result);

      // Close modal and refresh data
      onOpenChange(false);
      onSplitSuccess?.();

      toast.success("Success", {
        description:
          "Booking has been split successfully. The calendar will refresh to show the changes.",
      });
    } catch (error) {
      console.error("Error splitting booking:", error);
      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to split booking",
      });
    } finally {
      setIsSplitting(false);
    }
  };

  if (!booking) return null;

  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange}>
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>Split Booking</Drawer.Title>
          <Drawer.Description>
            Split this booking into two parts
          </Drawer.Description>
        </Drawer.Header>
        <Drawer.Body className="p-6 space-y-4">
          {!canSplit() ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <Text className="text-lg font-medium mb-2">
                Cannot Split Booking
              </Text>
              <Text className="text-muted-foreground">
                This booking is only one day long and cannot be split.
              </Text>
            </div>
          ) : (
            <>
              {/* Booking Info */}
              <div className="bg-muted/50 p-4 rounded-lg">
                <Text className="font-medium mb-2">Current Booking</Text>
                <div className="space-y-1 text-sm">
                  <div>
                    <strong>Booking ID:</strong>{" "}
                    {booking.order_id || booking.id}
                  </div>
                  {booking.guestName && (
                    <div>
                      <strong>Guest:</strong> {booking.guestName}
                    </div>
                  )}
                  <div>
                    <strong>Room:</strong> {room?.room_number} ({room?.name})
                  </div>
                  <div>
                    <strong>Current Dates:</strong>{" "}
                    {format(booking.checkIn, "MMM dd, yyyy")} -{" "}
                    {format(booking.checkOut, "MMM dd, yyyy")}
                  </div>
                </div>
              </div>

              {/* Date Selection */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Split date (check-out from first part, check-in to second
                  part):
                </label>
                <input
                  type="date"
                  className="w-full p-2 border border-border rounded-md"
                  value={splitDate ? format(splitDate, "yyyy-MM-dd") : ""}
                  onChange={(e) => {
                    if (e.target.value) {
                      const newDate = new Date(e.target.value);
                      const fromDate = new Date(booking.checkIn);
                      const toDate = new Date(booking.checkOut);

                      const minDate = addDays(fromDate, 1);
                      const maxDate = addDays(toDate, -1);

                      if (newDate < minDate) {
                        toast.error("Error", {
                          description:
                            "Split date must be after the check-in date",
                        });
                        return;
                      }

                      if (newDate > maxDate) {
                        toast.error("Error", {
                          description:
                            "Split date must be before the check-out date",
                        });
                        return;
                      }

                      setSplitDate(newDate);
                    }
                  }}
                  min={format(addDays(booking.checkIn, 1), "yyyy-MM-dd")}
                  max={format(addDays(booking.checkOut, -1), "yyyy-MM-dd")}
                />
                <Text className="text-xs text-muted-foreground mt-1">
                  Select a date between check-in and check-out
                </Text>
              </div>

              {/* Room Allocation Options */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Room allocation:
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      className="mr-2"
                      checked={keepFirstPart}
                      onChange={() => setKeepFirstPart(true)}
                    />
                    <span>
                      Keep first part in current room, move second part to
                      unallocated
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      className="mr-2"
                      checked={!keepFirstPart}
                      onChange={() => setKeepFirstPart(false)}
                    />
                    <span>
                      Move first part to unallocated, keep second part in
                      current room
                    </span>
                  </label>
                </div>
              </div>

              {/* Preview */}
              {splitDate && (
                <div className="border border-border rounded-md p-4 bg-muted/30">
                  <Text className="font-medium mb-2">Preview:</Text>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 bg-background rounded border">
                      <div className="text-sm font-medium">First Part</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {format(booking.checkIn, "MMM dd")} -{" "}
                        {format(splitDate, "MMM dd, yyyy")}
                      </div>
                      <div className="text-xs mt-2 px-2 py-1 rounded-full inline-block bg-primary/10 text-primary">
                        {keepFirstPart ? "Current Room" : "Unallocated"}
                      </div>
                    </div>
                    <div className="p-3 bg-background rounded border">
                      <div className="text-sm font-medium">Second Part</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {format(splitDate, "MMM dd")} -{" "}
                        {format(booking.checkOut, "MMM dd, yyyy")}
                      </div>
                      <div className="text-xs mt-2 px-2 py-1 rounded-full inline-block bg-primary/10 text-primary">
                        {!keepFirstPart ? "Current Room" : "Unallocated"}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </Drawer.Body>
        <Drawer.Footer className="flex justify-end gap-2 p-6 border-t">
          <Button variant="secondary" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          {canSplit() && (
            <Button
              variant="primary"
              onClick={handleSplitBooking}
              disabled={!splitDate || isSplitting}
            >
              {isSplitting ? "Splitting..." : "Split Booking"}
            </Button>
          )}
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};

export default SplitBookingModal;
