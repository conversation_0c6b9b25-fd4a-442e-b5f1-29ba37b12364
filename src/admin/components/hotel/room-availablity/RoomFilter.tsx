import React from "react";
import { Select } from "@camped-ai/ui";
import { Room } from "../types";

interface RoomFilterProps {
  selectedRoom: string;
  onRoomChange: (room: string) => void;
  rooms: Room[];
  selectedRoomType: string;
}

const RoomFilter: React.FC<RoomFilterProps> = ({
  selectedRoom,
  onRoomChange,
  rooms,
  selectedRoomType,
}) => {
  // Filter rooms based on selected room type
  const availableRooms =
    selectedRoomType === "All Types"
      ? rooms
      : rooms.filter((room) => room.config_name === selectedRoomType);

  // Create options for the select dropdown
  const roomOptions = [
    { value: "all", label: "All Rooms" },
    ...availableRooms.map((room) => ({
      value: room.id,
      label: `${room.name || room.title} (${room.config_name})`,
    })),
  ];

  return (
    <div className="flex flex-col">
      <label className="text-xs text-muted-foreground mb-1">Room</label>
      <Select value={selectedRoom} onValueChange={onRoomChange}>
        <Select.Trigger className="min-w-[180px]">
          <Select.Value placeholder="Select room" />
        </Select.Trigger>
        <Select.Content>
          {roomOptions.map((option) => (
            <Select.Item key={option.value} value={option.value}>
              {option.label}
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
    </div>
  );
};

export default RoomFilter;
