import React from "react";
import { formatDateShort } from "./timeUtils";

interface TimelineHeaderProps {
  dateSlots: Date[];
  currentDate: Date;
  timelineWidth: number;
}

const TimelineHeader: React.FC<TimelineHeaderProps> = ({
  dateSlots,
  currentDate,
  timelineWidth,
}) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return (
    <div
      className="bg-card border-b border-border flex-shrink-0 relative"
      style={{
        width: `${timelineWidth}px`,
        zIndex: 30,
      }}
    >
      {/* Date Slots Container - Full Width */}
      <div
        className="flex h-16 bg-card"
        style={{
          width: `${timelineWidth}px`,
          minWidth: `${timelineWidth}px`,
          maxWidth: `${timelineWidth}px`,
        }}
      >
        {dateSlots.map((date, index) => {
          const dateOnly = new Date(date);
          dateOnly.setHours(0, 0, 0, 0);
          const isToday = today.getTime() === dateOnly.getTime();
          const isWeekend = date.getDay() === 0 || date.getDay() === 6;

          return (
            <div
              key={index}
              className={`flex-shrink-0 border-r border-border relative flex flex-col items-center justify-center transition-colors ${
                isToday
                  ? "bg-primary/10 border-primary/30"
                  : isWeekend
                  ? "bg-muted/30"
                  : "hover:bg-muted/30"
              }`}
              style={{ width: "150px" }}
            >
              {/* Date Display */}
              <div
                className={`text-sm font-semibold transition-colors ${
                  isToday ? "text-primary" : "text-foreground"
                }`}
              >
                {formatDateShort(date)}
              </div>

              {/* Day of Week */}
              <div
                className={`text-xs mt-1 transition-colors ${
                  isToday ? "text-primary/80" : "text-muted-foreground"
                }`}
              >
                {date.toLocaleDateString("en-US", { weekday: "short" })}
              </div>

              {/* Today Indicator */}
              {isToday && (
                <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TimelineHeader;
