export const formatDate = (date: Date): string => {
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

export const formatDateShort = (date: Date): string => {
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

export const generateDateSlots = (startDate: Date, endDate: Date): Date[] => {
  const slots: Date[] = [];
  const current = new Date(startDate);

  while (current <= endDate) {
    slots.push(new Date(current));
    current.setDate(current.getDate() + 1);
  }

  return slots;
};

export const getDatePosition = (
  date: Date,
  startDate: Date,
  endDate: Date,
  containerWidth: number
): number => {
  const dateOnly = new Date(date);
  dateOnly.setHours(0, 0, 0, 0);
  const startDateOnly = new Date(startDate);
  startDateOnly.setHours(0, 0, 0, 0);
  const endDateOnly = new Date(endDate);
  endDateOnly.setHours(0, 0, 0, 0);

  const totalDays =
    Math.ceil(
      (endDateOnly.getTime() - startDateOnly.getTime()) / (1000 * 60 * 60 * 24)
    ) + 1;
  const dayOffset = Math.floor(
    (dateOnly.getTime() - startDateOnly.getTime()) / (1000 * 60 * 60 * 24)
  );

  const dayWidth = containerWidth / totalDays;

  // Position at the start of the day
  return dayOffset * dayWidth;
};

// Option C: Position booking block spanning from middle of start day to middle of end day
export const getCenteredBookingPosition = (
  checkInDate: Date,
  checkOutDate: Date,
  containerStartDate: Date,
  containerEndDate: Date,
  containerWidth: number
): number => {
  const checkInOnly = new Date(checkInDate);
  checkInOnly.setHours(0, 0, 0, 0);
  const containerStartOnly = new Date(containerStartDate);
  containerStartOnly.setHours(0, 0, 0, 0);

  // Calculate which day the check-in starts on
  const checkInOffset = Math.floor(
    (checkInOnly.getTime() - containerStartOnly.getTime()) /
      (1000 * 60 * 60 * 24)
  );

  // Position at the MIDDLE of the check-in day
  // For Jun 29 → Jun 30: checkInOffset = 0, so position = 0 * 150px + 75px = 75px
  const dayWidth = 150; // Fixed 150px per day as per your system

  return checkInOffset * dayWidth + dayWidth / 2; // Start at middle of first day
};

export const getDateBlockWidth = (
  startDate: Date,
  endDate: Date,
  containerStartDate: Date,
  containerEndDate: Date,
  containerWidth: number
): number => {
  // For hotel bookings, calculate the number of days the room is occupied
  // Check-in July 6, Check-out July 16 = 10 nights = room occupied for 10 days
  const startDateOnly = new Date(startDate);
  startDateOnly.setHours(0, 0, 0, 0);
  const endDateOnly = new Date(endDate);
  endDateOnly.setHours(0, 0, 0, 0);

  const diffMs = endDateOnly.getTime() - startDateOnly.getTime();
  const totalDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));

  // Calculate width per day (fixed at 150px per day)
  const dayWidth = 150;

  // For hotel bookings: Width should span from middle of check-in day
  // to middle of checkout day to show room occupancy until checkout
  // Jul 6 → Jul 16: 10 nights = visual span from middle Jul 6 to middle Jul 16
  if (totalDays === 1) {
    return dayWidth; // Same-day check-in/check-out (rare but possible)
  }

  // For multi-day bookings: span the full visual duration including checkout day
  // Jul 6 → Jul 16 (10 days): Should span 10 * 150px = 1500px
  // This shows the room as occupied from Jul 6 through Jul 16 (partial)
  return Math.max(30, totalDays * dayWidth);
};

export const getBookingDuration = (startDate: Date, endDate: Date): string => {
  // For hotel bookings, calculate the number of nights stayed
  // Jul 6 → Jul 16: 10 nights (guest stays from Jul 6 night until Jul 16 checkout)
  const startDateOnly = new Date(startDate);
  startDateOnly.setHours(0, 0, 0, 0);
  const endDateOnly = new Date(endDate);
  endDateOnly.setHours(0, 0, 0, 0);

  const diffMs = endDateOnly.getTime() - startDateOnly.getTime();
  const totalDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));

  // For hotel bookings, nights = days between check-in and check-out
  // Jul 6 → Jul 16 = 10 days difference = 10 nights
  // This is correct: guest sleeps 10 nights (Jul 6→7, 7→8, ..., 15→16)
  const nights = Math.max(1, totalDays);

  if (nights === 1) {
    return "1 night";
  } else {
    return `${nights} nights`;
  }
};
