import { RoomInventoryStatus } from "../types/booking";

export const statusColors = {
  [RoomInventoryStatus.AVAILABLE]:
    "bg-green-100 dark:bg-green-500/40 text-green-800 dark:text-green-100 border-green-200 dark:border-green-500/60",
  [RoomInventoryStatus.BOOKED]:
    "bg-red-100 dark:bg-red-500/40 text-red-800 dark:text-red-100 border-red-200 dark:border-red-500/60",
  [RoomInventoryStatus.RESERVED]:
    "bg-blue-100 dark:bg-blue-500/40 text-blue-800 dark:text-blue-100 border-blue-200 dark:border-blue-500/60",
  [RoomInventoryStatus.MAINTENANCE]:
    "bg-orange-100 dark:bg-orange-500/40 text-orange-800 dark:text-orange-100 border-orange-200 dark:border-orange-500/60",
  [RoomInventoryStatus.CLEANING]:
    "bg-orange-100 dark:bg-orange-500/40 text-orange-800 dark:text-orange-100 border-orange-200 dark:border-orange-500/60",
  [RoomInventoryStatus.UNAVAILABLE]:
    "bg-gray-100 dark:bg-gray-500/40 text-gray-800 dark:text-gray-100 border-gray-200 dark:border-gray-500/60",
  [RoomInventoryStatus.RESERVED_UNASSIGNED]:
    "bg-blue-100 dark:bg-blue-500/40 text-blue-800 dark:text-blue-100 border-blue-200 dark:border-blue-500/60",
  [RoomInventoryStatus.CART_RESERVED]:
    "bg-blue-100 dark:bg-blue-500/40 text-blue-800 dark:text-blue-100 border-blue-200 dark:border-blue-500/60",
  [RoomInventoryStatus.ON_DEMAND]:
    "bg-purple-100 dark:bg-purple-500/40 text-purple-800 dark:text-purple-100 border-purple-200 dark:border-purple-500/60",
};

export const statusBgColors = {
  [RoomInventoryStatus.AVAILABLE]: "bg-green-50 dark:bg-green-500/20",
  [RoomInventoryStatus.BOOKED]: "bg-red-50 dark:bg-red-500/20",
  [RoomInventoryStatus.RESERVED]: "bg-blue-50 dark:bg-blue-500/20",
  [RoomInventoryStatus.MAINTENANCE]: "bg-orange-50 dark:bg-orange-500/20",
  [RoomInventoryStatus.CLEANING]: "bg-orange-50 dark:bg-orange-500/20",
  [RoomInventoryStatus.UNAVAILABLE]: "bg-gray-50 dark:bg-gray-500/20",
  [RoomInventoryStatus.RESERVED_UNASSIGNED]: "bg-blue-50 dark:bg-blue-500/20",
  [RoomInventoryStatus.CART_RESERVED]: "bg-blue-50 dark:bg-blue-500/20",
  [RoomInventoryStatus.ON_DEMAND]: "bg-purple-50 dark:bg-purple-500/20",
};

export const getStatusDisplayName = (status: RoomInventoryStatus): string => {
  const displayNames = {
    [RoomInventoryStatus.AVAILABLE]: "Available",
    [RoomInventoryStatus.BOOKED]: "Booked",
    [RoomInventoryStatus.RESERVED]: "Reserved",
    [RoomInventoryStatus.MAINTENANCE]: "Maintenance",
    [RoomInventoryStatus.CLEANING]: "Maintenance", // Mapped to Maintenance as requested
    [RoomInventoryStatus.UNAVAILABLE]: "Unavailable",
    [RoomInventoryStatus.RESERVED_UNASSIGNED]: "Reserved", // Mapped to Reserved as requested
    [RoomInventoryStatus.CART_RESERVED]: "Reserved", // Mapped to Reserved as requested
    [RoomInventoryStatus.ON_DEMAND]: "On Request",
  };
  return displayNames[status];
};

export const getStatusIcon = (status: RoomInventoryStatus): string => {
  const icons = {
    [RoomInventoryStatus.AVAILABLE]: "🟢",
    [RoomInventoryStatus.BOOKED]: "🔴",
    [RoomInventoryStatus.RESERVED]: "🔵",
    [RoomInventoryStatus.MAINTENANCE]: "🟠",
    [RoomInventoryStatus.CLEANING]: "🟠", // Same as maintenance
    [RoomInventoryStatus.UNAVAILABLE]: "⚫",
    [RoomInventoryStatus.RESERVED_UNASSIGNED]: "🔵", // Same as reserved
    [RoomInventoryStatus.CART_RESERVED]: "🔵", // Same as reserved
    [RoomInventoryStatus.ON_DEMAND]: "🟣",
  };
  return icons[status];
};
