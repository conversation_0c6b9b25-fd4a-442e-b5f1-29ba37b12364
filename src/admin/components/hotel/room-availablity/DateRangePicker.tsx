import React from "react";
import { DatePicker } from "@camped-ai/ui";
import { Calendar } from "lucide-react";

interface DateRangePickerProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  currentDate,
  onDateChange,
}) => {
  const endDate = new Date(currentDate);
  endDate.setDate(endDate.getDate() + 6);

  const handleFromDateChange = (date: Date | null) => {
    if (date) {
      onDateChange(date);
    }
  };

  const handleToDateChange = (date: Date | null) => {
    if (date) {
      const newStartDate = new Date(date);
      newStartDate.setDate(newStartDate.getDate() - 6);
      onDateChange(newStartDate);
    }
  };

  return (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-2">
        <Calendar size={18} className="text-gray-500" />
        <div className="flex flex-col">
          <label className="text-xs text-gray-600 mb-1">From</label>
          <DatePicker
            value={currentDate}
            onChange={handleFromDateChange}
            className="text-sm"
          />
        </div>
      </div>

      <div className="flex items-center gap-2">
        <div className="flex flex-col">
          <label className="text-xs text-gray-600 mb-1">To</label>
          <DatePicker
            value={endDate}
            onChange={handleToDateChange}
            className="text-sm"
          />
        </div>
      </div>
    </div>
  );
};

export default DateRangePicker;
