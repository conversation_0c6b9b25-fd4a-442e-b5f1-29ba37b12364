import { useRef, useCallback } from "react";

export const useScrollSync = () => {
  const roomListRef = useRef<HTMLDivElement>(null);
  const timelineContentRef = useRef<HTMLDivElement>(null);

  const handleRoomListScroll = useCallback(() => {
    if (roomListRef.current && timelineContentRef.current) {
      timelineContentRef.current.scrollTop = roomListRef.current.scrollTop;
    }
  }, []);

  const handleTimelineScroll = useCallback(() => {
    if (roomListRef.current && timelineContentRef.current) {
      roomListRef.current.scrollTop = timelineContentRef.current.scrollTop;
    }
  }, []);

  return {
    roomListRef,
    timelineContentRef,
    handleRoomListScroll,
    handleTimelineScroll,
  };
};
