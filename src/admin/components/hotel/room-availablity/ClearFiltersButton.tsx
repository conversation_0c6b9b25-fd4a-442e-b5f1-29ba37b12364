import React from "react";
import { But<PERSON> } from "@camped-ai/ui";

interface ClearFiltersButtonProps {
  onClear: () => void;
  disabled?: boolean;
}

const ClearFiltersButton: React.FC<ClearFiltersButtonProps> = ({
  onClear,
  disabled = false,
}) => {
  return (
    <div className="flex flex-col">
      <label className="text-xs text-transparent mb-1">Clear</label>
      <Button
        onClick={onClear}
        variant="secondary"
        size="small"
        disabled={disabled}
        className="text-sm border border-border"
      >
        Clear Filters
      </Button>
    </div>
  );
};

export default ClearFiltersButton;
