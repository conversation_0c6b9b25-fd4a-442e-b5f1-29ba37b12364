/* Smooth scrolling behavior */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Custom scrollbar styles - Light theme */
.smooth-scroll::-webkit-scrollbar,
.timeline-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.smooth-scroll::-webkit-scrollbar-thumb,
.timeline-scroll::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 6px;
}

.smooth-scroll::-webkit-scrollbar-track,
.timeline-scroll::-webkit-scrollbar-track {
  background-color: hsl(var(--muted));
}

.smooth-scroll::-webkit-scrollbar-thumb:hover,
.timeline-scroll::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground));
}

/* Firefox scrollbar */
.smooth-scroll,
.timeline-scroll {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) hsl(var(--muted));
}

/* Dark theme scrollbar styles */
@media (prefers-color-scheme: dark) {
  .smooth-scroll::-webkit-scrollbar-thumb,
  .timeline-scroll::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
  }

  .smooth-scroll::-webkit-scrollbar-track,
  .timeline-scroll::-webkit-scrollbar-track {
    background-color: hsl(var(--background));
  }

  .smooth-scroll::-webkit-scrollbar-thumb:hover,
  .timeline-scroll::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }

  .smooth-scroll,
  .timeline-scroll {
    scrollbar-color: hsl(var(--border)) hsl(var(--background));
  }
}

/* Custom background colors - Theme aware */
.bg-gray-25 {
  background-color: hsl(var(--muted) / 0.3);
}

.bg-blue-25 {
  background-color: hsl(var(--primary) / 0.1);
}

.bg-green-25 {
  background-color: hsl(var(--muted) / 0.3);
}

/* Hide scrollbar on room list to avoid double scrollbars */
.room-list-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.room-list-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Timeline scroll improvements */
.timeline-scroll {
  scroll-behavior: auto; /* Disable smooth scroll for sync */
  overflow-x: auto;
  overflow-y: auto;
  /* Ensure container can accommodate full timeline width */
  width: 100%;
  position: relative;
}

/* Timeline content container */
.timeline-content {
  /* Ensure content takes full calculated width */
  flex-shrink: 0;
  overflow: visible;
}

/* Timeline header - ensure it stays above everything */
.timeline-header {
  position: sticky;
  top: 0;
  z-index: 40 !important;
  background-color: var(--card) !important;
  opacity: 1 !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(8px);
}

/* Ensure timeline content doesn't get clipped */
.timeline-content {
  display: block;
  /* width: max-content; - Removed to allow explicit width control */
  min-width: 100%;
}

/* Dynamic header height utilities */
.dynamic-header {
  min-height: 3rem; /* 48px minimum */
  height: auto; /* Allow content to determine height */
}

/* Ensure text wrapping in headers */
.header-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.3;
}

/* Grid alignment utilities */
.grid-aligned {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  align-items: center;
}

/* Text alignment and spacing */
.text-aligned {
  line-height: 1.4;
  letter-spacing: 0.025em;
}

/* Proper spacing for room elements */
.room-spacing {
  padding: 0.75rem 1.5rem;
  margin: 0;
  box-sizing: border-box;
}

/* Ensure consistent heights */
.room-height {
  height: 5rem; /* 80px */
  min-height: 5rem;
  max-height: 5rem;
}

/* Professional hover states */
.professional-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.professional-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Sticky header improvements - Theme aware */
.sticky-header {
  backdrop-filter: blur(8px);
  background-color: hsl(var(--card) / 0.95);
  border-bottom: 1px solid hsl(var(--border));
}

/* Timeline alignment */
.timeline-aligned {
  display: flex;
  align-items: stretch;
  min-height: 100%;
}

/* Perfect scroll synchronization */
.sync-scroll {
  overflow-x: hidden;
  overflow-y: auto;
}

/* Responsive text sizing for headers */
@media (max-width: 768px) {
  .header-text {
    font-size: 0.75rem;
    line-height: 1.2;
  }
}

/* Interactive Booking Elements - Adapted from simple-room-calendar.css */

/* Make booking blocks clickable with hover effects */
.booking-block-clickable {
  cursor: pointer !important;
  pointer-events: auto !important;
  user-select: none;
  -webkit-user-select: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.booking-block-clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
  z-index: 40 !important;
}

.booking-block-clickable:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Selected booking highlight */
.selected-booking {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border: 2px solid hsl(var(--primary)) !important;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
}

/* Booking menu button - show on hover */
.booking-menu-button {
  opacity: 0;
  transition: opacity 0.2s ease;
  background-color: hsl(var(--background) / 0.9) !important;
  backdrop-filter: blur(4px);
}

.booking-block-hover:hover .booking-menu-button {
  opacity: 1;
}

/* Context menu styling */
.context-menu {
  position: fixed;
  background-color: hsl(var(--popover));
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 180px;
  padding: 4px 0;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  color: hsl(var(--foreground));
  font-size: 0.875rem;
}

.context-menu-item:hover {
  background-color: hsl(var(--accent));
}

.context-menu-item:active {
  background-color: hsl(var(--accent) / 0.8);
}

.context-menu-separator {
  height: 1px;
  background-color: hsl(var(--border));
  margin: 4px 0;
}

/* Status-specific booking colors with better contrast */
.booking-status-booked {
  background-color: hsl(var(--success) / 0.1);
  border-color: hsl(var(--success));
  color: hsl(var(--success-foreground));
}

.booking-status-reserved {
  background-color: hsl(var(--warning) / 0.1);
  border-color: hsl(var(--warning));
  color: hsl(var(--warning-foreground));
}

.booking-status-maintenance {
  background-color: hsl(var(--destructive) / 0.1);
  border-color: hsl(var(--destructive));
  color: hsl(var(--destructive-foreground));
}

.booking-status-unavailable {
  background-color: hsl(var(--muted));
  border-color: hsl(var(--border));
  color: hsl(var(--muted-foreground));
}

/* Unallocated bookings panel styling */
.unallocated-booking-card {
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  padding: 16px;
  background-color: hsl(var(--card));
  transition: all 0.2s ease;
}

.unallocated-booking-card:hover {
  background-color: hsl(var(--accent) / 0.5);
  border-color: hsl(var(--primary) / 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Allocation button styling */
.allocate-button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.allocate-button:hover {
  background-color: hsl(var(--primary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.allocate-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Booking block interactive styles */
.booking-block-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.booking-block-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 30;
}

/* 3-dots menu button styling */
.booking-menu-button {
  opacity: 0;
  transition: opacity 0.2s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.booking-block-hover:hover .booking-menu-button {
  opacity: 1;
}

.booking-menu-button:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Booking detail sidebar */
.booking-detail-sidebar {
  background: hsl(var(--card));
  border-left: 1px solid hsl(var(--border));
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .booking-menu-button {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .booking-menu-button:hover {
    background: rgba(0, 0, 0, 0.9);
  }
}

/* Fix for drawer scroll locking issue */
/* Ensure timeline container remains scrollable when drawer is open */
.timeline-scroll {
  overflow-x: auto !important;
  overflow-y: auto !important;
  position: relative !important;
}

/* Force timeline container to maintain scroll when modal/drawer is open */
body:has([data-state="open"]) .timeline-scroll,
body[style*="overflow: hidden"] .timeline-scroll {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* Ensure main timeline container maintains scrollability */
.flex-1.flex.overflow-hidden {
  overflow: auto !important;
}

/* Override any body scroll lock that might affect timeline */
body[style*="overflow: hidden"] .timeline-scroll,
body[style*="overflow: hidden"] .timeline-content {
  overflow: auto !important;
  position: relative !important;
}
