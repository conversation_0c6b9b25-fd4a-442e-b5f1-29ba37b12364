import React from "react";

interface TimelineFooterProps {
  startDate: Date;
  endDate: Date;
  dateSlots: any[];
  groupedRooms: Record<string, any[]>;
  weekBookings: any[];
  timelineWidth: number;
}

const TimelineFooter: React.FC<TimelineFooterProps> = ({
  startDate,
  endDate,
  dateSlots,
  groupedRooms,
  weekBookings,
  timelineWidth,
}) => {
  const totalRooms = Object.keys(groupedRooms).reduce(
    (total, type) => total + groupedRooms[type].length,
    0
  );

  return (
    <div className="bg-muted border-t border-border px-6 py-3 flex-shrink-0">
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div className="flex items-center gap-4">
          <span>
            Showing {dateSlots.length} days from{" "}
            {startDate.toLocaleDateString()} to {endDate.toLocaleDateString()}
          </span>
          <span className="text-xs text-muted-foreground/80">
            • {totalRooms} rooms
          </span>
          <span className="text-xs text-muted-foreground/80">
            • {weekBookings.length} bookings in range
          </span>
        </div>
        {/* <span className="text-xs text-muted-foreground/80">
          Timeline width: {timelineWidth}px
        </span> */}
      </div>
    </div>
  );
};

export default TimelineFooter;
