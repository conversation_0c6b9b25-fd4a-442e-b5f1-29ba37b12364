import { forwardRef } from "react";
import { Room } from "../types";

interface RoomListProps {
  groupedRooms: Record<string, Room[]>;
  hoveredRoomId: string | null;
  onHover: (roomId: string | null) => void;
  onScroll: () => void;
}

const RoomList = forwardRef<HTMLDivElement, RoomListProps>(
  ({ groupedRooms, hoveredRoomId, onHover, onScroll }, ref) => {
    return (
      <div
        ref={ref}
        className="flex-1 overflow-y-auto room-list-scroll"
        onScroll={onScroll}
        style={{ height: "calc(100vh - 180px)" }}
      >
        {Object.entries(groupedRooms).map(([type, rooms]) => (
          <div key={type}>
            <div className="w-72 bg-card border-r border-border flex-shrink-0 flex flex-col">
              <div className="h-12 border-b border-border bg-muted/50 flex items-center px-5 flex-shrink-0">
                <h3 className="font-semibold text-foreground text-xs truncate ">
                  {type} ({rooms.length})
                </h3>
              </div>
            </div>
            {rooms.map((room) => (
              <div
                key={room.id}
                className={`h-20 border-b border-border flex items-center px-6 py-3 transition-all duration-200 cursor-pointer ${
                  hoveredRoomId === room.id
                    ? "bg-primary/10 border-l-4 border-l-primary shadow-sm"
                    : "hover:bg-muted/30"
                }`}
                onMouseEnter={() => onHover(room.id)}
                onMouseLeave={() => onHover(null)}
              >
                <div className="flex-1 min-w-0">
                  <div
                    className={`font-semibold text-xs leading-tight mb-1 transition-colors truncate ${
                      hoveredRoomId === room.id
                        ? "text-primary"
                        : "text-foreground"
                    }`}
                  >
                    {room.name}
                  </div>
                  {/* <div
                    className={`text-sm leading-tight transition-colors truncate ${
                      hoveredRoomId === room.id
                        ? "text-primary/80"
                        : "text-muted-foreground"
                    }`}
                  >
                    Floor {room.metadata.floor || "N/A"} •{" "}
                    {room.metadata.room_number}
                  </div> */}
                </div>
                <div className="flex-shrink-0 ml-3">
                  {hoveredRoomId === room.id && (
                    <div className="w-3 h-3 bg-primary rounded-full animate-pulse shadow-lg" />
                  )}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  }
);

RoomList.displayName = "RoomList";

export default RoomList;
