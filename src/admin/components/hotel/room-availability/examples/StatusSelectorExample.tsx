import React, { useState } from "react";
import { 
  StatusBadgeSelector, 
  ROOM_STATUSES, 
  STATUS_DISPLAY_NAMES,
  type StatusOption,
  type RoomStatusValue 
} from "../index";

/**
 * Example component showing how to import and reuse the StatusBadgeSelector
 * This demonstrates the import/export pattern learned from the availability page
 */
const StatusSelectorExample: React.FC = () => {
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [isDisabled, setIsDisabled] = useState(false);

  // Example of using custom status options
  const customStatuses: StatusOption[] = [
    {
      value: "cleaning",
      label: "Cleaning",
      colors: "bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200",
    },
    {
      value: "inspection",
      label: "Inspection",
      colors: "bg-indigo-100 text-indigo-800 border-indigo-200 hover:bg-indigo-200",
    },
  ];

  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
    console.log(`Status changed to: ${status}`);
    
    // Example of using STATUS_DISPLAY_NAMES
    const displayName = STATUS_DISPLAY_NAMES[status as RoomStatusValue];
    if (displayName) {
      console.log(`Display name: ${displayName}`);
    }
  };

  return (
    <div className="p-6 space-y-6 bg-white rounded-lg shadow">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Status Selector Examples
        </h3>
        
        {/* Example 1: Using default room statuses */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Room Statuses
            </label>
            <StatusBadgeSelector
              selectedStatus={selectedStatus}
              onStatusChange={handleStatusChange}
              options={ROOM_STATUSES}
              disabled={isDisabled}
            />
            {selectedStatus && (
              <p className="mt-2 text-sm text-gray-600">
                Selected: <span className="font-medium">{selectedStatus}</span>
              </p>
            )}
          </div>

          {/* Example 2: Using custom statuses */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Custom Statuses
            </label>
            <StatusBadgeSelector
              selectedStatus={selectedStatus}
              onStatusChange={handleStatusChange}
              options={customStatuses}
              disabled={isDisabled}
            />
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSelectedStatus(null)}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            >
              Clear Selection
            </button>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isDisabled}
                onChange={(e) => setIsDisabled(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">Disabled</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export { StatusSelectorExample };
