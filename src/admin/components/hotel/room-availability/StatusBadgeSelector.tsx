import React from "react";

interface StatusOption {
  value: string;
  label: string;
  colors: string;
}

interface StatusBadgeSelectorProps {
  selectedStatus: string | null;
  onStatusChange: (status: string) => void;
  options: StatusOption[];
  className?: string;
  disabled?: boolean;
}

const StatusBadgeSelector: React.FC<StatusBadgeSelectorProps> = ({
  selectedStatus,
  onStatusChange,
  options,
  className = "",
  disabled = false,
}) => {
  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {options.map((status) => (
        <button
          key={status.value}
          type="button"
          onClick={() => onStatusChange(status.value)}
          disabled={disabled}
          className={`
            px-4 py-2 text-sm font-medium rounded-md border transition-colors cursor-pointer
            ${
              selectedStatus === status.value
                ? `${status.colors} ring-2 ring-offset-1 ring-blue-500`
                : `${status.colors} opacity-60 hover:opacity-100`
            }
            ${disabled ? "opacity-40 cursor-not-allowed" : ""}
          `}
        >
          {status.label}
        </button>
      ))}
    </div>
  );
};

// Named export following the pattern
export { StatusBadgeSelector };
export type { StatusBadgeSelectorProps, StatusOption };
