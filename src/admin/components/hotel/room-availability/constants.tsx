import { StatusOption } from "./StatusBadgeSelector";

// Room status options (excluding 'booked' as per business requirements)
export const ROOM_STATUSES: StatusOption[] = [
  {
    value: "available",
    label: "Available",
    colors: "bg-green-100 text-green-800 border-green-200 hover:bg-green-200",
  },
  {
    value: "maintenance",
    label: "Maintenance",
    colors:
      "bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200",
  },
  {
    value: "reserved",
    label: "Reserved",
    colors: "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200",
  },
  {
    value: "unavailable",
    label: "Unavailable",
    colors: "bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200",
  },
  {
    value: "on_demand",
    label: "On Request",
    colors:
      "bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200",
  },
];

// Export individual status colors for other components
export const STATUS_COLORS = {
  available: "bg-green-100 text-green-800 border-green-200",
  booked: "bg-red-100 text-red-800 border-red-200",
  maintenance: "bg-orange-100 text-orange-800 border-orange-200",
  reserved: "bg-blue-100 text-blue-800 border-blue-200",
  unavailable: "bg-gray-100 text-gray-800 border-gray-200",
  on_demand: "bg-purple-100 text-purple-800 border-purple-200",
} as const;

// Export status display names
export const STATUS_DISPLAY_NAMES = {
  available: "Available",
  booked: "Booked",
  maintenance: "Maintenance",
  reserved: "Reserved",
  unavailable: "Unavailable",
  on_demand: "On Request",
} as const;

export type RoomStatusValue = keyof typeof STATUS_COLORS;
