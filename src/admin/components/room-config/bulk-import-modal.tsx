import { useState } from "react";
import {
  Button,
  FocusModal,
  Text,
  Heading,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { FileIcon, DownloadIcon, UploadIcon, CheckCircleIcon, XCircleIcon } from "lucide-react";
import { useNavigate } from "react-router-dom";
import * as XLSX from 'xlsx';

type BulkImportModalProps = {
  open: boolean;
  onClose: () => void;
  hotelId?: string;
};

const BulkImportModal = ({ open, onClose, hotelId }: BulkImportModalProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [filePreview, setFilePreview] = useState<any>(null);
  const [previewData, setPreviewData] = useState<{
    roomConfigs: {headers: string[], rows: any[]};
    rooms: {headers: string[], rows: any[]};
    hasRoomsSheet: boolean;
  }>({
    roomConfigs: {headers: [], rows: []},
    rooms: {headers: [], rows: []},
    hasRoomsSheet: false
  });
  const [previewError, setPreviewError] = useState<string>("");
  const navigate = useNavigate();

  // Function to truncate long text for preview
  const truncateText = (text: string, maxLength: number = 50): string => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Function to determine if a field should be truncated based on header name
  const shouldTruncateField = (headerName: string): boolean => {
    const truncateFields = ['description', 'amenities'];
    return truncateFields.some(field => headerName.toLowerCase().includes(field));
  };

  const handleDownloadTemplate = async () => {
    try {
      window.open("/admin/room-configs/template", "_blank");
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error("Error", {
        description: "Failed to download template",
      });
    }
  };

  // Function to parse Excel/CSV files with multi-sheet support
  const parseExcelFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          setPreviewError("Could not read file data");
          return;
        }

        let roomConfigsData: any[][] = [];
        let roomsData: any[][] = [];
        let hasRoomsSheet = false;

        // Parse the file using XLSX
        const workbook = XLSX.read(data, { type: 'array' });

        // Look for RoomConfigs sheet
        const roomConfigSheetName = workbook.SheetNames.find(name =>
          name.toLowerCase() === 'roomconfigs' ||
          name.toLowerCase() === 'room configs' ||
          name === 'Sheet1'
        ) || workbook.SheetNames[0];

        if (roomConfigSheetName) {
          const roomConfigWorksheet = workbook.Sheets[roomConfigSheetName];
          roomConfigsData = XLSX.utils.sheet_to_json(roomConfigWorksheet, { header: 1 }) as any[][];
        }

        // Look for Rooms sheet
        const roomsSheetName = workbook.SheetNames.find(name =>
          name.toLowerCase() === 'rooms' ||
          name.toLowerCase() === 'room'
        );

        if (roomsSheetName) {
          const roomsWorksheet = workbook.Sheets[roomsSheetName];
          roomsData = XLSX.utils.sheet_to_json(roomsWorksheet, { header: 1 }) as any[][];
          hasRoomsSheet = roomsData.length > 1; // Has data beyond headers
        }

        if (roomConfigsData.length === 0) {
          setPreviewError("File appears to be empty or no RoomConfigs sheet found");
          return;
        }

        // Extract room config headers and rows
        const roomConfigHeaders = roomConfigsData[0] as string[];
        const roomConfigRows = roomConfigsData.slice(1, 6) as any[]; // Limit to 5 for preview

        // Extract room headers and rows if available
        let roomHeaders: string[] = [];
        let roomRows: any[] = [];

        if (hasRoomsSheet && roomsData.length > 0) {
          roomHeaders = roomsData[0] as string[];
          roomRows = roomsData.slice(1, 6) as any[]; // Limit to 5 for preview
        }

        setPreviewData({
          roomConfigs: { headers: roomConfigHeaders, rows: roomConfigRows },
          rooms: { headers: roomHeaders, rows: roomRows },
          hasRoomsSheet
        });

        setFilePreview({
          name: file.name,
          size: (file.size / 1024).toFixed(2) + ' KB',
          type: file.type,
          previewAvailable: true
        });
      } catch (error) {
        console.error('Error parsing file:', error);
        setPreviewError("Could not parse file. Make sure it's a valid Excel or CSV file.");
        setFilePreview({
          name: file.name,
          size: (file.size / 1024).toFixed(2) + ' KB',
          type: file.type,
          previewAvailable: false,
          error: 'Could not generate preview'
        });
      }
    };
    reader.onerror = () => {
      setPreviewError("Error reading file");
    };
    reader.readAsArrayBuffer(file);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setFilePreview(null); // Reset preview when file changes
      setPreviewData({
        roomConfigs: {headers: [], rows: []},
        rooms: {headers: [], rows: []},
        hasRoomsSheet: false
      });
      setPreviewError("");

      // Generate preview for Excel/CSV files
      if (selectedFile.name.endsWith('.xlsx') || selectedFile.name.endsWith('.xls') || selectedFile.name.endsWith('.csv')) {
        parseExcelFile(selectedFile);
      }
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error("Error", {
        description: "Please select a file to upload",
      });
      return;
    }

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append("file", file);

      // Use XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();

      // We're not tracking progress anymore, just using loading state

      // Create a promise to handle the response
      const uploadPromise = new Promise<any>((resolve, reject) => {
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const result = JSON.parse(xhr.responseText);
                resolve(result);
              } catch (error) {
                reject(new Error('Invalid response format'));
              }
            } else {
              try {
                const errorResponse = JSON.parse(xhr.responseText);
                reject(new Error(errorResponse.message || 'Upload failed'));
              } catch (e) {
                reject(new Error(`Upload failed with status ${xhr.status}`));
              }
            }
          }
        };
      });

      // Open and send the request with hotel_id query parameter
      const importUrl = hotelId
        ? `/admin/room-configs/import?hotel_id=${hotelId}`
        : '/admin/room-configs/import';
      xhr.open('POST', importUrl, true);
      xhr.send(formData);

      // Wait for the upload to complete
      const result = await uploadPromise;

      // Process the result
      setUploadResult(result);

      // Calculate total successful imports
      const roomConfigsSuccessful = result.results?.room_configs?.successful || result.results?.successful || 0;
      const roomsSuccessful = result.results?.rooms?.successful || 0;
      const totalSuccessful = roomConfigsSuccessful + roomsSuccessful;

      if (totalSuccessful > 0) {
        let description = '';
        if (result.import_type === 'room_configs_and_rooms') {
          if (roomsSuccessful > 0) {
            description = `${roomConfigsSuccessful} room configurations and ${roomsSuccessful} rooms imported successfully`;
          } else {
            description = `${roomConfigsSuccessful} room configurations imported successfully (rooms failed)`;
          }
        } else {
          description = `${roomConfigsSuccessful} room configurations imported successfully`;
        }

        toast.success("Success", {
          description: description,
        });

        // Set a flag in localStorage to indicate successful import
        // This will be used to determine if we should refresh the page when the modal is closed
        localStorage.setItem('roomConfigImportSuccess', 'true');

        // Notify the parent component that data has been imported successfully
        // This will trigger a refresh of the room configurations list
        // Emit a custom event to refresh the data without closing the modal
        const refreshEvent = new CustomEvent('refreshData');
        window.dispatchEvent(refreshEvent);
      } else {
        // No successful imports, clear the flag
        localStorage.removeItem('roomConfigImportSuccess');
      }

      // Show warnings for failed imports
      const roomConfigsFailed = result.results?.room_configs?.failed || result.results?.failed || 0;
      const roomsFailed = result.results?.rooms?.failed || 0;
      const totalFailed = roomConfigsFailed + roomsFailed;

      if (totalFailed > 0) {
        let description = '';
        if (result.import_type === 'room_configs_and_rooms') {
          if (roomConfigsFailed > 0 && roomsFailed > 0) {
            description = `${roomConfigsFailed} room configurations and ${roomsFailed} rooms failed to import`;
          } else if (roomConfigsFailed > 0) {
            description = `${roomConfigsFailed} room configurations failed to import`;
          } else {
            description = `${roomsFailed} rooms failed to import`;
          }
        } else {
          description = `${roomConfigsFailed} room configurations failed to import`;
        }

        toast.error("Warning", {
          description: description,
        });
      }
    } catch (error: any) {
      console.error("Error uploading file:", error);
      toast.error("Error", {
        description: error.message || "Failed to upload file",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const resetModalState = () => {
    setUploadResult(null);
    setFile(null);
    setFilePreview(null);
    setPreviewData({
      roomConfigs: {headers: [], rows: []},
      rooms: {headers: [], rows: []},
      hasRoomsSheet: false
    });
    setPreviewError("");
  };

  const handleViewRoomConfigs = () => {
    resetModalState();

    // Close the modal first
    onClose();

    // Navigate to the specific hotel's room configurations page if hotelId is provided
    if (hotelId) {
      navigate(`/hotel-management/hotels/${hotelId}/room-configs`);
    } else {
      // Fallback to general room configs page
      navigate("/hotel-management/room-configs");
    }
  };

  return (
    <FocusModal
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          // Check if we had a successful import
          const hadSuccessfulImport = localStorage.getItem('roomConfigImportSuccess') === 'true';

          resetModalState();

          // If we had a successful import, emit a custom event to refresh the data
          if (hadSuccessfulImport) {
            const event = new CustomEvent('closeModal', {
              detail: { refresh: true }
            });
            window.dispatchEvent(event);

            // Clear the flag after using it
            localStorage.removeItem('roomConfigImportSuccess');
          } else {
            // Just close without refreshing
            onClose();
          }
        }
      }}
    >
      <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
        <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center w-full py-4 px-6">
            <div className="flex items-center gap-3">
              <Heading level="h2" className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Import Room Configurations & Rooms
              </Heading>
            </div>

          {/* Progress Indicator */}
          {!uploadResult && (
            <div className="px-6 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Step 1 */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                      1
                    </div>
                    <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">Download</span>
                  </div>
                  <div className="w-8 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                  {/* Step 2 */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      file ? 'bg-green-600 dark:bg-green-500 text-white' : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
                    }`}>
                      2
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      file ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'
                    }`}>Upload</span>
                  </div>
                  <div className={`w-8 h-0.5 ${file ? 'bg-green-300 dark:bg-green-600' : 'bg-gray-300 dark:bg-gray-600'}`}></div>

                  {/* Step 3 */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      filePreview ? 'bg-purple-600 dark:bg-purple-500 text-white' : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
                    }`}>
                      3
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      filePreview ? 'text-purple-600 dark:text-purple-400' : 'text-gray-500 dark:text-gray-400'
                    }`}>Review</span>
                  </div>
                  <div className={`w-8 h-0.5 ${filePreview ? 'bg-purple-300 dark:bg-purple-600' : 'bg-gray-300 dark:bg-gray-600'}`}></div>

                  {/* Step 4 */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      file && filePreview ? 'bg-orange-600 dark:bg-orange-500 text-white' : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
                    }`}>
                      4
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      file && filePreview ? 'text-orange-600 dark:text-orange-400' : 'text-gray-500 dark:text-gray-400'
                    }`}>Import</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          </div>

        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
            <Toaster />

            {!uploadResult ? (
              <>
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-700 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
                      </div>
                      <div className="flex-1">
                        <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                          Download Import Template
                        </Heading>
                        <Text className="text-gray-600 dark:text-gray-300 mb-3">
                          Get the multi-sheet Excel template with pre-configured fields for room configurations and individual rooms.
                          The template includes sample data and reference sheets to help you format your data correctly.
                        </Text>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-md border border-blue-200 dark:border-blue-700 mb-3">
                          <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Template includes:</Text>
                          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                            <li>• <strong className="text-gray-800 dark:text-gray-200">Room Configurations Sheet:</strong> Name, Description, Capacity settings (Handle auto-generated from hotel + name)</li>
                            <li>• <strong className="text-gray-800 dark:text-gray-200">Rooms Sheet (Optional):</strong> Individual rooms with relationships and room-specific data</li>
                            <li>• <strong className="text-gray-800 dark:text-gray-200">Enhanced Capacity Fields:</strong> Max Extra Beds, Max Cots, Max Adults Beyond Capacity</li>
                            <li>• <strong className="text-gray-800 dark:text-gray-200">Bed Types Reference:</strong> Available bed configuration options</li>
                            <li>• <strong className="text-gray-800 dark:text-gray-200">Flexible Import:</strong> Import room configs only OR room configs + individual rooms</li>
                          </ul>
                        </div>
                        <Button
                          variant="secondary"
                          onClick={handleDownloadTemplate}
                          className="flex items-center gap-2 bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600 shadow-lg"
                        >
                          <DownloadIcon className="w-4 h-4" />
                          <span>Download Excel Template</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                        <span className="text-green-600 dark:text-green-400 font-semibold text-sm">2</span>
                      </div>
                      <div className="flex-1">
                        <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                          Upload Your Data File
                        </Heading>
                        <Text className="text-gray-600 dark:text-gray-300 mb-4">
                          Upload the completed Excel file with your room configuration data and optional room data.
                        </Text>
                      </div>
                    </div>

                    <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-gray-400 dark:hover:border-gray-500 transition-colors bg-gray-50 dark:bg-gray-700/50">
                      <input
                        type="file"
                        accept=".xlsx,.xls,.csv"
                        onChange={handleFileChange}
                        className="hidden"
                        id="file-upload"
                      />
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <div className="flex flex-col items-center gap-3">
                          <UploadIcon className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                          <div>
                            <Text className="text-lg font-medium text-gray-700 dark:text-gray-300">
                              Click to upload or drag and drop
                            </Text>
                            <Text className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                              Excel (.xlsx, .xls) and CSV files • Maximum 5MB
                            </Text>
                          </div>
                        </div>
                      </label>
                    </div>

                    {file && (
                      <div className="mt-4">
                        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                              <CheckCircleIcon className="w-4 h-4 text-green-600 dark:text-green-400" />
                            </div>
                            <Text className="font-medium text-green-800 dark:text-green-200">File Selected Successfully</Text>
                          </div>
                          <div className="ml-9">
                            <div className="flex items-center gap-2 mb-1">
                              <FileIcon className="w-4 h-4 text-green-600 dark:text-green-400" />
                              <Text className="font-medium text-green-700 dark:text-green-300">{file.name}</Text>
                            </div>
                            <Text className="text-green-600 dark:text-green-400 text-sm">
                              Size: {(file.size / 1024).toFixed(2)} KB
                            </Text>
                          </div>
                        </div>
                      </div>
                    )}

                    {filePreview && (
                      <div className="mt-6">
                        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                          <div className="flex items-start gap-3 mb-4">
                            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                              <span className="text-purple-600 dark:text-purple-400 font-semibold text-sm">3</span>
                            </div>
                            <div className="flex-1">
                              <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                Review Your Data
                              </Heading>
                              <Text className="text-gray-600 dark:text-gray-300 mb-4">
                                Preview your room configuration and room data before importing to ensure everything looks correct.
                              </Text>
                            </div>
                          </div>

                          {filePreview.previewAvailable ? (
                            <div>
                              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700 mb-4">
                                <Text className="text-blue-800 dark:text-blue-200 font-medium mb-1">Data Validation</Text>
                                <Text className="text-blue-700 dark:text-blue-300 text-sm">
                                  ✓ Required columns: name, max adults<br/>
                                  ✓ Capacity fields: max adults, extra beds, cots, beyond capacity<br/>
                                  ✓ Hotel assignment: automatically assigned to current hotel<br/>
                                  ✓ File format and size are valid<br/>
                                  {previewData.hasRoomsSheet && (
                                    <>✓ Rooms sheet detected: individual rooms will be created<br/></>
                                  )}
                                  ✓ {previewData.hasRoomsSheet ? 'Multi-sheet' : 'Single-sheet'} import ready
                                </Text>
                              </div>

                              {previewData.roomConfigs.headers.length > 0 ? (
                                <div className="space-y-6">
                                  {/* Room Configurations Table */}
                                  <div className="mb-6">
                                    <div className="flex items-center gap-2 mb-3">
                                      <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full"></div>
                                      <Text className="font-semibold text-gray-800 dark:text-gray-200">Room Configurations Data Preview</Text>
                                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">
                                        {previewData.roomConfigs.rows.length} rows
                                      </span>
                                    </div>
                                    <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
                                      <div className="overflow-x-auto">
                                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                          <thead className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
                                            <tr>
                                              {previewData.roomConfigs.headers.map((header: string, index: number) => (
                                                <th
                                                  key={index}
                                                  className="px-4 py-3 text-left text-xs font-semibold text-blue-800 dark:text-blue-200 uppercase tracking-wider"
                                                >
                                                  {header}
                                                </th>
                                              ))}
                                            </tr>
                                          </thead>
                                          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
                                            {previewData.roomConfigs.rows.map((row: any, rowIndex: number) => (
                                              <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white dark:bg-gray-800 hover:bg-blue-25 dark:hover:bg-blue-900/20' : 'bg-blue-25 dark:bg-gray-750 hover:bg-blue-50 dark:hover:bg-blue-900/30'}>
                                                {Array.from({ length: previewData.roomConfigs.headers.length }).map((_, colIndex) => {
                                                  const cellValue = row[colIndex] !== undefined ? String(row[colIndex]) : '-';
                                                  const headerName = previewData.roomConfigs.headers[colIndex];
                                                  const shouldTruncate = shouldTruncateField(headerName);
                                                  const displayValue = shouldTruncate ? truncateText(cellValue) : cellValue;
                                                  const isTruncated = shouldTruncate && cellValue.length > 50;

                                                  return (
                                                    <td
                                                      key={colIndex}
                                                      className={`px-4 py-3 text-sm text-gray-700 dark:text-gray-300 font-medium`}
                                                      title={isTruncated ? `Full text: ${cellValue}` : undefined}
                                                    >
                                                      <div className="flex items-center gap-1">
                                                        <span>{displayValue}</span>
                                                      </div>
                                                    </td>
                                                  );
                                                })}
                                              </tr>
                                            ))}
                                          </tbody>
                                        </table>
                                      </div>
                                    </div>
                                    {previewData.roomConfigs.rows.length > 0 && (
                                      <div className="mt-3 space-y-2">
                                        <div className="flex items-center gap-2">
                                          <div className="w-2 h-2 bg-blue-400 dark:bg-blue-500 rounded-full"></div>
                                          <Text className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                                            Showing first {previewData.roomConfigs.rows.length} room configurations • Ready for import
                                          </Text>
                                        </div>
                                      </div>
                                    )}
                                  </div>

                                  {/* Rooms Table (if available) */}
                                  {previewData.hasRoomsSheet && previewData.rooms.headers.length > 0 && (
                                    <div className="mb-6">
                                      <div className="flex items-center gap-2 mb-3">
                                        <div className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full"></div>
                                        <Text className="font-semibold text-gray-800 dark:text-gray-200">Rooms Data Preview</Text>
                                        <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs font-medium px-2 py-1 rounded-full">
                                          {previewData.rooms.rows.length} rows
                                        </span>
                                      </div>
                                      <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
                                        <div className="overflow-x-auto">
                                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                            <thead className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30">
                                              <tr>
                                                {previewData.rooms.headers.map((header: string, index: number) => (
                                                  <th
                                                    key={index}
                                                    className="px-4 py-3 text-left text-xs font-semibold text-green-800 dark:text-green-200 uppercase tracking-wider"
                                                  >
                                                    {header}
                                                  </th>
                                                ))}
                                              </tr>
                                            </thead>
                                            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
                                              {previewData.rooms.rows.map((row: any, rowIndex: number) => (
                                                <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white dark:bg-gray-800 hover:bg-green-25 dark:hover:bg-green-900/20' : 'bg-green-25 dark:bg-gray-750 hover:bg-green-50 dark:hover:bg-green-900/30'}>
                                                  {Array.from({ length: previewData.rooms.headers.length }).map((_, colIndex) => {
                                                    const cellValue = row[colIndex] !== undefined ? String(row[colIndex]) : '-';
                                                    const headerName = previewData.rooms.headers[colIndex];
                                                    const shouldTruncate = shouldTruncateField(headerName);
                                                    const displayValue = shouldTruncate ? truncateText(cellValue) : cellValue;
                                                    const isTruncated = shouldTruncate && cellValue.length > 50;

                                                    return (
                                                      <td
                                                        key={colIndex}
                                                        className={`px-4 py-3 text-sm text-gray-700 dark:text-gray-300 font-medium`}
                                                        title={isTruncated ? `Full text: ${cellValue}` : undefined}
                                                      >
                                                        <div className="flex items-center gap-1">
                                                          <span>{displayValue}</span>
                                                        </div>
                                                      </td>
                                                    );
                                                  })}
                                                </tr>
                                              ))}
                                            </tbody>
                                          </table>
                                        </div>
                                      </div>
                                      {previewData.rooms.rows.length > 0 && (
                                        <div className="mt-3 space-y-2">
                                          <div className="flex items-center gap-2">
                                            <div className="w-2 h-2 bg-green-400 dark:bg-green-500 rounded-full"></div>
                                            <Text className="text-green-600 dark:text-green-400 text-sm font-medium">
                                              Showing first {previewData.rooms.rows.length} rooms • Will be linked to room configurations
                                            </Text>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  {/* Import Type Indicator */}
                                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                    <div className="flex items-center gap-3">
                                      {/* <div>
                                        <Text className="font-medium text-gray-900 dark:text-gray-100">
                                          {previewData.hasRoomsSheet ? 'Multi-Sheet Import' : 'Room Configurations Only'}
                                        </Text>
                                        <Text className="text-sm text-gray-600 dark:text-gray-400">
                                          {previewData.hasRoomsSheet
                                            ? `Will create ${previewData.roomConfigs.rows.length} room configurations and ${previewData.rooms.rows.length} individual rooms`
                                            : `Will create ${previewData.roomConfigs.rows.length} room configurations only`
                                          }
                                        </Text>
                                      </div> */}
                                      <Text className="text-sm text-gray-600 dark:text-gray-400">
                                          {previewData.hasRoomsSheet
                                            ? `Will create ${previewData.roomConfigs.rows.length} room configurations and ${previewData.rooms.rows.length} individual rooms`
                                            : `Will create ${previewData.roomConfigs.rows.length} room configurations only`
                                          }
                                        </Text>
                                    </div>
                                  </div>
                                </div>
                              ) : previewError ? (
                                <Text className="text-red-500 dark:text-red-400">{previewError}</Text>
                              ) : (
                                <Text className="text-gray-500 dark:text-gray-400">Loading preview...</Text>
                              )}
                            </div>
                          ) : (
                            <Text className="text-gray-500 dark:text-gray-400">
                              {filePreview.error || "Preview not available for this file type."}
                            </Text>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Success Header */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-lg border border-green-200 dark:border-green-700 shadow-sm">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                      <CheckCircleIcon className="w-8 h-8 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1">
                      <Heading level="h3" className="text-xl font-semibold text-green-800 dark:text-green-200 mb-1">
                        {uploadResult.import_type === 'room_configs_and_rooms' ? 'Multi-Sheet Import Completed!' : 'Import Completed Successfully!'}
                      </Heading>
                      <Text className="text-green-700 dark:text-green-300">
                        {uploadResult.import_type === 'room_configs_and_rooms'
                          ? 'Your room configurations and rooms have been imported and are now available in the system.'
                          : 'Your room configurations have been imported and are now available in the system.'
                        }
                      </Text>
                    </div>
                  </div>
                </div>

                {/* Import Summary */}
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex flex-col gap-6">
                    <div className="flex items-center justify-between">
                      <Heading level="h3" className="text-lg font-medium text-gray-800 dark:text-gray-200">
                        Import Summary
                      </Heading>
                      <div className="flex items-center gap-2">
                        <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">
                          {uploadResult.import_type === 'room_configs_and_rooms' ? 'Multi-Sheet Import' : 'Room Configs Only'}
                        </span>
                        <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs font-medium px-2 py-1 rounded-full">
                          {uploadResult.processing_time}
                        </span>
                      </div>
                    </div>

                    {/* Overall Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full"></div>
                          <Text className="font-semibold text-blue-800 dark:text-blue-200">Room Configurations</Text>
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <Text className="text-sm text-blue-700 dark:text-blue-300">Total:</Text>
                            <Text className="text-sm font-medium text-blue-800 dark:text-blue-200">{uploadResult.summary?.room_configs?.total || uploadResult.results?.room_configs?.successful + uploadResult.results?.room_configs?.failed || 0}</Text>
                          </div>
                          <div className="flex justify-between">
                            <Text className="text-sm text-green-700 dark:text-green-300">Successful:</Text>
                            <Text className="text-sm font-medium text-green-800 dark:text-green-200">{uploadResult.results?.room_configs?.successful || uploadResult.results?.successful || 0}</Text>
                          </div>
                          <div className="flex justify-between">
                            <Text className="text-sm text-red-700 dark:text-red-300">Failed:</Text>
                            <Text className="text-sm font-medium text-red-800 dark:text-red-200">{uploadResult.results?.room_configs?.failed || uploadResult.results?.failed || 0}</Text>
                          </div>
                        </div>
                      </div>

                      {uploadResult.import_type === 'room_configs_and_rooms' && (
                        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full"></div>
                            <Text className="font-semibold text-green-800 dark:text-green-200">Rooms</Text>
                          </div>
                          <div className="space-y-1">
                            <div className="flex justify-between">
                              <Text className="text-sm text-green-700 dark:text-green-300">Total:</Text>
                              <Text className="text-sm font-medium text-green-800 dark:text-green-200">{uploadResult.summary?.rooms?.total || uploadResult.results?.rooms?.successful + uploadResult.results?.rooms?.failed || 0}</Text>
                            </div>
                            <div className="flex justify-between">
                              <Text className="text-sm text-green-700 dark:text-green-300">Successful:</Text>
                              <Text className="text-sm font-medium text-green-800 dark:text-green-200">{uploadResult.results?.rooms?.successful || 0}</Text>
                            </div>
                            <div className="flex justify-between">
                              <Text className="text-sm text-red-700 dark:text-red-300">Failed:</Text>
                              <Text className="text-sm font-medium text-red-800 dark:text-red-200">{uploadResult.results?.rooms?.failed || 0}</Text>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
                          <Text className="font-semibold text-gray-800 dark:text-gray-200">Processing</Text>
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <Text className="text-sm text-gray-700 dark:text-gray-300">Sheets:</Text>
                            <Text className="text-sm font-medium text-gray-800 dark:text-gray-200">{uploadResult.summary?.total_sheets_processed || 1}</Text>
                          </div>
                          <div className="flex justify-between">
                            <Text className="text-sm text-gray-700 dark:text-gray-300">Time:</Text>
                            <Text className="text-sm font-medium text-gray-800 dark:text-gray-200">{uploadResult.processing_time}</Text>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Successful Items */}
                {(uploadResult.results?.room_configs?.created?.length > 0 || uploadResult.results?.rooms?.created?.length > 0) && (
                  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div className="flex flex-col gap-4">
                      <Heading level="h3" className="text-lg font-medium text-green-800 dark:text-green-200 mb-2">
                        Successfully Created Items
                      </Heading>

                      {/* Room Configurations */}
                      {uploadResult.results?.room_configs?.created?.length > 0 && (
                        <div className="mb-4">
                          <div className="flex items-center gap-2 mb-3">
                            <CheckCircleIcon className="w-5 h-5 text-green-500 dark:text-green-400" />
                            <Text className="font-semibold text-gray-800 dark:text-gray-200">Room Configurations ({uploadResult.results.room_configs.created.length})</Text>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {uploadResult.results.room_configs.created.map((item: any, index: number) => (
                              <div key={index} className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-md">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <Text className="font-medium text-green-800 dark:text-green-200">{item.name}</Text>
                                    <Text className="text-sm text-green-600 dark:text-green-400">Handle: {item.handle}</Text>
                                  </div>
                                  <span className="bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-200 text-xs px-2 py-1 rounded">Row {item.row}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Rooms */}
                      {uploadResult.results?.rooms?.created?.length > 0 && (
                        <div>
                          <div className="flex items-center gap-2 mb-3">
                            <CheckCircleIcon className="w-5 h-5 text-green-500 dark:text-green-400" />
                            <Text className="font-semibold text-gray-800 dark:text-gray-200">Rooms ({uploadResult.results.rooms.created.length})</Text>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {uploadResult.results.rooms.created.map((item: any, index: number) => (
                              <div key={index} className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-md">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <Text className="font-medium text-green-800 dark:text-green-200">Room {item.room_number}</Text>
                                    <Text className="text-sm text-green-600 dark:text-green-400">Config: {item.room_config_name}</Text>
                                  </div>
                                  <span className="bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-200 text-xs px-2 py-1 rounded">Row {item.row}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Errors */}
                {((uploadResult.results?.room_configs?.errors?.length > 0) || (uploadResult.results?.rooms?.errors?.length > 0)) && (
                  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div className="flex flex-col gap-4">
                      <Heading level="h3" className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">
                        Import Errors
                      </Heading>

                      {/* Room Config Errors */}
                      {uploadResult.results?.room_configs?.errors?.length > 0 && (
                        <div className="mb-4">
                          <div className="flex items-center gap-2 mb-3">
                            <XCircleIcon className="w-5 h-5 text-red-500 dark:text-red-400" />
                            <Text className="font-semibold text-gray-800 dark:text-gray-200">Room Configuration Errors ({uploadResult.results.room_configs.errors.length})</Text>
                          </div>
                          <div className="space-y-3">
                            {uploadResult.results.room_configs.errors.map((error: any, index: number) => (
                              <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-md">
                                <div className="flex justify-between items-start mb-2">
                                  <Text className="font-medium text-red-800 dark:text-red-200">Row {error.row}</Text>
                                  <span className="bg-red-100 dark:bg-red-800 text-red-700 dark:text-red-200 text-xs px-2 py-1 rounded">Error</span>
                                </div>
                                <Text className="text-sm text-red-700 dark:text-red-300">{error.error}</Text>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Room Errors */}
                      {uploadResult.results?.rooms?.errors?.length > 0 && (
                        <div>
                          <div className="flex items-center gap-2 mb-3">
                            <XCircleIcon className="w-5 h-5 text-red-500 dark:text-red-400" />
                            <Text className="font-semibold text-gray-800 dark:text-gray-200">Room Errors ({uploadResult.results.rooms.errors.length})</Text>
                          </div>
                          <div className="space-y-3">
                            {uploadResult.results.rooms.errors.map((error: any, index: number) => (
                              <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-md">
                                <div className="flex justify-between items-start mb-2">
                                  <Text className="font-medium text-red-800 dark:text-red-200">{error.row ? `Row ${error.row}` : 'Validation Error'}</Text>
                                  <span className="bg-red-100 dark:bg-red-800 text-red-700 dark:text-red-200 text-xs px-2 py-1 rounded">Error</span>
                                </div>
                                <Text className="text-sm text-red-700 dark:text-red-300">{error.error}</Text>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            {!uploadResult ? (
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {file ? `Ready to import: ${file.name}` : 'Select a file to import'}
                </div>
                <div className="flex gap-4">
                  <Button
                    variant="secondary"
                    onClick={() => {
                      resetModalState();
                      onClose();
                    }}
                    className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleUpload}
                    disabled={!file || isUploading}
                    className={`flex items-center gap-3 px-6 py-3 font-medium ${
                      !file || isUploading
                        ? 'bg-blue-300 dark:bg-blue-800 cursor-not-allowed text-blue-100 dark:text-blue-300 opacity-60 border-blue-300 dark:border-blue-800'
                        : 'bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white shadow-lg'
                    }`}
                  >
                    {isUploading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Importing...
                      </>
                    ) : (
                      <>
                        <UploadIcon className="w-5 h-5" />
                        Import Data
                      </>
                    )}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Import completed successfully
                </div>
                <div className="flex gap-4">
                  <Button
                    variant="secondary"
                    onClick={() => {
                      // Reset the upload result and file to allow re-upload
                      resetModalState();
                    }}
                    className="flex items-center gap-3 px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                  >
                    <UploadIcon className="w-5 h-5" />
                    Upload Another File
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => {
                      // Check if we had a successful import
                      const hadSuccessfulImport = localStorage.getItem('roomConfigImportSuccess') === 'true';

                      resetModalState();
                      // Use a direct close with refresh flag based on whether we had a successful import
                      if (open) {
                        const event = new CustomEvent('closeModal', {
                          detail: { refresh: hadSuccessfulImport }
                        });
                        window.dispatchEvent(event);

                        // Clear the flag after using it
                        localStorage.removeItem('roomConfigImportSuccess');
                      }
                    }}
                    className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                  >
                    Close
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleViewRoomConfigs}
                    className="flex items-center gap-3 px-6 py-3 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white shadow-lg font-medium"
                  >
                    <CheckCircleIcon className="w-5 h-5" />
                    View Room Configurations
                  </Button>
                </div>
              </div>
            )}
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default BulkImportModal;
