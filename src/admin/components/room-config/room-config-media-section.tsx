import React, { useState } from "react";
import { Text, Label } from "@camped-ai/ui";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  DropAnimation,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
} from "@dnd-kit/sortable";
import { UseFormReturn, useFieldArray } from "react-hook-form";

import MediaItem, { MediaField } from "../hotel/media-item";
import MediaGridItemOverlay from "../hotel/media-grid-item-overlay";
import { defaultDropAnimationSideEffects } from "@dnd-kit/core";

export type RoomConfigFormData = {
  id: string;
  name: string;
  handle?: string;
  description: string;
  is_active?: boolean;
  hotel_id: string;
  type: string;
  bed_type: string;
  room_size: string;
  max_adults: number;
  max_adults_beyond_capacity?: number;
  max_children: number;
  max_infants: number;
  max_occupancy: number;
  max_extra_beds?: number;
  max_cots?: number;
  amenities: string[];
  tags?: string[];
  media: MediaField[];
  image_ids?: string[];
  thumbnail_id?: string;
};

const dropAnimationConfig: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.4",
      },
    },
  }),
};

const RoomConfigMediaSection = ({
  form,
}: {
  form: UseFormReturn<RoomConfigFormData>;
}) => {
  const { fields, append, remove } = useFieldArray({
    name: "media",
    control: form.control,
    keyName: "field_id",
  });

  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // 5px movement to start dragging
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (active.id !== over?.id) {
      const oldIndex = fields.findIndex(
        (field) => field.field_id === active.id
      );
      const newIndex = fields.findIndex((field) => field.field_id === over?.id);

      form.setValue("media", arrayMove(fields, oldIndex, newIndex), {
        shouldDirty: true,
        shouldTouch: true,
      });
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  const getOnDelete = (index: number) => {
    return async () => {
      const mediaToDelete = fields[index];
      const formValues = form.getValues();
      const productId = formValues.id;

      console.log("DELETING IMAGE - FORM VALUES:", formValues);
      console.log("DELETING IMAGE - Media to delete:", mediaToDelete);
      console.log("DELETING IMAGE - Product ID:", productId);

      // If the image has an ID and we have a product ID, attempt to delete from the server
      if (mediaToDelete.id && productId) {
        try {
          // First try the direct approach - this is what works in the hotel images
          console.log(
            `Trying direct DELETE to /admin/products/${productId}/images/${mediaToDelete.id}`
          );

          const response = await fetch(
            `/admin/products/${productId}/images/${mediaToDelete.id}`,
            {
              method: "DELETE",
              credentials: "include",
            }
          );

          if (response.ok) {
            console.log("Image deleted successfully with direct approach");
          } else {
            console.log("Direct approach failed, trying fallback");

            // Fallback approach - update the product with all images except the one to delete
            const remainingImages = fields
              .filter((field, i) => i !== index)
              .filter((field) => field.id) // Only include images that have IDs (are saved on server)
              .map((field) => ({
                id: field.id,
                url: field.url,
              }));

            console.log("Remaining images:", remainingImages);

            // Update the product with the new images array
            const updateResponse = await fetch(`/admin/products/${productId}`, {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              credentials: "include",
              body: JSON.stringify({
                images: remainingImages,
              }),
            });

            if (!updateResponse.ok) {
              const errorText = await updateResponse.text();
              throw new Error(`Failed to delete image: ${errorText}`);
            } else {
              console.log("Image deleted successfully with fallback approach");
            }
          }
        } catch (error) {
          console.error("Error deleting image:", error);
          // Optionally show a toast or error message to the user
          return;
        }
      } else {
        console.warn("Cannot delete image: missing image ID or product ID");
        console.warn("Image ID:", mediaToDelete.id);
        console.warn("Product ID:", productId);
      }

      // Remove the image from the form
      remove(index);
    };
  };

  const getMakeThumbnail = (index: number) => {
    return () => {
      const newFields = fields.map((field, i) => ({
        ...field,
        isThumbnail: i === index,
      }));

      form.setValue("media", newFields, {
        shouldDirty: true,
        shouldTouch: true,
      });
    };
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach((file) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          append({
            url: e.target?.result as string,
            file,
            isThumbnail: fields.length === 0,
            field_id: crypto.randomUUID(),
          });
        };
        reader.readAsDataURL(file);
      });
    }
  };

  return (
    <div id="media" className="flex flex-col gap-y-2">
      <div className="flex justify-between items-center">
        <Text>Room Images</Text>
        <input
          type="file"
          multiple
          accept="image/*"
          onChange={handleMediaUpload}
          className="hidden"
          id="media-upload"
        />
        <Label
          htmlFor="media-upload"
          className="px-4 py-2 rounded cursor-pointer"
        >
          Upload Images
        </Label>
      </div>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        {fields.length > 0 ? (
          <DragOverlay dropAnimation={dropAnimationConfig}>
            {activeId ? (
              <MediaGridItemOverlay
                field={
                  fields.find(
                    (field) => field.field_id === activeId
                  ) as MediaField
                }
              />
            ) : null}
          </DragOverlay>
        ) : null}
        <ul className="flex flex-col gap-y-2">
          <SortableContext items={fields.map((field) => field.field_id)}>
            {fields.map((field, index) => {
              const onDelete = getOnDelete(index);
              const onMakeThumbnail = getMakeThumbnail(index);

              return (
                <MediaItem
                  key={field.field_id}
                  field={field}
                  onDelete={onDelete}
                  onMakeThumbnail={onMakeThumbnail}
                />
              );
            })}
          </SortableContext>
        </ul>
      </DndContext>
    </div>
  );
};

export default RoomConfigMediaSection;
