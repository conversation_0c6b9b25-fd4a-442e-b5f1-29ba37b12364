import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@camped-ai/ui";
import { Languages } from "lucide-react";
import {
  useAITextTranslation,
  TextTranslationRequest,
} from "../hooks/useAITextTranslation";

interface AITranslateButtonProps {
  sourceText: string;
  targetLanguage: string;
  fieldType: "name" | "description" | "title" | "content";
  onTranslate: (translatedText: string) => void;
  context?: {
    entityType?: "hotel" | "destination" | "cancellation_policy";
    entityName?: string;
    location?: string;
  };
  disabled?: boolean;
  className?: string;
  size?: "small" | "base" | "large";
  variant?: "primary" | "secondary" | "transparent";
  showText?: boolean;
  allowEmptySource?: boolean; // New prop to allow showing button even with empty source text
}

/**
 * AI Translation Button Component
 *
 * Provides a button that translates individual text fields using AI
 *
 * @example
 * <AITranslateButton
 *   sourceText="Standard Cancellation Policy"
 *   targetLanguage="de"
 *   fieldType="name"
 *   onTranslate={(translated) => setTranslatedName(translated)}
 *   context={{
 *     entityType: "cancellation_policy",
 *     entityName: "Hotel ABC"
 *   }}
 * />
 */
const AITranslateButton: React.FC<AITranslateButtonProps> = ({
  sourceText,
  targetLanguage,
  fieldType,
  onTranslate,
  context = {},
  disabled = false,
  className = "",
  size = "small",
  variant = "secondary",
  showText = false,
  allowEmptySource = false,
}) => {
  const { translateText, isTranslating, error } = useAITextTranslation({
    onSuccess: (translatedText) => {
      console.log("🎉 AI translation completed:", translatedText);
      onTranslate(translatedText);
    },
    onError: (error) => {
      console.error("❌ AI translation failed:", error);
    },
  });

  // Check if translation should be available
  const hasSourceText = sourceText && sourceText.trim().length > 0;
  const shouldShowTranslation =
    (hasSourceText || allowEmptySource) &&
    targetLanguage &&
    targetLanguage !== "en" &&
    !disabled;

  // Handle AI translation
  const handleTranslation = async () => {
    if (!shouldShowTranslation || isTranslating) return;

    // For empty source text, we can't translate, so show a helpful message
    if (!hasSourceText) {
      console.warn(
        "Cannot translate empty text. Please enter some text first."
      );
      return;
    }

    const request: TextTranslationRequest = {
      text: sourceText,
      targetLanguage,
      fieldType,
      context,
    };

    await translateText(request);
  };

  // Don't render if conditions aren't met
  if (!shouldShowTranslation) {
    return null;
  }

  const buttonContent = (
    <>
      <Languages
        size={size === "small" ? 12 : size === "large" ? 18 : 14}
        className={`${isTranslating ? "animate-spin" : ""} ${showText ? "mr-1" : ""
          }`}
      />
      {showText && (
        <span className="text-xs">
          {isTranslating ? "Translating..." : "Translate"}
        </span>
      )}
    </>
  );

  const tooltipText = error
    ? `Translation error: ${error}`
    : isTranslating
      ? "Translating with AI..."
      : !hasSourceText
        ? `Enter text in base language first to translate to ${targetLanguage.toUpperCase()}`
        : `Translate ${fieldType} to ${targetLanguage.toUpperCase()} with AI`;

  return (
    <Tooltip content={tooltipText}>
      <Button
        type="button"
        variant={variant}
        size={size}
        onClick={handleTranslation}
        disabled={isTranslating || disabled}
        className={`
          ${showText ? "px-2 py-1" : "p-1"} 
          text-blue-600 border border-blue-200 hover:bg-blue-50 
          disabled:opacity-50 disabled:cursor-not-allowed
          ${error ? "border-red-200 text-red-600 hover:bg-red-50" : ""}
          ${className}
        `}
      >
        {buttonContent}
      </Button>
    </Tooltip>
  );
};

export default AITranslateButton;
