import React from "react";
import { Badge } from "@camped-ai/ui";
import { UserRole } from "../../hooks/use-rbac";

interface RoleBadgeProps {
  role?: UserRole;
  isActive?: boolean;
  showInactive?: boolean;
  size?: "small" | "default" | "large";
}

export const RoleBadge: React.FC<RoleBadgeProps> = ({
  role,
  isActive = true,
  showInactive = true,
  size = "default",
}) => {
  if (!role) {
    return showInactive ? (
      <Badge variant="secondary" size={size}>
        No Role
      </Badge>
    ) : null;
  }

  if (!isActive) {
    return showInactive ? (
      <Badge variant="danger" size={size}>
        Inactive
      </Badge>
    ) : null;
  }

  const getRoleConfig = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return {
          label: "Admin",
          variant: "success" as const,
          icon: "👑",
        };

      default:
        return {
          label: role.replace("_", " "),
          variant: "secondary" as const,
          icon: "👤",
        };
    }
  };

  const config = getRoleConfig(role);

  return (
    <Badge variant={config.variant} size={"small"}>
      <span className="flex items-center gap-1">
        {/* <span>{config.icon}</span> */}
        <span>{config.label}</span>
      </span>
    </Badge>
  );
};
