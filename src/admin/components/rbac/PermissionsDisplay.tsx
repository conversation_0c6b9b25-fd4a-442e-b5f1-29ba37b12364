import React from "react";
import { Container, Badge } from "@camped-ai/ui";
import { Box } from "@mui/material";
import { useRbac } from "../../hooks/use-rbac";

interface Permission {
  key: string;
  label: string;
  description: string;
  granted: boolean;
}

interface PermissionsDisplayProps {
  userId?: string;
  compact?: boolean;
}

export const PermissionsDisplay: React.FC<PermissionsDisplayProps> = ({
  userId,
  compact = false,
}) => {
  const { currentUser, isAdmin, isHotelManager, canCreateHotels, canManageUsers } = useRbac();

  // Use current user if no userId provided
  const user = userId ? null : currentUser; // For now, only support current user
  
  if (!user?.rbac) {
    return compact ? (
      <Badge variant="secondary">No Permissions</Badge>
    ) : (
      <Container className="p-4">
        <Box textAlign="center" sx={{ color: 'text.secondary' }}>
          No role assigned - no permissions granted
        </Box>
      </Container>
    );
  }

  const permissions: Permission[] = [
    {
      key: "create_hotels",
      label: "Create Hotels",
      description: "Can create new hotel properties",
      granted: canCreateHotels(),
    },
    {
      key: "manage_users",
      label: "Manage Users",
      description: "Can create, edit, and assign roles to users",
      granted: canManageUsers(),
    },
    {
      key: "access_all_hotels",
      label: "Access All Hotels",
      description: "Can view and manage all hotel properties",
      granted: isAdmin(),
    },
    {
      key: "manage_hotel_operations",
      label: "Manage Hotel Operations",
      description: "Can manage rooms, bookings, and hotel settings",
      granted: isAdmin() || isHotelManager(),
    },
    {
      key: "view_analytics",
      label: "View Analytics",
      description: "Can access analytics and reporting features",
      granted: isAdmin() || isHotelManager(),
    },
  ];

  if (compact) {
    const grantedCount = permissions.filter(p => p.granted).length;
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <Badge variant="default">
          {grantedCount}/{permissions.length} Permissions
        </Badge>
        {user.rbac.assigned_hotels.length > 0 && (
          <Badge variant="secondary">
            {user.rbac.assigned_hotels.length} Hotels
          </Badge>
        )}
      </Box>
    );
  }

  return (
    <Container className="p-4">
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <h3 style={{ fontSize: '1.125rem', fontWeight: 500 }}>Permissions</h3>
          <Badge variant="default">
            {user.rbac.role.replace("_", " ").toUpperCase()}
          </Badge>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
          {permissions.map((permission) => (
            <Box
              key={permission.key}
              sx={{
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
                p: 1.5,
                border: 1,
                borderColor: 'divider',
                borderRadius: 1
              }}
            >
              <Box sx={{ flex: 1 }}>
                <Box display="flex" alignItems="center" gap={1}>
                  <span style={{ fontWeight: 500 }}>{permission.label}</span>
                  <Badge
                    variant={permission.granted ? "success" : "secondary"}
                    size="small"
                  >
                    {permission.granted ? "Granted" : "Denied"}
                  </Badge>
                </Box>
                <p style={{ fontSize: '0.875rem', color: '#666', marginTop: '0.25rem' }}>
                  {permission.description}
                </p>
              </Box>
            </Box>
          ))}
        </Box>

        {user.rbac.assigned_hotels.length > 0 && (
          <Box sx={{ borderTop: 1, borderColor: 'divider', pt: 2 }}>
            <h4 style={{ fontWeight: 500, marginBottom: '0.5rem' }}>Assigned Hotels</h4>
            <Box display="flex" flexWrap="wrap" gap={1}>
              {user.rbac.assigned_hotels.map((hotelId) => (
                <Badge key={hotelId} variant="secondary">
                  {hotelId}
                </Badge>
              ))}
            </Box>
          </Box>
        )}

        <Box sx={{ borderTop: 1, borderColor: 'divider', pt: 2, fontSize: '0.75rem', color: 'text.disabled' }}>
          <div>Role assigned: {new Date(user.rbac.updated_at || "").toLocaleString()}</div>
          {user.rbac.updated_by && (
            <div>Updated by: {user.rbac.updated_by}</div>
          )}
        </Box>
      </Box>
    </Container>
  );
};
