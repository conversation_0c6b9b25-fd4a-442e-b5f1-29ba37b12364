import React from "react";
import { useRbac } from "../../hooks/use-rbac";
import { AdminOnly } from "./RoleGuard";

interface NavigationItem {
  label: string;
  href: string;
  icon?: string;
  description?: string;
}

export const RoleBasedNavigation: React.FC = () => {
  const { currentUser, isAdmin, isHotelManager, hasPermission } = useRbac();

  if (!currentUser?.rbac) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="text-yellow-800">
          No role assigned. Contact an administrator to get access.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        Navigation based on your role:{" "}
        <strong>
          {currentUser.rbac.role
            ? currentUser.rbac.role.replace("_", " ")
            : currentUser.rbac.role_id
              ? "Custom Role"
              : "No Role"
          }
        </strong>
      </div>

      {/* Admin Only Navigation */}
      <AdminOnly>
        <div className="space-y-2">
          <h3 className="font-medium text-gray-900">Admin Functions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <NavigationCard
              label="User Management"
              href="/app/settings/user-management"
              icon="👥"
              description="Manage users, invites, and assign roles"
            />
            <NavigationCard
              label="Role Management"
              href="/app/settings/roles"
              icon="🛡️"
              description="Configure roles and permissions"
            />
            <NavigationCard
              label="System Settings"
              href="/admin/settings/system"
              icon="⚙️"
              description="Configure system-wide settings"
            />
            <NavigationCard
              label="All Hotels"
              href="/admin/hotel-management/hotels"
              icon="🏨"
              description="Manage all hotel properties"
            />
            <NavigationCard
              label="Bulk Import"
              href="/admin/settings/bulk-import"
              icon="📊"
              description="Import destinations, hotels, room configs & rooms"
            />
            <NavigationCard
              label="Analytics Dashboard"
              href="/admin/analytics"
              icon="📈"
              description="View system-wide analytics"
            />
          </div>
        </div>
      </AdminOnly>

      {/* Permission-Based Navigation - For users with specific permissions */}
      {!isAdmin() && currentUser.rbac && currentUser.rbac.role_id && (
        <div className="space-y-2">
          <h3 className="font-medium text-gray-900">Hotel Management</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {hasPermission("hotel_management:view") && (
              <NavigationCard
                label="Hotels"
                href="/admin/hotel-management/hotels"
                icon="🏨"
                description="Manage hotels"
              />
            )}
            {hasPermission("destinations:view") && (
              <NavigationCard
                label="Destinations"
                href="/admin/hotel-management/destinations"
                icon="🌍"
                description="Manage destinations"
              />
            )}
            {hasPermission("room_management:view") && (
              <NavigationCard
                label="Room Management"
                href="/admin/hotel-management/rooms"
                icon="🛏️"
                description="Manage rooms and configurations"
              />
            )}
            {hasPermission("booking_management:view") && (
              <NavigationCard
                label="Bookings"
                href="/admin/hotel-management/bookings"
                icon="📅"
                description="View and manage bookings"
              />
            )}
            {hasPermission("analytics:view") && (
              <NavigationCard
                label="Hotel Analytics"
                href="/admin/hotel-analytics"
                icon="📈"
                description="View hotel performance metrics"
              />
            )}
          </div>
        </div>
      )}

      {/* Common Navigation for All Roles */}
      {currentUser.rbac && (
        <div className="space-y-2">
          <h3 className="font-medium text-gray-900">Common Functions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <NavigationCard
              label="Profile Settings"
              href="/app/settings/profile"
              icon="👤"
              description="Manage your profile"
            />
            <NavigationCard
              label="Notifications"
              href="/admin/notifications"
              icon="🔔"
              description="View notifications"
            />
          </div>
        </div>
      )}


    </div>
  );
};

interface NavigationCardProps extends NavigationItem {}

const NavigationCard: React.FC<NavigationCardProps> = ({
  label,
  href,
  icon,
  description,
}) => {
  return (
    <a
      href={href}
      className="block p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors"
    >
      <div className="flex items-start gap-3">
        {icon && <span className="text-lg">{icon}</span>}
        <div>
          <div className="font-medium text-gray-900">{label}</div>
          {description && (
            <div className="text-sm text-gray-600">{description}</div>
          )}
        </div>
      </div>
    </a>
  );
};
