import React from "react";
import { useRbac, UserRole } from "../../hooks/use-rbac";

interface RoleGuardProps {
  children: React.ReactNode;
  roles?: UserRole[];
  requirePermission?: string; // Screen permission
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
}

/**
 * Component that conditionally renders children based on user roles and permissions
 */
export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  roles,
  requirePermission,
  fallback = null,
  loading: loadingComponent = <div>Loading...</div>,
}) => {
  const { currentUser, loading, hasRole, hasPermission } = useRbac();

  if (loading) {
    return <>{loadingComponent}</>;
  }

  // No user or no RBAC data
  if (!currentUser?.rbac) {
    return <>{fallback}</>;
  }

  // Check role requirements
  if (roles && roles.length > 0) {
    const hasRequiredRole = roles.some(role => hasRole(role));
    if (!hasRequiredRole) {
      return <>{fallback}</>;
    }
  }

  // Check permission requirements
  if (requirePermission && !hasPermission(requirePermission)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// Convenience components for common role checks
export const AdminOnly: React.FC<Omit<RoleGuardProps, 'roles'>> = (props) => (
  <RoleGuard {...props} roles={[UserRole.ADMIN]} />
);

// Note: HotelManagerOnly and AdminOrHotelManager are deprecated
// Use permission-based guards instead since hotel_manager is now a custom role
