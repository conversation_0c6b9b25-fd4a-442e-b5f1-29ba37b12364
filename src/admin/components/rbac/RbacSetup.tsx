import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Container } from "@camped-ai/ui";
import { Box, CircularProgress } from "@mui/material";
import { useRbac } from "../../hooks/use-rbac";
import { sdk } from "../../lib/sdk";

interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
}

export const RbacSetup: React.FC = () => {
  const { checkSetupStatus, setupInitialAdmin, currentUser, isAdmin } =
    useRbac();
  const [setupStatus, setSetupStatus] = useState<any>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUserId, setSelectedUserId] = useState("");
  const [loading, setLoading] = useState(true);
  const [setting, setSetting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load setup status and users
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // If current user is already an admin, skip setup
      if (currentUser?.rbac?.role === "admin" && currentUser?.rbac?.is_active) {
        setSetupStatus({
          needs_setup: false,
          admin_count: 1,
          message: "Role system is configured and you have admin access.",
        });
        setLoading(false);
        return;
      }

      // Check setup status
      const status = await checkSetupStatus();

      setSetupStatus(status);

      // If setup is needed, load users
      if (status.needs_setup) {
        const usersResponse = await sdk.admin.user.list();
        setUsers(usersResponse.users || []);
      }
    } catch (err) {
      // If API fails but user has admin role, assume system is configured
      if (currentUser?.rbac?.role === "admin" && currentUser?.rbac?.is_active) {
        setSetupStatus({
          needs_setup: false,
          admin_count: 1,
          message: "Role system is configured and you have admin access.",
        });
      } else {
        setError(
          err instanceof Error ? err.message : "Failed to load setup data"
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // Setup initial admin
  const handleSetupAdmin = async () => {
    if (!selectedUserId) return;

    try {
      setSetting(true);
      setError(null);
      setSuccess(null);

      await setupInitialAdmin(selectedUserId);
      setSuccess(
        "Admin role assigned successfully! RBAC system is now active."
      );

      // Reload status
      await loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to setup admin");
    } finally {
      setSetting(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [currentUser]);

  if (loading) {
    return (
      <Container className="p-6">
        <Box display="flex" alignItems="center" gap={2}>
          <CircularProgress size={20} />
          <span>Checking RBAC setup status...</span>
        </Box>
      </Container>
    );
  }

  // RBAC is already configured
  if (setupStatus && !setupStatus.needs_setup) {
    return (
      <Container className="p-6">
        <Box
          textAlign="center"
          sx={{ display: "flex", flexDirection: "column", gap: 2 }}
        >
          <Box
            sx={{
              color: "success.main",
              fontSize: "1.125rem",
              fontWeight: 500,
            }}
          >
            ✅ Role System Configured
          </Box>
          <Box sx={{ color: "text.secondary" }}>{setupStatus.message}</Box>
          <Box sx={{ fontSize: "0.875rem", color: "text.disabled" }}>
            Admin users: {setupStatus.admin_count}
          </Box>
        </Box>
      </Container>
    );
  }

  // Setup is needed
  return (
    <Container className="p-6">
      <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
        <Box textAlign="center">
          <h2
            style={{
              fontSize: "1.25rem",
              fontWeight: 700,
              marginBottom: "0.5rem",
            }}
          >
            Role System Setup
          </h2>
          <p style={{ color: "#666" }}>
            The role management system needs initial configuration. Please
            assign an admin role to a user to get started.
          </p>
        </Box>

        {error && <Alert variant="error">{error}</Alert>}

        {success && <Alert variant="success">{success}</Alert>}

        {setupStatus?.needs_setup && (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Box>
              <label
                style={{
                  display: "block",
                  fontSize: "0.875rem",
                  fontWeight: 500,
                  marginBottom: "0.5rem",
                }}
              >
                Select User to Make Admin
              </label>
              <select
                value={selectedUserId}
                onChange={(e) => setSelectedUserId(e.target.value)}
                disabled={setting}
                style={{
                  width: "100%",
                  padding: "0.5rem",
                  border: "1px solid #ccc",
                  borderRadius: "0.375rem",
                  fontSize: "0.875rem",
                }}
              >
                <option value="">Choose a user...</option>
                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.email} ({user.first_name} {user.last_name})
                  </option>
                ))}
              </select>
              <p
                style={{
                  fontSize: "0.75rem",
                  color: "#666",
                  marginTop: "0.25rem",
                }}
              >
                This user will have full administrative access to the system.
              </p>
            </Box>

            <Box display="flex" justifyContent="center">
              <Button
                onClick={handleSetupAdmin}
                disabled={!selectedUserId || setting}
                size="large"
              >
                Setup Admin Role
              </Button>
            </Box>
          </Box>
        )}

        <Box sx={{ borderTop: 1, borderColor: "divider", pt: 2 }}>
          <h3 style={{ fontWeight: 500, marginBottom: "0.5rem" }}>
            What happens next?
          </h3>
          <ul
            style={{
              fontSize: "0.875rem",
              color: "#666",
              listStyle: "none",
              padding: 0,
            }}
          >
            <li style={{ marginBottom: "0.25rem" }}>
              • The selected user will be assigned the Admin role
            </li>
            <li style={{ marginBottom: "0.25rem" }}>
              • Admin users can manage other users and assign roles
            </li>
            <li style={{ marginBottom: "0.25rem" }}>
              • Hotel-specific access controls will be enforced
            </li>
            <li style={{ marginBottom: "0.25rem" }}>
              • You can create Hotel Manager roles for property-specific access
            </li>
          </ul>
        </Box>
      </Box>
    </Container>
  );
};
