import { useState, useEffect, forwardRef, useImperative<PERSON><PERSON>le } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Container, Select } from "@camped-ai/ui";
import { CircularProgress } from "@mui/material";
import { useRbac, useUserRole, UserRole } from "../../hooks/use-rbac";

interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  created_at: string;
}

export interface UserRoleManagerRef {
  handleUpdateRole: () => Promise<void>;
  canUpdateRole: boolean;
  updating: boolean;
  selectedRole: UserRole | string | "";
  roleData: any;
}

interface UserRoleManagerProps {
  user: User;
  availableRoles?: any[]; // Array of available roles (system + custom)
  isOwner?: boolean; // Whether this user is the owner
  onRoleUpdated?: () => void;
  onRoleUpdateComplete?: () => void;
  onStateChange?: (state: {
    canUpdateRole: boolean;
    updating: boolean;
    roleData: any;
  }) => void;
}

export const UserRoleManager = forwardRef<
  UserRoleManagerRef,
  UserRoleManagerProps
>(
  (
    {
      user,
      availableRoles = [],
      isOwner = false,
      onRoleUpdated,
      onRoleUpdateComplete,
      onStateChange,
    },
    ref
  ) => {
    const { setUserRole } = useRbac();

    // Use the useUserRole hook to get user role data
    const {
      data: roleData,
      isLoading: loading,
      refetch: refetchUserRole,
    } = useUserRole(user.id);

    // Force refresh user role data when component mounts or user changes
    useEffect(() => {
      setHasUserManuallySelected(false); // Reset manual selection flag
      setSelectedRole(""); // Reset selected role
      setError(null); // Clear any previous errors
      refetchUserRole();
    }, [user.id, refetchUserRole]);

    const [updating, setUpdating] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedRole, setSelectedRole] = useState<UserRole | string | "">(
      ""
    );
    const [hasUserManuallySelected, setHasUserManuallySelected] =
      useState(false);

    // Update user role
    const handleUpdateRole = async () => {
      if (!selectedRole) return;

      try {
        setUpdating(true);
        setError(null);

        // Find the selected role from available roles to determine the correct API format
        const selectedRoleData = availableRoles.find(
          (role) => role.id === selectedRole
        );

        if (!selectedRoleData) {
          throw new Error("Selected role not found");
        }

        // Use the role ID directly - it should already be in the correct format
        // For system roles: role.id should be "admin"
        // For custom roles: role.id should be the database ID
        let roleOrRoleId = selectedRole;

        await setUserRole({
          userId: user.id,
          roleOrRoleId: roleOrRoleId,
        });

        // Wait for cache invalidation to propagate
        await new Promise((resolve) => setTimeout(resolve, 200));

        // Force refetch the user role data multiple times to ensure it's updated
        await refetchUserRole();

        // Wait and refetch again to ensure we get the latest data
        await new Promise((resolve) => setTimeout(resolve, 200));
        await refetchUserRole();

        // Reset manual selection flag so the component can reflect the updated role
        setHasUserManuallySelected(false);

        onRoleUpdated?.();
        onRoleUpdateComplete?.(); // Close the modal
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to update role");
      } finally {
        setUpdating(false);
      }
    };

    // Update selectedRole when roleData changes (but respect manual selections)
    useEffect(() => {
      // Don't override if user has manually selected a role
      if (hasUserManuallySelected) {
        return;
      }

      // Proceed if we have role data (even if rbac is null) and available roles
      if (roleData && availableRoles.length > 0) {
        // If user has no RBAC data, they have no role assigned
        if (!roleData.rbac) {
          setSelectedRole("");
          return;
        }
        // For custom roles, use role_id directly
        if (roleData.rbac.role_id) {
          // Verify the role exists in availableRoles
          const matchingRole = availableRoles.find(
            (role) => role.id === roleData.rbac.role_id
          );
          if (matchingRole) {
            setSelectedRole(roleData.rbac.role_id);
          } else {
            setSelectedRole(roleData.rbac.role_id); // Set it anyway, might be a timing issue
          }
        }
        // For system roles, find the matching role from availableRoles
        else if (roleData.rbac.role) {
          // Find the system role that matches the user's current role
          const matchingSystemRole = availableRoles.find(
            (role) =>
              role.isSystemRole &&
              (role.id === roleData.rbac.role ||
                role.name.toLowerCase() === roleData.rbac.role.toLowerCase() ||
                (roleData.rbac.role === "admin" &&
                  role.name.toLowerCase().includes("admin")))
          );

          if (matchingSystemRole) {
            setSelectedRole(matchingSystemRole.id);
          } else {
            setSelectedRole(roleData.rbac.role);
          }
        } else {
          setSelectedRole("");
        }
      } else {
        setSelectedRole("");
      }
    }, [roleData, availableRoles, hasUserManuallySelected]);

    // Notify parent of state changes
    useEffect(() => {
      onStateChange?.({
        canUpdateRole: !!selectedRole,
        updating,
        roleData,
      });
    }, [selectedRole, updating, roleData, onStateChange]);

    // Expose functions to parent component
    useImperativeHandle(ref, () => ({
      handleUpdateRole,
      canUpdateRole: !!selectedRole,
      updating,
      selectedRole,
      roleData,
    }));

    if (loading) {
      return (
        <Container className="p-4">
          <div className="flex items-center gap-2">
            <CircularProgress size={20} />
            <span>Loading role data...</span>
          </div>
        </Container>
      );
    }

    return (
      <Container className="p-4">
        <div className="flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Role Management</h3>
            {roleData?.rbac && (
              <Badge color={roleData.rbac.is_active ? "green" : "red"}>
                {roleData.rbac.is_active ? "Active" : "Inactive"}
              </Badge>
            )}
          </div>

          {error && <Alert variant="error">{error}</Alert>}

          <div className="flex flex-col gap-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Current Role
              </label>
              {roleData?.rbac ? (
                <div className="flex flex-col gap-2 text-sm">
                  <div>
                    {roleData.rbac.is_active === false ? (
                      <Badge className="text-xs px-2 py-1 rounded-full bg-red-100 text-red-800 border border-red-200 dark:bg-red-500/20 dark:text-red-300 dark:border-red-500/40">
                        Role Removed
                      </Badge>
                    ) : (
                      <Badge className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 border border-blue-200 dark:bg-blue-500/20 dark:text-blue-300 dark:border-blue-500/40">
                        {roleData.rbac.role_id
                          ? availableRoles.find(
                              (r) => r.id === roleData.rbac.role_id
                            )?.name || roleData.rbac.role_id
                          : roleData.rbac.role
                              ?.replace("_", " ")
                              .toUpperCase() || "Unknown Role"}
                      </Badge>
                    )}
                  </div>
                  <div className="text-muted-foreground">
                    Updated:{" "}
                    {new Date(
                      roleData.rbac.updated_at || ""
                    ).toLocaleDateString()}
                  </div>
                  {roleData.rbac.is_active === false &&
                    roleData.rbac.deactivated_at && (
                      <div className="text-red-600 text-xs">
                        Deactivated:{" "}
                        {new Date(
                          roleData.rbac.deactivated_at
                        ).toLocaleDateString()}
                      </div>
                    )}
                </div>
              ) : (
                <div className="text-muted-foreground">No role assigned</div>
              )}
            </div>
          </div>

          {!isOwner && roleData?.rbac?.is_active !== false && (
            <div className="border-t border-gray-200 pt-4">
              <h4 className="font-medium mb-3">Update Role</h4>

              <div className="flex flex-col gap-3">
                <div>
                  <label className="block text-sm font-medium mb-1">Role</label>
                  <Select
                    value={selectedRole}
                    onValueChange={(value) => {
                      setSelectedRole(value);
                      setHasUserManuallySelected(true);
                    }}
                    disabled={updating || availableRoles.length === 0}
                  >
                    <Select.Trigger className="w-full">
                      <Select.Value placeholder="Select a role" />
                    </Select.Trigger>
                    <Select.Content>
                      {availableRoles.length > 0 ? (
                        availableRoles.map((role) => {
                          // For both system and custom roles, use the role ID
                          const roleValue = role.id;

                          return (
                            <Select.Item key={role.id} value={roleValue}>
                              {role.name}{" "}
                              {role.isSystemRole ? "(System)" : "(Custom)"}
                            </Select.Item>
                          );
                        })
                      ) : (
                        <Select.Item value="" disabled>
                          No roles available
                        </Select.Item>
                      )}
                    </Select.Content>
                  </Select>
                </div>
              </div>
            </div>
          )}
          {!isOwner && roleData?.rbac?.is_active === false && (
            <div className="border-t border-gray-200 pt-4">
              <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">!</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-orange-800">
                      User Deactivated
                    </h4>
                    <p className="text-sm text-orange-600">
                      This user is deactivated and has no role assigned.
                      Reactivate the user first to assign a new role.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          {isOwner && (
            <div className="border-t border-gray-200 pt-4">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">!</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-800">Owner Account</h4>
                    <p className="text-sm text-blue-600">
                      This is the owner account and cannot be modified or
                      deactivated.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Container>
    );
  }
);

UserRoleManager.displayName = "UserRoleManager";
