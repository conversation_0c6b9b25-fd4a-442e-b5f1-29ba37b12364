import React, { useState, useEffect } from "react";
import {
  FocusModal,
  Heading,
  Text,
  Button,
  Label,
  Select,
  DatePicker,
  toast,
} from "@camped-ai/ui";
import {
  Download,
  CheckCircle,
  Settings,
  Filter,
  FileText,
} from "lucide-react";

interface Hotel {
  id: string;
  name: string;
  slug: string;
  // Add other hotel properties as needed
}

interface ExportModalProps {
  open: boolean;
  onClose: () => void;
  hotelId?: string; // This can be either hotel ID or hotel slug
  currentHotel?: Hotel | null; // Current hotel data for pre-population
}

const ExportModal: React.FC<ExportModalProps> = ({
  open,
  onClose,
  hotelId,
  currentHotel,
}) => {
  // Export process state
  const [currentStep, setCurrentStep] = useState<
    "configure" | "exporting" | "success"
  >("configure");
  const [isExporting, setIsExporting] = useState(false);
  const [exportResult, setExportResult] = useState<{
    url?: string;
    filename?: string;
  } | null>(null);

  // State for selected fields - inventory_item_id column 1, hotel_id column 2, include room config name
  const [selectedFields, setSelectedFields] = useState<Record<string, boolean>>(
    {
      inventory_item_id: true,  // Column 1
      hotel_id: true,           // Column 2
      room_number: true,
      room_config_name: true,   // Room configuration name
      from_date: true,
      to_date: true,
      available_quantity: true,
      status: true,
      notes: true,
      check_in_time: true,
      check_out_time: true,
      is_noon_to_noon: true,
    }
  );

  // Room reference sheet is always included automatically for import compatibility

  // Field labels mapping - include inventory_item_id and room config name
  const fieldLabels: Record<string, string> = {
    inventory_item_id: "Inventory Item ID",
    room_number: "Room Number",
    room_config_name: "Room Configuration Name",
    hotel_id: "Hotel ID",
    from_date: "From Date",
    to_date: "To Date",
    available_quantity: "Available Quantity",
    status: "Status",
    notes: "Notes",
    check_in_time: "Check-in Time",
    check_out_time: "Check-out Time",
    is_noon_to_noon: "Noon to Noon",
  };

  // Reset modal state when opening
  const resetModalState = () => {
    setCurrentStep("configure");
    setIsExporting(false);
    setExportResult(null);
  };

  // State for filters - use current hotel's ID if available, otherwise fall back to hotelId or "all"
  const [filters, setFilters] = useState({
    from_date: formatDate(new Date()),
    to_date: formatDate(addDays(new Date(), 30)),
    status: "all", // "all", "available", "unavailable", "booked"
    hotel_id: currentHotel?.id || hotelId || "all", // Pre-populate with current hotel's ID
  });

  // State for available hotels
  const [hotels, setHotels] = useState<
    Array<{ id: string; name: string; slug?: string }>
  >([]);
  const [isLoadingHotels, setIsLoadingHotels] = useState(false);

  // No need to fetch rooms for now

  // State for file format
  const [fileFormat, setFileFormat] = useState<"csv" | "xlsx">("xlsx");

  // Helper function to format date as YYYY-MM-DD
  function formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  // Helper function to add days to a date
  function addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  // Quick filter functions
  const applyQuickFilter = (days: number) => {
    const today = new Date();
    const fromDate = formatDate(today);
    const toDate = formatDate(addDays(today, days));

    setFilters(prev => ({
      ...prev,
      from_date: fromDate,
      to_date: toDate,
    }));
  };

  const quickFilterOptions = [
    { label: "Next 7 days", days: 7 },
    { label: "Next 30 days", days: 30 },
    { label: "Next 90 days", days: 90 },
  ];

  // Fetch hotels when modal opens
  useEffect(() => {
    if (open) {
      console.log("ExportModal opened with hotelId:", hotelId, "currentHotel:", currentHotel);
      fetchHotels();

      // If we have the current hotel data, ensure it's selected in the dropdown
      if (currentHotel?.id) {
        setFilters(prev => ({
          ...prev,
          hotel_id: currentHotel.id
        }));
      }
    }
  }, [open, currentHotel]);

  // Fetch hotels for the dropdown
  const fetchHotels = async () => {
    setIsLoadingHotels(true);
    try {
      const response = await fetch("/admin/hotel-management/hotels", {
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch hotels");
      }

      const { hotels: hotelsList } = await response.json();

      // Format hotels for the dropdown
      const formattedHotels = hotelsList.map((hotel: any) => ({
        id: hotel.id,
        name: hotel.name,
        slug: hotel.slug,
      }));

      // Ensure current hotel is in the list if it exists
      if (currentHotel && !formattedHotels.find(h => h.id === currentHotel.id)) {
        formattedHotels.unshift({
          id: currentHotel.id,
          name: currentHotel.name,
          slug: currentHotel.slug,
        });
      }

      setHotels(formattedHotels);
    } catch (error) {
      console.error("Error fetching hotels:", error);
    } finally {
      setIsLoadingHotels(false);
    }
  };

  // Room fetching removed for simplicity

  // Handle field selection
  const handleFieldChange = (field: string, checked: boolean) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  // Handle select all fields
  const handleSelectAll = () => {
    const allFields = { ...selectedFields };
    const allSelected = Object.values(allFields).every((value) => value);

    Object.keys(allFields).forEach((key) => {
      allFields[key] = !allSelected;
    });

    setSelectedFields(allFields);
  };

  // Handle filter changes
  const handleFilterChange = (filterName: string, value: string | boolean) => {
    setFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }));
  };

  // Handle export
  const handleExport = async () => {
    try {
      setIsExporting(true);
      setCurrentStep("exporting");

      // Validate date range
      if (!filters.from_date || !filters.to_date) {
        throw new Error("From date and To date are required");
      }

      const fromDate = new Date(filters.from_date);
      const toDate = new Date(filters.to_date);

      if (fromDate > toDate) {
        throw new Error("From date must be before To date");
      }

      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add fields
      const fields = Object.entries(selectedFields)
        .filter(([_, selected]) => selected)
        .map(([field]) => field);

      if (fields.length === 0) {
        throw new Error("Please select at least one field to export");
      }

      queryParams.append("fields", fields.join(","));

      // Room reference sheet is always included for import compatibility
      queryParams.append("include_room_reference", "true");

      // Add filters
      console.log("Export filters being sent:", {
        from_date: filters.from_date,
        to_date: filters.to_date,
        status: filters.status,
        hotel_id: filters.hotel_id,
        original_hotelId_prop: hotelId,
      });
      queryParams.append("from_date", filters.from_date);
      queryParams.append("to_date", filters.to_date);

      if (filters.status !== "all") {
        queryParams.append("status", filters.status);
      }

      if (filters.hotel_id !== "all") {
        queryParams.append("hotel_id", filters.hotel_id);
      }

      // Add format
      queryParams.append("format", fileFormat);

      // Use the new hotel-management export endpoint for proper authentication
      const exportUrl = `/admin/hotel-management/room-inventory/export?${queryParams.toString()}`;
      console.log("Export URL:", exportUrl);

      // Simulate processing time for better UX
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Create filename
      const filename = `room-inventory-export-${filters.from_date}-to-${filters.to_date}.${fileFormat}`;

      // Open in a new tab for download
      window.open(exportUrl, "_blank");

      // Set success state
      setExportResult({ url: exportUrl, filename });
      setCurrentStep("success");

      toast.success("Export completed successfully!", {
        description: `Your room inventory data has been exported as ${filename}`,
      });

      console.log("Export request successful");
    } catch (error) {
      console.error("Error exporting room inventory:", error);
      setCurrentStep("configure");

      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to export room inventory";

      toast.error("Export Failed", {
        description: errorMessage,
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <FocusModal
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          resetModalState();
          onClose();
        }
      }}
    >
      <FocusModal.Content className="flex flex-col ">
        <FocusModal.Header className="flex-shrink-0">
          <div className="flex justify-between w-full items-center py-4">
            <Heading level="h2" className="text-xl font-semibold">
              Export Room Inventory
            </Heading>
            {/* Progress Indicator */}
            {currentStep !== "success" && (
              <div className="px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {/* Step 1 */}
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                        1
                      </div>
                      <span className="ml-2 text-sm font-medium text-blue-600">
                        Configure
                      </span>
                    </div>
                    <div className="w-8 h-0.5 bg-gray-300"></div>

                    {/* Step 2 */}
                    <div className="flex items-center">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                          currentStep === "exporting"
                            ? "bg-orange-600 text-white"
                            : "bg-gray-300 text-gray-600"
                        }`}
                      >
                        2
                      </div>
                      <span
                        className={`ml-2 text-sm font-medium ${
                          currentStep === "exporting"
                            ? "text-orange-600"
                            : "text-gray-500"
                        }`}
                      >
                        Export
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

        </FocusModal.Header>
        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
            {currentStep === "configure" && (
              <>
                {/* Filters Section */}
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex items-center gap-2 mb-4">
                    <Filter className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      Filter Room Inventory
                    </Heading>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <Label htmlFor="from_date" className="text-gray-700 dark:text-gray-200">From Date*</Label>
                      <DatePicker
                        key={`from_date_${filters.from_date}`}
                        value={
                          filters.from_date
                            ? new Date(filters.from_date + 'T00:00:00')
                            : undefined
                        }
                        onChange={(date) =>
                          handleFilterChange(
                            "from_date",
                            date ? formatDate(date) : ""
                          )
                        }
                        className="w-full"
                      />
                    </div>

                    <div>
                      <Label htmlFor="to_date" className="text-gray-700 dark:text-gray-200">To Date*</Label>
                      <DatePicker
                        key={`to_date_${filters.to_date}`}
                        value={
                          filters.to_date
                            ? new Date(filters.to_date + 'T00:00:00')
                            : undefined
                        }
                        onChange={(date) =>
                          handleFilterChange(
                            "to_date",
                            date ? formatDate(date) : ""
                          )
                        }
                        className="w-full"
                      />
                    </div>

                    <div>
                      <Label htmlFor="status" className="text-gray-700 dark:text-gray-300">Status</Label>
                      <Select
                        value={filters.status}
                        onValueChange={(value) =>
                          handleFilterChange("status", value)
                        }
                      >
                        <Select.Trigger>
                          <Select.Value placeholder="Select status" />
                        </Select.Trigger>
                        <Select.Content>
                          <Select.Item value="all">
                            All Statuses
                          </Select.Item>
                          <Select.Item value="available">
                            Available
                          </Select.Item>
                          <Select.Item value="booked">Booked</Select.Item>
                          <Select.Item value="reserved">
                            Reserved
                          </Select.Item>
                          <Select.Item value="maintenance">
                            Maintenance
                          </Select.Item>
                          <Select.Item value="cleaning">
                            Cleaning
                          </Select.Item>
                          <Select.Item value="unavailable">
                            Unavailable
                          </Select.Item>
                          <Select.Item value="reserved_unassigned">
                            Reserved Unassigned
                          </Select.Item>
                          <Select.Item value="cart_reserved">
                            Cart Reserved
                          </Select.Item>
                          <Select.Item value="on_demand">
                            On Request
                          </Select.Item>
                        </Select.Content>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="hotel_id" className="text-gray-700 dark:text-gray-300">Hotel</Label>
                      {isLoadingHotels ? (
                        <div className="flex items-center justify-center py-2">
                          <div className="animate-spin h-4 w-4 border-2 border-blue-500 dark:border-blue-400 border-t-transparent rounded-full mr-2"></div>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Loading hotels...
                          </span>
                        </div>
                      ) : (
                        <Select
                          value={filters.hotel_id}
                          onValueChange={(value) =>
                            handleFilterChange("hotel_id", value)
                          }
                        >
                          <Select.Trigger>
                            <Select.Value placeholder="Select hotel" />
                          </Select.Trigger>
                          <Select.Content>
                            <Select.Item value="all">
                              All Hotels
                            </Select.Item>
                            {hotels.map((hotel) => (
                              <Select.Item
                                key={hotel.id}
                                value={hotel.id}
                                className={currentHotel && hotel.id === currentHotel.id ? "font-semibold bg-blue-50 dark:bg-blue-900/20" : ""}
                              >
                                {hotel.name}
                                {currentHotel && hotel.id === currentHotel.id && " (Current)"}
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select>
                      )}
                    </div>
                  </div>

                  {/* Quick Date Filters */}
                  <div className="mt-4">
                    <Label className="text-gray-700 dark:text-gray-200 mb-3 block">Quick Date Filters</Label>
                    <div className="flex flex-wrap gap-2">
                      {quickFilterOptions.map((option) => (
                        <Button
                          key={option.label}
                          variant="secondary"
                          size="small"
                          onClick={() => applyQuickFilter(option.days)}
                          className="text-sm px-3 py-1.5 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                          {option.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Import Fields Section */}
                  <div className="lg:col-span-2 bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                          <FileText className="w-5 h-5 text-blue-600 dark:text-blue-300" />
                        </div>
                        <div>
                          <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            Export Data Fields
                          </Heading>
                          <Text className="text-sm text-gray-600 dark:text-gray-300">
                            Select fields to include in export
                          </Text>
                        </div>
                      </div>
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={handleSelectAll}
                        className="text-sm px-4 py-2 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg font-medium transition-colors duration-200 text-gray-700 dark:text-gray-200"
                      >
                        {Object.values(selectedFields).every((v) => v)
                          ? "Deselect All"
                          : "Select All"}
                      </Button>
                    </div>

                    <div className="rounded-lg p-4 border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-[320px] overflow-y-auto pr-2">
                        {Object.keys(selectedFields).map((field) => (
                          <div key={field} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-150">
                            <input
                              type="checkbox"
                              id={`field-${field}`}
                              checked={selectedFields[field]}
                              onChange={(e) =>
                                handleFieldChange(field, e.target.checked)
                              }
                              className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded border-gray-300 dark:border-gray-500 dark:bg-gray-600"
                            />
                            <Label
                              htmlFor={`field-${field}`}
                              className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-200 flex-1"
                            >
                              {fieldLabels[field] || field}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="mt-4 text-sm text-gray-600 dark:text-gray-300">
                      <span>
                        {Object.values(selectedFields).filter(Boolean).length} of {Object.keys(selectedFields).length} fields selected
                      </span>
                    </div>
                  </div>

                  {/* Export Options Section */}
                  <div className="space-y-6">
                    {/* Export Format */}
                    <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                          <FileText className="w-4 h-4 text-orange-600 dark:text-orange-300" />
                        </div>
                        <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                          Export Format
                        </Heading>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                          <input
                            type="radio"
                            id="format-xlsx"
                            name="fileFormat"
                            value="xlsx"
                            checked={fileFormat === "xlsx"}
                            onChange={() => setFileFormat("xlsx")}
                            className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 dark:bg-gray-600"
                          />
                          <Label
                            htmlFor="format-xlsx"
                            className="cursor-pointer text-gray-700 dark:text-gray-200 flex-1"
                          >
                            <div className="font-medium">Excel (.xlsx)</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">Recommended for import compatibility</div>
                          </Label>
                        </div>

                        <div className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                          <input
                            type="radio"
                            id="format-csv"
                            name="fileFormat"
                            value="csv"
                            checked={fileFormat === "csv"}
                            onChange={() => setFileFormat("csv")}
                            className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 dark:bg-gray-600"
                          />
                          <Label
                            htmlFor="format-csv"
                            className="cursor-pointer text-gray-700 dark:text-gray-200 flex-1"
                          >
                            <div className="font-medium">CSV (.csv)</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">Simple text format</div>
                          </Label>
                        </div>
                      </div>
                    </div>

                    {/* Export Summary */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-700">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center">
                          <Settings className="w-4 h-4 text-blue-600 dark:text-blue-300" />
                        </div>
                        <Heading level="h3" className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                          Export Summary
                        </Heading>
                      </div>

                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between items-center">
                          <span className="text-blue-700 dark:text-blue-300">Fields Selected:</span>
                          <span className="font-medium text-blue-900 dark:text-blue-100">
                            {Object.values(selectedFields).filter(Boolean).length}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-blue-700 dark:text-blue-300">Date Range:</span>
                          <span className="font-medium text-blue-900 dark:text-blue-100">
                            {filters.from_date} to {filters.to_date}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-blue-700 dark:text-blue-300">Status Filter:</span>
                          <span className="font-medium text-blue-900 dark:text-blue-100 capitalize">
                            {filters.status === "all" ? "All Statuses" : filters.status}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-blue-700 dark:text-blue-300">Format:</span>
                          <span className="font-medium text-blue-900 dark:text-blue-100 uppercase">
                            {fileFormat}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}

            {currentStep === "exporting" && (
              <div className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 p-8 rounded-lg border border-orange-200 dark:border-orange-700 shadow-sm">
                <div className="flex flex-col items-center gap-4">
                  <div className="w-16 h-16 bg-orange-100 dark:bg-orange-800 rounded-full flex items-center justify-center">
                    <div className="w-8 h-8 border-4 border-orange-600 dark:border-orange-400 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                  <div className="text-center">
                    <Heading
                      level="h3"
                      className="text-xl font-semibold text-orange-800 dark:text-orange-200 mb-2"
                    >
                      Exporting Your Data
                    </Heading>
                    <Text className="text-orange-700 dark:text-orange-300">
                      Please wait while we prepare your room inventory export
                      file...
                    </Text>
                  </div>
                </div>
              </div>
            )}

            {currentStep === "success" && exportResult && (
              <>
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-lg border border-green-200 dark:border-green-700 shadow-sm">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-300" />
                    </div>
                    <div className="flex-1">
                      <Heading
                        level="h3"
                        className="text-xl font-semibold text-green-800 dark:text-green-200 mb-1"
                      >
                        Export Completed Successfully!
                      </Heading>
                      <Text className="text-green-700 dark:text-green-300">
                        Your room inventory data has been exported and the
                        download should start automatically.
                      </Text>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <Heading
                    level="h3"
                    className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4"
                  >
                    Export Details
                  </Heading>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      <Text className="font-medium text-gray-700 dark:text-gray-200">
                        Filename: {exportResult.filename}
                      </Text>
                    </div>
                    <div className="flex items-center gap-2">
                      <Download className="w-5 h-5 text-green-600 dark:text-green-400" />
                      <Text className="text-gray-700 dark:text-gray-200">Format: {fileFormat.toUpperCase()}</Text>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </FocusModal.Body>
        <div className="flex-shrink-0 py-6 px-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-t border-gray-200 dark:border-gray-600">
          {currentStep === "configure" && (
            <div className="flex gap-3 justify-end">
              <Button
                variant="secondary"
                onClick={() => {
                  resetModalState();
                  onClose();
                }}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleExport}
                disabled={isExporting}
                className="flex items-center gap-2 px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg"
              >
                <Download className="w-4 h-4" />
                Export Data
              </Button>
            </div>
          )}

          {currentStep === "exporting" && (
            <div className="flex gap-3 justify-end">
              <Button
                variant="secondary"
                disabled
                className="px-6 opacity-50 cursor-not-allowed"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                disabled
                className="flex items-center gap-2 px-6 py-2 bg-orange-400 cursor-not-allowed"
              >
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Exporting...
              </Button>
            </div>
          )}

          {currentStep === "success" && (
            <div className="flex gap-3 justify-end">
              <Button
                variant="secondary"
                onClick={() => {
                  resetModalState();
                }}
                className="flex items-center gap-2 px-6"
              >
                <Download className="w-4 h-4" />
                Export Again
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  resetModalState();
                  onClose();
                }}
                className="flex items-center gap-2 px-6 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 shadow-lg"
              >
                <CheckCircle className="w-4 h-4" />
                Done
              </Button>
            </div>
          )}
        </div>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default ExportModal;
