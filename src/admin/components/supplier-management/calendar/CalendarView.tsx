import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { CalendarViewType } from "./calendar";
import { EventCard } from "./EventCard";
import {
  Text,
  Heading,
  Button,
  Badge,
  Table,
  Select,
  FocusModal,
  DropdownMenu,
  toast,
} from "@camped-ai/ui";
import {
  startOfWeek,
  addDays,
  format,
  startOfMonth,
  endOfMonth,
  startOfDay,
  endOfDay,
} from "date-fns";
import { ExternalLink } from "lucide-react";
import { BookingAddon } from "../../../hooks/supplier-management/use-booking-addons";
import { useCreateSupplierOrders } from "../../../hooks/supplier-management/use-create-supplier-orders";
import {
  CreateSupplierOrdersModal,
  SupplierOrderCreationData,
} from "./CreateSupplierOrdersModal";
import { useCategories } from "../../../hooks/supplier-products-services/use-categories";

interface CalendarViewProps {
  offerings: any;
  currentDate: Date;
  viewType: CalendarViewType;
  onEventClick: (event: any) => void;
  expandedDays?: Set<string>;
  onExpandedDaysChange?: (expandedDays: Set<string>) => void;
  bookingAddonsData?: any;
  isLoadingAddons?: boolean;
  onRefreshData?: () => void; // Callback to refresh booking add-ons data
}

// Helper functions for status display
const getStatusLabel = (status?: string) => {
  switch (status) {
    case "pending":
      return "Pending";
    case "confirmed":
      return "Confirmed";
    case "in_progress":
      return "In Progress";
    case "completed":
      return "Completed";
    case "cancelled":
      return "Cancelled";
    default:
      return "Unknown";
  }
};

const getStatusBadgeColor = (status?: string) => {
  switch (status) {
    case "pending":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "confirmed":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "in_progress":
      return "bg-purple-100 text-purple-800 border-purple-200";
    case "completed":
      return "bg-green-100 text-green-800 border-green-200";
    case "cancelled":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

export function CalendarView({
  offerings,
  currentDate,
  viewType,
  onEventClick,
  expandedDays = new Set(),
  onExpandedDaysChange,
  bookingAddonsData,
  isLoadingAddons,
  onRefreshData,
}: CalendarViewProps) {
  const navigate = useNavigate();
  const [localExpandedDays, setLocalExpandedDays] = useState<Set<string>>(
    new Set()
  );

  // Use prop expandedDays if provided, otherwise use local state
  const activeExpandedDays = onExpandedDaysChange
    ? expandedDays
    : localExpandedDays;
  const setExpandedDays = onExpandedDaysChange || setLocalExpandedDays;

  // Category filtering state (single select)
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  // Order status filtering state
  const [selectedOrderStatus, setSelectedOrderStatus] = useState<string>("");

  // Add modal state
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedAddonForAdd, setSelectedAddonForAdd] =
    useState<BookingAddon | null>(null);

  // Fetch categories for filtering and display
  const { data: categoriesData, isLoading: isLoadingCategories } =
    useCategories({
      is_active: true,
      limit: 100,
    });

  const categories = categoriesData?.categories || [];

  // Order status options for filtering
  const orderStatusOptions = [
    { value: "all", label: "All statuses" },
    { value: "not_ordered", label: "Not Ordered" },
    { value: "pending", label: "Pending" },
    { value: "confirmed", label: "Confirmed" },
    { value: "in_progress", label: "In Progress" },
    { value: "completed", label: "Completed" },
    { value: "cancelled", label: "Cancelled" },
  ];

  // Create a map of category ID to category name for quick lookup
  const categoryMap = useMemo(() => {
    const map = new Map<string, string>();
    categories.forEach((category) => {
      map.set(category.id, category.name);
    });
    return map;
  }, [categories]);

  // Filter booking addons based on selected category and order status
  const filteredBookingAddons = useMemo(() => {
    const bookingAddons = bookingAddonsData?.booking_addons || [];

    return bookingAddons.filter((addon: BookingAddon) => {
      // Category filter
      if (selectedCategory) {
        const categoryId = (addon.add_on?.metadata as any)?.category;
        if (categoryId !== selectedCategory) {
          return false;
        }
      }

      // Order status filter
      if (selectedOrderStatus && selectedOrderStatus !== "all") {
        if (selectedOrderStatus === "not_ordered") {
          // Show add-ons without supplier orders
          if (addon.supplier_order_id) {
            return false;
          }
        } else {
          // Show add-ons with specific order status
          if (
            !addon.supplier_order_id ||
            addon.order_status !== selectedOrderStatus
          ) {
            return false;
          }
        }
      }

      return true;
    });
  }, [
    bookingAddonsData?.booking_addons,
    selectedCategory,
    selectedOrderStatus,
  ]);

  const getWeekDays = () => {
    const start = startOfWeek(currentDate, { weekStartsOn: 0 });
    return Array.from({ length: 7 }, (_, i) => addDays(start, i));
  };

  const getOfferingsForDate = (date: Date) => {
    return offerings.filter((offering) => {
      const activeFrom = new Date(offering.active_from);
      const activeTo = new Date(offering.active_to);
      const dayStart = startOfDay(date);
      const dayEnd = endOfDay(date);

      return (
        (activeFrom <= dayEnd && activeTo >= dayStart) ||
        (activeFrom <= dayStart && activeTo >= dayEnd)
      );
    });
  };

  // State for modal
  const [isCreateOrderModalOpen, setIsCreateOrderModalOpen] = useState(false);
  const [selectedAddonsForOrder, setSelectedAddonsForOrder] = useState<
    BookingAddon[]
  >([]);

  // Hook for creating supplier orders
  const createSupplierOrdersMutation = useCreateSupplierOrders();

  // Open the order creation modal for multiple add-ons
  const handleCreateSupplierOrders = (addons: BookingAddon[]) => {
    console.log("🔍 Opening modal for multiple add-ons:", addons.length);
    setSelectedAddonsForOrder(addons);
    setIsCreateOrderModalOpen(true);
  };

  // Create order for a single add-on
  const handleCreateOrderForAddon = (addon: BookingAddon) => {
    console.log("🔍 Creating order for single add-on:", addon);
    setSelectedAddonsForOrder([addon]);
    setIsCreateOrderModalOpen(true);
  };

  // Handle actual order creation from modal
  const handleConfirmCreateOrders = async (
    orderData: SupplierOrderCreationData[]
  ) => {
    try {
      console.log("🔍 Creating supplier orders from modal data...", orderData);

      // Transform modal data to API format
      const supplier_groups = orderData.map((order) => ({
        supplier_id: order.supplier_id,
        supplier_name: order.supplier_name,
        addons: order.items
          .map((item) => {
            // Find the original addon data
            const originalAddon = bookingAddonsData?.booking_addons?.find(
              (addon) => addon.id === item.addon_id
            );
            return {
              ...originalAddon,
              // Override with modal selections
              delivery_date: order.delivery_date,
              notes: order.notes,
            };
          })
          .filter(Boolean), // Remove any undefined items
      }));

      // Create supplier orders using the mutation
      const result = await createSupplierOrdersMutation.mutateAsync({
        supplier_groups,
      });

      // Close modal and show success message
      setIsCreateOrderModalOpen(false);

      // Refresh the booking add-ons data to show updated status
      if (onRefreshData) {
        onRefreshData();
      }

      if (result.errors && result.errors.length > 0) {
        toast.warning("Supplier Orders Created with Warnings", {
          description: `Successfully created ${result.orders_created} supplier orders, but encountered ${result.errors.length} errors. Check console for details.`,
          duration: 8000,
        });
        console.warn("⚠️ Order creation errors:", result.errors);
      } else {
        toast.success("Supplier Orders Created Successfully", {
          description: `Created ${
            result.orders_created
          } supplier orders totaling $${result.orders
            .reduce((sum, order) => sum + order.total_amount, 0)
            .toFixed(2)}`,
          duration: 5000,
        });
      }
    } catch (error) {
      console.error("❌ Failed to create supplier orders:", error);
      toast.error("Failed to Create Supplier Orders", {
        description: `Error: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        duration: 8000,
      });
    }
  };

  const renderWeekView = () => {
    const weekDays = getWeekDays();

    return (
      <div className="h-full overflow-y-auto">
        {/* Calendar Header */}
        <div className="bg-white border-b border-border sticky top-0 z-40">
          <div className="grid grid-cols-8 gap-0">
            <div className="p-4 text-center">
              <Text className="text-xs text-textSecondary font-medium">
                TIME
              </Text>
            </div>
            {weekDays.map((day) => (
              <div
                key={day.toISOString()}
                className="p-4 text-center border-l border-border"
              >
                <Text className="text-xs text-textSecondary font-medium mb-1">
                  {format(day, "EEE").toUpperCase()}
                </Text>
                <Text className="text-2xl font-light text-textPrimary">
                  {format(day, "d")}
                </Text>
              </div>
            ))}
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-8 gap-0 min-h-screen">
          {/* Time Column */}
          <div className="bg-white border-r border-border">
            <div className="space-y-0">
              <div className="h-16 flex items-center justify-center border-b border-border">
                <Text className="text-xs text-textSecondary font-medium">
                  All Day
                </Text>
              </div>
              {Array.from({ length: 24 }, (_, i) => (
                <div
                  key={i}
                  className="h-16 flex items-center justify-center border-b border-border"
                >
                  <Text className="text-xs text-textSecondary">
                    {i === 0
                      ? "12 AM"
                      : i < 12
                      ? `${i} AM`
                      : i === 12
                      ? "12 PM"
                      : `${i - 12} PM`}
                  </Text>
                </div>
              ))}
            </div>
          </div>

          {/* Day Columns */}
          {weekDays.map((day) => {
            const dayOfferings = getOfferingsForDate(day);
            return (
              <div
                key={day.toISOString()}
                className="border-r border-border relative"
              >
                {/* All Day Events */}
                <div className="h-16 border-b border-border p-2 space-y-1">
                  {dayOfferings.map((offering) => (
                    <EventCard
                      key={offering.id}
                      offering={offering}
                      onClick={onEventClick}
                      compact={true}
                    />
                  ))}
                </div>

                {/* Hour Slots */}
                {Array.from({ length: 24 }, (_, i) => (
                  <div key={i} className="h-16 border-b border-border"></div>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderMonthView = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 0 });
    const endDate = endOfMonth(monthEnd);

    const days = [];
    let day = startDate;
    while (day <= endDate) {
      days.push(day);
      day = addDays(day, 1);
    }

    return (
      <div className="h-full overflow-y-auto">
        {/* Month Header */}
        <div className="bg-white border-b border-border sticky top-0 z-40">
          <div className="grid grid-cols-7 gap-0">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
              (dayName) => (
                <div
                  key={dayName}
                  className="p-4 text-center border-r border-border last:border-r-0"
                >
                  <Text className="text-xs text-textSecondary font-medium">
                    {dayName.toUpperCase()}
                  </Text>
                </div>
              )
            )}
          </div>
        </div>

        {/* Month Grid */}
        <div className="grid grid-cols-7 gap-0 min-h-[calc(100vh-200px)]">
          {days.map((day) => {
            const dayOfferings = getOfferingsForDate(day);
            const isCurrentMonth = day.getMonth() === currentDate.getMonth();

            return (
              <div
                key={day.toISOString()}
                className={`border-r border-b border-border p-2 ${
                  activeExpandedDays.has(day.toISOString())
                    ? "min-h-[200px]"
                    : "min-h-[120px]"
                } ${!isCurrentMonth ? "bg-gray-50" : ""}`}
              >
                <Text
                  className={`text-sm font-medium mb-2 ${
                    isCurrentMonth ? "text-textPrimary" : "text-textSecondary"
                  }`}
                >
                  {format(day, "d")}
                </Text>
                <div className="space-y-1">
                  {(activeExpandedDays.has(day.toISOString())
                    ? dayOfferings
                    : dayOfferings.slice(0, 3)
                  ).map((offering) => (
                    <EventCard
                      key={offering.id}
                      offering={offering}
                      onClick={onEventClick}
                      compact={true}
                    />
                  ))}
                  {dayOfferings.length > 3 && (
                    <Button
                      variant="transparent"
                      size="small"
                      className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer p-0 h-auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        const dayKey = day.toISOString();
                        const newExpanded = new Set(activeExpandedDays);
                        if (activeExpandedDays.has(dayKey)) {
                          newExpanded.delete(dayKey);
                        } else {
                          newExpanded.add(dayKey);
                        }
                        setExpandedDays(newExpanded);
                      }}
                    >
                      {activeExpandedDays.has(day.toISOString())
                        ? `Show less`
                        : `+${dayOfferings.length - 3} more`}
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderDayView = () => {
    const dayOfferings = getOfferingsForDate(currentDate);

    return (
      <div className="h-full overflow-y-auto">
        {/* Day Header */}
        <div className="bg-white border-b border-border sticky top-0 z-40">
          <div className="flex">
            <div className="w-16 flex-shrink-0 p-2 text-center border-r border-border">
              <Text className="text-xs text-textSecondary font-medium">
                TIME
              </Text>
            </div>
            <div className="flex-1 p-4 text-center">
              <Text className="text-xs text-textSecondary font-medium mb-1">
                {format(currentDate, "EEEE").toUpperCase()}
              </Text>
              <Text className="text-3xl font-light text-textPrimary">
                {format(currentDate, "d")}
              </Text>
            </div>
          </div>
        </div>

        {/* Day Grid */}
        <div className="flex min-h-screen">
          {/* Time Column - Made narrower */}
          <div className="w-16 flex-shrink-0 bg-white border-r border-border">
            <div className="space-y-0">
              <div className="h-16 flex items-center justify-center border-b border-border px-1">
                <Text className="text-xs text-textSecondary font-medium">
                  All Day
                </Text>
              </div>
              {Array.from({ length: 24 }, (_, i) => (
                <div
                  key={i}
                  className="h-16 flex items-center justify-center border-b border-border px-1"
                >
                  <Text className="text-xs text-textSecondary transform -rotate-90 whitespace-nowrap">
                    {i === 0
                      ? "12 AM"
                      : i < 12
                      ? `${i} AM`
                      : i === 12
                      ? "12 PM"
                      : `${i - 12} PM`}
                  </Text>
                </div>
              ))}
            </div>
          </div>

          {/* Day Column - Maximum Width */}
          <div className="flex-1 min-w-0">
            {/* All Day Events */}
            <div className="h-16 border-b border-border p-4">
              <Text className="text-sm font-medium text-textPrimary mb-2">
                All Day Events
              </Text>
              <div className="space-y-2">
                {dayOfferings.map((offering) => (
                  <EventCard
                    key={offering.id}
                    offering={offering}
                    onClick={onEventClick}
                    compact={false}
                  />
                ))}
                {dayOfferings.length === 0 && (
                  <Text className="text-textSecondary text-sm">
                    No events scheduled for this day
                  </Text>
                )}
              </div>
            </div>

            {/* Hour Slots */}
            {Array.from({ length: 24 }, (_, i) => (
              <div
                key={i}
                className="h-16 border-b border-border flex items-center px-6"
              >
                {/* Time slot events could be displayed here */}
                <Text className="w-full text-textSecondary text-sm">
                  {/* Placeholder for timed events */}
                </Text>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderListView = () => {
    // Use filtered booking add-ons data
    const sortedAddons = [...filteredBookingAddons].sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    // Filter add-ons that don't have supplier orders for bulk operations
    const addonsWithoutOrders = sortedAddons.filter(
      (addon) => !addon.supplier_order_id
    );

    return (
      <div className="h-full overflow-y-auto">
        <div className="p-0">
          {/* Header with filters and actions */}
          <div className="flex items-center justify-between mb-4 p-6 pb-1">
            <div className="flex items-center gap-4">
              {/* Category Filter */}
              <div className="flex items-center gap-2">
                <Select
                  value={selectedCategory || "all"}
                  onValueChange={(value) => {
                    if (value === "all") {
                      setSelectedCategory("");
                    } else {
                      setSelectedCategory(value);
                    }
                  }}
                >
                  <Select.Trigger className="w-48">
                    <Select.Value placeholder="All categories">
                      {!selectedCategory
                        ? "All categories"
                        : categories.find((c) => c.id === selectedCategory)
                            ?.name}
                    </Select.Value>
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All categories</Select.Item>
                    {categories.map((category) => (
                      <Select.Item key={category.id} value={category.id}>
                        {category.name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>

                {/* Order Status Filter */}
                <Select
                  value={selectedOrderStatus || "all"}
                  onValueChange={(value) => {
                    if (value === "all") {
                      setSelectedOrderStatus("");
                    } else {
                      setSelectedOrderStatus(value);
                    }
                  }}
                >
                  <Select.Trigger className="w-48">
                    <Select.Value placeholder="All statuses">
                      {!selectedOrderStatus || selectedOrderStatus === "all"
                        ? "All statuses"
                        : orderStatusOptions.find(
                            (s) => s.value === selectedOrderStatus
                          )?.label}
                    </Select.Value>
                  </Select.Trigger>
                  <Select.Content>
                    {orderStatusOptions.map((status) => (
                      <Select.Item key={status.value} value={status.value}>
                        {status.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>

                {/* Clear filters button */}
                {(selectedCategory || selectedOrderStatus) && (
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => {
                      setSelectedCategory("");
                      setSelectedOrderStatus("");
                    }}
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {addonsWithoutOrders.length > 0 && (
                <Button
                  variant="primary"
                  size="small"
                  onClick={() =>
                    handleCreateSupplierOrders(addonsWithoutOrders)
                  }
                >
                  Create Supplier Orders ({addonsWithoutOrders.length})
                </Button>
              )}
              {sortedAddons.length > 0 && addonsWithoutOrders.length === 0 && (
                <Text className="text-sm text-textSecondary">
                  All add-ons have been converted to supplier orders
                </Text>
              )}
            </div>
          </div>

          {isLoadingAddons && (
            <div className="text-center py-12">
              <Text className="text-textSecondary">
                Loading booking add-ons...
              </Text>
            </div>
          )}

          {!isLoadingAddons && sortedAddons.length === 0 ? (
            <div className="text-center py-12">
              <Text className="text-textSecondary text-lg">
                No booked add-ons found
              </Text>
              <Text className="text-textSecondary text-sm mt-2">
                Add-on services will appear here when customers book them
              </Text>
            </div>
          ) : (
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Add-on Item</Table.HeaderCell>
                  <Table.HeaderCell>Category</Table.HeaderCell>
                  <Table.HeaderCell>Customer</Table.HeaderCell>
                  <Table.HeaderCell>Check-in Date</Table.HeaderCell>
                  <Table.HeaderCell>Check-out Date</Table.HeaderCell>
                  <Table.HeaderCell>Order Status</Table.HeaderCell>
                  <Table.HeaderCell>Order ID</Table.HeaderCell>
                  <Table.HeaderCell className="text-right">
                    Total Price
                  </Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {sortedAddons.map((addon) => {
                  const categoryId = (addon.add_on?.metadata as any)?.category;
                  const categoryName = categoryId
                    ? categoryMap.get(categoryId)
                    : null;

                  return (
                    <Table.Row key={addon.id}>
                      <Table.Cell>
                        <Text className="font-medium truncate text-ellipsis max-w-sm overflow-hidden">
                          {addon.add_on_name}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        {categoryName ? (
                          <Badge>{categoryName}</Badge>
                        ) : (
                          <Text className="text-textSecondary">-</Text>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        <Text>
                          {addon.order?.metadata?.customer_name ||
                            addon.order?.email}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        <Text>
                          {addon.order?.metadata?.check_in_date
                            ? format(
                                new Date(addon.order.metadata.check_in_date),
                                "MMM d, yyyy"
                              )
                            : "-"}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        <Text>
                          {addon.order?.metadata?.check_out_date
                            ? format(
                                new Date(addon.order.metadata.check_out_date),
                                "MMM d, yyyy"
                              )
                            : "-"}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        {addon.supplier_order_id ? (
                          <Badge
                            className={`rounded-full ${getStatusBadgeColor(
                              addon.order_status
                            )}`}
                          >
                            {getStatusLabel(addon.order_status)}
                          </Badge>
                        ) : (
                          <Badge className="rounded-full bg-gray-100 text-gray-600">
                            Not Ordered
                          </Badge>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {addon.supplier_order_id ? (
                          <Button
                            variant="transparent"
                            size="small"
                            onClick={() =>
                              navigate(`/supplier-management/orders`)
                            }
                            className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                            title={`View order ${addon.supplier_order_id} in orders page`}
                          >
                            <Text className="font-mono text-sm truncate max-w-[120px]">
                              {addon.supplier_order_id}
                            </Text>
                            <ExternalLink className="h-3 w-3 flex-shrink-0" />
                          </Button>
                        ) : (
                          <Text className="text-textSecondary">-</Text>
                        )}
                      </Table.Cell>
                      <Table.Cell className="text-right">
                        <Text className="font-medium">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              addon.add_on?.metadata?.selling_currency || "CHF",
                          }).format(addon.total_price)}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        <DropdownMenu>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="secondary" size="small">
                              ⋯
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content align="end">
                            <DropdownMenu.Item
                              onClick={() => {
                                setSelectedAddonForAdd(addon);
                                setIsAddModalOpen(true);
                              }}
                            >
                              View Details
                            </DropdownMenu.Item>
                            {addon.supplier_order_id ? (
                              <DropdownMenu.Item disabled>
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                  Order Already Created
                                </div>
                              </DropdownMenu.Item>
                            ) : (
                              <DropdownMenu.Item
                                onClick={() => {
                                  handleCreateOrderForAddon(addon);
                                }}
                              >
                                Create Order
                              </DropdownMenu.Item>
                            )}
                          </DropdownMenu.Content>
                        </DropdownMenu>
                      </Table.Cell>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          )}
        </div>
      </div>
    );
  };

  const renderContent = () => {
    if (viewType === "week") {
      return renderWeekView();
    } else if (viewType === "month") {
      return renderMonthView();
    } else if (viewType === "list") {
      return renderListView();
    } else {
      return renderDayView();
    }
  };

  return (
    <>
      {renderContent()}

      {/* Add Modal for displaying booking addon details */}
      <FocusModal open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <FocusModal.Content>
          <FocusModal.Header>
            <FocusModal.Title>
              Add-on Service & Customer Details
            </FocusModal.Title>
            <FocusModal.Description>
              View complete service information, customer details, and create
              supplier order
            </FocusModal.Description>
          </FocusModal.Header>

          <FocusModal.Body className="space-y-6 p-6">
            {selectedAddonForAdd ? (
              <div className="space-y-4">
                {/* Basic Information */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <Heading level="h3" className="mb-3">
                    Basic Information
                  </Heading>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Text className="text-sm font-medium text-textSecondary">
                        Service Name
                      </Text>
                      <Text className="font-medium">
                        {selectedAddonForAdd.add_on_name}
                      </Text>
                    </div>
                    <div>
                      <Text className="text-sm font-medium text-textSecondary">
                        Category
                      </Text>
                      {(() => {
                        const categoryId = (
                          selectedAddonForAdd.add_on?.metadata as any
                        )?.category;
                        const categoryName = categoryId
                          ? categoryMap.get(categoryId)
                          : null;
                        return categoryName ? (
                          <Badge>{categoryName}</Badge>
                        ) : (
                          <Text className="text-textSecondary">-</Text>
                        );
                      })()}
                    </div>
                    <div>
                      <Text className="text-sm font-medium text-textSecondary">
                        Quantity
                      </Text>
                      <Text>{selectedAddonForAdd.quantity}</Text>
                    </div>
                    <div>
                      <Text className="text-sm font-medium text-textSecondary">
                        Total Price
                      </Text>
                      <Text className="font-medium">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency:
                            (selectedAddonForAdd.add_on?.metadata as any)
                              ?.selling_currency || "CHF",
                        }).format(selectedAddonForAdd.total_price)}
                      </Text>
                    </div>
                  </div>
                </div>

                {/* Customer Information */}
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <Heading level="h3" className="mb-4 text-blue-900">
                    Customer Information
                  </Heading>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <Text className="text-sm font-medium text-textSecondary mb-1">
                          Customer Name
                        </Text>
                        <Text className="font-medium text-lg">
                          {selectedAddonForAdd.order?.metadata?.customer_name ||
                            selectedAddonForAdd.order?.email ||
                            "Unknown"}
                        </Text>
                      </div>
                      <div>
                        <Text className="text-sm font-medium text-textSecondary mb-1">
                          Email Address
                        </Text>
                        <Text className="text-blue-600">
                          {selectedAddonForAdd.order?.email || "-"}
                        </Text>
                      </div>
                      <div>
                        <Text className="text-sm font-medium text-textSecondary mb-1">
                          Order ID
                        </Text>
                        <Text className="font-mono text-sm bg-white px-2 py-1 rounded border">
                          {selectedAddonForAdd.order?.display_id ||
                            selectedAddonForAdd.order_id}
                        </Text>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div>
                        <Text className="text-sm font-medium text-textSecondary mb-1">
                          Check-in Date
                        </Text>
                        <Text className="font-medium">
                          {selectedAddonForAdd.order?.metadata?.check_in_date
                            ? format(
                                new Date(
                                  selectedAddonForAdd.order.metadata.check_in_date
                                ),
                                "EEEE, MMM d, yyyy"
                              )
                            : "-"}
                        </Text>
                      </div>
                      <div>
                        <Text className="text-sm font-medium text-textSecondary mb-1">
                          Check-out Date
                        </Text>
                        <Text className="font-medium">
                          {selectedAddonForAdd.order?.metadata?.check_out_date
                            ? format(
                                new Date(
                                  selectedAddonForAdd.order.metadata.check_out_date
                                ),
                                "EEEE, MMM d, yyyy"
                              )
                            : "-"}
                        </Text>
                      </div>
                      <div>
                        <Text className="text-sm font-medium text-textSecondary mb-1">
                          Booking Date
                        </Text>
                        <Text>
                          {selectedAddonForAdd.created_at
                            ? format(
                                new Date(selectedAddonForAdd.created_at),
                                "MMM d, yyyy 'at' h:mm a"
                              )
                            : "-"}
                        </Text>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Customer Field Responses */}
                {selectedAddonForAdd.customer_field_responses &&
                  Object.keys(selectedAddonForAdd.customer_field_responses)
                    .length > 0 && (
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <Heading level="h3" className="mb-4 text-green-900">
                        Additional Customer Details
                      </Heading>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {Object.entries(
                          selectedAddonForAdd.customer_field_responses
                        ).map(([key, value]) => (
                          <div
                            key={key}
                            className="bg-white p-3 rounded border border-green-100"
                          >
                            <Text className="text-sm font-medium text-textSecondary mb-1">
                              {key
                                .replace(/_/g, " ")
                                .replace(/\b\w/g, (l) => l.toUpperCase())}
                            </Text>
                            <Text className="font-medium break-words">
                              {String(value)}
                            </Text>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                {/* Show message when no customer details are available */}
                {(!selectedAddonForAdd.customer_field_responses ||
                  Object.keys(selectedAddonForAdd.customer_field_responses)
                    .length === 0) && (
                  <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <Heading level="h3" className="mb-2 text-yellow-900">
                      Additional Customer Details
                    </Heading>
                    <Text className="text-yellow-700">
                      No additional customer details were provided for this
                      booking.
                    </Text>
                  </div>
                )}
              </div>
            ) : (
              <Text>No add-on selected</Text>
            )}
          </FocusModal.Body>

          <FocusModal.Footer>
            <div className="flex justify-between items-center w-full">
              <Button
                variant="secondary"
                onClick={() => setIsAddModalOpen(false)}
              >
                Close
              </Button>
              {selectedAddonForAdd && (
                <Button
                  variant="primary"
                  onClick={() => {
                    handleCreateOrderForAddon(selectedAddonForAdd);
                    setIsAddModalOpen(false);
                  }}
                >
                  Create Supplier Order
                </Button>
              )}
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>

      {/* Create Supplier Orders Modal */}
      <CreateSupplierOrdersModal
        isOpen={isCreateOrderModalOpen}
        onClose={() => {
          setIsCreateOrderModalOpen(false);
          setSelectedAddonsForOrder([]);
        }}
        addons={selectedAddonsForOrder}
        onCreateOrders={handleConfirmCreateOrders}
        selectedCategory={selectedCategory}
      />
    </>
  );
}
