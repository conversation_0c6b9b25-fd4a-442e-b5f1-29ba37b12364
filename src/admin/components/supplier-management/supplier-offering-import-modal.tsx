import React, { useState, useRef, useEffect } from "react";
import {
  Button,
  FocusModal,
  Text,
  Heading,
  Input,
  Table,
  Badge,
  toast,
  Select,
  Label,
} from "@camped-ai/ui";
import {
  Upload,
  Download,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import {
  useSupplierOfferingImportExport,
  type ImportValidationError,
} from "../../hooks/supplier-products-services/use-supplier-offerings";
import { useCategories } from "../../hooks/supplier-products-services/use-categories";



interface SupplierOfferingImportModalProps {
  open: boolean;
  onClose: () => void;
  onImportComplete?: () => void;
}

// Remove import steps - we'll use a simpler flow like products & services

const SupplierOfferingImportModal: React.FC<SupplierOfferingImportModalProps> = ({
  open,
  onClose,
  onImportComplete,
}) => {
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<ImportValidationError[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [importSuccess, setImportSuccess] = useState(false);
  const [importedCount, setImportedCount] = useState(0);
  const [importBackendErrors, setImportBackendErrors] = useState<any[]>([]);
  const [importResult, setImportResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Define import steps
  const importSteps = [
    { id: 1, title: "Download", description: "Get template" },
    { id: 2, title: "Upload", description: "Upload file" },
    { id: 3, title: "Import", description: "Process data" },
  ];

  // Determine current step based on state
  const getCurrentStep = () => {
    if (importSuccess || importResult) return 3; // Import/Complete step
    if (importFile && (importData.length > 0 || importErrors.length > 0)) return 3; // Ready to import
    if (importFile) return 2; // File uploaded
    return 1; // Download step
  };

  // Get step status
  const getStepStatus = (stepId: number) => {
    const current = getCurrentStep();
    if (stepId < current) return 'completed';
    if (stepId === current) return 'current';
    return 'pending';
  };

  const {
    parseImportFile,
    importSupplierOfferings,
    isImporting,
  } = useSupplierOfferingImportExport();

  // Fetch categories for the dropdown
  const { data: categoriesData } = useCategories({ is_active: true });
  const categories = categoriesData?.categories || [];

  // Reset form when category changes
  useEffect(() => {
    if (selectedCategoryId) {
      setImportFile(null);
      setImportData([]);
      setImportErrors([]);
      setImportSuccess(false);
      setImportedCount(0);
      setImportBackendErrors([]);
      setImportResult(null);
    }
  }, [selectedCategoryId]);

  const resetImportModal = () => {
    setImportFile(null);
    setImportData([]);
    setImportErrors([]);
    setSelectedCategoryId('');
    setImportSuccess(false);
    setImportedCount(0);
    setImportBackendErrors([]);
    setImportResult(null);
  };

  const handleClose = () => {
    resetImportModal();
    onClose();
  };

  // Auto-parse file when uploaded (like products & services)
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
      setImportData([]);
      setImportErrors([]);
      setImportBackendErrors([]);
      setImportResult(null);
      setImportSuccess(false);

      try {
        const { data, errors } = await parseImportFile(file);
        setImportData(data);
        setImportErrors(errors);
      } catch (error) {
        toast.error("Failed to parse file");
      }
    }
  };

  const handleImport = async () => {
    if (importData.length === 0) return;

    try {
      const result = await importSupplierOfferings(importData);

      setImportResult(result);

      // Check if there were any errors in the result
      if (result.errors && result.errors.length > 0) {
        setImportBackendErrors(result.errors);
        setImportSuccess(false);


        // Show partial success message if some records were imported
        if (result.created > 0) {
          toast.success(`Imported ${result.created} supplier offerings with ${result.errors.length} errors`);
        } else {
          toast.error("Import failed with errors. Please check the error details below.");
        }
      } else {
        setImportSuccess(true);
        setImportedCount(result.created || importData.length);
        onImportComplete?.();
        toast.success(result.message || "Import completed successfully");
      }
    } catch (error) {

      // Try to extract detailed error information
      let errorMessage = "Import failed";
      let backendErrors: any[] = [];

      if (error instanceof Error) {
        errorMessage = error.message;

        // Check if the error contains structured error data
        try {
          const errorData = JSON.parse(error.message);
          if (errorData.errors && Array.isArray(errorData.errors)) {
            backendErrors = errorData.errors;
          }
        } catch (parseError) {
          // Error message is not JSON, check if it's a fetch error with response data
          if (error.message.includes("Import failed:")) {
            // This might be from the mutation error handling
            errorMessage = error.message;
          }
        }
      }

      // If we have specific backend errors, display them
      if (backendErrors.length > 0) {
        setImportBackendErrors(backendErrors);
        setImportSuccess(false);
        toast.error(`Import failed with ${backendErrors.length} errors. Please check the details below.`);
      } else {
        // For general errors, create a single error entry for display
        const generalError = {
          index: 0,
          message: errorMessage,
          data: null
        };
        setImportBackendErrors([generalError]);
        setImportSuccess(false);
        toast.error("Import failed. Please check the error details below.");
      }
    }
  };

  const handleDownloadTemplate = async () => {
    if (!selectedCategoryId) {
      toast.error("Please select a category first");
      return;
    }

    const selectedCategory = categories.find((cat: any) => cat.id === selectedCategoryId);
    if (!selectedCategory) {
      toast.error("Selected category not found");
      return;
    }

    try {
      // Use the API endpoint to download the template with dynamic dropdowns
      const templateUrl = `/admin/supplier-management/supplier-offerings/template?category_id=${selectedCategoryId}`;

      const response = await fetch(templateUrl, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to download template: ${response.statusText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `supplier_offerings_template_${selectedCategory.name}_${
        new Date().toISOString().split("T")[0]
      }.xlsx`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Template for ${selectedCategory.name} downloaded successfully`);
    } catch (error) {
      toast.error(`Failed to download template for ${selectedCategory.name}`);
    }
  };

  const renderImportContent = () => (
    <>
      {/* Step 1: Download Template */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex items-start gap-3 p-6">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
            <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
          </div>
          <div className="flex-1">
            <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Download Import Template
            </Heading>
            <Text className="text-gray-600 dark:text-gray-400 mb-4">
              Download category-specific Excel templates with pre-configured fields for supplier offerings. Each
              category has its own template with custom fields specific to that category.
            </Text>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-700 mb-4">
              <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Each template includes:
              </Text>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">Category-specific fields:</strong>{" "}
                  Custom fields and validation for the selected category
                </li>
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">User-friendly dropdowns:</strong>{" "}
                  Supplier names, product/service names, status options
                </li>
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">Reference sheets:</strong>{" "}
                  Easy lookup for multi-select fields (supports comma-separated values)
                </li>
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">Custom field dropdowns:</strong>{" "}
                  Dynamic validation for category-specific fields
                </li>
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">Instructions sheet:</strong>{" "}
                  Comprehensive field guide with all available options
                </li>
              </ul>
            </div>

            {/* Category Selection and Download */}
            {categories && categories.length > 0 ? (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                    Select Category:
                  </Label>
                  <Select value={selectedCategoryId} onValueChange={setSelectedCategoryId}>
                    <Select.Trigger className="w-80 max-w-full">
                      <Select.Value placeholder="Choose a category..." />
                    </Select.Trigger>
                    <Select.Content>
                      {categories.map((category: any) => (
                        <Select.Item key={category.id} value={category.id}>
                          <div className="flex items-center gap-2">
                            <span>{category.icon || '📦'}</span>
                            <span>{category.name}</span>
                            <span className="text-xs text-gray-500">
                              ({category.category_type || 'Category'})
                            </span>
                          </div>
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>

                <Button
                  variant="secondary"
                  onClick={handleDownloadTemplate}
                  disabled={!selectedCategoryId}
                  className="flex items-center gap-2 bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Download className="w-4 h-4" />
                  Download Excel Template
                </Button>
              </div>
            ) : (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-700">
                <Text className="text-sm text-yellow-700 dark:text-yellow-300">
                  No categories found. Please create categories first before generating templates.
                </Text>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Step 2: Upload File */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex items-start gap-3 p-6">
          <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <span className="text-green-600 dark:text-green-400 font-semibold text-sm">2</span>
          </div>
          <div className="flex-1">
            <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Upload & Parse Your Data File
            </Heading>
            <Text className="text-gray-600 dark:text-gray-400 mb-4">
              Upload the completed Excel file with your supplier offerings data. The file will be automatically parsed and validated.
            </Text>

            {/* File Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                importFile
                  ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                  : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
              }`}
            >
              {importFile ? (
                <>
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                  <Text className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    File Selected: {importFile.name}
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Size: {(importFile.size / 1024).toFixed(1)} KB
                  </Text>
                  <Button
                    variant="secondary"
                    onClick={() => setImportFile(null)}
                    className="text-sm"
                  >
                    Choose Different File
                  </Button>
                </>
              ) : (
                <>
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <Text className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Choose a file to upload
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Excel (.xlsx, .xls) or CSV files supported
                  </Text>
                  <Input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.xls,.csv"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <Button
                    variant="secondary"
                    onClick={() => fileInputRef.current?.click()}
                    className="mx-auto"
                  >
                    Select File
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Validation Errors Section */}
      {importErrors.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-red-200 dark:border-red-700 shadow-sm">
          <div className="flex items-start gap-3 p-6">
            <div className="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
            <div className="flex-1">
              <Heading level="h3" className="text-lg font-medium text-red-900 dark:text-red-100 mb-2">
                Validation Errors Found
              </Heading>
              <Text className="text-red-600 dark:text-red-400 mb-4">
                Please fix the following errors before importing:
              </Text>
              <div className="max-h-64 overflow-y-auto">
                <Table>
                  <Table.Header>
                    <Table.Row>
                      <Table.HeaderCell>Row</Table.HeaderCell>
                      <Table.HeaderCell>Field</Table.HeaderCell>
                      <Table.HeaderCell>Error</Table.HeaderCell>
                      <Table.HeaderCell>Value</Table.HeaderCell>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {importErrors.map((error, index) => (
                      <Table.Row key={index}>
                        <Table.Cell>{error.row}</Table.Cell>
                        <Table.Cell>
                          <Badge>{error.field}</Badge>
                        </Table.Cell>
                        <Table.Cell className="text-red-600 dark:text-red-400">
                          {error.message}
                        </Table.Cell>
                        <Table.Cell className="font-mono text-sm">
                          {error.value ? String(error.value) : '-'}
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backend Import Errors Section */}
      {importBackendErrors.length > 0 && (() => {
        const dateConflictErrors = importBackendErrors.filter(error =>
          error.message?.includes("Date range conflict") || error.message?.includes("Validity period overlaps")
        );
        const otherErrors = importBackendErrors.filter(error =>
          !error.message?.includes("Date range conflict") && !error.message?.includes("Validity period overlaps")
        );

        return (
          <div className="space-y-4">
            {/* Date Range Conflicts Section */}
            {dateConflictErrors.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-orange-200 dark:border-orange-700 shadow-sm">
                <div className="flex items-start gap-3 p-6">
                  <div className="flex-shrink-0 w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div className="flex-1">
                    <Heading level="h3" className="text-lg font-medium text-orange-900 dark:text-orange-100 mb-2">
                      Date Range Conflicts ({dateConflictErrors.length})
                    </Heading>
                    <Text className="text-orange-600 dark:text-orange-400 mb-4">
                      The following supplier offerings have overlapping validity periods with existing offerings:
                    </Text>
                    <div className="max-h-64 overflow-y-auto">
                      <div className="space-y-3">
                        {dateConflictErrors.slice(0, 10).map((error, index) => (
                          <div key={index} className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-md border border-orange-200 dark:border-orange-800">
                            <div className="flex items-start justify-between mb-2">
                              <Text className="font-medium text-orange-900 dark:text-orange-100">
                                Row {error.index}
                              </Text>
                            </div>
                            <Text className="text-sm text-orange-700 dark:text-orange-300 mb-2">
                              {error.message}
                            </Text>
                            <div className="mt-2 p-2 bg-orange-100 dark:bg-orange-900/30 rounded text-xs text-orange-700 dark:text-orange-300">
                              <strong>💡 Solution:</strong> Adjust the validity dates or update the existing offering first.
                            </div>
                          </div>
                        ))}
                        {dateConflictErrors.length > 10 && (
                          <div className="text-center py-2">
                            <Text className="text-sm text-orange-600 dark:text-orange-400">
                              ... and {dateConflictErrors.length - 10} more date conflicts
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Other Errors Section */}
            {otherErrors.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-red-200 dark:border-red-700 shadow-sm">
                <div className="flex items-start gap-3 p-6">
                  <div className="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                  </div>
                  <div className="flex-1">
                    <Heading level="h3" className="text-lg font-medium text-red-900 dark:text-red-100 mb-2">
                      Other Import Errors ({otherErrors.length})
                    </Heading>
                    <Text className="text-red-600 dark:text-red-400 mb-4">
                      {importResult?.created > 0
                        ? `${importResult.created} supplier offerings were imported successfully, but ${otherErrors.length} failed:`
                        : `Failed to import ${otherErrors.length} supplier offerings:`
                      }
                    </Text>
                    <div className="max-h-64 overflow-y-auto">
                      <div className="space-y-3">
                        {otherErrors.slice(0, 20).map((error, index) => (
                          <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
                            <div className="flex items-start justify-between mb-2">
                              <Text className="font-medium text-red-900 dark:text-red-100">
                                Row {error.index}
                              </Text>
                            </div>
                            <Text className="text-sm text-red-700 dark:text-red-300 mb-2">
                              {error.message}
                            </Text>
                            {error.data && (
                              <details className="mt-2">
                                <summary className="text-xs text-red-600 dark:text-red-400 cursor-pointer hover:text-red-800 dark:hover:text-red-200">
                                  Show data
                                </summary>
                                <pre className="text-xs text-red-600 dark:text-red-400 mt-1 p-2 bg-red-100 dark:bg-red-900/30 rounded overflow-x-auto">
                                  {JSON.stringify(error.data, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        ))}
                        {otherErrors.length > 20 && (
                          <div className="text-center py-2">
                            <Text className="text-sm text-red-600 dark:text-red-400">
                              ... and {otherErrors.length - 20} more errors
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })()}

      {/* Success Message */}
      {importSuccess && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700 shadow-sm">
          <div className="flex items-start gap-3 p-6">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <Heading level="h3" className="text-lg font-medium text-green-900 dark:text-green-100 mb-2">
                Import Completed Successfully!
              </Heading>
              <Text className="text-green-600 dark:text-green-400">
                Your supplier offerings have been imported and are now available in the supplier offerings list.
              </Text>
              <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md border border-green-200 dark:border-green-700 mt-4">
                <Text className="text-sm text-green-700 dark:text-green-300">
                  ✓ Data imported and validated
                  <br />
                  ✓ Records added to database
                  <br />✓ Available in supplier offerings list
                </Text>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );







  return (
    <FocusModal open={open} onOpenChange={handleClose}>
      <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
        <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center w-full py-4 px-6">
            <div className="flex items-center gap-3">
              <Heading
                level="h2"
                className="text-xl font-semibold text-gray-900 dark:text-gray-100"
              >
                Import Supplier Offerings
              </Heading>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {importSteps.map((step, index) => {
                  const status = getStepStatus(step.id);
                  const isCompleted = status === 'completed';
                  const isCurrent = status === 'current';

                  return (
                    <React.Fragment key={step.id}>
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                          isCompleted
                            ? "bg-green-600 dark:bg-green-500 text-white"
                            : isCurrent
                            ? "bg-blue-600 dark:bg-blue-500 text-white"
                            : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                        }`}>
                          {isCompleted ? "✓" : step.id}
                        </div>
                        <span className={`ml-2 text-sm font-medium ${
                          isCompleted
                            ? "text-green-600 dark:text-green-400"
                            : isCurrent
                            ? "text-blue-600 dark:text-blue-400"
                            : "text-gray-500 dark:text-gray-400"
                        }`}>
                          {step.id === 3 && importSuccess ? "Complete" : step.title}
                        </span>
                      </div>
                      {index < importSteps.length - 1 && (
                        <div className={`w-12 h-0.5 ${
                          isCompleted ? "bg-green-300 dark:bg-green-600" : "bg-gray-300 dark:bg-gray-600"
                        }`}></div>
                      )}
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
            {renderImportContent()}
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {importSuccess ? (
                  <span className="text-green-600 dark:text-green-400 font-medium">
                    ✅ Successfully imported {importedCount} records
                  </span>
                ) : importFile ? (
                  <>
                    <span className="text-green-600 dark:text-green-400">
                      ✓
                    </span>{" "}
                    {importFile.name}
                    {importData.length > 0 && importErrors.length === 0 && (
                      <span className="text-green-600 dark:text-green-400">
                        {" "}
                        • {importData.length} records ready
                      </span>
                    )}
                    {importErrors.length > 0 && (
                      <span className="text-red-600 dark:text-red-400">
                        {" "}
                        • {importErrors.length} errors found
                      </span>
                    )}
                  </>
                ) : (
                  "Select a file to automatically parse and import"
                )}
              </div>
              <div className="flex gap-4">
                <Button
                  variant="secondary"
                  onClick={resetImportModal}
                  className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleImport}
                  disabled={
                    isImporting ||
                    importSuccess ||
                    !importFile ||
                    importData.length === 0 ||
                    importErrors.length > 0
                  }
                  className={`flex items-center gap-3 px-6 py-3 shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed ${
                    importSuccess
                      ? "bg-green-600 dark:bg-green-500 text-white"
                      : "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white"
                  }`}
                >
                  {importSuccess ? (
                    <>
                      <CheckCircle className="w-5 h-5" />
                      Import Complete
                    </>
                  ) : (
                    <>
                      <Upload className="w-5 h-5" />
                      {isImporting
                        ? "Importing..."
                        : `Import Data${
                            importData.length > 0
                              ? ` (${importData.length} Records)`
                              : ""
                          }`}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </FocusModal.Body>


      </FocusModal.Content>
    </FocusModal>
  );
};

export default SupplierOfferingImportModal;
