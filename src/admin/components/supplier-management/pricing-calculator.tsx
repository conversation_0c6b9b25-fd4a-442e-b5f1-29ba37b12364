import React, { useState, useEffect } from "react";
import { Input, Label, Text, Button, Badge, Select } from "@camped-ai/ui";
import { Plus, X, Calculator, Edit2, Check, RefreshCw } from "lucide-react";

interface CustomPrice {
  name: string;
  price: number;
}

interface PricingData {
  commission?: number;
  publicPrice?: number;
  supplierPrice?: number;
  netPrice?: number;
  marginRate?: number;
  sellingPrice?: number;
  customPrices?: CustomPrice[];

  // Currency fields
  currency?: string; // Cost currency

  // Selling currency fields
  sellingCurrency?: string;
  sellingPriceSellingCurrency?: number;
  exchangeRate?: number;
  exchangeRateDate?: Date;
}

interface PricingCalculatorProps {
  initialData?: PricingData;
  onChange?: (
    data: PricingData & {
      calculatedSupplierPrice?: number;
      calculatedNetPrice?: number;
      calculatedSellingPrice?: number;
      calculatedSellingPriceSellingCurrency?: number;
      isPricingComplete?: boolean;
      pricingErrors?: string[];
    }
  ) => void;
  disabled?: boolean;
  showCalculations?: boolean;
  costCurrency?: string; // The cost currency (e.g., CHF)
  onExchangeRateUpdate?: (
    fromCurrency: string,
    toCurrency: string
  ) => Promise<number>;
}

const PricingCalculator: React.FC<PricingCalculatorProps> = ({
  initialData = {},
  onChange,
  disabled = false,
  showCalculations = true,
  costCurrency = "CHF",
  onExchangeRateUpdate,
}) => {
  const [pricingData, setPricingData] = useState<PricingData>(initialData);
  const [customPrices, setCustomPrices] = useState<CustomPrice[]>(
    initialData.customPrices || []
  );
  const [newCustomPrice, setNewCustomPrice] = useState({ name: "", price: 0 });
  const [errors, setErrors] = useState<string[]>([]);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingPrice, setEditingPrice] = useState<CustomPrice>({
    name: "",
    price: 0,
  });
  const [isUpdatingExchangeRate, setIsUpdatingExchangeRate] = useState(false);

  // Calculate derived values
  const calculatePricing = (
    data: PricingData,
    customPricesList: CustomPrice[]
  ) => {
    const calculationErrors: string[] = [];
    let calculatedSupplierPrice: number | undefined;
    let calculatedNetPrice: number | undefined;
    let calculatedSellingPrice: number | undefined;

    try {
      // Calculate supplier price: Commission × Public Price (if both provided)
      if (data.commission && data.publicPrice) {
        if (data.commission < 0 || data.commission > 1) {
          calculationErrors.push(
            "Commission must be between 0 and 1 (0% to 100%)"
          );
        } else if (data.publicPrice < 0) {
          calculationErrors.push("Public price must be non-negative");
        } else {
          calculatedSupplierPrice = data.commission * data.publicPrice;
        }
      }

      // Use calculated supplier price if available, otherwise use manually entered supplier price
      const supplierPrice = calculatedSupplierPrice || data.supplierPrice;

      // Calculate net price: Supplier Price + sum of custom prices
      if (supplierPrice) {
        calculatedNetPrice = supplierPrice;

        if (customPricesList.length > 0) {
          const customPricesSum = customPricesList.reduce((sum, item) => {
            if (typeof item.price !== "number" || item.price < 0) {
              calculationErrors.push(
                `Custom price "${item.name}" must be a non-negative number`
              );
              return sum;
            }
            return sum + item.price;
          }, 0);
          calculatedNetPrice += customPricesSum;
        }
      }

      // Calculate selling price: Net Price ÷ (1 - Margin Rate)
      // Always use calculated net price for real-time updates
      if (calculatedNetPrice && data.marginRate) {
        if (data.marginRate < 0 || data.marginRate >= 1) {
          calculationErrors.push(
            "Margin rate must be between 0 and 1 (0% to 99.99%)"
          );
        } else {
          calculatedSellingPrice = calculatedNetPrice / (1 - data.marginRate);
        }
      }
    } catch (error) {
      calculationErrors.push("Error in pricing calculations");
    }

    // Validate custom prices
    customPricesList.forEach((item, index) => {
      if (!item.name || typeof item.name !== "string") {
        calculationErrors.push(`Custom price ${index + 1}: Name is required`);
      }
      if (typeof item.price !== "number" || item.price < 0) {
        calculationErrors.push(
          `Custom price ${index + 1}: Price must be a non-negative number`
        );
      }
    });

    // Calculate selling price in selling currency
    let calculatedSellingPriceSellingCurrency: number | undefined;
    if (calculatedSellingPrice && data.sellingCurrency && data.exchangeRate) {
      if (data.sellingCurrency === costCurrency) {
        // Same currency, no conversion needed
        calculatedSellingPriceSellingCurrency = calculatedSellingPrice;
      } else {
        // Convert using exchange rate
        calculatedSellingPriceSellingCurrency =
          calculatedSellingPrice * data.exchangeRate;
      }
    }

    // Pricing is complete if we have either:
    // 1. Commission + Public Price (calculated supplier price), OR
    // 2. Direct supplier price entry
    // Plus margin rate and all calculated values
    const hasSupplierPrice = !!(calculatedSupplierPrice || data.supplierPrice);
    const isPricingComplete = !!(
      hasSupplierPrice &&
      data.marginRate &&
      calculatedNetPrice &&
      calculatedSellingPrice
    );

    return {
      calculatedSupplierPrice,
      calculatedNetPrice,
      calculatedSellingPrice,
      calculatedSellingPriceSellingCurrency,
      isPricingComplete,
      pricingErrors: calculationErrors,
    };
  };

  // Update calculations when data changes
  useEffect(() => {
    const calculations = calculatePricing(pricingData, customPrices);
    setErrors(calculations.pricingErrors);

    if (onChange) {
      onChange({
        ...pricingData,
        customPrices,
        ...calculations,
      });
    }
  }, [pricingData, customPrices, onChange]);

  const handleInputChange = (
    field: keyof PricingData,
    value: number | undefined
  ) => {
    setPricingData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addCustomPrice = () => {
    if (newCustomPrice.name && newCustomPrice.price >= 0) {
      setCustomPrices((prev) => [...prev, { ...newCustomPrice }]);
      setNewCustomPrice({ name: "", price: 0 });
    }
  };

  const removeCustomPrice = (index: number) => {
    setCustomPrices((prev) => prev.filter((_, i) => i !== index));
    // Cancel editing if we're removing the item being edited
    if (editingIndex === index) {
      setEditingIndex(null);
    }
  };

  const startEditingCustomPrice = (index: number) => {
    setEditingIndex(index);
    setEditingPrice({ ...customPrices[index] });
  };

  const saveEditingCustomPrice = () => {
    if (editingIndex !== null && editingPrice.name && editingPrice.price >= 0) {
      setCustomPrices((prev) =>
        prev.map((item, index) =>
          index === editingIndex ? { ...editingPrice } : item
        )
      );
      setEditingIndex(null);
      setEditingPrice({ name: "", price: 0 });
    }
  };

  const cancelEditingCustomPrice = () => {
    setEditingIndex(null);
    setEditingPrice({ name: "", price: 0 });
  };

  // Handle selling currency change
  const handleSellingCurrencyChange = async (newSellingCurrency: string) => {
    setPricingData((prev) => ({
      ...prev,
      sellingCurrency: newSellingCurrency,
    }));

    // Auto-fetch exchange rate if different currency and callback provided
    if (newSellingCurrency !== costCurrency && onExchangeRateUpdate) {
      try {
        setIsUpdatingExchangeRate(true);
        const newExchangeRate = await onExchangeRateUpdate(
          costCurrency,
          newSellingCurrency
        );
        setPricingData((prev) => ({
          ...prev,
          exchangeRate: newExchangeRate,
          exchangeRateDate: new Date(),
        }));
      } catch (error) {
        console.error("Failed to fetch exchange rate:", error);
        // Set default exchange rate to 1.0 and let user manually adjust
        setPricingData((prev) => ({
          ...prev,
          exchangeRate: 1.0,
          exchangeRateDate: new Date(),
        }));
      } finally {
        setIsUpdatingExchangeRate(false);
      }
    } else if (newSellingCurrency === costCurrency) {
      // Same currency, set exchange rate to 1.0
      setPricingData((prev) => ({
        ...prev,
        exchangeRate: 1.0,
        exchangeRateDate: new Date(),
      }));
    }
  };

  // Handle manual exchange rate update
  const handleExchangeRateUpdate = async () => {
    if (
      !pricingData.sellingCurrency ||
      pricingData.sellingCurrency === costCurrency ||
      !onExchangeRateUpdate
    ) {
      return;
    }

    try {
      setIsUpdatingExchangeRate(true);
      const newExchangeRate = await onExchangeRateUpdate(
        costCurrency,
        pricingData.sellingCurrency
      );
      setPricingData((prev) => ({
        ...prev,
        exchangeRate: newExchangeRate,
        exchangeRateDate: new Date(),
      }));
    } catch (error) {
      console.error("Failed to update exchange rate:", error);
    } finally {
      setIsUpdatingExchangeRate(false);
    }
  };

  const calculations = calculatePricing(pricingData, customPrices);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Calculator className="h-5 w-5" />
        <Text weight="plus" size="large">
          Pricing Calculator
        </Text>
      </div>

      {/* Basic Pricing Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="commission">Commission (%)</Label>
          <Input
            id="commission"
            type="number"
            step="0.01"
            min="0"
            max="100"
            placeholder="10"
            value={
              pricingData.commission
                ? (pricingData.commission * 100).toString()
                : ""
            }
            onChange={(e) => {
              const value = e.target.value
                ? parseFloat(e.target.value) / 100
                : undefined;
              handleInputChange("commission", value);
            }}
            disabled={disabled}
          />
          <Text size="small" className="text-ui-fg-subtle">
            Optional: Commission percentage (e.g., 10 for 10%). Leave empty to
            enter supplier price directly.
          </Text>
        </div>

        <div className="space-y-2">
          <Label htmlFor="publicPrice">
            Public Price
            <span className="text-ui-fg-subtle ml-1">
              (Required for commission calculation)
            </span>
          </Label>
          <Input
            id="publicPrice"
            type="number"
            step="0.01"
            min="0"
            placeholder="100.00"
            value={pricingData.publicPrice?.toString() || ""}
            onChange={(e) => {
              const value = e.target.value
                ? parseFloat(e.target.value)
                : undefined;
              handleInputChange("publicPrice", value);
            }}
            disabled={disabled}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="supplierPrice">
            Supplier Price
            <span className="text-ui-fg-subtle ml-1">(Optional)</span>
          </Label>
          <Input
            id="supplierPrice"
            type="number"
            step="0.01"
            min="0"
            placeholder="90.00"
            value={pricingData.supplierPrice?.toString() || ""}
            onChange={(e) => {
              const value = e.target.value
                ? parseFloat(e.target.value)
                : undefined;
              handleInputChange("supplierPrice", value);
            }}
            disabled={disabled}
          />
          <Text size="small" className="text-ui-fg-subtle">
            Enter directly if not using commission calculation
          </Text>
        </div>

        <div className="space-y-2">
          <Label htmlFor="marginRate">Margin Rate (%)</Label>
          <Input
            id="marginRate"
            type="number"
            step="0.01"
            min="0"
            max="99.99"
            placeholder="10"
            value={
              pricingData.marginRate
                ? (pricingData.marginRate * 100).toString()
                : ""
            }
            onChange={(e) => {
              const value = e.target.value
                ? parseFloat(e.target.value) / 100
                : undefined;
              handleInputChange("marginRate", value);
            }}
            disabled={disabled}
          />
          <Text size="small" className="text-ui-fg-subtle">
            Margin rate percentage (e.g., 10 for 10%)
          </Text>
        </div>
      </div>

      {/* Custom Prices */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Text weight="plus">Custom Additional Prices</Text>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={addCustomPrice}
            disabled={
              disabled || !newCustomPrice.name || newCustomPrice.price < 0
            }
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Price
          </Button>
        </div>

        {/* Add new custom price */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 p-3 bg-ui-bg-subtle rounded-lg">
          <Input
            placeholder="Price name (e.g., Setup Fee)"
            value={newCustomPrice.name}
            onChange={(e) =>
              setNewCustomPrice((prev) => ({ ...prev, name: e.target.value }))
            }
            disabled={disabled}
          />
          <Input
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            value={newCustomPrice.price.toString()}
            onChange={(e) =>
              setNewCustomPrice((prev) => ({
                ...prev,
                price: parseFloat(e.target.value) || 0,
              }))
            }
            disabled={disabled}
          />
        </div>

        {/* Existing custom prices */}
        {customPrices.map((customPrice, index) => (
          <div
            key={index}
            className="flex items-center gap-2 p-2 bg-ui-bg-base border rounded-lg"
          >
            {editingIndex === index ? (
              // Editing mode
              <>
                <Input
                  value={editingPrice.name}
                  onChange={(e) =>
                    setEditingPrice((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="Price name"
                  className="flex-1"
                  disabled={disabled}
                />
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={editingPrice.price.toString()}
                  onChange={(e) =>
                    setEditingPrice((prev) => ({
                      ...prev,
                      price: parseFloat(e.target.value) || 0,
                    }))
                  }
                  placeholder="0.00"
                  className="w-24"
                  disabled={disabled}
                />
                <Button
                  type="button"
                  variant="transparent"
                  size="small"
                  onClick={saveEditingCustomPrice}
                  disabled={
                    disabled || !editingPrice.name || editingPrice.price < 0
                  }
                >
                  <Check className="h-4 w-4 text-green-600" />
                </Button>
                <Button
                  type="button"
                  variant="transparent"
                  size="small"
                  onClick={cancelEditingCustomPrice}
                  disabled={disabled}
                >
                  <X className="h-4 w-4 text-gray-600" />
                </Button>
              </>
            ) : (
              // Display mode
              <>
                <Text className="flex-1">{customPrice.name}</Text>
                <Text weight="plus" className="w-24 text-right">
                  {customPrice.price.toFixed(2)}
                </Text>
                <Button
                  type="button"
                  variant="transparent"
                  size="small"
                  onClick={() => startEditingCustomPrice(index)}
                  disabled={disabled}
                >
                  <Edit2 className="h-4 w-4 text-blue-600" />
                </Button>
                <Button
                  type="button"
                  variant="transparent"
                  size="small"
                  onClick={() => removeCustomPrice(index)}
                  disabled={disabled}
                >
                  <X className="h-4 w-4 text-red-600" />
                </Button>
              </>
            )}
          </div>
        ))}
      </div>

      {/* Calculated Values - Supplier Price & Net Price */}
      {showCalculations && (
        <div className="space-y-4 p-4 bg-ui-bg-subtle rounded-lg">
          <Text weight="plus">Calculated Values</Text>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Text size="small" className="text-ui-fg-subtle">
                Supplier Price
              </Text>
              <Text weight="plus">
                {calculations.calculatedSupplierPrice ||
                pricingData.supplierPrice
                  ? (
                      calculations.calculatedSupplierPrice ||
                      pricingData.supplierPrice
                    )?.toFixed(2)
                  : "—"}
              </Text>
              <Text size="small" className="text-ui-fg-subtle">
                {calculations.calculatedSupplierPrice
                  ? "Commission × Public Price"
                  : pricingData.supplierPrice
                  ? "Manually entered"
                  : "Commission × Public Price"}
              </Text>
            </div>
            <div>
              <Text size="small" className="text-ui-fg-subtle">
                Net Price
              </Text>
              <Text weight="plus">
                {calculations.calculatedNetPrice
                  ? calculations.calculatedNetPrice.toFixed(2)
                  : "—"}
              </Text>
              <Text size="small" className="text-ui-fg-subtle">
                Supplier Price + Custom Prices
              </Text>
            </div>
          </div>
        </div>
      )}

      {/* Selling Currency Section */}
      <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="sellingCurrency">Selling Currency</Label>
            <Select
              value={pricingData.sellingCurrency || costCurrency}
              onValueChange={handleSellingCurrencyChange}
              disabled={disabled}
            >
              <Select.Trigger>
                <Select.Value placeholder="Select selling currency" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="CHF">CHF - Swiss Franc</Select.Item>
                <Select.Item value="EUR">EUR - Euro</Select.Item>
                <Select.Item value="USD">USD - US Dollar</Select.Item>
                <Select.Item value="GBP">GBP - British Pound</Select.Item>
                <Select.Item value="CAD">CAD - Canadian Dollar</Select.Item>
                <Select.Item value="AUD">AUD - Australian Dollar</Select.Item>
                <Select.Item value="JPY">JPY - Japanese Yen</Select.Item>
              </Select.Content>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="exchangeRate">
              Exchange Rate
              {pricingData.sellingCurrency &&
                pricingData.sellingCurrency !== costCurrency && (
                  <span className="text-ui-fg-subtle ml-1">
                    ({costCurrency} → {pricingData.sellingCurrency})
                  </span>
                )}
            </Label>
            <div className="flex gap-2">
              <Input
                id="exchangeRate"
                type="number"
                step="0.01"
                min="0"
                placeholder="1.0000"
                value={pricingData.exchangeRate?.toString() || ""}
                onChange={(e) => {
                  const value = e.target.value
                    ? parseFloat(e.target.value)
                    : undefined;
                  setPricingData((prev) => ({ ...prev, exchangeRate: value }));
                }}
                disabled={
                  disabled || pricingData.sellingCurrency === costCurrency
                }
                className="flex-1"
              />
              {onExchangeRateUpdate &&
                pricingData.sellingCurrency &&
                pricingData.sellingCurrency !== costCurrency && (
                  <Button
                    type="button"
                    variant="secondary"
                    size="small"
                    onClick={handleExchangeRateUpdate}
                    disabled={disabled || isUpdatingExchangeRate}
                  >
                    <RefreshCw
                      className={`h-4 w-4 ${
                        isUpdatingExchangeRate ? "animate-spin" : ""
                      }`}
                    />
                  </Button>
                )}
            </div>
            {pricingData.sellingCurrency === costCurrency && (
              <Text size="small" className="text-ui-fg-subtle">
                Same currency - no conversion needed
              </Text>
            )}
            {pricingData.exchangeRateDate && (
              <Text size="small" className="text-ui-fg-subtle">
                Updated: {pricingData.exchangeRateDate.toLocaleString()}
              </Text>
            )}
          </div>
        </div>
      </div>

      {/* Calculated Selling Prices */}
      {showCalculations && (
        <div className="space-y-4 p-4 bg-green-50 rounded-lg border border-green-200">
          <Text weight="plus">Calculated Selling Prices</Text>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Text size="small" className="text-ui-fg-subtle">
                Selling Price (Cost Currency - {costCurrency})
              </Text>
              <Text weight="plus">
                {calculations.calculatedSellingPrice
                  ? calculations.calculatedSellingPrice.toFixed(2)
                  : "—"}
              </Text>
              <Text size="small" className="text-ui-fg-subtle">
                Net Price ÷ (1 - Margin Rate)
              </Text>
            </div>
            {pricingData.sellingCurrency &&
              pricingData.sellingCurrency !== costCurrency && (
                <div>
                  <Text size="small" className="text-ui-fg-subtle">
                    Selling Price (Selling Currency -{" "}
                    {pricingData.sellingCurrency})
                  </Text>
                  <Text weight="plus">
                    {calculations.calculatedSellingPriceSellingCurrency
                      ? calculations.calculatedSellingPriceSellingCurrency.toFixed(
                          2
                        )
                      : "—"}
                  </Text>
                  <Text size="small" className="text-ui-fg-subtle">
                    Rate:{" "}
                    {pricingData.exchangeRate
                      ? parseFloat(pricingData.exchangeRate.toString()).toFixed(
                          4
                        )
                      : "—"}
                  </Text>
                </div>
              )}
          </div>
        </div>
      )}

      {/* Errors */}
      {errors.length > 0 && (
        <div className="space-y-2">
          <Text weight="plus" className="text-ui-fg-error">
            Pricing Errors:
          </Text>
          {errors.map((error, index) => (
            <Text key={index} size="small" className="text-ui-fg-error">
              • {error}
            </Text>
          ))}
        </div>
      )}
    </div>
  );
};

export default PricingCalculator;
