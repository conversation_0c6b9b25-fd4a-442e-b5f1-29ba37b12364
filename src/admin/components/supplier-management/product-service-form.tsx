import React, { useState, useEffect } from "react";
import { Save, X } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  toast,
} from "@camped-ai/ui";
import { useCategories } from "../../hooks/supplier-products-services/use-categories";
import { useUnitTypes } from "../../hooks/supplier-products-services/use-unit-types";
import { useTags } from "../../hooks/supplier-products-services/use-tags";
import DynamicFieldRenderer, {
  type DynamicFieldSchema,
} from "./dynamic-field-renderer";
import { generateProductServiceName } from "../../utils/name-generator";

export interface FormData {
  name?: string; // For edit mode
  type: "Product" | "Service";
  description: string;
  base_cost: number | "";
  category_id: string;
  unit_type_id: string;
  tags: string[];
  custom_fields: Record<string, any>;
  status: "active" | "inactive";
  change_reason?: string; // For edit mode
}

export interface ProductService {
  id: string;
  name: string;
  type: "Product" | "Service";
  description?: string;
  base_cost?: number;
  category_id: string;
  unit_type_id: string;
  tags?: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  custom_fields?: Record<string, any>;
  status: "active" | "inactive";
  category?: {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
    dynamic_field_schema?: any[];
  };
  unit_type?: { id: string; name: string };
}

interface ProductServiceFormProps {
  mode: "create" | "edit";
  initialData?: ProductService;
  onSubmit: (data: FormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  title?: string;
  submitButtonText?: string;
}

const ProductServiceForm: React.FC<ProductServiceFormProps> = ({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  submitButtonText,
}) => {
  const { data: categoriesResponse, isLoading: categoriesLoading } =
    useCategories({ is_active: true });
  const { data: unitTypesResponse } = useUnitTypes({ is_active: true });
  const { data: tagsResponse } = useTags({ is_active: true });

  // Extract arrays from response objects
  const categories = categoriesResponse?.categories || [];
  const unitTypes = unitTypesResponse?.unit_types || [];
  const tags = tagsResponse?.tags || [];

  // Initialize form data based on mode
  const [formData, setFormData] = useState<FormData>(() => {
    if (mode === "edit" && initialData) {
      return {
        name: initialData.name,
        type: initialData.type,
        description: initialData.description || "",
        base_cost: initialData.base_cost || "",
        category_id: initialData.category_id || "",
        unit_type_id: initialData.unit_type_id || "",
        tags: initialData.tags?.map((tag) => tag.id) || [],
        custom_fields: initialData.custom_fields || {},
        status: initialData.status,
        change_reason: "",
      };
    }
    return {
      type: "Product",
      description: "",
      base_cost: "",
      category_id: "",
      unit_type_id: "",
      tags: [],
      custom_fields: {},
      status: "active",
    };
  });

  // State for auto-generated name (create mode only)
  const [generatedName, setGeneratedName] = useState<string>("");
  const [showChangeReason, setShowChangeReason] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [customFieldErrors, setCustomFieldErrors] = useState<
    Record<string, string>
  >({});

  // Filter categories based on selected type
  const filteredCategories = React.useMemo(() => {
    return categories.filter((category) => {
      return (
        category.category_type === formData.type ||
        category.category_type === "Both"
      );
    });
  }, [categories, formData.type]);

  // Get selected category for dynamic fields
  const selectedCategory = categories.find(
    (cat) => cat.id === formData.category_id
  );
  const allDynamicFields = selectedCategory?.dynamic_field_schema || [];

  // Filter fields to only show those marked for product use
  const dynamicFieldSchema = allDynamicFields.filter(
    (field) => field.used_in_product !== false
  );

  // Generate name when category or custom fields change (create mode only)
  useEffect(() => {
    if (mode === "create" && selectedCategory) {
      const newGeneratedName = generateProductServiceName(
        selectedCategory.name,
        formData.custom_fields,
        dynamicFieldSchema
      );
      setGeneratedName(newGeneratedName);
    }
  }, [mode, selectedCategory, formData.custom_fields, dynamicFieldSchema]);

  // Check if base cost changed (edit mode only)
  useEffect(() => {
    if (mode === "edit" && initialData) {
      const currentCost =
        formData.base_cost === "" ? 0 : Number(formData.base_cost);
      const originalCost = initialData.base_cost || 0;
      setShowChangeReason(currentCost !== originalCost);
    }
  }, [mode, formData.base_cost, initialData]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleCustomFieldChange = (key: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      custom_fields: { ...prev.custom_fields, [key]: value },
    }));

    // Clear custom field error when user starts typing
    if (customFieldErrors[key]) {
      setCustomFieldErrors((prev) => ({ ...prev, [key]: "" }));
    }
  };

  // Clear category when type changes to prevent invalid combinations
  useEffect(() => {
    if (formData.category_id) {
      const currentCategory = categories.find(
        (cat) => cat.id === formData.category_id
      );
      if (
        currentCategory &&
        currentCategory.category_type !== formData.type &&
        currentCategory.category_type !== "Both"
      ) {
        setFormData((prev) => ({
          ...prev,
          category_id: "",
          custom_fields: {},
        }));
        setCustomFieldErrors({});
        setGeneratedName("");
      }
    }
  }, [formData.type, categories, formData.category_id]);

  // Reset custom fields when category changes (create mode only)
  useEffect(() => {
    if (mode === "create" && formData.category_id) {
      setFormData((prev) => ({ ...prev, custom_fields: {} }));
      setCustomFieldErrors({});
    }
  }, [mode, formData.category_id]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const newCustomFieldErrors: Record<string, string> = {};

    // Name validation for create mode
    if (mode === "create" && !generatedName.trim()) {
      newErrors.generated_name =
        "Unable to generate name. Please fill in all required fields.";
    }

    if (!formData.category_id) {
      newErrors.category_id = "Category is required";
    }

    if (!formData.unit_type_id) {
      newErrors.unit_type_id = "Unit type is required";
    }

    if (formData.base_cost !== "" && formData.base_cost < 0) {
      newErrors.base_cost = "Base cost must be non-negative";
    }

    // Validate change reason for edit mode when cost changed
    // if (
    //   mode === "edit" &&
    //   showChangeReason &&
    //   !formData.change_reason?.trim()
    // ) {
    //   newErrors.change_reason = "Please provide a reason for the cost change";
    //   console.log("❌ Missing cost change reason in edit mode");
    // }

    // Validate custom fields
    dynamicFieldSchema.forEach((field: DynamicFieldSchema) => {
      if (field.required) {
        const value = formData.custom_fields[field.key];
        if (!value || (Array.isArray(value) && value.length === 0)) {
          newCustomFieldErrors[field.key] = `${field.label} is required`;
        }
      }
    });

    setErrors(newErrors);
    setCustomFieldErrors(newCustomFieldErrors);
    return (
      Object.keys(newErrors).length === 0 &&
      Object.keys(newCustomFieldErrors).length === 0
    );
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error("Please fill all the required fields");
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error(
        `Error ${mode === "create" ? "creating" : "updating"} product/service:`,
        error
      );
    }
  };

  const defaultSubmitText =
    mode === "create"
      ? isSubmitting
        ? "Creating..."
        : "Create Product/Service"
      : isSubmitting
      ? "Updating..."
      : "Update Product/Service";

  return (
    <Container className="p-6">
      <div className="mx-auto space-y-8">
        {/* Basic Information */}
        <div className="bg-white rounded-lg border p-6">
          <div className="mb-6">
            <Heading level="h3" className="text-lg font-medium">
              Basic Information
            </Heading>
            <Text className="text-ui-fg-subtle text-sm">
              Enter the basic details for your product or service
            </Text>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Name field - different for create vs edit */}
            {mode === "create" ? (
              <div className="space-y-2">
                <Label>Generated Name</Label>
                <div className="p-3 bg-ui-bg-subtle border border-ui-border-base rounded-md">
                  <Text
                    className={
                      generatedName ? "text-ui-fg-base" : "text-ui-fg-muted"
                    }
                  >
                    {generatedName ||
                      "Name will be generated based on category and required fields"}
                  </Text>
                </div>
                {errors.generated_name && (
                  <Text size="small" className="text-red-600">
                    {errors.generated_name}
                  </Text>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="name">Name * (Auto-generated)</Label>
                <Input
                  id="name"
                  value={formData.name || ""}
                  readOnly
                  placeholder="Name is auto-generated based on category and mandatory fields"
                  className="bg-gray-50"
                />
                <Text size="small" className="text-ui-fg-subtle">
                  Generated from: {selectedCategory?.name} + mandatory fields
                </Text>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="type">Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleInputChange("type", value)}
              >
                <Select.Trigger>
                  <Select.Value placeholder="Select type" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="Product">Product</Select.Item>
                  <Select.Item value="Service">Service</Select.Item>
                </Select.Content>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) =>
                  handleInputChange("category_id", value)
                }
                disabled={categoriesLoading}
              >
                <Select.Trigger
                  className={errors.category_id ? "border-red-500" : ""}
                >
                  <Select.Value
                    placeholder={
                      categoriesLoading
                        ? "Loading categories..."
                        : filteredCategories.length === 0
                        ? `No categories available for ${formData.type.toLowerCase()}s`
                        : "Select category"
                    }
                  />
                </Select.Trigger>
                <Select.Content>
                  {filteredCategories.length === 0 ? (
                    <div className="px-2 py-1.5 text-sm text-ui-fg-muted">
                      No categories available for {formData.type.toLowerCase()}s
                    </div>
                  ) : (
                    filteredCategories.map((category) => (
                      <Select.Item key={category.id} value={category.id}>
                        <div className="flex items-center gap-2">
                          {category.icon && (
                            <span className="text-sm">{category.icon}</span>
                          )}
                          <span>{category.name}</span>
                          <span className="text-xs text-gray-500 ml-auto">
                            ({category.category_type})
                          </span>
                        </div>
                      </Select.Item>
                    ))
                  )}
                </Select.Content>
              </Select>
              {errors.category_id && (
                <Text size="small" className="text-red-600">
                  {errors.category_id}
                </Text>
              )}
              {filteredCategories.length === 0 && !categoriesLoading && (
                <Text size="small" className="text-amber-600">
                  No categories are configured for {formData.type.toLowerCase()}
                  s. Please configure categories in the Categories Configuration
                  page.
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="unit_type">Unit Type *</Label>
              <Select
                value={formData.unit_type_id}
                onValueChange={(value) =>
                  handleInputChange("unit_type_id", value)
                }
              >
                <Select.Trigger
                  className={errors.unit_type_id ? "border-red-500" : ""}
                >
                  <Select.Value placeholder="Select unit type" />
                </Select.Trigger>
                <Select.Content>
                  {unitTypes.map((unitType) => (
                    <Select.Item key={unitType.id} value={unitType.id}>
                      {unitType.name}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
              {errors.unit_type_id && (
                <Text size="small" className="text-red-600">
                  {errors.unit_type_id}
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <Select
                value=""
                onValueChange={(value) => {
                  if (value && !formData.tags.includes(value)) {
                    handleInputChange("tags", [...formData.tags, value]);
                  }
                }}
              >
                <Select.Trigger>
                  <Select.Value placeholder="Add tags" />
                </Select.Trigger>
                <Select.Content>
                  {tags
                    .filter((tag) => !formData.tags.includes(tag.id))
                    .map((tag) => (
                      <Select.Item key={tag.id} value={tag.id}>
                        <div className="flex items-center gap-2">
                          {tag.color && (
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: tag.color }}
                            />
                          )}
                          {tag.name}
                        </div>
                      </Select.Item>
                    ))}
                </Select.Content>
              </Select>
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tagId) => {
                    const tag = tags.find((t) => t.id === tagId);
                    return tag ? (
                      <div
                        key={tagId}
                        className="flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-md text-sm"
                      >
                        {tag.color && (
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: tag.color }}
                          />
                        )}
                        {tag.name}
                        <button
                          type="button"
                          onClick={() => {
                            handleInputChange(
                              "tags",
                              formData.tags.filter((id) => id !== tagId)
                            );
                          }}
                          className="ml-1 text-gray-500 hover:text-red-500"
                        >
                          ×
                        </button>
                      </div>
                    ) : null;
                  })}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="base_cost">Base Cost</Label>
              <Input
                id="base_cost"
                type="number"
                min="0"
                step="0.01"
                value={formData.base_cost}
                onChange={(e) =>
                  handleInputChange(
                    "base_cost",
                    e.target.value ? parseFloat(e.target.value) : ""
                  )
                }
                placeholder="Enter base cost (optional)"
                className={errors.base_cost ? "border-red-500" : ""}
              />
              {errors.base_cost && (
                <Text size="small" className="text-red-600">
                  {errors.base_cost}
                </Text>
              )}
              <Text size="small" className="text-ui-fg-subtle">
                Optional base cost used for pricing calculations
              </Text>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="status"
                    value="active"
                    checked={formData.status === "active"}
                    onChange={(e) =>
                      handleInputChange("status", e.target.value)
                    }
                    className="w-4 h-4 text-blue-600"
                  />
                  <span className="text-sm">Active</span>
                </label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="status"
                    value="inactive"
                    checked={formData.status === "inactive"}
                    onChange={(e) =>
                      handleInputChange("status", e.target.value)
                    }
                    className="w-4 h-4 text-blue-600"
                  />
                  <span className="text-sm">Inactive</span>
                </label>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Enter product/service description"
              rows={4}
            />
          </div>
        </div>

        {/* Dynamic Custom Fields */}
        {dynamicFieldSchema.length > 0 && (
          <div className="bg-white rounded-lg border p-6">
            <DynamicFieldRenderer
              schema={dynamicFieldSchema}
              values={formData.custom_fields}
              onChange={handleCustomFieldChange}
              errors={customFieldErrors}
              disabled={isSubmitting}
              fieldContext="supplier"
            />
          </div>
        )}

        {/* Form Actions */}
        <div className="flex items-center justify-end gap-3 pt-6 border-t">
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button type="button" onClick={handleSubmit} disabled={isSubmitting}>
            <Save className="w-4 h-4 mr-2" />
            {submitButtonText || defaultSubmitText}
          </Button>
        </div>
      </div>
    </Container>
  );
};

export default ProductServiceForm;
