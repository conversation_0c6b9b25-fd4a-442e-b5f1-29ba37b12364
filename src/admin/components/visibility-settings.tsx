import { Switch, Label, Text } from "@camped-ai/ui";

export interface VisibilityOption {
  id: string;
  label: string;
  description: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
}

export interface VisibilitySettingsProps {
  title: string;
  options: VisibilityOption[];
  className?: string;
}

const VisibilitySettings = ({
  title,
  options,
  className = "",
}: VisibilitySettingsProps) => {
  return (
    <div
      className={`bg-card pt-4 p-6 rounded-lg shadow-sm border border-border ${className}`}
    >
      <h3 className="text-lg font-medium mb-4 text-card-foreground">{title}</h3>
      <div className="space-y-4">
        {options.map((option) => (
          <div key={option.id} className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Switch
                id={option.id}
                checked={option.checked}
                onCheckedChange={option.onChange}
              />
              <Label
                htmlFor={option.id}
                className="cursor-pointer text-foreground"
              >
                {option.label}
              </Label>
            </div>
            <Text className="text-sm text-muted-foreground">
              {option.description}
            </Text>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VisibilitySettings;
