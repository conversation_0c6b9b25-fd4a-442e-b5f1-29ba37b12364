import React from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import { 
  DollarSign, 
  Bed, 
  Users, 
  Plus,
  Receipt,
  CreditCard
} from "lucide-react";

interface PricingItem {
  label: string;
  description?: string;
  quantity?: number;
  rate?: number;
  total: number;
  currency?: string;
}

interface PricingSummaryProps {
  roomCharges: PricingItem;
  addOns?: PricingItem[];
  extraAdults?: PricingItem;
  currency?: string;
  className?: string;
}

const ImprovedPricingSummary: React.FC<PricingSummaryProps> = ({
  roomCharges,
  addOns = [],
  extraAdults,
  currency = "USD",
  className = "",
}) => {
  // Calculate total
  const subtotal = roomCharges.total + 
    (addOns.reduce((sum, addon) => sum + addon.total, 0)) + 
    (extraAdults?.total || 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  return (
    <Container className={`bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
            <Receipt className="w-4 h-4 text-white" />
          </div>
          <Heading level="h3" className="text-white font-semibold text-lg">
            Pricing Summary
          </Heading>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Room Charges */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-6 h-6 bg-blue-100 rounded-md flex items-center justify-center">
              <Bed className="w-4 h-4 text-blue-600" />
            </div>
            <Text className="font-medium text-gray-900">Room Charges</Text>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <Text className="font-medium text-gray-900">{roomCharges.label}</Text>
                {roomCharges.description && (
                  <Text className="text-sm text-gray-600 mt-1">{roomCharges.description}</Text>
                )}
              </div>
              <div className="text-right ml-4">
                <Text className="font-semibold text-lg text-gray-900">
                  {formatCurrency(roomCharges.total)}
                </Text>
              </div>
            </div>
          </div>
        </div>

        {/* Add-ons & Services */}
        {addOns.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-6 h-6 bg-green-100 rounded-md flex items-center justify-center">
                <Plus className="w-4 h-4 text-green-600" />
              </div>
              <Text className="font-medium text-gray-900">Add-ons & Services</Text>
            </div>
            
            <div className="space-y-2">
              {addOns.map((addon, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <Text className="font-medium text-gray-900">{addon.label}</Text>
                      {addon.description && (
                        <Text className="text-sm text-gray-600 mt-1">{addon.description}</Text>
                      )}
                      {addon.quantity && (
                        <Text className="text-xs text-gray-500 mt-1">
                          Quantity: {addon.quantity}
                        </Text>
                      )}
                    </div>
                    <div className="text-right ml-4">
                      <Text className="font-semibold text-gray-900">
                        {formatCurrency(addon.total)}
                      </Text>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Extra Adults */}
        {extraAdults && (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-6 h-6 bg-orange-100 rounded-md flex items-center justify-center">
                <Users className="w-4 h-4 text-orange-600" />
              </div>
              <Text className="font-medium text-gray-900">Extra Adults</Text>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <Text className="font-medium text-gray-900">{extraAdults.label}</Text>
                  {extraAdults.description && (
                    <Text className="text-sm text-gray-600 mt-1">{extraAdults.description}</Text>
                  )}
                  {extraAdults.quantity && (
                    <Text className="text-xs text-gray-500 mt-1">
                      {extraAdults.quantity} adult{extraAdults.quantity > 1 ? 's' : ''}
                    </Text>
                  )}
                </div>
                <div className="text-right ml-4">
                  <Text className="font-semibold text-gray-900">
                    {formatCurrency(extraAdults.total)}
                  </Text>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Total */}
        <div className="border-t border-gray-200 pt-4">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-600 rounded-md flex items-center justify-center">
                  <CreditCard className="w-4 h-4 text-white" />
                </div>
                <Text className="font-semibold text-lg text-gray-900">Total Amount</Text>
              </div>
              <Text className="font-bold text-2xl text-blue-700">
                {formatCurrency(subtotal)}
              </Text>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default ImprovedPricingSummary;
