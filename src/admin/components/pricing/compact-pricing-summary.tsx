import React from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import { 
  Receipt,
  ChevronRight
} from "lucide-react";

interface PricingItem {
  label: string;
  description?: string;
  quantity?: number;
  rate?: number;
  total: number;
  currency?: string;
}

interface CompactPricingSummaryProps {
  roomCharges: PricingItem;
  addOns?: PricingItem[];
  extraAdults?: PricingItem;
  currency?: string;
  className?: string;
}

const CompactPricingSummary: React.FC<CompactPricingSummaryProps> = ({
  roomCharges,
  addOns = [],
  extraAdults,
  currency = "USD",
  className = "",
}) => {
  // Calculate total
  const subtotal = roomCharges.total + 
    (addOns.reduce((sum, addon) => sum + addon.total, 0)) + 
    (extraAdults?.total || 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  return (
    <Container className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-blue-600 px-4 py-3">
        <div className="flex items-center gap-2">
          <Receipt className="w-4 h-4 text-white" />
          <Heading level="h4" className="text-white font-medium text-sm uppercase tracking-wide">
            Pricing Summary
          </Heading>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-3">
        {/* Room Charges */}
        <div className="flex justify-between items-center py-2">
          <div>
            <Text className="font-medium text-gray-900 text-sm">Room Charges</Text>
            <Text className="text-xs text-gray-600">{roomCharges.label}</Text>
          </div>
          <Text className="font-semibold text-gray-900">
            {formatCurrency(roomCharges.total)}
          </Text>
        </div>

        {/* Add-ons & Services */}
        {addOns.length > 0 && (
          <div className="border-t border-gray-100 pt-3">
            <div className="flex justify-between items-center py-2">
              <div>
                <Text className="font-medium text-gray-900 text-sm">Add-ons & Services</Text>
                <Text className="text-xs text-gray-600">
                  {addOns.map(addon => addon.label).join(", ")}
                </Text>
              </div>
              <Text className="font-semibold text-gray-900">
                {formatCurrency(addOns.reduce((sum, addon) => sum + addon.total, 0))}
              </Text>
            </div>
          </div>
        )}

        {/* Extra Adults */}
        {extraAdults && (
          <div className="border-t border-gray-100 pt-3">
            <div className="flex justify-between items-center py-2">
              <div>
                <Text className="font-medium text-gray-900 text-sm">Extra Adults</Text>
                <Text className="text-xs text-gray-600">{extraAdults.label}</Text>
              </div>
              <Text className="font-semibold text-gray-900">
                {formatCurrency(extraAdults.total)}
              </Text>
            </div>
          </div>
        )}

        {/* Total */}
        <div className="border-t-2 border-blue-100 pt-3 mt-4">
          <div className="flex justify-between items-center">
            <Text className="font-bold text-gray-900">Total Amount</Text>
            <Text className="font-bold text-xl text-blue-600">
              {formatCurrency(subtotal)}
            </Text>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default CompactPricingSummary;
