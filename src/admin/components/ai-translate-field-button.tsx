import React from "react";
import { <PERSON><PERSON>, <PERSON>lt<PERSON> } from "@camped-ai/ui";
import { Languages, Sparkles } from "lucide-react";
import { toast } from "sonner";
import { generateTranslationKey } from "../utils/translation-utils";

/**
 * Clean up translated text by removing quotes and extra whitespace
 */
const cleanTranslatedText = (text: string): string => {
  if (!text) return text;

  // Remove leading and trailing whitespace
  let cleaned = text.trim();

  // Remove quotes from both ends if they exist
  // Handle both single and double quotes
  if ((cleaned.startsWith('"') && cleaned.endsWith('"')) ||
    (cleaned.startsWith("'") && cleaned.endsWith("'"))) {
    cleaned = cleaned.slice(1, -1).trim();
  }

  // Remove any remaining quotes that might be at the beginning or end
  // This handles cases where AI might add quotes inconsistently
  cleaned = cleaned.replace(/^["']|["']$/g, '').trim();

  return cleaned;
};

interface AITranslateFieldButtonProps {
  entityType: "destination" | "hotel" | "room" | "add_on" | "add_on_category";
  entityId: string;
  fieldName: string;
  fieldValue: string;
  targetLanguage: string;
  onTranslationComplete?: (translatedValue: string) => void;
  onTranslationError?: (error: Error) => void;
  buttonVariant?: "primary" | "secondary" | "transparent";
  buttonSize?: "small" | "base" | "large";
  className?: string;
  disabled?: boolean;
}

/**
 * A button component that provides AI translation for a specific field
 *
 * @example
 * <AITranslateFieldButton
 *   entityType="add_on"
 *   entityId="addon_123"
 *   fieldName="name"
 *   fieldValue="Ski Lessons"
 *   targetLanguage="de"
 *   onTranslationComplete={(translatedValue) => {
 *     setTranslatedName(translatedValue);
 *   }}
 * />
 */
const AITranslateFieldButton: React.FC<AITranslateFieldButtonProps> = ({
  entityType,
  entityId,
  fieldName,
  fieldValue,
  targetLanguage,
  onTranslationComplete,
  onTranslationError,
  buttonVariant = "transparent",
  buttonSize = "small",
  className = "",
  disabled = false,
}) => {
  const [isTranslating, setIsTranslating] = React.useState(false);

  const handleTranslate = async () => {
    if (!fieldValue || !fieldValue.trim()) {
      toast.error("No content to translate");
      return;
    }

    try {
      setIsTranslating(true);

      // Create a system prompt for field translation
      const systemPrompt = `You are a professional translator specializing in hospitality and travel content. Your task is to translate ${fieldName} content from English to ${targetLanguage}.

Context:
- Entity type: ${entityType}
- Field: ${fieldName}
- Target language: ${targetLanguage}

Instructions:
1. Translate the content accurately while preserving meaning
2. Use appropriate terminology for the hospitality industry
3. Keep translations concise and clear
4. Maintain consistency in style and tone
5. Consider cultural context and local preferences
6. Return ONLY the translated text without explanations or additional formatting
7. Do not add quotes, brackets, or other formatting around the translation`;

      const userPrompt = `Translate the following ${fieldName} to ${targetLanguage}:

${fieldValue}

Return only the translated text.`;

      // Use the existing AI generation service
      const response = await fetch("/admin/ai-generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "general",
          prompt: userPrompt,
          context: {
            systemPrompt,
            fieldName,
            targetLanguage,
            entityType,
            entityId,
          },
          maxLength: 500,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `Translation API error: ${response.status}`
        );
      }

      const data = await response.json();
      const rawTranslatedText = data.content?.trim();

      if (!rawTranslatedText) {
        throw new Error("No translation received from AI service");
      }

      // Clean up the translated text by removing quotes
      const translatedText = cleanTranslatedText(rawTranslatedText);

      // Save the translation to Tolgee
      const translationKey = generateTranslationKey(
        entityType,
        entityId,
        fieldName
      );
      await saveTranslationToTolgee(
        translationKey,
        translatedText,
        targetLanguage
      );

      toast.success(
        `Translated ${fieldName} to ${targetLanguage.toUpperCase()}`
      );
      onTranslationComplete?.(translatedText);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Translation failed";
      toast.error(`Translation failed: ${errorMessage}`);
      onTranslationError?.(new Error(errorMessage));
    } finally {
      setIsTranslating(false);
    }
  };

  // Helper function to save translation to Tolgee
  const saveTranslationToTolgee = async (
    key: string,
    value: string,
    language: string
  ) => {
    try {
      const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
      const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

      if (!apiKey || !projectId) {
        console.warn(
          "Tolgee API configuration missing, skipping translation save"
        );
        return;
      }

      const response = await fetch(
        `https://app.tolgee.io/v2/projects/${projectId}/translations`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey,
          },
          body: JSON.stringify({
            key,
            translations: {
              [language]: value,
            },
          }),
        }
      );

      if (!response.ok) {
        console.error("Failed to save translation to Tolgee:", response.status);
      }
    } catch (error) {
      console.error("Error saving translation to Tolgee:", error);
    }
  };

  const getTooltipText = () => {
    if (isTranslating)
      return `Translating ${fieldName} to ${targetLanguage.toUpperCase()}...`;
    if (!fieldValue || !fieldValue.trim()) return "No content to translate";
    return `Translate ${fieldName} to ${targetLanguage.toUpperCase()} with AI`;
  };

  const isButtonDisabled =
    disabled || isTranslating || !fieldValue || !fieldValue.trim();

  return (
    <Tooltip content={getTooltipText()}>
      <Button
        variant={buttonVariant}
        size={buttonSize}
        onClick={handleTranslate}
        disabled={isButtonDisabled}
        className={`flex items-center ${className}`}
      >
        {isTranslating ? (
          <Sparkles size={14} className="animate-pulse text-blue-500" />
        ) : (
          <Languages size={14} />
        )}
      </Button>
    </Tooltip>
  );
};

export default AITranslateFieldButton;
