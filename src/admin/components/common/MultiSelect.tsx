import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, X } from "lucide-react";
import { Text } from "@camped-ai/ui";

interface MultiSelectOption {
  value: string;
  label: string;
}

interface MultiSelectProps {
  options: MultiSelectOption[];
  selectedValues: string[];
  onChange: (selectedValues: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showSelectAll?: boolean;
  showSelectedTags?: boolean;
  maxHeight?: string;
}

export const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  selectedValues,
  onChange,
  placeholder = "Select options...",
  className = "",
  disabled = false,
  showSelectAll = true,
  showSelectedTags = true,
  maxHeight = "max-h-60",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleToggleOption = (value: string) => {
    if (selectedValues.includes(value)) {
      onChange(selectedValues.filter((v) => v !== value));
    } else {
      onChange([...selectedValues, value]);
    }
  };

  const handleSelectAll = () => {
    if (selectedValues.length === options.length) {
      // Deselect all
      onChange([]);
    } else {
      // Select all
      onChange(options.map((option) => option.value));
    }
  };

  const handleRemoveTag = (value: string, event: React.MouseEvent) => {
    event.stopPropagation();
    onChange(selectedValues.filter((v) => v !== value));
  };

  const getSelectedLabels = () => {
    return selectedValues.map((value) => {
      const option = options.find((opt) => opt.value === value);
      return option ? option.label : value;
    });
  };

  const isAllSelected =
    selectedValues.length === options.length && options.length > 0;

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Selected Tags Display */}

      {/* Dropdown Trigger */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          my-1 relative z-10 w-full flex items-center justify-between px-3 py-1 text-left
          border border-input rounded-md shadow-sm
          bg-ui-bg-field dark:bg-ui-bg-field
          ${
            disabled
              ? "bg-gray-100 dark:bg-gray-700 cursor-not-allowed opacity-50"
              : "hover:border-accent-foreground hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
          }
          ${isOpen ? "border-primary ring-1 ring-primary/20" : ""}
        `}
      >
        <Text>
          {selectedValues.length === 0
            ? placeholder
            : `${selectedValues.length} selected`}
        </Text>
        <ChevronDown
          className={`h-4 w-4 text-gray-400 dark:text-gray-500 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className={`absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-border rounded-md shadow-lg ${maxHeight} overflow-auto`}
        >
          {/* Select All Option */}
          {showSelectAll && options.length > 0 && (
            <>
              <button
                type="button"
                onClick={handleSelectAll}
                className="w-full px-3 py-2 text-left hover:bg-accent flex items-center gap-2 border-b border-border"
              >
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  onChange={() => {}} // Handled by button click
                  className="rounded border-input text-primary focus:ring-primary"
                />
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {isAllSelected ? "Deselect All" : "Select All"}
                </span>
              </button>
            </>
          )}

          {/* Options */}
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleToggleOption(option.value)}
              className="w-full px-3 py-1 text-left hover:bg-accent flex items-center gap-2"
            >
              <input
                type="checkbox"
                checked={selectedValues.includes(option.value)}
                onChange={() => {}} // Handled by button click
                className="rounded border-input text-primary focus:ring-primary"
              />
              <span className="text-sm text-gray-900 dark:text-gray-100">
                {option.label}
              </span>
            </button>
          ))}

          {/* Empty State */}
          {options.length === 0 && (
            <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
              No options available
            </div>
          )}
        </div>
      )}

      {showSelectedTags && selectedValues.length > 0 && (
        <div className="mt-1 flex flex-wrap gap-1">
          {getSelectedLabels().map((label, index) => (
            <span
              key={selectedValues[index]}
              className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-blue-600 text-white rounded-md"
            >
              <span className="truncate max-w-32">{label}</span>
              {!disabled && (
                <button
                  type="button"
                  onClick={(e) => handleRemoveTag(selectedValues[index], e)}
                  className="hover:bg-white/20 rounded-full p-0.5 flex-shrink-0 text-white"
                >
                  <X className="h-2.5 w-2.5" />
                </button>
              )}
            </span>
          ))}
        </div>
      )}
    </div>
  );
};
