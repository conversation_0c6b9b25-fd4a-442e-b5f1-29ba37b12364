import React from "react";
import { useAdminCurrency } from "../../hooks/use-admin-currencies";
import { formatCurrencyAmount, getCurrencySymbol } from "../../utils/currency-utils";

interface CurrencyDisplayProps {
  amount: number;
  currencyCode: string;
  className?: string;
  showSymbol?: boolean;
  showCode?: boolean;
  symbolPosition?: "before" | "after";
  fallbackSymbol?: string;
}

export const CurrencyDisplay: React.FC<CurrencyDisplayProps> = ({
  amount,
  currencyCode,
  className = "",
  showSymbol = true,
  showCode = false,
  symbolPosition = "before",
  fallbackSymbol,
}) => {
  const { currency, isLoading } = useAdminCurrency(currencyCode);

  if (isLoading) {
    return (
      <span className={`inline-block bg-gray-200 rounded animate-pulse ${className}`}>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      </span>
    );
  }

  // Use currency from hook if available, otherwise create a fallback
  const displayCurrency = currency || {
    currency_code: currencyCode.toUpperCase(),
    symbol: fallbackSymbol || getCurrencySymbol(currencyCode),
    decimal_digits: 2,
    name: currencyCode.toUpperCase(),
    is_default: false,
  };

  const formattedAmount = formatCurrencyAmount(amount, displayCurrency, {
    showSymbol,
    showCode,
    symbolPosition,
  });

  return <span className={className}>{formattedAmount}</span>;
};

interface CurrencyInputProps {
  value: number;
  onChange: (value: number) => void;
  currencyCode: string;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  min?: number;
  max?: number;
  step?: number;
}

export const CurrencyInput: React.FC<CurrencyInputProps> = ({
  value,
  onChange,
  currencyCode,
  className = "",
  placeholder = "0.00",
  disabled = false,
  min,
  max,
  step,
}) => {
  const { currency } = useAdminCurrency(currencyCode);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseFloat(event.target.value) || 0;
    onChange(newValue);
  };

  const decimalPlaces = currency?.decimal_digits || 2;
  const stepValue = step || 1 / Math.pow(10, decimalPlaces);

  return (
    <input
      type="number"
      value={value}
      onChange={handleChange}
      className={`border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
      placeholder={placeholder}
      disabled={disabled}
      min={min}
      max={max}
      step={stepValue}
    />
  );
};
