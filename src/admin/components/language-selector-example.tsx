import { useState } from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import LanguageSelector from "./language-selector";

/**
 * Example component showing how to use the LanguageSelector component
 * This can be used as a reference for integrating language selection
 * into any form or component that needs translation support.
 */
const LanguageSelectorExample = () => {
  const [selectedLanguage, setSelectedLanguage] = useState("en");

  const handleLanguageChange = (languageCode: string) => {
    console.log("Language changed to:", languageCode);
    setSelectedLanguage(languageCode);
    
    // Here you would typically:
    // 1. Update the form fields to show content in the selected language
    // 2. Fetch translations for the selected language
    // 3. Update the UI to reflect the language change
  };

  return (
    <Container className="p-6 max-w-4xl mx-auto">
      <div className="space-y-6">
        {/* Example 1: In a header */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <Heading level="h2" className="text-xl font-semibold">
              Form with Language Selector
            </Heading>
            <LanguageSelector
              selectedLanguage={selectedLanguage}
              onLanguageChange={handleLanguageChange}
            />
          </div>
          <Text className="text-gray-600">
            This shows how to place the language selector in a header next to a title.
            Current language: <strong>{selectedLanguage}</strong>
          </Text>
        </div>

        {/* Example 2: Standalone */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <Heading level="h3" className="text-lg font-medium mb-4">
            Standalone Language Selector
          </Heading>
          <div className="mb-4">
            <Text className="text-sm text-gray-600 mb-2">
              Select a language for content translation:
            </Text>
            <LanguageSelector
              selectedLanguage={selectedLanguage}
              onLanguageChange={handleLanguageChange}
              className="w-fit"
            />
          </div>
          <Text className="text-gray-600">
            This shows the language selector as a standalone component.
          </Text>
        </div>

        {/* Example 3: In a form context */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <Heading level="h3" className="text-lg font-medium">
              Content Form
            </Heading>
            <LanguageSelector
              selectedLanguage={selectedLanguage}
              onLanguageChange={handleLanguageChange}
            />
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title ({selectedLanguage.toUpperCase()})
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder={`Enter title in ${selectedLanguage === 'en' ? 'English' : selectedLanguage === 'de' ? 'German' : 'Japanese'}`}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description ({selectedLanguage.toUpperCase()})
              </label>
              <textarea
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder={`Enter description in ${selectedLanguage === 'en' ? 'English' : selectedLanguage === 'de' ? 'German' : 'Japanese'}`}
              />
            </div>
          </div>
          
          <Text className="text-sm text-gray-500 mt-4">
            This example shows how form fields can be updated based on the selected language.
            The language selector helps users switch between different language versions of the content.
          </Text>
        </div>

        {/* Usage Instructions */}
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <Heading level="h3" className="text-lg font-medium mb-3 text-blue-900">
            Usage Instructions
          </Heading>
          <div className="space-y-2 text-sm text-blue-800">
            <p><strong>Import:</strong> <code>import LanguageSelector from "./language-selector";</code></p>
            <p><strong>Props:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li><code>selectedLanguage</code> - Currently selected language code (default: "en")</li>
              <li><code>onLanguageChange</code> - Callback function when language changes</li>
              <li><code>className</code> - Additional CSS classes for styling</li>
            </ul>
            <p><strong>Features:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Fetches languages from Tolgee API with fallback to mock data</li>
              <li>Shows flag emojis and language names</li>
              <li>Highlights base language with "BASE" badge</li>
              <li>Dropdown with language selection</li>
              <li>Loading and error states</li>
            </ul>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default LanguageSelectorExample;
