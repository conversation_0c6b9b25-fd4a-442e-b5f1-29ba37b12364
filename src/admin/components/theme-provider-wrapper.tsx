import React from 'react';
import { ThemeProvider } from '../hooks/useTheme';
import '../styles/theme-variables.css';

interface ThemeProviderWrapperProps {
  children: React.ReactNode;
  defaultTheme?: 'light' | 'dark';
}

/**
 * A wrapper component that provides theme context to its children
 * This should be used at the root level of the application or around
 * components that need theme support
 */
const ThemeProviderWrapper: React.FC<ThemeProviderWrapperProps> = ({
  children,
  defaultTheme = 'light',
}) => {
  return (
    <ThemeProvider defaultTheme={defaultTheme}>
      <div className="theme-provider-wrapper">
        {children}
      </div>
    </ThemeProvider>
  );
};

export default ThemeProviderWrapper;
