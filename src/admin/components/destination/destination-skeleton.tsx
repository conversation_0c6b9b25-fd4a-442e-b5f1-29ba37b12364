import React from "react";

interface DestinationSkeletonProps {
  viewMode: "grid" | "list";
  count?: number;
}

/**
 * Skeleton loading component for destinations that supports both grid and list views
 * Uses theme-aware colors and shimmer animation
 */
const DestinationSkeleton: React.FC<DestinationSkeletonProps> = ({ 
  viewMode, 
  count = 6 
}) => {
  const shimmerStyle = `
    @keyframes shimmer {
      0% {
        background-position: -100% 0;
      }
      100% {
        background-position: 100% 0;
      }
    }
    .shimmer {
      background: linear-gradient(
        90deg,
        hsl(var(--ui-bg-subtle)) 0%,
        hsl(var(--ui-bg-subtle)) 40%,
        hsl(var(--ui-fg-muted) / 0.08) 50%,
        hsl(var(--ui-bg-subtle)) 60%,
        hsl(var(--ui-bg-subtle)) 100%
      );
      background-size: 200% 100%;
      animation: shimmer 2s ease-in-out infinite;
    }
  `;

  if (viewMode === "grid") {
    return (
      <>
        <style>{shimmerStyle}</style>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: count }).map((_, index) => (
            <div
              key={`grid-skeleton-${index}`}
              className="border border-ui-border-base rounded-lg overflow-hidden bg-ui-bg-base shadow-sm flex flex-col"
            >
              {/* Image placeholder */}
              <div className="h-40 relative flex-shrink-0 shimmer">
                {/* Hotel count badge placeholder */}
                <div className="absolute top-2 right-2 bg-ui-bg-base/45 rounded-full px-2 py-1">
                  <div className="h-3 w-12 shimmer rounded"></div>
                </div>
              </div>
              
              {/* Content */}
              <div className="p-4 flex flex-col flex-grow">
                <div className="flex-grow flex flex-col">
                  {/* Header with country and badges */}
                  <div className="flex justify-between items-center mb-2">
                    <div className="h-4 w-20 shimmer rounded"></div>
                    <div className="flex gap-1">
                      <div className="h-5 w-12 shimmer rounded-full"></div>
                      <div className="h-5 w-16 shimmer rounded-full"></div>
                    </div>
                  </div>
                  
                  {/* Description */}
                  <div className="space-y-1 mb-3">
                    <div className="h-3 w-full shimmer rounded"></div>
                    <div className="h-3 w-3/4 shimmer rounded"></div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    <div className="h-6 w-16 shimmer rounded-full"></div>
                    <div className="h-6 w-20 shimmer rounded-full"></div>
                    <div className="h-6 w-14 shimmer rounded-full"></div>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex gap-2 mt-auto">
                  <div className="h-8 flex-1 shimmer rounded"></div>
                  <div className="h-8 flex-1 shimmer rounded"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </>
    );
  }

  // List view skeleton
  return (
    <>
      <style>{shimmerStyle}</style>
      <div className="border border-ui-border-base rounded-lg overflow-hidden bg-ui-bg-base shadow-sm">
        <table className="min-w-full divide-y divide-ui-border-base">
          <thead className="bg-ui-bg-subtle/50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-ui-fg-muted uppercase tracking-wider">
                <div className="h-3 w-20 shimmer rounded"></div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-ui-fg-muted uppercase tracking-wider">
                <div className="h-3 w-16 shimmer rounded"></div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-ui-fg-muted uppercase tracking-wider">
                <div className="h-3 w-12 shimmer rounded"></div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-ui-fg-muted uppercase tracking-wider">
                <div className="h-3 w-14 shimmer rounded"></div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-ui-fg-muted uppercase tracking-wider">
                <div className="h-3 w-16 shimmer rounded"></div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-ui-bg-base divide-y divide-ui-border-base">
            {Array.from({ length: count }).map((_, index) => (
              <tr key={`list-skeleton-${index}`} className="hover:bg-ui-bg-subtle">
                {/* Destination column */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 relative">
                      <div className="h-10 w-10 rounded-md shimmer"></div>
                    </div>
                    <div className="ml-4 space-y-1">
                      <div className="h-4 w-32 shimmer rounded"></div>
                      <div className="h-3 w-24 shimmer rounded"></div>
                    </div>
                  </div>
                </td>
                
                {/* Country column */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="h-4 w-20 shimmer rounded"></div>
                </td>
                
                {/* Hotels column */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="h-4 w-8 shimmer rounded"></div>
                </td>
                
                {/* Status column */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex gap-1">
                    <div className="h-5 w-12 shimmer rounded-full"></div>
                    <div className="h-5 w-16 shimmer rounded-full"></div>
                  </div>
                </td>
                
                {/* Actions column */}
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex gap-2">
                    <div className="h-7 w-16 shimmer rounded"></div>
                    <div className="h-7 w-16 shimmer rounded"></div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default DestinationSkeleton;
