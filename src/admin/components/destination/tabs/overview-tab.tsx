import React, { useState } from "react";
import { Container, <PERSON>ing, Text, Badge, Button, Textarea, Input, toast } from "@camped-ai/ui";
import {
  FileText,
  MapPin,
  Globe,
  Tag,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Edit,
  X,
  Check
} from "lucide-react";
import { DestinationData } from "../../../types";

interface OverviewTabProps {
  destination: DestinationData | null;
  getTranslatedContent: (field: string, content: string) => string;
  getTranslatedTags: () => string[] | null;
  hasEditPermission?: boolean;
  onDescriptionUpdate?: (newDescription: string) => Promise<void>;
  onDetailsUpdate?: (field: string, value: string | string[]) => Promise<void>;
}

const OverviewTab: React.FC<OverviewTabProps> = ({
  destination,
  getTranslatedContent,
  getTranslatedTags,
  hasEditPermission = false,
  onDescriptionUpdate,
  onDetailsUpdate,
}) => {
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [editedDescription, setEditedDescription] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  // New state for editing destination details
  const [isEditingDetails, setIsEditingDetails] = useState(false);
  const [editedLocation, setEditedLocation] = useState("");
  const [editedCountry, setEditedCountry] = useState("");
  const [editedWebsite, setEditedWebsite] = useState("");
  const [editedTags, setEditedTags] = useState("");

  if (!destination) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading destination details...</Text>
      </Container>
    );
  }

  const description = getTranslatedContent(
    "description",
    destination?.description || ""
  ) || "No description available for this destination.";

  const isLongDescription = description.length > 200;
  const tags = getTranslatedTags();

  // Edit functionality handlers
  const handleEditClick = () => {
    setEditedDescription(destination?.description || "");
    setIsEditingDescription(true);
  };

  const handleCancelEdit = () => {
    setIsEditingDescription(false);
    setEditedDescription("");
  };

  const handleSaveEdit = async () => {
    if (!onDescriptionUpdate) return;

    setIsSaving(true);
    try {
      await onDescriptionUpdate(editedDescription);
      setIsEditingDescription(false);
      toast.success("Success", {
        description: "Description updated successfully",
      });
    } catch (error) {
      console.error("Error updating description:", error);
      toast.error("Error", {
        description: "Failed to update description. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // New handlers for destination details editing
  const handleEditDetailsClick = () => {
    setEditedLocation(destination?.location || "");
    setEditedCountry(destination?.country || "");
    setEditedWebsite(destination?.website || "");
    const tags = getTranslatedTags();
    setEditedTags(tags ? tags.join(", ") : "");
    setIsEditingDetails(true);
  };

  const handleCancelDetailsEdit = () => {
    setIsEditingDetails(false);
    setEditedLocation("");
    setEditedCountry("");
    setEditedWebsite("");
    setEditedTags("");
  };

  const handleSaveDetailsEdit = async () => {
    if (!onDetailsUpdate) return;

    setIsSaving(true);
    try {
      // Update each field that has changed
      if (editedLocation !== (destination?.location || "")) {
        await onDetailsUpdate("location", editedLocation);
      }
      if (editedCountry !== (destination?.country || "")) {
        await onDetailsUpdate("country", editedCountry);
      }
      if (editedWebsite !== (destination?.website || "")) {
        await onDetailsUpdate("website", editedWebsite);
      }

      // Handle tags
      const currentTags = getTranslatedTags();
      const newTags = editedTags.split(",").map(tag => tag.trim()).filter(tag => tag);
      const currentTagsStr = currentTags ? currentTags.join(", ") : "";
      if (editedTags !== currentTagsStr) {
        await onDetailsUpdate("tags", newTags);
      }

      setIsEditingDetails(false);
      toast.success("Success", {
        description: "Destination details updated successfully",
      });
    } catch (error) {
      console.error("Error updating destination details:", error);
      toast.error("Error", {
        description: "Failed to update destination details. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Description Container */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
          <div className="flex items-center gap-3">
            <FileText className="w-5 h-5 text-muted-foreground flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Description
            </Heading>
          </div>
          {hasEditPermission && !isEditingDescription && (
            <Button
              variant="secondary"
              onClick={handleEditClick}
              className="px-4 py-2 text-sm font-medium"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
        <div className="p-6">
          {isEditingDescription ? (
            // Edit Mode
            <div className="space-y-4">
              <Textarea
                value={editedDescription}
                onChange={(e) => setEditedDescription(e.target.value)}
                placeholder="Enter destination description..."
                className="min-h-[120px] text-sm"
                disabled={isSaving}
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="secondary"
                  onClick={handleCancelEdit}
                  disabled={isSaving}
                  className="px-4 py-2 text-sm font-medium"
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSaveEdit}
                  disabled={isSaving}
                  className="px-4 py-2 text-sm font-medium"
                >
                  <Check className="w-4 h-4 mr-2" />
                  {isSaving ? "Saving..." : "Save"}
                </Button>
              </div>
            </div>
          ) : (
            // Read-only Mode
            <div>
              <div
                className={`text-sm text-foreground leading-relaxed break-words transition-all duration-300 ease-in-out ${
                  !showFullDescription && isLongDescription
                    ? "line-clamp-4 overflow-hidden"
                    : ""
                }`}
              >
                <div
                  dangerouslySetInnerHTML={{
                    __html: description,
                  }}
                />
              </div>
              {isLongDescription && (
                <div className="flex justify-end mt-3">
                  <button
                    onClick={() => setShowFullDescription(!showFullDescription)}
                    className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline cursor-pointer flex items-center gap-1 transition-colors duration-200"
                  >
                    {showFullDescription ? (
                      <>
                        <ChevronUp className="w-3 h-3" />
                        Show Less
                      </>
                    ) : (
                      <>
                        <ChevronDown className="w-3 h-3" />
                        Show More
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </Container>

      {/* Destination Details Card */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
          <div className="flex items-center gap-3">
            <Tag className="w-5 h-5 text-muted-foreground flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Destination Details
            </Heading>
          </div>
          {hasEditPermission && !isEditingDetails && (
            <Button
              variant="secondary"
              onClick={handleEditDetailsClick}
              className="px-4 py-2 text-sm font-medium"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
        <div className="p-6 space-y-6">
          {isEditingDetails ? (
            // Edit Mode for Destination Details
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Location */}
                <div className="space-y-2">
                  <Text className="text-sm font-medium text-foreground">Location</Text>
                  <Input
                    value={editedLocation}
                    onChange={(e) => setEditedLocation(e.target.value)}
                    placeholder="Enter location..."
                    className="text-sm"
                    disabled={isSaving}
                  />
                </div>

                {/* Country */}
                <div className="space-y-2">
                  <Text className="text-sm font-medium text-foreground">Country</Text>
                  <Input
                    value={editedCountry}
                    onChange={(e) => setEditedCountry(e.target.value)}
                    placeholder="Enter country..."
                    className="text-sm"
                    disabled={isSaving}
                  />
                </div>

                {/* Website */}
                <div className="space-y-2">
                  <Text className="text-sm font-medium text-foreground">Website</Text>
                  <Input
                    value={editedWebsite}
                    onChange={(e) => setEditedWebsite(e.target.value)}
                    placeholder="Enter website URL..."
                    className="text-sm"
                    disabled={isSaving}
                  />
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Text className="text-sm font-medium text-foreground">Tags</Text>
                <Input
                  value={editedTags}
                  onChange={(e) => setEditedTags(e.target.value)}
                  placeholder="Enter tags separated by commas..."
                  className="text-sm"
                  disabled={isSaving}
                />
                <Text className="text-xs text-muted-foreground">Separate multiple tags with commas</Text>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="secondary"
                  onClick={handleCancelDetailsEdit}
                  disabled={isSaving}
                  className="px-4 py-2 text-sm font-medium"
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSaveDetailsEdit}
                  disabled={isSaving}
                  className="px-4 py-2 text-sm font-medium"
                >
                  <Check className="w-4 h-4 mr-2" />
                  {isSaving ? "Saving..." : "Save"}
                </Button>
              </div>
            </div>
          ) : (
            // Read-only Mode for Destination Details
            <>
              {/* Metadata Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Location */}
                {(getTranslatedContent("location", destination?.location || "") || destination?.country) && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <Text className="text-sm font-medium text-foreground">Location</Text>
                    </div>
                    <Text className="text-sm text-muted-foreground">
                      {getTranslatedContent("location", destination?.location || "") || destination?.country}
                    </Text>
                  </div>
                )}

                {/* Country */}
                {destination?.country && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4 text-muted-foreground" />
                      <Text className="text-sm font-medium text-foreground">Country</Text>
                    </div>
                    <Text className="text-sm text-muted-foreground">
                      {destination.country}
                    </Text>
                  </div>
                )}

                {/* Website */}
                {destination?.website && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <ExternalLink className="w-4 h-4 text-muted-foreground" />
                      <Text className="text-sm font-medium text-foreground">Website</Text>
                    </div>
                    <a
                      href={destination.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 dark:text-blue-400 hover:underline break-all"
                    >
                      {destination.website}
                    </a>
                  </div>
                )}
              </div>

              {/* Tags */}
              {tags && tags.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Tag className="w-4 h-4 text-muted-foreground" />
                    <Text className="text-sm font-medium text-foreground">Tags</Text>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <Badge
                        key={index}
                        className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </Container>
    </div>
  );
};

export default OverviewTab;
