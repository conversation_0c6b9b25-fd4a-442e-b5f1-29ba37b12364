import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Button, Badge } from "@camped-ai/ui";
import { Hotel, XCircle, MapPin, Globe, Edit } from "lucide-react";
import { PlusMini, MagnifyingGlass, Buildings } from "@camped-ai/icons";
import { Link, useNavigate } from "react-router-dom";
import { HotelData } from "../../../types";

interface HotelsTabProps {
  hotels: HotelData[];
  filteredHotels: HotelData[];
  isLoadingHotels: boolean;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  destination: any;
  hasCreatePermission: boolean;
  hasEditPermission: boolean;
  getTranslatedContent: (field: string, content: string) => string;
}

const HotelsTab: React.FC<HotelsTabProps> = ({
  hotels,
  filteredHotels,
  isLoadingHotels,
  searchQuery,
  setSearchQuery,
  destination,
  hasCreatePermission,
  hasEditPermission,
  getTranslatedContent,
}) => {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      {/* Hotel search and filter */}
      <div className="flex items-center justify-between gap-4 mb-6">
        <div className="relative max-w-[480px]">
          <input
            type="text"
            placeholder="Search hotels..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-9 pr-4 py-2 rounded-md border border-input bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
          />
          <MagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery("")}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <XCircle className="w-4 h-4" />
            </button>
          )}
        </div>

        {hasCreatePermission && (
          <Link
            to="/hotel-management/hotels/?add=true"
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm font-medium"
          >
            <PlusMini className="w-4 h-4" />
            <span>Add Hotel</span>
          </Link>
        )}
      </div>

      <div>
        {isLoadingHotels ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : filteredHotels.length === 0 ? (
          <div className="text-center py-12">
            <MagnifyingGlass className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
            <h3 className="text-lg font-medium text-foreground mb-1">
              {searchQuery ? "No matching hotels found" : "No hotels available"}
            </h3>
            <p className="text-muted-foreground text-sm">
              {searchQuery
                ? "Try adjusting your search or check back later."
                : "Add a new hotel to get started."}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredHotels.map((hotel) => (
              <div
                key={hotel.id}
                className="overflow-hidden hover:shadow-md transition-all border border-border rounded-lg bg-card flex flex-col h-auto relative z-1"
              >
                <div className="w-full h-48 relative overflow-hidden">
                  {hotel.images && hotel.images.length > 0 ? (
                    <img
                      src={hotel.images[0].url}
                      alt={hotel.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-900/30 dark:to-blue-800/20 flex items-center justify-center">
                      <Buildings className="w-12 h-12 text-blue-300 dark:text-blue-400" />
                    </div>
                  )}
                  <Badge
                    color={hotel.is_active ? "green" : "grey"}
                    className="absolute top-2 right-2 shadow-sm z-1"
                  >
                    {hotel.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>

                <div className="w-full p-5 flex flex-col">
                  <div className="flex justify-between items-start mb-2">
                    <Link
                      to={`/hotel-management/hotels/${hotel.id}`}
                      className="text-xl font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline"
                    >
                      {hotel.name}
                    </Link>
                  </div>

                  <div className="flex flex-wrap gap-x-4 gap-y-2 mb-3">
                    {hotel.location && (
                      <div className="flex items-center gap-1 text-muted-foreground text-sm">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span>{hotel.location}</span>
                      </div>
                    )}

                    {hotel.website && (
                      <div className="flex items-center gap-1 text-muted-foreground text-sm">
                        <Globe className="w-4 h-4 text-muted-foreground" />
                        <a
                          href={hotel.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 dark:text-blue-400 hover:underline truncate"
                        >
                          {hotel.website.replace(/^https?:\/\//, "")}
                        </a>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2 mt-auto">
                    {hasEditPermission && (
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() =>
                          navigate(`/hotel-management/hotels/${hotel.id}`)
                        }
                        className="flex-1 flex items-center justify-center gap-1"
                      >
                        <Edit className="w-4 h-4" /> Manage
                      </Button>
                    )}
                    {hasEditPermission && (
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() =>
                          navigate(
                            `/hotel-management/hotels/${hotel.id}/room-configs`
                          )
                        }
                        className="flex-1 flex items-center justify-center gap-1"
                      >
                        Room Config
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default HotelsTab;
