import React, { useState } from "react";
import { Container, Heading, Text, Button, toast } from "@camped-ai/ui";
import { Sparkles } from "lucide-react";
import { DestinationData } from "../../../types";
import RichTextEditor from "../../rich-text-editor";

interface BaileyAITabProps {
  destination: DestinationData | null;
  hasEditPermission: boolean;
}

const BaileyAITab: React.FC<BaileyAITabProps> = ({
  destination,
  hasEditPermission,
}) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [aiContent, setAiContent] = useState(destination?.ai_content || "");

  // Update local state when destination changes
  React.useEffect(() => {
    setAiContent(destination?.ai_content || "");
  }, [destination?.ai_content]);

  const handleSyncWithBaileyAI = async () => {
    if (!destination?.id) {
      console.error("Cannot sync with Bailey AI: destination ID is missing");
      return;
    }

    setIsSyncing(true);

    try {
      console.log("🤖 Syncing with Bailey AI for destination:", destination.id);

      // First, save current AI content to database
      if (aiContent !== destination.ai_content) {
        console.log("📝 Saving current AI content changes...");
        const saveResponse = await fetch(`/admin/hotel-management/destinations`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: destination.id,
            ai_content: aiContent,
          }),
        });

        if (!saveResponse.ok) {
          const saveError = await saveResponse.json().catch(() => ({}));
          console.error("❌ Failed to save AI content:", saveError);
          throw new Error("Failed to save current AI content");
        }
      }

      // Use the dedicated Bailey AI sync endpoint
      const response = await fetch(`/admin/hotel-management/destinations/${destination.id}/bailey-ai-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Bailey AI sync successful:", result);

        toast.success("Success", {
          description: "Successfully synced with Bailey AI",
        });

        // Update the AI content if the API returns updated content
        if (result.destination?.ai_content) {
          setAiContent(result.destination.ai_content);
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error("❌ Bailey AI sync failed:", response.status, errorData);

        // Check if it's a partial success (database updated but external API failed)
        if (response.status === 207 && errorData.destination) {
          console.log("⚠️ Database updated but external API failed");
          toast.error("Partial Success", {
            description: "Content saved but Bailey AI sync failed. Please try again.",
          });

          if (errorData.destination.ai_content) {
            setAiContent(errorData.destination.ai_content);
          }
        } else {
          toast.error("Error", {
            description: errorData.message || "Failed to sync with Bailey AI. Please try again.",
          });
        }

        throw new Error(errorData.message || "Bailey AI sync failed");
      }
    } catch (error) {
      console.error("Error syncing with Bailey AI:", error);

      // Only show error toast if we haven't already shown one above
      if (!error.message?.includes("Bailey AI sync failed")) {
        toast.error("Error", {
          description: "An unexpected error occurred. Please try again.",
        });
      }
    } finally {
      setIsSyncing(false);
    }
  };

  if (!destination) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading destination details...</Text>
      </Container>
    );
  }

  return (
    <div className="space-y-6">
      {/* Bailey AI Content Section */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
          <div className="flex items-center gap-3">
            <Sparkles className="w-5 h-5 text-primary flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Bailey AI Content
            </Heading>
          </div>
          
          {hasEditPermission && (
            <Button
              onClick={handleSyncWithBaileyAI}
              disabled={isSyncing || !destination.id}
              size="small"
              className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 hover:from-purple-700 hover:via-blue-700 hover:to-indigo-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group overflow-hidden"
              title={!destination.id ? "Save destination first to sync with Bailey AI" : "Sync destination content with Bailey AI"}
            >
              {/* Animated background gradient */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>

              {/* Spark icon */}
              <Sparkles 
                size={16} 
                className="mr-2 relative z-10 group-hover:animate-pulse" 
              />

              <span className="relative z-10">
                {isSyncing ? "Syncing..." : "Sync with Bailey AI"}
              </span>

              {/* Shimmer effect */}
              <div className="absolute inset-0 -top-1 -bottom-1 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            </Button>
          )}
        </div>
        
        <div className="p-6">
          <RichTextEditor
            value={aiContent}
            onChange={setAiContent}
            placeholder="Enter destination details to guide Bailey AI's conversations…"
            height="400px"
            helpText="This content powers Bailey AI. Add rich, engaging details about the destination to enhance AI-guided conversations and personalized recommendations."
            disabled={!hasEditPermission}
          />

          {destination.id && (
            <div className="mt-4 p-3 bg-muted rounded-lg">
              <Text className="text-sm text-muted-foreground">
                💡 <strong>Tip:</strong> Use the "Sync with Bailey AI" button to update Bailey AI with the latest destination content and ensure optimal AI-powered conversations.
              </Text>
            </div>
          )}
        </div>
      </Container>

      {/* AI Content Guidelines */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-6 rounded-lg bg-card border border-border">
        <Heading level="h3" className="text-md font-medium text-foreground mb-4">
          Content Guidelines for Bailey AI
        </Heading>
        
        <div className="space-y-3 text-sm text-muted-foreground">
          <div className="flex items-start gap-2">
            <span className="text-primary font-medium">•</span>
            <Text>Include key attractions, activities, and unique features of the destination</Text>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-primary font-medium">•</span>
            <Text>Mention seasonal highlights, weather patterns, and best times to visit</Text>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-primary font-medium">•</span>
            <Text>Add local culture, cuisine, and dining recommendations</Text>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-primary font-medium">•</span>
            <Text>Include transportation options and accessibility information</Text>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-primary font-medium">•</span>
            <Text>Provide practical tips for travelers (currency, language, customs)</Text>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default BaileyAITab;
