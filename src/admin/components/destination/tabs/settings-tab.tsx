import React from "react";
import { Container, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, But<PERSON> } from "@camped-ai/ui";
import { 
  Settings, 
  Calendar, 
  User, 
  Eye, 
  <PERSON>Off, 
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON> as LinkIcon
} from "lucide-react";
import { DestinationData } from "../../../types";
import Prompt from "../../prompt";

interface SettingsTabProps {
  destination: DestinationData | null;
  onDelete: () => void;
  deleteOpen: boolean;
  setDeleteOpen: (open: boolean) => void;
  hasDeletePermission: boolean;
  hasEditPermission: boolean;
}

const SettingsTab: React.FC<SettingsTabProps> = ({
  destination,
  onDelete,
  deleteOpen,
  setDeleteOpen,
  hasDeletePermission,
  hasEditPermission,
}) => {
  if (!destination) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading destination settings...</Text>
      </Container>
    );
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Not available";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6">
      {/* Basic Settings */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center gap-3 bg-muted/50">
          <Settings className="w-5 h-5 text-muted-foreground flex-shrink-0" />
          <Heading
            level="h2"
            className="text-lg font-medium text-foreground"
          >
            Destination Settings
          </Heading>
        </div>
        <div className="p-6 space-y-6">
          {/* Handle */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <LinkIcon className="w-4 h-4 text-muted-foreground" />
              <Text className="text-sm font-medium text-foreground">Handle (URL Slug)</Text>
            </div>
            <Text className="text-sm text-muted-foreground font-mono bg-muted px-3 py-2 rounded border">
              {destination.handle}
            </Text>
            <Text className="text-xs text-muted-foreground">
              This is used in the destination's URL and cannot be changed after creation.
            </Text>
          </div>

          {/* Visibility Settings */}
          <div className="space-y-3">
            <Text className="text-sm font-medium text-foreground">Visibility</Text>
            <div className="flex flex-wrap gap-3">
              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                {destination.is_active ? (
                  <Eye className="w-4 h-4 text-green-600" />
                ) : (
                  <EyeOff className="w-4 h-4 text-red-600" />
                )}
                <div>
                  <Text className="text-sm font-medium text-foreground">
                    {destination.is_active ? "Active" : "Inactive"}
                  </Text>
                  <Text className="text-xs text-muted-foreground">
                    {destination.is_active 
                      ? "Visible to users and search engines"
                      : "Hidden from public view"
                    }
                  </Text>
                </div>
              </div>

              {destination.is_featured && (
                <div className="flex items-center gap-2 p-3 bg-purple-50 dark:bg-purple-950/30 rounded-lg">
                  <div className="w-4 h-4 bg-purple-600 rounded-full"></div>
                  <div>
                    <Text className="text-sm font-medium text-foreground">Featured</Text>
                    <Text className="text-xs text-muted-foreground">
                      Highlighted in featured sections
                    </Text>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Container>

      {/* Metadata */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center gap-3 bg-muted/50">
          <Calendar className="w-5 h-5 text-muted-foreground flex-shrink-0" />
          <Heading
            level="h2"
            className="text-lg font-medium text-foreground"
          >
            Metadata
          </Heading>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Created Date */}
            <div className="space-y-2">
              <Text className="text-sm font-medium text-foreground">Created</Text>
              <Text className="text-sm text-muted-foreground">
                {formatDate(destination.created_at)}
              </Text>
            </div>

            {/* Updated Date */}
            <div className="space-y-2">
              <Text className="text-sm font-medium text-foreground">Last Updated</Text>
              <Text className="text-sm text-muted-foreground">
                {formatDate(destination.updated_at)}
              </Text>
            </div>

            {/* ID */}
            <div className="space-y-2">
              <Text className="text-sm font-medium text-foreground">Destination ID</Text>
              <Text className="text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded">
                {destination.id}
              </Text>
            </div>

            {/* Last Updated By (if available) */}
            {destination.updated_by && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  <Text className="text-sm font-medium text-foreground">Last Updated By</Text>
                </div>
                <Text className="text-sm text-muted-foreground">
                  {destination.updated_by}
                </Text>
              </div>
            )}
          </div>
        </div>
      </Container>

      {/* Danger Zone */}
      {/*hasDeletePermission && (
        <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border-red-200 dark:border-red-800 border border-border">
          <div className="px-6 py-4 border-b border-red-200 dark:border-red-800 flex items-center gap-3 bg-red-50 dark:bg-red-950/30">
            <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-red-800 dark:text-red-200"
            >
              Danger Zone
            </Heading>
          </div>
          <div className="p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <Text className="font-medium text-foreground mb-1">
                  Delete Destination
                </Text>
                <Text className="text-sm text-muted-foreground">
                  Permanently delete this destination and all associated data. This action cannot be undone.
                </Text>
              </div>
              <Prompt
                open={deleteOpen}
                onOpenChange={setDeleteOpen}
                title="Delete Destination"
                description="Are you sure you want to delete this destination? This action cannot be undone and will remove all associated hotels, bookings, and data."
                onDelete={onDelete}
                trigger={
                  <Button
                    variant="danger"
                    size="small"
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete Destination
                  </Button>
                }
              />
            </div>
          </div>
        </Container>
      )*/}
    </div>
  );
};

export default SettingsTab;
