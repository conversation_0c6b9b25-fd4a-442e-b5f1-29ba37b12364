import React, { useState } from "react";
import { Container, Heading, Text, Button } from "@camped-ai/ui";
import { 
  HelpCircle, 
  ChevronDown, 
  ChevronUp, 
  Plus,
  Edit,
  Trash2
} from "lucide-react";

interface FAQ {
  id?: string;
  question: string;
  answer: string;
}

interface FAQsTabProps {
  faqs: FAQ[];
  getTranslatedFaqs: () => FAQ[];
  onAddFaq?: () => void;
  onEditFaq?: (faq: FAQ) => void;
  onDeleteFaq?: (faqId: string) => void;
  hasEditPermission: boolean;
}

const FAQsTab: React.FC<FAQsTabProps> = ({
  faqs,
  getTranslatedFaqs,
  onAddFaq,
  onEditFaq,
  onDeleteFaq,
  hasEditPermission,
}) => {
  const [expandedFaqs, setExpandedFaqs] = useState<Set<string>>(new Set());

  const translatedFaqs = getTranslatedFaqs();
  const displayFaqs = translatedFaqs && translatedFaqs.length > 0 ? translatedFaqs : faqs;

  const toggleFaq = (faqId: string) => {
    const newExpanded = new Set(expandedFaqs);
    if (newExpanded.has(faqId)) {
      newExpanded.delete(faqId);
    } else {
      newExpanded.add(faqId);
    }
    setExpandedFaqs(newExpanded);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-end mb-4">
        {hasEditPermission && onAddFaq && (
          <Button
            variant="secondary"
            size="small"
            onClick={onAddFaq}
            className="flex items-center gap-1"
          >
            <Plus className="w-4 h-4" />
            Add FAQ
          </Button>
        )}
      </div>

      <div>
          {displayFaqs && displayFaqs.length > 0 ? (
            <div className="space-y-4">
              {displayFaqs.map((faq: FAQ, index: number) => {
                const faqId = faq.id || index.toString();
                const isExpanded = expandedFaqs.has(faqId);

                return (
                  <div
                    key={faqId}
                    className="border border-border rounded-lg bg-card hover:border-muted-foreground/20 transition-colors"
                  >
                    <button
                      onClick={() => toggleFaq(faqId)}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors rounded-t-lg"
                    >
                      <Text className="font-medium text-foreground pr-4">
                        {faq.question}
                      </Text>
                      <div className="flex items-center gap-2">
                        {hasEditPermission && (
                          <div className="flex gap-1" onClick={(e) => e.stopPropagation()}>
                            {onEditFaq && (
                              <Button
                                variant="ghost"
                                size="small"
                                onClick={() => onEditFaq(faq)}
                                className="p-1 h-auto"
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                            )}
                            {onDeleteFaq && faq.id && (
                              <Button
                                variant="ghost"
                                size="small"
                                onClick={() => onDeleteFaq(faq.id!)}
                                className="p-1 h-auto text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            )}
                          </div>
                        )}
                        {isExpanded ? (
                          <ChevronUp className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                        ) : (
                          <ChevronDown className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                        )}
                      </div>
                    </button>
                    
                    {isExpanded && (
                      <div className="px-4 pb-4 border-t border-border">
                        <Text className="text-sm text-muted-foreground leading-relaxed mt-3">
                          <div
                            dangerouslySetInnerHTML={{
                              __html: faq.answer,
                            }}
                          />
                        </Text>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <HelpCircle className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
              <h3 className="text-lg font-medium text-foreground mb-1">
                No FAQs available
              </h3>
              <p className="text-muted-foreground text-sm mb-4">
                Add frequently asked questions to help visitors learn more about this destination.
              </p>
              {hasEditPermission && onAddFaq && (
                <Button
                  variant="secondary"
                  onClick={onAddFaq}
                  className="flex items-center gap-2 mx-auto"
                >
                  <Plus className="w-4 h-4" />
                  Add First FAQ
                </Button>
              )}
            </div>
          )}
        </div>
    </div>
  );
};

export default FAQsTab;
