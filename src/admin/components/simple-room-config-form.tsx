import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Heading,
  Button,
  Input,
  Toaster,
  toast,
  InlineTip,
} from "@camped-ai/ui";
import {
  Home,
  Users,
  Bed,
  Coffee,
  Eye,
} from "lucide-react";
import CustomSelect from "./custom-select";
import { TextareaField } from "./ai-enhanced-inputs";
import { useForm } from "react-hook-form";
import RoomConfigMediaSection, {
  RoomConfigFormData,
} from "./room-config/room-config-media-section";
import { MediaField } from "./hotel/media-item";

import { useProjectLanguages } from "../hooks/languages/useProjectLanguages";
import { useRoomTranslations } from "../hooks/translations/useRoomTranslations";
import TranslatableTagInput from "./translatable-tag-input";
import AITranslateButton from "./ai-translate-button";
import "../styles/room-config-modal-fix.css";

// Define which fields are translatable
const TRANSLATABLE_FIELDS = ["name", "description"] as const;

// Define which fields are translatable arrays
const TRANSLATABLE_ARRAY_FIELDS = ["amenities"] as const;

// Helper function to check if a field is translatable
const isFieldTranslatable = (fieldName: string): boolean => {
  return (
    TRANSLATABLE_FIELDS.includes(fieldName as any) ||
    TRANSLATABLE_ARRAY_FIELDS.includes(fieldName as any)
  );
};

// Helper function to determine if a field should be enabled based on language selection
const isFieldEnabled = (
  fieldName: string,
  isBaseLanguage: boolean
): boolean => {
  // If it's the base language, all fields are enabled
  if (isBaseLanguage) return true;

  // For non-base languages, only translatable fields (including array fields) are enabled
  return isFieldTranslatable(fieldName);
};

interface SimpleRoomConfigFormProps {
  hotelId: string;
  onComplete: (success: boolean) => void;
  initialData?: any;
  isEdit?: boolean;
  roomConfigId?: string;
  selectedLanguage?: string;
  onLoadingChange?: (isLoading: boolean) => void;
}

const SimpleRoomConfigForm: React.FC<SimpleRoomConfigFormProps> = ({
  hotelId,
  onComplete,
  initialData,
  isEdit = false,
  roomConfigId,
  selectedLanguage = "en",
  onLoadingChange,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [images, setImages] = useState<any[]>([]);

  // State to track translated values for ALL languages (not just current)
  const [allTranslatedValues, setAllTranslatedValues] = useState<
    Record<string, Record<string, string>>
  >({});

  // Track which languages have user input to prevent overwrites
  const userInputLanguages = useRef<Set<string>>(new Set());

  // Get translated values for the current language
  const translatedValues = allTranslatedValues[selectedLanguage] || {};

  // Get available languages to determine base language
  const { languages: tolgeeLanguages } = useProjectLanguages();

  // Determine if current language is base language
  const isBaseLanguage =
    selectedLanguage === "en" ||
    tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)?.base ===
      true;

  // Translation management
  const roomId = roomConfigId || initialData?.id || "new";
  const {
    saveTranslations,
    loadTranslations,
    getTranslationKeys,
    translations,
    arrayTranslations,
    setArrayTranslations,
  } = useRoomTranslations(roomId);

  console.log("Initial data:", initialData);

  // Track which languages we're currently loading to prevent duplicate API calls
  const loadingLanguagesRef = useRef<Set<string>>(new Set());

  // Load translations when language changes
  useEffect(() => {
    const loadTranslationsForLanguage = async () => {
      if (!isBaseLanguage && roomId && roomId !== "new") {
        // Check if we're already loading this language
        if (loadingLanguagesRef.current.has(selectedLanguage)) {
          console.log(
            `⏳ Already loading translations for ${selectedLanguage}, skipping...`
          );
          return;
        }

        // Check if we already have translations for this language
        const hasExistingTranslations =
          translations[selectedLanguage] &&
          Object.keys(translations[selectedLanguage]).length > 0;

        if (hasExistingTranslations) {
          console.log(
            `✅ Translations already exist for ${selectedLanguage}, skipping load`
          );
          return;
        }

        console.log(
          `Loading translations for language: ${selectedLanguage}, room: ${roomId}`
        );
        loadingLanguagesRef.current.add(selectedLanguage); // Mark as loading

        try {
          await loadTranslations(selectedLanguage);
          console.log(`✅ loadTranslations completed for ${selectedLanguage}`);
        } catch (error) {
          console.error(
            `❌ Error loading translations for ${selectedLanguage}:`,
            error
          );
        } finally {
          loadingLanguagesRef.current.delete(selectedLanguage); // Remove from loading set
        }
      } else if (!isBaseLanguage && roomId === "new") {
        // For new rooms, initialize with base language values ONLY if no translations exist yet
        const hasUserInput = userInputLanguages.current.has(selectedLanguage);
        if (!allTranslatedValues[selectedLanguage] && !hasUserInput) {
          console.log(
            `🆕 First time switching to ${selectedLanguage} - initializing with base values`
          );
          const initialTranslations: Record<string, string> = {};
          TRANSLATABLE_FIELDS.forEach((fieldName) => {
            const baseValue = formData[fieldName as keyof typeof formData];
            if (baseValue && typeof baseValue === "string") {
              initialTranslations[fieldName] = baseValue;
            }
          });

          if (Object.keys(initialTranslations).length > 0) {
            console.log(
              `🔧 Setting initial translations for ${selectedLanguage}:`,
              initialTranslations
            );
            setAllTranslatedValues((prev) => ({
              ...prev,
              [selectedLanguage]: initialTranslations,
            }));
          }
        } else {
          console.log(
            `✅ ${selectedLanguage} translations already exist - keeping user input`
          );
        }
      } else {
        console.log(
          "Switched to base language - keeping translated values for later use"
        );
      }
    };

    loadTranslationsForLanguage();
  }, [selectedLanguage, isBaseLanguage, roomId]); // Minimal dependencies to prevent infinite loop

  // Get translation keys once
  const translationKeys = getTranslationKeys();

  // Update translated values when translations change in the hook - ONLY if no user input exists
  useEffect(() => {
    if (!isBaseLanguage && translations[selectedLanguage]) {
      const currentTranslations = translations[selectedLanguage];
      const newTranslatedValues: Record<string, string> = {};

      // Extract only the values for our translatable fields
      TRANSLATABLE_FIELDS.forEach((fieldName) => {
        const key = translationKeys[fieldName];
        if (key && currentTranslations[key]) {
          newTranslatedValues[fieldName] = currentTranslations[key];
          console.log(
            `Found API translation for ${fieldName}: ${currentTranslations[key]}`
          );
        }
      });

      // Only update if we don't already have user input for this language
      const hasUserInput = userInputLanguages.current.has(selectedLanguage);
      const existingTranslations = allTranslatedValues[selectedLanguage] || {};

      if (!hasUserInput && Object.keys(newTranslatedValues).length > 0) {
        console.log(
          `📥 Loading API translations for ${selectedLanguage} (no user input exists):`,
          newTranslatedValues
        );
        setAllTranslatedValues((prev) => ({
          ...prev,
          [selectedLanguage]: newTranslatedValues,
        }));
      } else if (hasUserInput) {
        console.log(
          `🚫 Skipping API translations for ${selectedLanguage} - user input exists:`,
          existingTranslations
        );
      }
    }
  }, [translations, selectedLanguage, isBaseLanguage]);

  // Helper function to get field value based on selected language
  const getFieldValue = (fieldName: string, baseValue: string): string => {
    if (isBaseLanguage || !isFieldTranslatable(fieldName)) {
      return baseValue;
    }

    // For non-base languages, get the most up-to-date translated value
    const currentLanguageTranslations =
      allTranslatedValues[selectedLanguage] || {};
    const translatedValue = currentLanguageTranslations[fieldName];
    const finalValue =
      translatedValue !== undefined ? translatedValue : baseValue;

    console.log(
      `getFieldValue(${fieldName}): base="${baseValue}", translated="${translatedValue}", final="${finalValue}", lang="${selectedLanguage}"`
    );
    return finalValue;
  };

  // Helper function to handle field changes
  const handleFieldChange = (fieldName: string, value: string) => {
    console.log(
      `🔄 handleFieldChange called: ${fieldName} = "${value}" (lang: ${selectedLanguage}, isBase: ${isBaseLanguage})`
    );

    if (isBaseLanguage) {
      // Update the base form data
      console.log(`📝 Updating base form data for ${fieldName}`);
      const updated = { ...formData, [fieldName]: value };
      console.log(`📝 Base form data updated:`, updated);
      setFormData(updated);
    } else if (isFieldTranslatable(fieldName)) {
      // Update the translated values for the current language in multi-language state
      console.log(
        `🌐 Updating translation for ${fieldName} in ${selectedLanguage}`
      );

      // Mark this language as having user input
      userInputLanguages.current.add(selectedLanguage);
      console.log(`👤 Marked ${selectedLanguage} as having user input`);

      setAllTranslatedValues((prev) => {
        const updated = {
          ...prev,
          [selectedLanguage]: {
            ...prev[selectedLanguage],
            [fieldName]: value,
          },
        };
        console.log(`🌐 All translated values updated:`, updated);
        console.log(
          `🎯 Current language (${selectedLanguage}) now has:`,
          updated[selectedLanguage]
        );
        return updated;
      });
    } else {
      console.log(
        `⚠️ Field ${fieldName} is not translatable or language conditions not met`
      );
    }
  };

  // Use react-hook-form for better form handling
  // Calculate initial max_occupancy for the form
  const formInitialMaxAdults = initialData?.max_adults || 2;
  const formInitialMaxChildren = initialData?.max_children || 0;
  const formInitialMaxInfants = initialData?.max_infants || 0;
  const formInitialMaxOccupancy =
    formInitialMaxAdults + formInitialMaxChildren + formInitialMaxInfants;

  const form = useForm<RoomConfigFormData>({
    defaultValues: {
      id: initialData?.id || "",
      name: initialData?.name || "",
      handle: initialData?.handle || "",
      type: initialData?.type || "standard",
      description: initialData?.description || "",
      room_size: initialData?.room_size || "",
      bed_type: initialData?.bed_type || "queen",
      max_extra_beds: initialData?.max_extra_beds || 0,
      max_cots: initialData?.max_cots || 0,
      max_adults: formInitialMaxAdults,
      max_adults_beyond_capacity: initialData?.max_adults_beyond_capacity || 0,
      max_children: formInitialMaxChildren,
      max_infants: formInitialMaxInfants,
      max_occupancy: formInitialMaxOccupancy,
      amenities: initialData?.amenities || [],
      is_active: initialData?.is_active || true,
      tags: initialData?.tags || [],
      hotel_id: hotelId,
      media: [],
    },
  });

  console.log("Form initialized with ID:", initialData?.id);
  console.log("DEBUG: Form initialData:", {
    max_extra_beds: initialData?.max_extra_beds,
    max_cots: initialData?.max_cots,
    max_adults_beyond_capacity: initialData?.max_adults_beyond_capacity,
  });

  // Calculate initial max_occupancy based on adults, children, and infants
  const initialMaxAdults = initialData?.max_adults || 2;
  const initialMaxChildren = initialData?.max_children || 0;
  const initialMaxInfants = initialData?.max_infants || 0;
  const initialMaxOccupancy =
    initialMaxAdults + initialMaxChildren + initialMaxInfants;

  const [formData, setFormData] = useState({
    id: initialData?.id || "",
    name: initialData?.name || "",
    type: initialData?.type || "standard",
    description: initialData?.description || "",
    room_size: initialData?.room_size || "",
    bed_type: initialData?.bed_type || "queen",
    max_extra_beds: initialData?.max_extra_beds || 0,
    max_cots: initialData?.max_cots || 0,
    max_adults: initialMaxAdults,
    max_adults_beyond_capacity: initialData?.max_adults_beyond_capacity || 0,
    max_children: initialMaxChildren,
    max_infants: initialMaxInfants,
    max_occupancy: initialMaxOccupancy,
    amenities: initialData?.amenities || [],
    hotel_id: hotelId,
  });

  console.log("Initial form data:", formData);

  // Convert amenities array to comma-separated string for the input field
  const [amenitiesInput, setAmenitiesInput] = useState(() => {
    if (!initialData?.amenities) return "";
    if (Array.isArray(initialData.amenities))
      return initialData.amenities.join(", ");
    return "";
  });

  // Fetch existing images if in edit mode
  useEffect(() => {
    if (isEdit && initialData?.id) {
      fetchRoomConfigImages(initialData.id);
    }
  }, [isEdit, initialData]);

  // Fetch room config images
  const fetchRoomConfigImages = async (roomConfigId: string) => {
    try {
      const response = await fetch(
        `/admin/room-configs/${roomConfigId}/images`,
        {
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch room config images");
      }

      const { images } = await response.json();

      // Update the form with the fetched images
      const formattedImages = images.map((image: any) => ({
        id: image.id,
        url: image.url,
        isThumbnail: image.isThumbnail,
        field_id: image.id,
      }));

      form.setValue("media", formattedImages);
      setImages(formattedImages);
    } catch (error) {
      console.error("Error fetching room config images:", error);
    }
  };

  const roomTypes = [
    { value: "standard", label: "Standard Room" },
    { value: "deluxe", label: "Deluxe Room" },
    { value: "suite", label: "Suite" },
    { value: "executive", label: "Executive Room" },
    { value: "family", label: "Family Room" },
  ];

  const bedTypes = [
    { value: "single", label: "Single Bed" },
    { value: "twin", label: "Twin Beds" },
    { value: "double", label: "Double Bed" },
    { value: "queen", label: "Queen Bed" },
    { value: "king", label: "King Bed" },
  ];

  const calculateMaxOccupancy = (data: any) => {
    // Calculate max occupancy based on adults, children, and infants
    const adults = parseInt(data.max_adults?.toString()) || 1;
    const children = parseInt(data.max_children?.toString()) || 0;
    const infants = parseInt(data.max_infants?.toString()) || 0;

    return adults + children + infants;
  };

  const handleInputChange = (field: string, value: any) => {
    const updatedData = { ...formData, [field]: value };

    // If one of the occupancy fields is changed, recalculate max_occupancy
    if (["max_adults", "max_children", "max_infants"].includes(field)) {
      updatedData.max_occupancy = calculateMaxOccupancy(updatedData);
    }

    setFormData(updatedData);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    onLoadingChange?.(true);
    try {
      // Prepare the data for submission
      // Parse the occupancy values
      const max_adults = parseInt(formData.max_adults.toString()) || 1;
      const max_children = parseInt(formData.max_children.toString()) || 0;
      const max_infants = parseInt(formData.max_infants.toString()) || 0;
      const max_extra_beds = parseInt(formData.max_extra_beds.toString()) || 0;
      const max_cots = parseInt(formData.max_cots.toString()) || 0;

      // Calculate max_occupancy based on the parsed values
      const max_occupancy = max_adults + max_children + max_infants;

      // Process amenities input into an array
      const amenitiesArray = amenitiesInput
        ? amenitiesInput
            .split(",")
            .map((amenity: string) => amenity.trim())
            .filter((amenity: string) => amenity)
        : [];

      const newRoomConfig = {
        ...formData,
        // Set the parsed and calculated values
        max_extra_beds,
        max_cots,
        max_adults,
        max_adults_beyond_capacity: formData.max_adults_beyond_capacity,
        max_children,
        max_infants,
        max_occupancy,
        // Update amenities with the processed array
        amenities: amenitiesArray,
        // Add an ID if this is a new room config
        id: isEdit ? initialData.id : `room_config_${Date.now()}`,
        // Ensure hotel_id is included
        hotel_id: hotelId,
      };

      console.log("Room configuration data:", newRoomConfig);

      // Send the data to the API
      const endpoint = isEdit
        ? `/admin/hotel-management/room-configs/${initialData.id}`
        : "/admin/hotel-management/room-configs";
      const method = isEdit ? "PUT" : "POST";

      // Ensure hotel_id is included in the request
      const requestData = {
        ...newRoomConfig,
        hotel_id: hotelId, // Make sure hotel_id is explicitly set
      };

      // Log the hotel ID to make sure it's being sent correctly
      console.log(`Hotel ID: ${hotelId}`);
      console.log(
        `Sending ${method} request to ${endpoint} with data:`,
        requestData
      );

      const response = await fetch(endpoint, {
        method,
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to save room configuration"
        );
      }

      const data = await response.json();
      console.log("API response:", data);

      // Get the room config ID from the response
      const roomConfigId =
        data.roomConfig?.id || (isEdit ? initialData.id : null);

      // Save translations for ALL languages that have translation data
      if (roomConfigId) {
        try {
          console.log(
            `💾 Saving translations for room ${roomConfigId} for all languages with data`
          );

          // Get all languages that have translation data
          const languagesWithData = Object.keys(allTranslatedValues).filter(
            (lang) =>
              lang !== "en" &&
              Object.keys(allTranslatedValues[lang] || {}).length > 0
          );

          // Also check for array translations
          const languagesWithArrayData = Object.keys(arrayTranslations).filter(
            (lang) =>
              lang !== "en" &&
              Object.keys(arrayTranslations[lang] || {}).length > 0
          );

          // Combine both sets of languages
          const allLanguagesWithData = [
            ...new Set([...languagesWithData, ...languagesWithArrayData]),
          ];

          console.log(
            `🌐 Languages with translation data:`,
            allLanguagesWithData
          );
          console.log(`📊 All translated values:`, allTranslatedValues);
          console.log(`📊 All array translations:`, arrayTranslations);

          // Save text field translations for each language
          for (const language of allLanguagesWithData) {
            const translationsToSave: Record<string, string> = {};

            TRANSLATABLE_FIELDS.forEach((fieldName) => {
              // Use translated values for this specific language
              const value = allTranslatedValues[language]?.[fieldName];
              if (value && typeof value === "string") {
                // Generate translation key with the actual room ID
                const key = `room.${roomConfigId}.${fieldName}`;
                translationsToSave[key] = value;
                console.log(
                  `📝 Adding ${language} translation: ${key} = "${value}"`
                );
              }
            });

            // Only save if we have translations to save for this language
            if (Object.keys(translationsToSave).length > 0) {
              await saveTranslations(language, translationsToSave);
              console.log(
                `✅ ${language} room translations saved successfully`
              );
            }
          }

          // Save amenities translations for each language
          for (const language of allLanguagesWithData) {
            const languageAmenities = arrayTranslations[language]?.amenities;
            if (languageAmenities && languageAmenities.length > 0) {
              // For new rooms, we need to save array translations manually since the hook was initialized with "new"
              // We'll use the Tolgee API directly with the actual room ID
              try {
                const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
                const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

                if (apiKey && projectId) {
                  const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;

                  // Save each amenity as a separate translation key
                  for (let i = 0; i < languageAmenities.length; i++) {
                    const amenityKey = `room.${roomConfigId}.amenities.${i}`;
                    const amenityValue = languageAmenities[i];

                    const payload = {
                      key: amenityKey,
                      translations: {
                        [language]: amenityValue,
                      },
                    };

                    console.log(
                      `💾 Saving ${language} amenity translation: ${amenityKey} = "${amenityValue}"`
                    );

                    const response = await fetch(url, {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                        Accept: "application/json",
                        "X-API-Key": apiKey,
                      },
                      body: JSON.stringify(payload),
                    });

                    if (!response.ok) {
                      console.error(
                        `Failed to save ${language} amenity translation for ${amenityKey}`
                      );
                    } else {
                      console.log(
                        `✅ Successfully saved ${language} amenity translation: ${amenityKey}`
                      );
                    }
                  }

                  console.log(
                    `✅ All ${language} amenities translations saved successfully`
                  );
                }
              } catch (amenityError) {
                console.error(
                  `❌ Error saving ${language} amenities translations:`,
                  amenityError
                );
              }
            }
          }
        } catch (error) {
          console.error("❌ Error saving translations:", error);
          // Don't fail the entire operation if translations fail
          toast.error("Warning", {
            description:
              "Room created successfully, but translations could not be saved. You can edit the room to add translations.",
          });
        }
      }

      if (roomConfigId) {
        // Handle image uploads if there are any new images
        const mediaFields = form.getValues("media") || [];
        const newMediaFiles = mediaFields.filter(
          (media) => media.file !== undefined
        );

        if (newMediaFiles.length > 0) {
          console.log(
            `Uploading ${newMediaFiles.length} images for room config ${roomConfigId}`
          );

          // Upload each new image
          for (const media of newMediaFiles) {
            if (media.file) {
              const formData = new FormData();
              formData.append("files", media.file);

              try {
                const uploadResponse = await fetch(
                  `/admin/hotel-management/room-configs/${roomConfigId}/upload`,
                  {
                    method: "POST",
                    credentials: "include",
                    body: formData,
                  }
                );

                if (!uploadResponse.ok) {
                  console.error(
                    "Failed to upload image",
                    await uploadResponse.text()
                  );
                } else {
                  const uploadResult = await uploadResponse.json();
                  console.log("Image upload result:", uploadResult);

                  // If this is a thumbnail image, set it as the product thumbnail
                  if (
                    media.isThumbnail &&
                    uploadResult.images &&
                    uploadResult.images.length > 0
                  ) {
                    const imageId = uploadResult.images[0].id;

                    await fetch(
                      `/admin/hotel-management/room-configs/${roomConfigId}/thumbnail`,
                      {
                        method: "POST",
                        credentials: "include",
                        headers: {
                          "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ image_id: imageId }),
                      }
                    );
                  }
                }
              } catch (uploadError) {
                console.error("Error uploading image:", uploadError);
              }
            }
          }
        }
      }

      toast.success("Success", {
        description: isEdit
          ? "Room configuration updated successfully"
          : "Room configuration created successfully",
      });
      onComplete(true);
    } catch (error) {
      console.error("Error saving room configuration:", error);
      toast.error("Error", {
        description:
          error instanceof Error
            ? error.message
            : "Failed to save room configuration",
      });
    } finally {
      setIsSubmitting(false);
      onLoadingChange?.(false);
    }
  };

  return (
    <div className="h-full">
      <div className="w-full p-6">
        <Toaster />

        {/* Translation Info Tip */}
        {!isBaseLanguage && (
          <div className="mb-6">
            <InlineTip label="Translation Mode">
              You are editing in{" "}
              {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)
                ?.name || selectedLanguage}{" "}
              language. Only translatable fields (name, description, amenities)
              can be edited. Other fields show base language values and are
              read-only.
            </InlineTip>
          </div>
        )}

        <div className="space-y-8">
          {/* Basic Information Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <Home className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Basic Information
              </Heading>
            </div>

            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Room Type Name *
                  </label>
                  {/* AI Translate button for name field - Only show for non-base languages */}
                  {!isBaseLanguage && formData.name && (
                    <AITranslateButton
                      sourceText={formData.name}
                      targetLanguage={selectedLanguage}
                      fieldType="name"
                      onTranslate={(translatedText) => {
                        console.log(`🤖 AI translated name: "${translatedText}"`);
                        handleFieldChange("name", translatedText);
                      }}
                      context={{
                        entityType: "hotel",
                        entityName: formData.name,
                      }}
                      size="small"
                      variant="transparent"
                      showText={false}
                    />
                  )}
                </div>
                <Input
                  key={`name-${selectedLanguage}`}
                  value={getFieldValue("name", formData.name)}
                  onChange={(e) => {
                    const value = e.target.value;
                    console.log(
                      `🎯 Name input onChange: "${value}" (lang: ${selectedLanguage})`
                    );
                    handleFieldChange("name", value);
                  }}
                  placeholder="e.g., Deluxe King Room"
                  className="w-full"
                  disabled={!isFieldEnabled("name", isBaseLanguage)}
                  required
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Description
                  </label>
                  {/* AI Translate button for description field - Only show for non-base languages */}
                  {!isBaseLanguage && formData.description && (
                    <AITranslateButton
                      sourceText={formData.description}
                      targetLanguage={selectedLanguage}
                      fieldType="description"
                      onTranslate={(translatedText) => {
                        console.log(
                          `🤖 AI translated description: "${translatedText}"`
                        );
                        handleFieldChange("description", translatedText);
                      }}
                      context={{
                        entityType: "hotel",
                        entityName: formData.name,
                      }}
                      size="small"
                      variant="transparent"
                      showText={false}
                    />
                  )}
                </div>
                <TextareaField
                  id="description"
                  key={`description-${selectedLanguage}`}
                  label=""
                  value={getFieldValue("description", formData.description)}
                  onChange={(value) => {
                    console.log(
                      `🎯 Description input onChange: "${value}" (lang: ${selectedLanguage})`
                    );
                    handleFieldChange("description", value);
                  }}
                  placeholder="Describe the room, its features, and what makes it special..."
                  rows={4}
                  contentType="description"
                  context={{
                    name: formData.name,
                    type: formData.type,
                    hotel_id: hotelId,
                  }}
                  helpText="A detailed description helps guests understand what to expect"
                  disabled={!isFieldEnabled("description", isBaseLanguage)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Room Size
                  </label>
                  <Input
                    value={formData.room_size}
                    onChange={(e) => handleInputChange("room_size", e.target.value)}
                    placeholder="e.g., 32 m²"
                    disabled={!isFieldEnabled("room_size", isBaseLanguage)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Bed Type
                  </label>
                  <CustomSelect
                    value={formData.bed_type}
                    onChange={(value) => handleInputChange("bed_type", value)}
                    options={bedTypes.map((type) => ({
                      value: type.value,
                      label: type.label,
                    }))}
                    placeholder="Select a bed type"
                    className="w-full"
                    disabled={!isFieldEnabled("bed_type", isBaseLanguage)}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Capacity & Occupancy Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                <Users className="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Capacity & Occupancy
              </Heading>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Maximum Adults *
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max="10"
                    value={formData.max_adults.toString()}
                    onChange={(e) =>
                      handleInputChange("max_adults", parseInt(e.target.value) || 1)
                    }
                    disabled={!isFieldEnabled("max_adults", isBaseLanguage)}
                    required
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Base room capacity for adults
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Maximum Children
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="10"
                    value={formData.max_children.toString()}
                    onChange={(e) =>
                      handleInputChange(
                        "max_children",
                        parseInt(e.target.value) || 0
                      )
                    }
                    disabled={!isFieldEnabled("max_children", isBaseLanguage)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Maximum Infants
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="5"
                    value={formData.max_infants.toString()}
                    onChange={(e) =>
                      handleInputChange(
                        "max_infants",
                        parseInt(e.target.value) || 0
                      )
                    }
                    disabled={!isFieldEnabled("max_infants", isBaseLanguage)}
                  />
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Maximum Occupancy
                </label>
                <Input
                  type="number"
                  value={formData.max_occupancy.toString()}
                  className="bg-gray-100 dark:bg-gray-600"
                  readOnly
                  disabled
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Total occupancy (adults + children + infants) - automatically calculated
                </p>
              </div>
            </div>
          </div>

          {/* Additional Options Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                <Bed className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Additional Options
              </Heading>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Maximum Extra Beds
                </label>
                <Input
                  type="number"
                  min="0"
                  max="5"
                  value={formData.max_extra_beds.toString()}
                  onChange={(e) =>
                    handleInputChange(
                      "max_extra_beds",
                      parseInt(e.target.value) || 0
                    )
                  }
                  disabled={!isFieldEnabled("max_extra_beds", isBaseLanguage)}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Additional beds for extra guests
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Maximum Cots
                </label>
                <Input
                  type="number"
                  min="0"
                  max="3"
                  value={formData.max_cots.toString()}
                  onChange={(e) =>
                    handleInputChange("max_cots", parseInt(e.target.value) || 0)
                  }
                  disabled={!isFieldEnabled("max_cots", isBaseLanguage)}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Baby cots for infants/toddlers
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Extra Adults Beyond Capacity
                </label>
                <Input
                  type="number"
                  min="0"
                  max="5"
                  value={formData.max_adults_beyond_capacity.toString()}
                  onChange={(e) =>
                    handleInputChange(
                      "max_adults_beyond_capacity",
                      parseInt(e.target.value) || 0
                    )
                  }
                  disabled={
                    !isFieldEnabled("max_adults_beyond_capacity", isBaseLanguage)
                  }
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Additional adults allowed beyond base capacity (with surcharge)
                </p>
              </div>
            </div>
          </div>

          {/* Amenities Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                <Coffee className="w-4 h-4 text-orange-600 dark:text-orange-400" />
              </div>
              <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Amenities
              </Heading>
            </div>

            <TranslatableTagInput
              label=""
              placeholder="Add amenity..."
              helpText="List the amenities available in this room type"
              baseTags={formData.amenities || []}
              translatedTags={
                arrayTranslations[selectedLanguage]?.amenities || []
              }
              onBaseTagsChange={(values: string[]) => {
                setFormData({ ...formData, amenities: values });
                // Also update amenitiesInput for backward compatibility
                setAmenitiesInput(values.join(", "));
              }}
              onTranslatedTagsChange={(values: string[]) => {
                // Only update local state, don't save to API immediately
                // The actual saving will happen when the form is submitted
                console.log(
                  `🏷️ Amenities translated values updated for ${selectedLanguage}:`,
                  values
                );

                // Update the array translations state locally
                const updatedArrayTranslations = {
                  ...arrayTranslations,
                  [selectedLanguage]: {
                    ...arrayTranslations[selectedLanguage],
                    amenities: values,
                  },
                };

                // Update the hook's state directly (this is exposed from the hook)
                if (typeof setArrayTranslations === "function") {
                  setArrayTranslations(updatedArrayTranslations);
                }
              }}
              isBaseLanguage={isBaseLanguage}
              currentLanguage={selectedLanguage}
              fieldType="amenities"
              entityType="hotel"
              entityName={formData.name}
              colorScheme="blue"
              enableAITranslation={true}
            />
          </div>

          {/* Room Images Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 room-config-media-section">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                <Eye className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
              </div>
              <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Room Images
              </Heading>
            </div>
            <RoomConfigMediaSection form={form} />
          </div>
        </div>

        {/* Hidden button that can be triggered from the modal footer */}
        <button
          id="room-config-save-button"
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.name}
          style={{ display: "none" }}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default SimpleRoomConfigForm;
