import { useState, useEffect } from "react";
import {
  Button,
  Heading,
  Text,
  Toaster,
  toast,
  FocusModal,
  Textarea,
} from "@camped-ai/ui";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import Spinner from "../shared/spinner";
import PaymentLinkButton from "./payment-link-button";
import WhatsAppMessagesPanel from "./whatsapp-messages-panel";
import { useRbac } from "../../hooks/use-rbac";

// Status badge colors
const statusColors: Record<string, string> = {
  pending: "yellow",
  confirmed: "green",
  checked_in: "blue",
  checked_out: "purple",
  canceled: "gray",
  no_show: "gray",
};

// Payment status badge colors
const paymentStatusColors: Record<string, string> = {
  not_paid: "red",
  awaiting_payment: "yellow",
  partially_paid: "orange",
  paid: "green",
  refunded: "purple",
  partially_refunded: "blue",
};

interface SimpleBookingDetailProps {
  bookingId: string | undefined;
  isInSidebar?: boolean;
}

const SimpleBookingDetail = ({
  bookingId,
  isInSidebar = false,
}: SimpleBookingDetailProps) => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [booking, setBooking] = useState<any>(null);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [roomDetails, setRoomDetails] = useState<any>(null);
  const [addOns, setAddOns] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDownloadingInvoice, setIsDownloadingInvoice] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");
  const [taxRate, setTaxRate] = useState<number>(0);

  // Function to fetch tax rate from tax regions
  const fetchTaxRate = async () => {
    try {
      // Fetch tax regions to get the configured tax rate
      const response = await fetch("/admin/tax-regions");

      if (response.ok) {
        const data = await response.json();

        // Find the tax region and get the tax rate
        // For now, we'll use the first tax region's rate
        // In a real implementation, you might want to match by country/region
        if (data.tax_regions && data.tax_regions.length > 0) {
          const taxRegion = data.tax_regions[0];
          if (taxRegion.tax_rates && taxRegion.tax_rates.length > 0) {
            const rate = taxRegion.tax_rates[0].rate || 0;
            setTaxRate(rate);
            console.log("Tax rate fetched:", rate);
          }
        }
      } else {
        console.warn("Failed to fetch tax regions, using 0% tax rate");
      }
    } catch (error) {
      console.error("Error fetching tax rate:", error);
      // Default to 0% if we can't fetch tax rate
      setTaxRate(0);
    }
  };

  // Function to handle invoice download
  const handleDownloadInvoice = async () => {
    if (!bookingId) {
      toast.error("Error", {
        description: "Booking ID is missing.",
      });
      return;
    }

    setIsDownloadingInvoice(true);
    try {
      // Endpoint should match your backend route
      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}/invoice`
      );

      if (!response.ok) {
        // Try to parse error message from backend if available
        let errorMsg = `Failed to download invoice: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData && errorData.message) {
            errorMsg = errorData.message;
          }
        } catch (e) {
          // Ignore if response is not JSON
        }
        throw new Error(errorMsg);
      }

      const blob = await response.blob();
      if (blob.type !== "application/pdf") {
        // If the backend didn't send a PDF, it might be an error message in text/html or json
        const errorText = await blob.text();
        let parsedError =
          "An unexpected error occurred. The server did not return a PDF.";
        try {
          const jsonError = JSON.parse(errorText);
          if (jsonError.message) parsedError = jsonError.message;
        } catch (e) {
          // If not JSON, use the raw text if it's short, or a generic message
          if (errorText.length < 200) parsedError = errorText;
        }
        throw new Error(parsedError);
      }

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      // Use booking.id or booking.order_id from the booking state object
      a.download = `invoice-${
        booking?.display_id || booking?.id || booking?.order_id || bookingId
      }.pdf`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

      toast.success("Success", {
        description: "Invoice download started.",
      });
    } catch (error: any) {
      console.error("Error downloading invoice:", error);
      toast.error("Download Failed", {
        description: error.message || "Could not download invoice.",
      });
    } finally {
      setIsDownloadingInvoice(false);
    }
  };

  // Fetch booking details
  const fetchBookingDetails = async () => {
    if (!bookingId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}`
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch booking details: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.booking) {
        throw new Error("No booking data in response");
      }

      // Process booking data
      const processedBooking = { ...data.booking };

      // Parse metadata if it's a string
      if (typeof processedBooking.metadata === "string") {
        try {
          processedBooking.metadata = JSON.parse(processedBooking.metadata);
        } catch (e) {
          console.error("Failed to parse metadata string:", e);
        }
      }

      // Debug total amount
      console.log("Total amount sources:", {
        total_amount: processedBooking.total_amount,
        total: processedBooking.total,
        metadata_total_amount: processedBooking.metadata?.total_amount,
        metadata_total: processedBooking.metadata?.total,
        items: processedBooking.items,
        item_price:
          processedBooking.items && processedBooking.items[0]?.unit_price,
        item_quantity:
          processedBooking.items && processedBooking.items[0]?.quantity,
      });

      // If total amount is missing, try to calculate it
      if (
        !processedBooking.total_amount &&
        processedBooking.items &&
        processedBooking.items.length > 0
      ) {
        const item = processedBooking.items[0];
        if (item.unit_price && item.quantity) {
          processedBooking.total_amount = item.unit_price * item.quantity;
          console.log(
            "Calculated total amount:",
            processedBooking.total_amount
          );
        }
      }

      setBooking(processedBooking);
      console.log("Booking state set:", processedBooking);

      // Set add-ons if they exist in the response
      if (data.add_ons) {
        setAddOns(data.add_ons);
        console.log(
          `✅ Found ${data.add_ons.length} add-ons for booking:`,
          data.add_ons
        );
      } else {
        setAddOns([]);
      }

      // Fetch hotel details if we have a hotel ID
      if (processedBooking.hotel_id || processedBooking.metadata?.hotel_id) {
        fetchHotelDetails(
          processedBooking.hotel_id || processedBooking.metadata?.hotel_id
        );
      }

      // Fetch room details if we have a room config ID
      if (
        processedBooking.room_config_id ||
        processedBooking.product_id ||
        processedBooking.metadata?.room_config_id ||
        processedBooking.metadata?.room_id
      ) {
        fetchRoomDetails(
          processedBooking.room_config_id ||
            processedBooking.product_id ||
            processedBooking.metadata?.room_config_id ||
            processedBooking.metadata?.room_id
        );
      }
    } catch (error: any) {
      console.error("Error fetching booking details:", error);
      toast.error("Error", {
        description: `Failed to fetch booking details: ${error.message}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch hotel details
  const fetchHotelDetails = async (hotelId: string) => {
    try {
      console.log(`Fetching hotel details for ID: ${hotelId}`);
      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}`);

      if (!response.ok) {
        console.error(`Failed to fetch hotel details: ${response.status}`);
        return;
      }

      const data = await response.json();

      if (data.hotel) {
        setHotelDetails(data.hotel);
      } else if (data.hotels && data.hotels.length > 0) {
        setHotelDetails(data.hotels[0]);
      } else {
        console.error("No hotel data found in response");
      }
    } catch (error) {
      console.error("Error fetching hotel details:", error);
    }
  };

  // Function to fetch room details
  const fetchRoomDetails = async (roomId: string) => {
    try {
      console.log(`Fetching room details for ID: ${roomId}`);

      // Try different endpoints to get room details
      let response = await fetch(`/admin/products/${roomId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.product) {
          setRoomDetails(data.product);
          return;
        }
      }

      // Try direct-room-configs endpoint
      response = await fetch(`/admin/direct-room-configs/${roomId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.product) {
          setRoomDetails(data.product);
          return;
        }
      }

      // Try variants endpoint
      response = await fetch(`/admin/products/variants/${roomId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.variant && data.variant.product_id) {
          const productResponse = await fetch(
            `/admin/direct-room-configs/${data.variant.product_id}`
          );

          if (productResponse.ok) {
            const productData = await productResponse.json();
            if (productData.product) {
              productData.product.variant = data.variant;
              setRoomDetails(productData.product);
              return;
            }
          }
        }
      }

      console.error(`Could not fetch room details for ID: ${roomId}`);
    } catch (error) {
      console.error("Error fetching room details:", error);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchBookingDetails();
    fetchTaxRate();
  }, [bookingId]);

  // Cancel booking
  const handleCancelBooking = async () => {
    if (!bookingId) {
      toast.error("Error", {
        description: "No booking ID provided",
      });
      return;
    }

    try {
      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            reason: cancellationReason,
            cancelled_by: "admin",
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to cancel booking");
      }

      const data = await response.json();
      setBooking(data.booking);

      toast.success("Success", {
        description: "Booking cancelled successfully",
      });

      // Check if we need to refresh availability data
      if (data.refresh_availability) {
        // Dispatch an event to notify that availability data should be refreshed
        const event = new CustomEvent("booking_cancelled", {
          detail: {
            bookingId: bookingId,
            roomId: booking?.room_id || booking?.metadata?.room_id,
            checkInDate:
              booking?.check_in_date || booking?.metadata?.check_in_date,
            checkOutDate:
              booking?.check_out_date || booking?.metadata?.check_out_date,
          },
        });
        window.dispatchEvent(event);
      }

      // Close dialog
      setIsCancelDialogOpen(false);
    } catch (error) {
      console.error("Error cancelling booking:", error);
      toast.error("Error", {
        description: "Failed to cancel booking",
      });
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd MMM yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Go back to bookings list or close sidebar
  const handleGoBack = () => {
    if (isInSidebar) {
      // If in sidebar, we'll handle this in the parent component
      // by setting selectedBookingId to null
      // The close button is now in the parent component, so this is not needed
    } else {
      // Otherwise, go back to the bookings list
      navigate("/hotel-management/bookings");
    }
  };

  // Handle payment link success
  const handlePaymentLinkSuccess = (paymentLink: any) => {
    // Update the booking object with the payment link information
    setBooking({
      ...booking,
      metadata: {
        ...booking.metadata,
        payment_link: paymentLink.url,
        payment_link_id: paymentLink.id,
        payment_link_expires_at: paymentLink.expires_at,
      },
    });
  };

  // Update traveler info
  const handleUpdateTravelerInfo = () => {
    if (!bookingId) return;
    navigate(`/hotel-management/bookings/${bookingId}/update-guest`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="medium" />
        <div className="ml-4 text-muted-foreground">
          Loading booking details...
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="text-center py-8">
        <Heading>Booking Not Found</Heading>
        <Text className="mt-2">
          The booking you're looking for doesn't exist or has been removed.
        </Text>
        <Button className="mt-4" onClick={handleGoBack}>
          Back to Bookings
        </Button>
      </div>
    );
  }

  console.log({ hotelDetails, booking });
  console.log(hotelDetails?.[0]?.name);

  return (
    <div className={`space-y-6 ${isInSidebar ? "p-4" : ""}`}>
      <Toaster />

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
          {!isInSidebar && (
            <div className="flex items-center gap-2 mb-2">
              <Button variant="secondary" onClick={handleGoBack} size="small">
                ← Back
              </Button>
              <Heading className="text-2xl">Booking Details</Heading>
            </div>
          )}
          <Text className="text-muted-foreground">
            Booking ID: {booking.id || booking.order_id}
          </Text>
        </div>

        {/* <div className="flex flex-col gap-2 mt-4 sm:mt-0">
          <div className="flex items-center gap-2">
            <Text className="text-sm text-gray-500">Status:</Text>
            <div
              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium capitalize bg-${
                statusColors[booking.status] || "gray"
              }-100 text-${statusColors[booking.status] || "gray"}-800`}
            >
              {booking.status?.replace("_", " ") || "Pending"}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Text className="text-sm text-gray-500">Payment:</Text>
            <div
              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium capitalize bg-${
                paymentStatusColors[booking.payment_status] || "gray"
              }-100 text-${
                paymentStatusColors[booking.payment_status] || "gray"
              }-800`}
            >
              {booking.payment_status?.replace("_", " ") || "Not Paid"}
            </div>
          </div>
        </div> */}
      </div>

      {/* Main Content - Layout depends on whether in sidebar or not */}
      <div
        className={
          isInSidebar ? "space-y-6" : "grid grid-cols-1 lg:grid-cols-3 gap-6"
        }
      >
        {/* Booking Details (full width in sidebar, 2/3 width on large screens otherwise) */}
        <div className={isInSidebar ? "space-y-6" : "lg:col-span-2 space-y-6"}>
          {/* Guest Information Card */}
          <div className="bg-card p-6 rounded-lg border border-border shadow-sm">
            <h3 className="text-lg font-medium mb-4">Guest Information</h3>

            <div className="space-y-4">
              <div>
                <Text className="text-sm text-muted-foreground">
                  Guest Name
                </Text>
                <Text className="font-medium">
                  {booking.guest_name ||
                    booking.metadata?.guest_name ||
                    "Not provided"}
                </Text>
              </div>

              <div>
                <Text className="text-sm text-muted-foreground">Email</Text>
                <Text className="font-medium">
                  {booking.guest_email ||
                    booking.metadata?.guest_email ||
                    booking.email ||
                    "Not provided"}
                </Text>
              </div>

              <div>
                <Text className="text-sm text-muted-foreground">Phone</Text>
                <Text className="font-medium">
                  {booking.guest_phone ||
                    booking.metadata?.guest_phone ||
                    "Not provided"}
                </Text>
              </div>

              <div>
                <Text className="text-sm text-muted-foreground">
                  Number of Guests
                </Text>
                <Text className="font-medium">
                  {booking.number_of_guests ||
                    booking.metadata?.number_of_guests ||
                    1}
                </Text>
              </div>
            </div>
          </div>

          {/* Booking Information Card */}
          <div className="bg-card p-6 rounded-lg border border-border shadow-sm mt-6">
            <h3 className="text-lg font-medium mb-4">Booking Information</h3>

            <div className="space-y-4">
              {/* Booking Creation Date & Time */}
              <div>
                <Text className="text-sm text-muted-foreground">
                  Booking Created
                </Text>
                <Text className="font-medium">
                  {booking.created_at
                    ? format(
                        new Date(booking.created_at),
                        "dd MMM yyyy 'at' HH:mm"
                      )
                    : "Not available"}
                </Text>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-4">
                <div>
                  <Text className="text-sm text-muted-foreground">
                    Check-in Date & Time
                  </Text>
                  <Text className="font-medium">
                    {formatDate(
                      booking.check_in_date || booking.metadata?.check_in_date
                    )}{" "}
                    {booking.check_in_time ||
                      booking.metadata?.check_in_time ||
                      "12:00"}
                  </Text>
                </div>

                <div>
                  <Text className="text-sm text-muted-foreground">
                    Check-out Date & Time
                  </Text>
                  <Text className="font-medium">
                    {formatDate(
                      booking.check_out_date || booking.metadata?.check_out_date
                    )}{" "}
                    {booking.check_out_time ||
                      booking.metadata?.check_out_time ||
                      "12:00"}
                  </Text>
                </div>
              </div>

              <div>
                <Text className="text-sm text-muted-foreground">Hotel</Text>
                <Text className="font-medium">
                  {/* {hotelDetails?.name || booking[0].hotel_name || "Grand Hotel"} */}
                  {hotelDetails && hotelDetails?.[0]?.name}
                </Text>
                {!hotelDetails?.name &&
                  (booking.hotel_id || booking.metadata?.hotel_id) && (
                    <Text className="text-xs text-muted-foreground/70">
                      ID: {booking.hotel_id || booking.metadata?.hotel_id}
                    </Text>
                  )}
              </div>

              <div>
                <Text className="text-sm text-muted-foreground">Room Type</Text>
                <Text className="font-medium">
                  {roomDetails?.title ||
                    booking.room_type ||
                    booking.metadata?.room_type ||
                    "Standard Room"}
                </Text>
                {!roomDetails?.title &&
                  (booking.room_config_id ||
                    booking.metadata?.room_config_id ||
                    booking.metadata?.room_id) && (
                    <Text className="text-xs text-muted-foreground/70">
                      ID:{" "}
                      {booking.room_config_id ||
                        booking.metadata?.room_config_id ||
                        booking.metadata?.room_id}
                    </Text>
                  )}
              </div>

              <div>
                <Text className="text-sm text-muted-foreground">
                  Number of Rooms
                </Text>
                <Text className="font-medium">
                  {booking.number_of_rooms ||
                    booking.metadata?.number_of_rooms ||
                    1}
                </Text>
              </div>

              {/* Pricing Breakdown */}
              <div className="space-y-2">
                <Text className="text-sm text-muted-foreground">
                  Pricing Breakdown
                </Text>

                {/* Calculate individual amounts */}
                {(() => {
                  const extraBedTotal = booking.metadata?.extra_bed_total || 0;
                  const extraAdultsBeyondCapacityTotal =
                    booking.metadata?.extra_adults_beyond_capacity_total || 0;
                  const cotTotal = booking.metadata?.cot_total || 0;
                  const addOnTotal = addOns.reduce(
                    (total, addon) =>
                      total + (parseFloat(addon.total_price) || 0),
                    0
                  );
                  const roomOnlyAmount =
                    (parseFloat(booking.metadata.total_amount) || 0) -
                    extraBedTotal -
                    cotTotal -
                    extraAdultsBeyondCapacityTotal -
                    addOnTotal;

                  // Debug logging
                  console.log("Pricing calculation debug:", {
                    totalAmount: booking.metadata.total_amount,
                    extraBedTotal,
                    cotTotal,
                    extraAdultsBeyondCapacityTotal,
                    addOnTotal,
                    roomOnlyAmount,
                    taxRate,
                  });

                  return (
                    <>
                      {/* Room Amount */}
                      <div className="flex justify-between items-center">
                        <Text className="text-sm">Room Cost:</Text>
                        <Text className="font-medium">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: booking.currency_code || "USD",
                          }).format(
                            taxRate > 0
                              ? roomOnlyAmount / (1 + taxRate / 100)
                              : roomOnlyAmount
                          )}
                        </Text>
                      </div>

                      {booking.metadata?.extra_adults_beyond_capacity > 0
                        ? extraAdultsBeyondCapacityTotal > 0 && (
                            <div className="flex justify-between items-center">
                              <Text className="text-sm">Extra Adults:</Text>
                              <Text className="font-medium">
                                {new Intl.NumberFormat("en-US", {
                                  style: "currency",
                                  currency: booking.currency_code || "USD",
                                }).format(extraAdultsBeyondCapacityTotal)}
                              </Text>
                            </div>
                          )
                        : null}

                      {/* Extra Beds Amount (if exists) */}
                      {booking.metadata?.extra_beds > 0 &&
                        extraBedTotal > 0 && (
                          <div className="flex justify-between items-center">
                            <Text className="text-sm">Extra Beds:</Text>
                            <Text className="font-medium">
                              {new Intl.NumberFormat("en-US", {
                                style: "currency",
                                currency: booking.currency_code || "USD",
                              }).format(extraBedTotal)}
                            </Text>
                          </div>
                        )}

                      {/* Cots Amount (if exists) */}
                      {booking.metadata?.cots > 0 && cotTotal > 0 && (
                        <div className="flex justify-between items-center">
                          <Text className="text-sm">Cots:</Text>
                          <Text className="font-medium">
                            {new Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: booking.currency_code || "USD",
                            }).format(cotTotal)}
                          </Text>
                        </div>
                      )}

                      {/* Add-ons Amount (if exists) */}
                      {addOnTotal > 0 && (
                        <div className="flex justify-between items-center">
                          <Text className="text-sm">Add-ons:</Text>
                          <Text className="font-medium">
                            {new Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: booking.currency_code || "USD",
                            }).format(addOnTotal)}
                          </Text>
                        </div>
                      )}

                      {/* Tax Information (if tax rate is configured) */}
                      {taxRate > 0 && (
                        <>
                          <div className="flex justify-between items-center">
                            <Text className="text-sm">
                              Subtotal (excl. tax):
                            </Text>
                            <Text className="font-medium">
                              {new Intl.NumberFormat("en-US", {
                                style: "currency",
                                currency: booking.currency_code || "USD",
                              }).format(
                                (roomOnlyAmount +
                                  extraBedTotal +
                                  cotTotal +
                                  extraAdultsBeyondCapacityTotal +
                                  addOnTotal) /
                                  (1 + taxRate / 100)
                              )}
                            </Text>
                          </div>
                          <div className="flex justify-between items-center">
                            <Text className="text-sm">Tax ({taxRate}%):</Text>
                            <Text className="font-medium">
                              {new Intl.NumberFormat("en-US", {
                                style: "currency",
                                currency: booking.currency_code || "USD",
                              }).format(
                                ((roomOnlyAmount +
                                  extraBedTotal +
                                  cotTotal +
                                  extraAdultsBeyondCapacityTotal +
                                  addOnTotal) /
                                  (1 + taxRate / 100)) *
                                  (taxRate / 100)
                              )}
                            </Text>
                          </div>
                        </>
                      )}

                      {/* Total Amount */}
                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <Text className="font-medium">
                          {taxRate > 0
                            ? "Total Amount (incl. tax):"
                            : "Total Amount:"}
                        </Text>
                        <Text className="font-semibold text-lg">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: booking.currency_code || "USD",
                          }).format(
                            (roomOnlyAmount || 0) +
                              (extraBedTotal || 0) +
                              (cotTotal || 0) +
                              (extraAdultsBeyondCapacityTotal || 0) +
                              (addOnTotal || 0)
                          )}
                        </Text>
                      </div>
                    </>
                  );
                })()}
              </div>

              {/* Payment Link Information (if exists) */}
              {booking.metadata?.payment_link && (
                <div className="mt-2 p-3 bg-muted rounded-md">
                  <div className="flex items-center justify-between">
                    <Text className="text-sm font-medium">Payment Link</Text>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          booking.metadata.payment_link
                        );
                        toast.success("Copied", {
                          description: "Payment link copied to clipboard",
                        });
                      }}
                    >
                      Copy Link
                    </Button>
                  </div>
                  {booking.metadata.payment_link_expires_at && (
                    <Text className="text-xs text-muted-foreground mt-1">
                      Expires:{" "}
                      {new Date(
                        booking.metadata.payment_link_expires_at
                      ).toLocaleString()}
                    </Text>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Extra Bed Information Section */}
          {booking.metadata?.extra_beds > 0 &&
            booking.metadata?.extra_bed_details && (
              <div className="bg-card p-6 rounded-lg border border-border shadow-sm">
                <h3 className="text-lg font-medium mb-4">
                  Extra Bed Information
                </h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Text className="text-sm text-muted-foreground">
                        Number of Extra Beds
                      </Text>
                      <Text className="font-medium">
                        {booking.metadata.extra_bed_details.number_of_beds ||
                          booking.metadata.extra_beds}{" "}
                        ×{" "}
                        {booking.metadata.extra_bed_details.extra_bed_config
                          ?.name || "Extra Bed"}
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm text-muted-foreground">
                        Room
                      </Text>
                      <Text className="font-medium">
                        {booking.metadata.extra_bed_details.room_name ||
                          roomDetails?.title ||
                          "Room"}
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm text-muted-foreground">
                        Duration
                      </Text>
                      <Text className="font-medium">
                        {formatDate(
                          booking.metadata.extra_bed_details.check_in
                        )}{" "}
                        -{" "}
                        {formatDate(
                          booking.metadata.extra_bed_details.check_out
                        )}
                        <span className="text-muted-foreground ml-2">
                          ({booking.metadata.extra_bed_details.nights} night
                          {booking.metadata.extra_bed_details.nights > 1
                            ? "s"
                            : ""}
                          )
                        </span>
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm text-muted-foreground">
                        Price per bed per night
                      </Text>
                      <Text className="font-medium">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency:
                            booking.metadata.extra_bed_details.currency_code ||
                            booking.currency_code ||
                            "USD",
                        }).format(
                          booking.metadata.extra_bed_details
                            .price_per_bed_per_night || 0
                        )}
                      </Text>
                    </div>
                  </div>

                  {/* Calculation breakdown */}
                  <div className="bg-muted p-4 rounded-md">
                    <Text className="text-sm text-muted-foreground mb-2">
                      Calculation
                    </Text>
                    <div className="flex justify-between items-center">
                      <Text className="text-sm">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency:
                            booking.metadata.extra_bed_details.currency_code ||
                            booking.currency_code ||
                            "USD",
                        }).format(
                          booking.metadata.extra_bed_details
                            .price_per_bed_per_night || 0
                        )}{" "}
                        ×{" "}
                        {booking.metadata.extra_bed_details.number_of_beds ||
                          booking.metadata.extra_beds}{" "}
                        bed
                        {(booking.metadata.extra_bed_details.number_of_beds ||
                          booking.metadata.extra_beds) > 1
                          ? "s"
                          : ""}{" "}
                        × {booking.metadata.extra_bed_details.nights} night
                        {booking.metadata.extra_bed_details.nights > 1
                          ? "s"
                          : ""}
                      </Text>
                      <Text className="font-semibold">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency:
                            booking.metadata.extra_bed_details.currency_code ||
                            booking.currency_code ||
                            "USD",
                        }).format(
                          booking.metadata.extra_bed_details.total_price ||
                            booking.metadata.extra_bed_total ||
                            0
                        )}
                      </Text>
                    </div>
                  </div>

                  {/* Guest assignments if available */}
                  {booking.metadata.extra_bed_details.guest_assignments &&
                    booking.metadata.extra_bed_details.guest_assignments
                      .length > 0 && (
                      <div>
                        <Text className="text-sm text-muted-foreground mb-2">
                          Assigned to
                        </Text>
                        <div className="space-y-1">
                          {booking.metadata.extra_bed_details.guest_assignments.map(
                            (assignment: any, index: number) => (
                              <Text key={index} className="text-sm">
                                • {assignment.guest_name}
                              </Text>
                            )
                          )}
                        </div>
                      </div>
                    )}
                </div>
              </div>
            )}

          {/* Cot Information Section */}
          {booking.metadata?.cots > 0 && booking.metadata?.cot_details && (
            <div className="bg-card p-6 rounded-lg border border-border shadow-sm">
              <h3 className="text-lg font-medium mb-4">Cot Information</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Number of Cots
                    </Text>
                    <Text className="font-medium">
                      {booking.metadata.cot_details.number_of_cots ||
                        booking.metadata.cots}{" "}
                      × {booking.metadata.cot_details.cot_config?.name || "Cot"}
                    </Text>
                  </div>

                  <div>
                    <Text className="text-sm text-muted-foreground">Room</Text>
                    <Text className="font-medium">
                      {booking.metadata.cot_details.room_name ||
                        roomDetails?.title ||
                        "Room"}
                    </Text>
                  </div>

                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Duration
                    </Text>
                    <Text className="font-medium">
                      {formatDate(booking.metadata.cot_details.check_in)} -{" "}
                      {formatDate(booking.metadata.cot_details.check_out)}
                      <span className="text-muted-foreground ml-2">
                        ({booking.metadata.cot_details.nights} night
                        {booking.metadata.cot_details.nights > 1 ? "s" : ""})
                      </span>
                    </Text>
                  </div>

                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Price per cot per night
                    </Text>
                    <Text className="font-medium">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency:
                          booking.metadata.cot_details.currency_code ||
                          booking.currency_code ||
                          "USD",
                      }).format(
                        booking.metadata.cot_details.price_per_cot_per_night ||
                          0
                      )}
                    </Text>
                  </div>
                </div>

                {/* Calculation breakdown */}
                <div className="bg-muted p-4 rounded-md">
                  <Text className="text-sm text-muted-foreground mb-2">
                    Calculation
                  </Text>
                  <div className="flex justify-between items-center">
                    <Text className="text-sm">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency:
                          booking.metadata.cot_details.currency_code ||
                          booking.currency_code ||
                          "USD",
                      }).format(
                        booking.metadata.cot_details.price_per_cot_per_night ||
                          0
                      )}{" "}
                      ×{" "}
                      {booking.metadata.cot_details.number_of_cots ||
                        booking.metadata.cots}{" "}
                      cot
                      {(booking.metadata.cot_details.number_of_cots ||
                        booking.metadata.cots) > 1
                        ? "s"
                        : ""}{" "}
                      × {booking.metadata.cot_details.nights} night
                      {booking.metadata.cot_details.nights > 1 ? "s" : ""}
                    </Text>
                    <Text className="font-semibold">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency:
                          booking.metadata.cot_details.currency_code ||
                          booking.currency_code ||
                          "USD",
                      }).format(
                        booking.metadata.cot_details.total_price ||
                          booking.metadata.cot_total ||
                          0
                      )}
                    </Text>
                  </div>
                </div>

                {/* Guest assignments if available */}
                {booking.metadata.cot_details.guest_assignments &&
                  booking.metadata.cot_details.guest_assignments.length > 0 && (
                    <div>
                      <Text className="text-sm text-muted-foreground mb-2">
                        Assigned to
                      </Text>
                      <div className="space-y-1">
                        {booking.metadata.cot_details.guest_assignments.map(
                          (assignment: any, index: number) => (
                            <Text key={index} className="text-sm">
                              • {assignment.guest_name}
                            </Text>
                          )
                        )}
                      </div>
                    </div>
                  )}
              </div>
            </div>
          )}

          {/* Add-ons Section */}
          {addOns && addOns.length > 0 && (
            <div className="bg-card p-6 rounded-lg border border-border shadow-sm">
              <h3 className="text-lg font-medium mb-4">Add-on Items</h3>
              <div className="space-y-4">
                {addOns.map((addon: any, index: number) => (
                  <div
                    key={index}
                    className="border-b border-border/50 pb-4 last:border-b-0 last:pb-0"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <Text className="font-medium text-base">
                          {addon.add_on_name || addon.name}
                        </Text>
                        {/* <Text className="text-sm text-muted-foreground mt-1">
                          Quantity: {addon.quantity || 1}
                        </Text> */}

                        {/* Customer Field Responses */}
                        {addon.customer_field_responses &&
                          Object.keys(addon.customer_field_responses).length >
                            0 && (
                            <div className="mt-2">
                              <Text className="text-xs font-medium text-muted-foreground mb-1">
                                Details:
                              </Text>
                              {Object.entries(
                                addon.customer_field_responses
                              ).map(([key, value]) => (
                                <div
                                  key={key}
                                  className="text-xs text-muted-foreground"
                                >
                                  <span className="capitalize">{key}:</span>{" "}
                                  {String(value)}
                                </div>
                              ))}
                            </div>
                          )}

                        {addon.description && (
                          <Text className="text-sm text-muted-foreground mt-1">
                            {addon.description}
                          </Text>
                        )}
                        {/* Pricing Type Badge */}
                        {addon.pricing_type && (
                          <div className="mt-2">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300">
                              {addon.pricing_type === "usage_based"
                                ? "Usage Based"
                                : addon.pricing_type === "package"
                                ? "Package"
                                : "Per Person"}
                            </span>
                          </div>
                        )}

                        {/* Usage-based pricing details */}
                        {addon.pricing_type === "usage_based" &&
                        addon.guest_usage &&
                        addon.guest_usage.length > 0 ? (
                          <div className="mt-3 space-y-2">
                            <div className="text-sm font-medium text-foreground">
                              Guest Usage Details:
                            </div>
                            <div className="space-y-2">
                              {addon.guest_usage.map(
                                (guestUsage: any, guestIndex: number) => {
                                  // Get guest name from travelers data
                                  const getGuestName = () => {
                                    const travelers =
                                      booking.metadata?.travelers;
                                    if (!travelers)
                                      return `${
                                        guestUsage.guest_type === "adult"
                                          ? "Adult"
                                          : "Child"
                                      } Guest ${guestUsage.guest_index + 1}`;

                                    if (
                                      guestUsage.guest_type === "adult" &&
                                      travelers.adults &&
                                      travelers.adults[guestUsage.guest_index]
                                    ) {
                                      return (
                                        travelers.adults[guestUsage.guest_index]
                                          .name ||
                                        `Adult Guest ${
                                          guestUsage.guest_index + 1
                                        }`
                                      );
                                    } else if (
                                      guestUsage.guest_type === "child" &&
                                      travelers.children &&
                                      travelers.children[guestUsage.guest_index]
                                    ) {
                                      return (
                                        travelers.children[
                                          guestUsage.guest_index
                                        ].name ||
                                        `Child Guest ${
                                          guestUsage.guest_index + 1
                                        }`
                                      );
                                    }

                                    return `${
                                      guestUsage.guest_type === "adult"
                                        ? "Adult"
                                        : "Child"
                                    } Guest ${guestUsage.guest_index + 1}`;
                                  };

                                  return (
                                    <div
                                      key={guestIndex}
                                      className="bg-muted p-3 rounded-md"
                                    >
                                      <div className="flex justify-between items-start">
                                        <div>
                                          <div className="text-sm font-medium">
                                            {getGuestName()}
                                          </div>
                                          <div className="text-xs text-muted-foreground mt-1">
                                            Usage Dates:{" "}
                                            {guestUsage.usage_dates.join(", ")}
                                          </div>
                                          <div className="text-xs text-muted-foreground">
                                            {guestUsage.usage_dates.length} day
                                            {guestUsage.usage_dates.length !== 1
                                              ? "s"
                                              : ""}
                                          </div>
                                        </div>
                                        <div className="text-right">
                                          <div className="text-sm font-medium">
                                            {new Intl.NumberFormat("en-US", {
                                              style: "currency",
                                              currency:
                                                addon.currency_code ||
                                                booking.currency_code ||
                                                "USD",
                                            }).format(
                                              (guestUsage.guest_type === "adult"
                                                ? addon.per_day_adult_price || 0
                                                : addon.per_day_child_price ||
                                                  0) *
                                                guestUsage.usage_dates.length
                                            )}
                                          </div>
                                          <div className="text-xs text-muted-foreground">
                                            {new Intl.NumberFormat("en-US", {
                                              style: "currency",
                                              currency:
                                                addon.currency_code ||
                                                booking.currency_code ||
                                                "USD",
                                            }).format(
                                              guestUsage.guest_type === "adult"
                                                ? addon.per_day_adult_price || 0
                                                : addon.per_day_child_price || 0
                                            )}{" "}
                                            per day
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                }
                              )}
                            </div>
                            {addon.usage_dates &&
                              addon.usage_dates.length > 0 && (
                                <div className="text-xs text-muted-foreground mt-2">
                                  <span className="font-medium">
                                    All usage dates:
                                  </span>{" "}
                                  {addon.usage_dates.join(", ")}
                                </div>
                              )}
                          </div>
                        ) : (
                          /* Traditional pricing display for per-person and package */
                          <div className="flex gap-4 mt-2">
                            {addon.pricing_type === "package" ? (
                              <div className="text-sm">
                                <span className="text-muted-foreground">
                                  Package Quantity:
                                </span>{" "}
                                <span className="font-medium">
                                  {addon.package_quantity || 1}
                                </span>
                                {addon.package_price > 0 && (
                                  <span className="text-muted-foreground ml-1">
                                    @{" "}
                                    {new Intl.NumberFormat("en-US", {
                                      style: "currency",
                                      currency:
                                        addon.currency_code ||
                                        booking.currency_code ||
                                        "USD",
                                    }).format(addon.package_price || 0)}
                                  </span>
                                )}
                                {addon.total_occupancy && (
                                  <span className="text-muted-foreground ml-2">
                                    (for {addon.total_occupancy} guests)
                                  </span>
                                )}
                              </div>
                            ) : (
                              /* Per-person pricing with guest names */
                              <>
                                {addon.adult_quantity > 0 && (
                                  <div className="text-sm space-y-1">
                                    <div>
                                      <span className="text-muted-foreground">
                                        Adults:
                                      </span>{" "}
                                      <span className="font-medium">
                                        {addon.adult_quantity}
                                      </span>
                                      {addon.adult_price > 0 && (
                                        <span className="text-muted-foreground ml-1">
                                          @{" "}
                                          {new Intl.NumberFormat("en-US", {
                                            style: "currency",
                                            currency:
                                              addon.currency_code ||
                                              booking.currency_code ||
                                              "USD",
                                          }).format(addon.adult_price || 0)}
                                        </span>
                                      )}
                                    </div>
                                    {/* Show adult guest names */}
                                    {booking.metadata?.travelers?.adults && (
                                      <div className="ml-4 text-xs text-muted-foreground">
                                        {booking.metadata.travelers.adults
                                          .slice(0, addon.adult_quantity)
                                          .map((adult: any, index: number) => (
                                            <div key={index}>
                                              •{" "}
                                              {adult.name ||
                                                `Adult Guest ${index + 1}`}
                                            </div>
                                          ))}
                                      </div>
                                    )}
                                  </div>
                                )}
                                {addon.child_quantity > 0 && (
                                  <div className="text-sm space-y-1">
                                    <div>
                                      <span className="text-muted-foreground">
                                        Children:
                                      </span>{" "}
                                      <span className="font-medium">
                                        {addon.child_quantity}
                                      </span>
                                      {addon.child_price > 0 && (
                                        <span className="text-muted-foreground ml-1">
                                          @{" "}
                                          {new Intl.NumberFormat("en-US", {
                                            style: "currency",
                                            currency:
                                              addon.currency_code ||
                                              booking.currency_code ||
                                              "USD",
                                          }).format(addon.child_price || 0)}
                                        </span>
                                      )}
                                    </div>
                                    {/* Show child guest names */}
                                    {booking.metadata?.travelers?.children && (
                                      <div className="ml-4 text-xs text-muted-foreground">
                                        {booking.metadata.travelers.children
                                          .slice(0, addon.child_quantity)
                                          .map((child: any, index: number) => (
                                            <div key={index}>
                                              •{" "}
                                              {child.name ||
                                                `Child Guest ${index + 1}`}
                                            </div>
                                          ))}
                                      </div>
                                    )}
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        )}
                        {/* {addon.service_level && (
                          <div className="mt-1">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {addon.service_level === "hotel"
                                ? "Hotel Service"
                                : "Destination Service"}
                            </span>
                          </div>
                        )} */}
                      </div>
                      <div className="text-right ml-4">
                        <Text className="font-semibold text-lg">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              addon.currency_code ||
                              booking.currency_code ||
                              "USD",
                          }).format(addon.total_price || 0)}
                        </Text>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Add-ons Total */}
                {addOns.length > 0 && (
                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <div className="flex justify-between items-center">
                      <Text className="font-medium text-base">
                        Add-ons Total:
                      </Text>
                      <Text className="font-semibold text-lg">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: booking.currency_code || "CHF",
                        }).format(
                          addOns.reduce(
                            (total, addon) =>
                              total + (parseFloat(addon.total_price) || 0),
                            0
                          )
                        )}
                      </Text>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Special Requests Section (if any) */}
          {booking.special_requests && (
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium mb-2">Special Requests</h3>
              <Text>{booking.special_requests}</Text>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 justify-end mt-6">
            <PaymentLinkButton
              bookingId={bookingId || ""}
              onSuccess={handlePaymentLinkSuccess}
            />

            {hasPermission("bookings:edit") && (
              <Button variant="secondary" onClick={handleUpdateTravelerInfo}>
                Update Traveler Info
              </Button>
            )}

            <Button
              variant="secondary"
              onClick={handleDownloadInvoice}
              disabled={isDownloadingInvoice}
            >
              {isDownloadingInvoice ? "Downloading..." : "Download Invoice"}
            </Button>

            {booking.status !== "canceled" &&
              hasPermission("bookings:cancel") && (
                <>
                  <Button
                    variant="danger"
                    onClick={() => setIsCancelDialogOpen(true)}
                  >
                    Cancel Booking
                  </Button>

                  <FocusModal
                    open={isCancelDialogOpen}
                    onOpenChange={setIsCancelDialogOpen}
                  >
                    <FocusModal.Content>
                      <FocusModal.Header>
                        <FocusModal.Title>Cancel Booking</FocusModal.Title>
                      </FocusModal.Header>
                      <FocusModal.Body className="p-6">
                        <Text className="mb-4">
                          Are you sure you want to cancel this booking? This
                          action cannot be undone.
                        </Text>
                        <div className="py-4">
                          <Text className="mb-2">Cancellation Reason</Text>
                          <Textarea
                            placeholder="Enter reason for cancellation"
                            value={cancellationReason}
                            onChange={(e) =>
                              setCancellationReason(e.target.value)
                            }
                          />
                        </div>
                      </FocusModal.Body>
                      <FocusModal.Footer>
                        <div className="flex justify-end gap-x-2">
                          <Button
                            variant="secondary"
                            onClick={() => setIsCancelDialogOpen(false)}
                          >
                            Cancel
                          </Button>
                          <Button
                            variant="danger"
                            onClick={handleCancelBooking}
                          >
                            Confirm Cancellation
                          </Button>
                        </div>
                      </FocusModal.Footer>
                    </FocusModal.Content>
                  </FocusModal>
                </>
              )}
          </div>
        </div>

        {/* Right Column - WhatsApp Messages (1/3 width on large screens, only if not in sidebar) */}
        {!isInSidebar && (
          <div className="lg:col-span-1">
            <div className="bg-background rounded-lg shadow-sm h-full">
              <WhatsAppMessagesPanel bookingId={bookingId} />
            </div>
          </div>
        )}

        {/* WhatsApp Messages (if in sidebar, show below booking details) */}
        {isInSidebar && (
          <div className="mt-6">
            <div className="bg-background rounded-lg shadow-sm">
              <WhatsAppMessagesPanel bookingId={bookingId} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleBookingDetail;
