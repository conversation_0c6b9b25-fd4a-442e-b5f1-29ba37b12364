import React, { useState, useEffect } from "react";
import {
  Table,
  Badge,
  Button,
  Select,
  Input,
  DatePicker,
  Heading,
  Text,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { format } from "date-fns";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Co<PERSON>, Filter, ChevronDown } from "lucide-react";
import Spinner from "../shared/spinner";
import { useRbac } from "../../hooks/use-rbac";

// Type definitions
interface BookingMetadata {
  room_id?: string;
  room_name?: string;
  room_configuration_name?: string;
  room_type?: string;
  hotel_id?: string;
  hotel_name?: string;
  guest_name?: string;
  guest_email?: string;
  check_in_date?: string;
  check_out_date?: string;
  nights?: number;
  created_at?: string;
  payment_status?: string;
  currency_code?: string;
  total_amount?: number;
  add_on_total_amount?: number;
}

interface Booking {
  order_id: string;
  status: string;
  total: number;
  currency_code: string;
  created_at: string;
  guest_name?: string;
  guest_email?: string;
  room_type?: string;
  payment_status?: string;
  total_amount?: number;
  check_in_date?: string;
  check_out_date?: string;
  metadata?: BookingMetadata;
}

interface Hotel {
  id: string;
  name: string;
}

// Status badge colors
const statusColors = {
  pending: "yellow",
  confirmed: "green",
  checked_in: "blue",
  checked_out: "purple",
  canceled: "gray", // Changed from red to gray (American spelling)
  no_show: "gray",
};

interface BookingListProps {
  hotelId?: string | null;
}

const BookingList = ({ hotelId = null }: BookingListProps) => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { hasPermission } = useRbac();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [isLoading, setIsLoading] = useState(false); // Start with false to allow initial fetch
  const [totalCount, setTotalCount] = useState(0);
  const [showFilters, setShowFilters] = useState(false);

  // Get pagination from URL parameters
  const currentPage = parseInt(searchParams.get("page") || "0");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");

  // Calculate pagination values
  const pageCount = Math.ceil(totalCount / pageSize);
  const canNextPage = currentPage < pageCount - 1;
  const canPreviousPage = currentPage > 0;

  // Initialize filter states from URL parameters or defaults
  const [hotelFilter, setHotelFilter] = useState(
    searchParams.get("hotel") || hotelId || "all"
  );
  const [customerNameFilter, setCustomerNameFilter] = useState(
    searchParams.get("customerName") || ""
  );
  const [customerEmailFilter, setCustomerEmailFilter] = useState(
    searchParams.get("customerEmail") || ""
  );
  const [fromDateFilter, setFromDateFilter] = useState(() => {
    const fromDate = searchParams.get("fromDate");
    return fromDate ? new Date(fromDate) : null;
  });
  const [toDateFilter, setToDateFilter] = useState(() => {
    const toDate = searchParams.get("toDate");
    return toDate ? new Date(toDate) : null;
  });

  // Initialize hotelFilter from hotelId prop on component mount
  useEffect(() => {
    if (hotelId) {
      setHotelFilter(hotelId);
    }
  }, []);

  // Update hotelFilter when hotelId prop changes
  useEffect(() => {
    if (hotelId) {
      setHotelFilter(hotelId);
    }
  }, [hotelId]);

  // Fetch hotels for the filter dropdown
  const fetchHotels = async () => {
    try {
      const response = await fetch(`/admin/hotel-management/hotels?limit=100`);

      if (!response.ok) {
        throw new Error("Failed to fetch hotels");
      }

      const data = await response.json();
      setHotels(data.hotels || []);
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Failed to fetch hotels");
    }
  };

  // Fetch bookings
  const fetchBookings = async () => {
    try {
      // Prevent multiple simultaneous API calls
      if (isLoading) {
        return;
      }

      setIsLoading(true);

      // Get current filter values from URL or state
      const currentFilters = getCurrentFiltersFromURL();

      // Build query parameters
      const params = new URLSearchParams();

      // Always use hotel filter, prioritizing URL then state then prop
      const effectiveHotelFilter = currentFilters.hotel;
      if (effectiveHotelFilter && effectiveHotelFilter !== "all") {
        params.append("hotel_id", effectiveHotelFilter);
      } else if (hotelId) {
        // Fallback to hotelId prop if no filter is set
        params.append("hotel_id", hotelId);
      }

      // Payment status filter removed

      if (currentFilters.customerName) {
        params.append("guest_name", currentFilters.customerName);
      }

      if (currentFilters.customerEmail) {
        params.append("guest_email", currentFilters.customerEmail);
      }

      if (currentFilters.fromDate) {
        params.append(
          "from_date",
          format(currentFilters.fromDate, "yyyy-MM-dd")
        );
      }

      if (currentFilters.toDate) {
        params.append("to_date", format(currentFilters.toDate, "yyyy-MM-dd"));
      }

      // Pagination - use URL parameters directly or calculate from page
      const effectiveLimit = searchParams.get("limit") || pageSize.toString();
      const effectiveOffset =
        searchParams.get("offset") || (currentPage * pageSize).toString();

      params.append("limit", effectiveLimit);
      params.append("offset", effectiveOffset);

      // Fetch data
      const url = `/admin/hotel-management/bookings?${params.toString()}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch bookings");
      }

      const data = await response.json();

      // Backend now handles pagination properly
      setBookings(data.bookings || []);
      setTotalCount(data.count || 0);
    } catch (error) {
      console.error("Error fetching bookings:", error);
      toast.error("Failed to fetch bookings");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch hotels on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Initialize default URL parameters if missing
  useEffect(() => {
    const hasPageParam = searchParams.has("page");
    const hasPageSizeParam = searchParams.has("pageSize");
    const hasLimitParam = searchParams.has("limit");
    const hasOffsetParam = searchParams.has("offset");

    // If any pagination parameters are missing, set all defaults
    if (
      !hasPageParam ||
      !hasPageSizeParam ||
      !hasLimitParam ||
      !hasOffsetParam
    ) {
      const newSearchParams = new URLSearchParams(searchParams);

      if (!hasPageParam) newSearchParams.set("page", "0");
      if (!hasPageSizeParam) newSearchParams.set("pageSize", "10");
      if (!hasLimitParam) newSearchParams.set("limit", "10");
      if (!hasOffsetParam) newSearchParams.set("offset", "0");

      setSearchParams(newSearchParams, { replace: true });
    }
  }, []); // Only run once on mount

  // Read current values from URL for API calls (no state updates to avoid loops)
  const getCurrentFiltersFromURL = () => {
    return {
      hotel: searchParams.get("hotel") || hotelFilter,
      customerName: searchParams.get("customerName") || customerNameFilter,
      customerEmail: searchParams.get("customerEmail") || customerEmailFilter,
      fromDate: searchParams.get("fromDate")
        ? new Date(searchParams.get("fromDate")!)
        : fromDateFilter,
      toDate: searchParams.get("toDate")
        ? new Date(searchParams.get("toDate")!)
        : toDateFilter,
    };
  };

  // Single useEffect for all data fetching - only depend on URL params and hotelId prop
  useEffect(() => {
    // Check if we have the required pagination parameters
    const hasRequiredParams =
      searchParams.has("page") && searchParams.has("limit");

    if (hasRequiredParams) {
      fetchBookings();
    }
  }, [searchParams, hotelId]);

  // Update URL parameters for pagination
  const updatePage = (newPage: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("page", newPage.toString());
    newSearchParams.set("pageSize", pageSize.toString());
    newSearchParams.set("limit", pageSize.toString());
    newSearchParams.set("offset", (newPage * pageSize).toString());
    setSearchParams(newSearchParams);
  };

  // Update page size and reset to first page
  const updatePageSize = (newPageSize: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("page", "0"); // Reset to first page
    newSearchParams.set("pageSize", newPageSize.toString());
    newSearchParams.set("limit", newPageSize.toString());
    newSearchParams.set("offset", "0"); // Reset offset
    setSearchParams(newSearchParams);
  };

  // Quick date select handlers
  const handleQuickDateSelect = (period: string) => {
    const today = new Date();
    let fromDate: Date | null = null;
    let toDate: Date | null = null;

    switch (period) {
      case "today":
        fromDate = new Date(today);
        toDate = new Date(today);
        break;
      case "thisWeek":
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        fromDate = startOfWeek;
        toDate = endOfWeek;
        break;
      case "thisMonth":
        fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
        toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        break;
      case "last30Days":
        fromDate = new Date(today);
        fromDate.setDate(today.getDate() - 30);
        toDate = new Date(today);
        break;
    }

    setFromDateFilter(fromDate);
    setToDateFilter(toDate);

    // Auto-apply filters when quick date is selected
    setTimeout(() => {
      handleApplyFilters();
    }, 100);
  };

  // Apply filters
  const handleApplyFilters = () => {
    const newSearchParams = new URLSearchParams(searchParams);

    // Update all filter parameters in URL
    newSearchParams.set("hotel", hotelFilter);
    newSearchParams.set("customerName", customerNameFilter);
    newSearchParams.set("customerEmail", customerEmailFilter);

    if (fromDateFilter) {
      newSearchParams.set("fromDate", format(fromDateFilter, "yyyy-MM-dd"));
    } else {
      newSearchParams.delete("fromDate");
    }

    if (toDateFilter) {
      newSearchParams.set("toDate", format(toDateFilter, "yyyy-MM-dd"));
    } else {
      newSearchParams.delete("toDate");
    }

    // Reset pagination
    newSearchParams.set("page", "0");
    newSearchParams.set("pageSize", pageSize.toString());
    newSearchParams.set("limit", pageSize.toString());
    newSearchParams.set("offset", "0");

    setSearchParams(newSearchParams);
  };

  // Reset filters
  const handleResetFilters = () => {
    // If hotelId is provided from URL, keep that filter
    setHotelFilter(hotelId || "all");
    setCustomerNameFilter("");
    setCustomerEmailFilter("");
    setFromDateFilter(null);
    setToDateFilter(null);

    const newSearchParams = new URLSearchParams(searchParams);

    // Reset all filter parameters in URL
    newSearchParams.set("hotel", hotelId || "all");
    newSearchParams.delete("customerName");
    newSearchParams.delete("customerEmail");
    newSearchParams.delete("fromDate");
    newSearchParams.delete("toDate");

    // Reset pagination
    newSearchParams.set("page", "0");
    newSearchParams.set("pageSize", pageSize.toString());
    newSearchParams.set("limit", pageSize.toString());
    newSearchParams.set("offset", "0");

    setSearchParams(newSearchParams);
  };

  // View booking details
  const handleViewBooking = (bookingId: string) => {
    navigate(`/hotel-management/bookings/${bookingId}`);
  };

  // Create new booking
  const handleCreateBooking = () => {
    navigate("/hotel-management/bookings/create");
  };

  // Copy booking ID to clipboard
  const handleCopyBookingId = (bookingId: string) => {
    navigator.clipboard.writeText(bookingId);
    toast.success("Copied", {
      description: "Booking ID copied to clipboard",
    });
  };

  // Calculate duration in nights
  const calculateDuration = (checkInDate: string, checkOutDate: string) => {
    try {
      const checkIn = new Date(checkInDate);
      const checkOut = new Date(checkOutDate);

      if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
        return 0;
      }

      const diffTime = checkOut.getTime() - checkIn.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return Math.max(0, diffDays);
    } catch (error) {
      console.error("Error calculating duration:", error);
      return 0;
    }
  };

  // Format booking dates range
  const formatBookingDates = (checkInDate: string, checkOutDate: string) => {
    try {
      const checkIn = new Date(checkInDate);
      const checkOut = new Date(checkOutDate);

      if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
        return "Invalid dates";
      }

      return `${format(checkIn, "MMM dd")} → ${format(checkOut, "MMM dd")}`;
    } catch (error) {
      console.error("Error formatting booking dates:", error);
      return "Invalid dates";
    }
  };

  return (
    <div className="">
      <Toaster />
      <div className="flex justify-between items-center mb-6">
        <Heading>Bookings</Heading>
        <div className="flex gap-2">
          <Button
            variant="secondary"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
            <ChevronDown
              className={`h-4 w-4 transition-transform duration-200 ease-in-out ${
                showFilters ? "rotate-180" : "rotate-0"
              }`}
            />
          </Button>
          {hasPermission("carts:view") && (
            <Button
              variant="secondary"
              onClick={() => navigate("/hotel-management/carts")}
            >
              View Pending Carts
            </Button>
          )}
          {hasPermission("bookings:create") && (
            <Button onClick={handleCreateBooking}>Create Booking</Button>
          )}
        </div>
      </div>

      {/* Collapsible Filters */}
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out mb-6 ${
          showFilters ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <div className="bg-card p-6 rounded-md shadow-sm border border-border transform transition-transform duration-300 ease-in-out">
          <div className="space-y-4">
            {/* Hotel - Full Width */}
            <div>
              <Text className="text-xs font-medium text-muted-foreground mb-1">
                HOTEL
              </Text>
              <Select
                value={hotelFilter}
                onValueChange={setHotelFilter}
                disabled={!!hotelId}
              >
                <Select.Trigger className={hotelId ? "bg-muted" : ""}>
                  <Select.Value placeholder="All Hotels" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="all">All Hotels</Select.Item>
                  {hotels.map((hotel) => (
                    <Select.Item key={hotel.id} value={hotel.id}>
                      {hotel.name}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>

            {/* Customer Name and Email - Same Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Text className="text-xs font-medium text-muted-foreground mb-1">
                  CUSTOMER NAME
                </Text>
                <Input
                  placeholder="Search by name..."
                  value={customerNameFilter}
                  onChange={(e) => setCustomerNameFilter(e.target.value)}
                />
              </div>
              <div>
                <Text className="text-xs font-medium text-muted-foreground mb-1">
                  CUSTOMER EMAIL
                </Text>
                <Input
                  placeholder="Search by email..."
                  value={customerEmailFilter}
                  onChange={(e) => setCustomerEmailFilter(e.target.value)}
                />
              </div>
            </div>

            {/* Check-in and Check-out - Same Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Text className="text-xs font-medium text-muted-foreground mb-1">
                  CHECK-IN DATE
                </Text>
                <DatePicker
                  value={fromDateFilter}
                  onChange={setFromDateFilter}
                />
              </div>
              <div>
                <Text className="text-xs font-medium text-muted-foreground mb-1">
                  CHECK-OUT DATE
                </Text>
                <DatePicker value={toDateFilter} onChange={setToDateFilter} />
              </div>
            </div>

            {/* Actions Row - Left aligned buttons, Right aligned counts */}
            <div className="flex justify-between items-center pt-4">
              <div className="flex gap-2">
                <Button onClick={handleApplyFilters}>Apply Filters</Button>
                <Button variant="secondary" onClick={handleResetFilters}>
                  Reset All
                </Button>
              </div>

              <div className="text-xs text-muted-foreground space-y-1 text-right">
                <div>
                  Total Results:{" "}
                  <span className="font-medium">{totalCount}</span>
                </div>
                <div>
                  Showing:{" "}
                  <span className="font-medium">{bookings.length}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bookings Table */}
      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Spinner size="medium" />
          <div className="ml-4 text-muted-foreground">Loading bookings...</div>
        </div>
      ) : (
        <>
          <div className="bg-card rounded-md shadow-sm border border-border border-t-0">
            <div className="overflow-x-auto">
              <Table>
                <Table.Header>
                  <Table.Row>
                    <Table.HeaderCell>Booking ID</Table.HeaderCell>
                    <Table.HeaderCell>Customer</Table.HeaderCell>
                    <Table.HeaderCell>Hotel</Table.HeaderCell>
                    <Table.HeaderCell>Room Type</Table.HeaderCell>
                    <Table.HeaderCell>Duration</Table.HeaderCell>
                    <Table.HeaderCell>Booking Dates</Table.HeaderCell>
                    <Table.HeaderCell>Status</Table.HeaderCell>
                    <Table.HeaderCell>Payment Status</Table.HeaderCell>
                    <Table.HeaderCell>Total</Table.HeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {bookings.length === 0 ? (
                    <Table.Row>
                      <Table.Cell colSpan={8} className="text-center py-4">
                        No bookings found
                      </Table.Cell>
                    </Table.Row>
                  ) : (
                    bookings.map((booking) => (
                      <Table.Row
                        key={booking.order_id}
                        className="cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => handleViewBooking(booking.order_id)}
                      >
                        <Table.Cell className="font-medium">
                          <div className="flex items-center gap-2">
                            <span className="truncate max-w-[80px]">
                              {booking.order_id}
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCopyBookingId(booking.order_id);
                              }}
                              className="p-1 hover:bg-muted rounded transition-colors"
                              title="Copy booking ID"
                            >
                              <Copy className="h-3 w-3 text-muted-foreground" />
                            </button>
                          </div>
                        </Table.Cell>
                        <Table.Cell>
                          <div className="flex items-center gap-3">
                            <div className="min-w-0 flex-1">
                              <div className="font-medium text-sm truncate">
                                {booking.metadata?.guest_name ||
                                  booking.guest_name ||
                                  "Guest"}
                              </div>
                              <div className="text-xs text-muted-foreground truncate">
                                {booking.guest_email || "No email"}
                              </div>
                            </div>
                          </div>
                        </Table.Cell>
                        <Table.Cell>
                          {booking.metadata?.hotel_name ||
                            (booking.metadata?.hotel_id
                              ? hotels.find(
                                  (h) => h.id === booking.metadata?.hotel_id
                                )?.name
                              : "Not specified")}
                        </Table.Cell>
                        <Table.Cell className="text-sm">
                          {booking.metadata?.room_type ||
                            booking.room_type ||
                            "Standard"}
                        </Table.Cell>
                        <Table.Cell>
                          {(() => {
                            const checkIn =
                              booking.metadata?.check_in_date ||
                              booking.check_in_date;
                            const checkOut =
                              booking.metadata?.check_out_date ||
                              booking.check_out_date;
                            const nights = calculateDuration(checkIn, checkOut);
                            return nights > 0 ? (
                              <Badge
                                color="blue"
                                size="small"
                                className="truncate max-w-[80px]  text-xs"
                              >
                                {nights} {nights === 1 ? "Night" : "Nights"}
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm">
                                -
                              </span>
                            );
                          })()}
                        </Table.Cell>
                        <Table.Cell>
                          <div className="text-sm max-w-[120px]">
                            {formatBookingDates(
                              booking.metadata?.check_in_date ||
                                booking.check_in_date,
                              booking.metadata?.check_out_date ||
                                booking.check_out_date
                            )}
                          </div>
                        </Table.Cell>
                        <Table.Cell>
                          {booking.status === "pending" ? (
                            booking.metadata?.room_id ? (
                              <Badge color="green" className="text-xs">
                                Booked{" "}
                              </Badge>
                            ) : (
                              <Badge color="orange">Reserved </Badge>
                            )
                          ) : (
                            <Badge
                              color={statusColors[booking.status] || "gray"}
                              className="text-xs"
                            >
                              {booking.status}
                            </Badge>
                          )}
                        </Table.Cell>
                        <Table.Cell>
                          {(() => {
                            const paymentStatus =
                              booking.metadata?.payment_status ||
                              booking.payment_status ||
                              "pending";
                            const paymentStatusColors: Record<string, any> = {
                              pending: "orange",
                              paid: "green",
                              failed: "red",
                              refunded: "grey",
                              partially_paid: "blue",
                              cancelled: "grey",
                            };

                            return (
                              <Badge
                                color={
                                  paymentStatusColors[paymentStatus] || "gray"
                                }
                                className="text-xs"
                              >
                                {paymentStatus
                                  .replace(/_/g, " ")
                                  .replace(/\b\w/g, (l: string) =>
                                    l.toUpperCase()
                                  )}
                              </Badge>
                            );
                          })()}
                        </Table.Cell>
                        <Table.Cell>
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              booking.metadata?.currency_code ||
                              booking.currency_code ||
                              "USD",
                          }).format(
                            (booking.metadata?.total_amount ||
                              booking.total_amount ||
                              0) +
                              (booking.metadata?.add_on_total_amount || 0) / 100
                          )}
                        </Table.Cell>
                      </Table.Row>
                    ))
                  )}
                </Table.Body>
              </Table>
            </div>
          </div>

          {/* Pagination Controls */}
          <div className="mt-4 flex items-center justify-between">
            {/* Page Size Selector */}
            <div className="flex items-center gap-2">
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => {
                  const newPageSize = parseInt(value);
                  updatePageSize(newPageSize);
                }}
              >
                <Select.Trigger className="w-20">
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="10">10</Select.Item>
                  <Select.Item value="25">25</Select.Item>
                  <Select.Item value="50">50</Select.Item>
                  <Select.Item value="100">100</Select.Item>
                </Select.Content>
              </Select>
            </div>

            {/* Pagination */}
            <Table.Pagination
              count={totalCount}
              pageSize={pageSize}
              pageIndex={currentPage}
              pageCount={pageCount}
              canPreviousPage={canPreviousPage}
              canNextPage={canNextPage}
              previousPage={() => {
                updatePage(currentPage - 1);
              }}
              nextPage={() => {
                updatePage(currentPage + 1);
              }}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default BookingList;
