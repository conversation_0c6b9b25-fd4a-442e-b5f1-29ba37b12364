import { useState, useEffect } from "react";
import {
  Input,
  Button,
  Select,
  DatePicker,
  Textarea,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { <PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format, differenceInDays } from "date-fns";
import { useNavigate } from "react-router-dom";
import Spinner from "../shared/spinner";
import AddOnServiceSelector from "./add-on-service-selector";

// Define types for add-on items and custom line items
interface AddOnItem {
  id: string;
  name: string;
  adult_quantity: number;
  child_quantity: number;
  adult_price: number;
  child_price?: number;
  total_price: number;
  currency_code: string;
  service_id: string;
}

interface CustomLineItem {
  name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency_code: string;
}

// Define add-on item schema
const addOnItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  adult_quantity: z.number().default(0),
  child_quantity: z.number().default(0),
  adult_price: z.number(),
  child_price: z.number().optional(),
  total_price: z.number(),
  currency_code: z.string().default("USD"),
  service_id: z.string(),
});

// Define custom line item schema
const customLineItemSchema = z.object({
  name: z.string().min(1, "Name is required"),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  unit_price: z.number().min(0, "Price must be a positive number"),
  total_price: z.number(),
  currency_code: z.string().default("USD"),
});

// Child age schema
const childAgeSchema = z.object({
  age: z
    .union([z.number(), z.string()])
    .transform((val) => (typeof val === "string" ? parseInt(val, 10) : val)),
});

// Form schema
const bookingFormSchema = z.object({
  hotel_id: z.string().min(1, "Hotel is required"),
  room_config_id: z.string().min(1, "Room configuration is required"),
  room_type: z.string().optional(), // Room type from the room configuration
  check_in_date: z.date(),
  check_out_date: z.date(),
  check_in_time: z.string().default("12:00"),
  check_out_time: z.string().default("12:00"),
  guest_name: z.string().min(1, "Guest name is required"),
  guest_email: z.string().email("Invalid email address"),
  guest_phone: z.string().optional(),
  adults: z.number().min(1, "At least 1 adult is required"),
  children: z.number().default(0),
  infants: z.number().default(0),
  extra_beds: z.number().min(0, "Extra beds must be non-negative").default(0),
  child_ages: z.array(childAgeSchema).default([]),
  number_of_rooms: z.number().min(1, "At least 1 room is required"),
  board_type: z.string().default("none"),
  total_amount: z.number().min(0, "Total amount must be a positive number"),
  currency_code: z.string().default("USD"),
  region_id: z.string().default("reg_01JP9R0NP6B5DXGDYHFSSW0FK1"),
  special_requests: z.string().optional(),
  notes: z.string().optional(),
  add_ons: z.array(addOnItemSchema).default([]),
  custom_line_items: z.array(customLineItemSchema).default([]),
  metadata: z.record(z.any()).optional(),
});

const BookingForm = ({ bookingId = null, isEdit = false }) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hotels, setHotels] = useState<any[]>([]);
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false);
  const [availabilityResult, setAvailabilityResult] = useState<any>(null);

  // State for room configurations and available rooms
  const [roomConfigs, setRoomConfigs] = useState<any[]>([]);
  const [availableRooms, setAvailableRooms] = useState<any[]>([]);
  const [availableRoomConfigs, setAvailableRoomConfigs] = useState<any[]>([]);
  const [selectedRoomWithTax, setSelectedRoomWithTax] = useState<any>(null);

  // State for customer search
  const [customerSearchQuery, setCustomerSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Initialize form
  const form = useForm({
    resolver: zodResolver(bookingFormSchema),
    defaultValues: {
      hotel_id: "",
      room_config_id: "",
      room_type: "",
      check_in_date: new Date(),
      check_out_date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      check_in_time: "12:00",
      check_out_time: "12:00",
      guest_name: "",
      guest_email: "",
      guest_phone: "",
      adults: 2,
      children: 0,
      infants: 0,
      extra_beds: 0,
      child_ages: [],
      number_of_rooms: 1,
      board_type: "none",
      total_amount: 0,
      currency_code: "USD",
      region_id: "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
      special_requests: "",
      notes: "",
      add_ons: [],
      custom_line_items: [],
      metadata: {
        travelers: {
          adults: [],
          children: [],
          infants: [],
        },
      },
    },
  });

  // Define types for travelers
  type Traveler = {
    name: string;
    age?: number;
  };

  type TravelersMetadata = {
    adults: Traveler[];
    children: Traveler[];
    infants: Traveler[];
  };

  // Search for customers
  const searchCustomers = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const response = await fetch(
        `/admin/customers?q=${encodeURIComponent(query)}&limit=5`
      );

      if (!response.ok) {
        throw new Error("Failed to search customers");
      }

      const data = await response.json();
      setSearchResults(data.customers || []);
    } catch (error) {
      console.error("Error searching customers:", error);
      toast.error("Error", {
        description: "Failed to search customers",
      });
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Select a customer from search results
  const selectCustomer = (customer: any) => {
    form.setValue("guest_name", `${customer.first_name} ${customer.last_name}`);
    form.setValue("guest_email", customer.email);
    form.setValue("guest_phone", customer.phone || "");

    // Store customer ID in metadata
    const metadataValue = form.getValues("metadata") || {};
    const updatedMetadata = {
      ...metadataValue,
      customer_id: customer.id,
    };

    form.setValue("metadata", updatedMetadata as any);

    // Clear search results
    setSearchResults([]);
    setCustomerSearchQuery("");
  };

  // Helper function to update travelers in metadata
  const updateTravelerMetadata = (
    category: "adults" | "children" | "infants",
    index: number,
    data: Partial<Traveler>
  ) => {
    const metadataValue = form.getValues("metadata") || {};
    const travelers =
      metadataValue.travelers ||
      ({ adults: [], children: [], infants: [] } as TravelersMetadata);

    // Create a copy of the array
    const categoryArray = [...(travelers[category] || [])] as Traveler[];

    // Ensure the array has enough elements
    while (categoryArray.length <= index) {
      categoryArray.push(
        category === "adults" ? { name: "" } : { name: "", age: 0 }
      );
    }

    // Update the traveler data
    categoryArray[index] = {
      ...categoryArray[index],
      ...data,
    };

    // Update the metadata
    form.setValue("metadata", {
      ...metadataValue,
      travelers: {
        ...travelers,
        [category]: categoryArray,
      },
    });

    // If this is a child, also update the child_ages array
    if (category === "children" && "age" in data) {
      const childAges = (form.getValues("child_ages") as any[]) || [];
      const newChildAges = [...childAges];

      // Ensure the array has enough elements
      while (newChildAges.length <= index) {
        newChildAges.push({ age: 10 }); // Default age
      }

      // Update the specific child's age
      newChildAges[index] = { age: data.age as number };

      // Update the form
      form.setValue("child_ages", newChildAges as any);
    }
  };

  // Watch form values for dependent fields
  const watchHotelId = form.watch("hotel_id");
  const watchCheckInDate = form.watch("check_in_date");
  const watchCheckOutDate = form.watch("check_out_date");
  const watchRoomConfigId = form.watch("room_config_id");
  const watchBoardType = form.watch("board_type");
  const watchAdults = form.watch("adults");
  const watchChildren = form.watch("children");
  const watchInfants = form.watch("infants");
  const watchNumberOfRooms = form.watch("number_of_rooms");

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      const response = await fetch("/admin/hotel-management/hotels");

      if (!response.ok) {
        throw new Error("Failed to fetch hotels");
      }

      const data = await response.json();
      setHotels(data.hotels || []);
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Error", {
        description: "Failed to fetch hotels",
      });
    }
  };

  // Fetch room configurations for selected hotel
  const fetchRoomConfigs = async (hotelId: string) => {
    try {
      if (!hotelId) return;

      const response = await fetch(
        `/admin/direct-room-configs?hotel_id=${hotelId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch room configurations");
      }

      const data = await response.json();
      console.log("Room configs response:", data);
      setRoomConfigs(data.roomConfigs || []);

      // Reset room_type when room configurations change
      form.setValue("room_type", "");
    } catch (error) {
      console.error("Error fetching room configurations:", error);
      toast.error("Error", {
        description: "Failed to fetch room configurations",
      });
    }
  };

  // This function has been removed as we no longer need to fetch available rooms

  // Fetch booking details if editing
  const fetchBookingDetails = async () => {
    try {
      setIsLoading(true);

      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch booking details");
      }

      const data = await response.json();
      const booking = data.booking;

      // Populate form with booking data
      form.reset({
        hotel_id: booking.hotel_id,
        room_config_id: booking.room_config_id || booking.product_id || "",
        room_type: booking.room_type || "",
        check_in_date: new Date(booking.check_in_date),
        check_out_date: new Date(booking.check_out_date),
        check_in_time: booking.check_in_time,
        check_out_time: booking.check_out_time,
        guest_name: booking.guest_name,
        guest_email: booking.guest_email,
        guest_phone: booking.guest_phone || "",
        adults: booking.adults || 2,
        children: booking.children || 0,
        infants: booking.infants || 0,
        number_of_rooms:
          booking.number_of_rooms || booking.metadata?.number_of_rooms || 1,
        board_type: booking.board_type || "none",
        total_amount: booking.total_amount,
        currency_code: booking.currency_code,
        region_id: booking.region_id || "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
        special_requests: booking.special_requests || "",
        notes: booking.notes || "",
      });

      // Fetch room configurations for the selected hotel
      await fetchRoomConfigs(booking.hotel_id);
    } catch (error) {
      console.error("Error fetching booking details:", error);
      toast.error("Error", {
        description: "Failed to fetch booking details",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchHotels();

    if (isEdit && bookingId) {
      fetchBookingDetails();
    }
  }, [isEdit, bookingId]);

  // Fetch room configurations when hotel changes
  useEffect(() => {
    if (watchHotelId) {
      fetchRoomConfigs(watchHotelId);
    } else {
      setRoomConfigs([]);
    }
  }, [watchHotelId]);

  // We no longer need to fetch available rooms

  // We've removed the automatic availability check
  // Now availability is only checked when the user clicks the "Check Availability" button

  // Update total amount when number of rooms changes
  useEffect(() => {
    // Only update if we have a selected room configuration and a total amount
    if (
      watchRoomConfigId &&
      form.getValues("total_amount") > 0 &&
      availabilityResult
    ) {
      const selectedConfig = availabilityResult.available_rooms?.find(
        (config: any) => config.id === watchRoomConfigId
      );

      if (selectedConfig && selectedConfig.available) {
        // Get the current total amount per room
        const currentTotal = form.getValues("total_amount");
        const currentRooms = form.getValues("number_of_rooms") || 1;

        // Calculate the amount per room
        const amountPerRoom =
          currentTotal / (watchNumberOfRooms > 0 ? watchNumberOfRooms : 1);

        // Update the total amount based on the new number of rooms
        const newTotal = amountPerRoom * watchNumberOfRooms;

        // Set the new total amount
        form.setValue("total_amount", newTotal);
      }
    }
  }, [watchNumberOfRooms, watchRoomConfigId]);

  // Update travelers metadata when the number of adults, children, or infants changes
  useEffect(() => {
    const metadataValue = form.getValues("metadata") || {};
    const travelers =
      metadataValue.travelers ||
      ({ adults: [], children: [], infants: [] } as TravelersMetadata);

    // Adjust adults array
    let adults = [...(travelers.adults || [])] as Traveler[];
    while (adults.length < watchAdults) {
      adults.push({ name: "" });
    }
    if (adults.length > watchAdults) {
      adults = adults.slice(0, watchAdults);
    }

    // Adjust children array
    let children = [...(travelers.children || [])] as Traveler[];
    while (children.length < watchChildren) {
      children.push({ name: "", age: 10 });
    }
    if (children.length > watchChildren) {
      children = children.slice(0, watchChildren);
    }

    // Make sure child_ages array is synchronized with children array
    const childAges = (form.getValues("child_ages") as any[]) || [];
    let newChildAges = [...childAges];

    // Adjust the array size based on the number of children
    while (newChildAges.length < watchChildren) {
      newChildAges.push({ age: 10 }); // Default age of 10
    }
    if (newChildAges.length > watchChildren) {
      newChildAges = newChildAges.slice(0, watchChildren);
    }

    // Synchronize ages between children and child_ages arrays
    for (let i = 0; i < children.length; i++) {
      if (newChildAges[i]) {
        children[i].age = newChildAges[i].age;
      } else if (children[i].age) {
        newChildAges[i] = { age: children[i].age };
      }
    }

    // Update child_ages in the form
    form.setValue("child_ages", newChildAges as any);

    // Child ages are now handled directly in the children input onChange

    // Adjust infants array
    let infants = [...(travelers.infants || [])] as Traveler[];
    while (infants.length < watchInfants) {
      infants.push({ name: "", age: 0 });
    }
    if (infants.length > watchInfants) {
      infants = infants.slice(0, watchInfants);
    }

    // Update metadata
    const updatedTravelers: TravelersMetadata = {
      adults,
      children,
      infants,
    };

    // Need to cast to any to avoid TypeScript errors with the metadata structure
    form.setValue("metadata", {
      ...metadataValue,
      travelers: updatedTravelers,
    } as any);
  }, [watchAdults, watchChildren, watchInfants]);

  // Check hotel availability for all room configurations
  const checkAvailability = async () => {
    try {
      setIsCheckingAvailability(true);
      setAvailabilityResult(null);

      // Get form values for occupancy
      const adults = form.getValues("adults") || 2;
      const children = form.getValues("children") || 0;
      const infants = form.getValues("infants") || 0;
      const extraBeds = form.getValues("extra_beds") || 0;
      const childAges = form.getValues("child_ages") || [];
      const numberOfRooms = form.getValues("number_of_rooms") || 1;
      const boardType = form.getValues("board_type") || "none";

      if (!watchHotelId || !watchCheckInDate || !watchCheckOutDate) {
        toast.error("Error", {
          description: "Please select a hotel and dates to check availability",
        });
        return;
      }

      const formattedCheckIn = format(watchCheckInDate, "yyyy-MM-dd");
      const formattedCheckOut = format(watchCheckOutDate, "yyyy-MM-dd");

      // Prepare child_ages parameter
      const childAgesParam = encodeURIComponent(JSON.stringify(childAges));

      // Use the hotel availability API to get all available room configurations with prices
      const response = await fetch(
        `/admin/hotel-management/hotels/${watchHotelId}/availability?check_in=${formattedCheckIn}&check_out=${formattedCheckOut}&adults=${adults}&children=${children}&infants=${infants}&extra_beds=${extraBeds}&board_type=${boardType}&include_unavailable=true&child_ages=${childAgesParam}`
      );

      if (!response.ok) {
        throw new Error("Failed to check availability");
      }

      const data = await response.json();
      console.log("Availability data:", data);

      // Set the availability result
      setAvailabilityResult(data);

      // Update available room configurations
      setAvailableRoomConfigs(data.available_rooms || []);

      // If a room configuration is already selected, check if it's available
      if (watchRoomConfigId) {
        const selectedConfig = data.available_rooms?.find(
          (config: any) => config.id === watchRoomConfigId
        );

        if (selectedConfig) {
          // If the selected room configuration is available, update the price
          if (selectedConfig.available) {
            // Check if the requested number of rooms is available
            if (
              selectedConfig.available_rooms &&
              selectedConfig.available_rooms < numberOfRooms
            ) {
              toast.warning("Room Availability", {
                description:
                  selectedConfig.available_rooms === 0
                    ? "This room is fully booked for the selected dates."
                    : `Only ${selectedConfig.available_rooms} room(s) available. Adjusting your selection.`,
              });
              // Adjust the number of rooms to match availability
              form.setValue(
                "number_of_rooms",
                selectedConfig.available_rooms > 0
                  ? selectedConfig.available_rooms
                  : 1
              );
            } else {
              toast.success("Available", {
                description: `${
                  selectedConfig.title || selectedConfig.name
                } is available for the selected dates`,
              });
            }

            // Update the price if available
            if (selectedConfig.price) {
              // Get the selected board type
              const boardType = form.getValues("board_type") || "none";

              // Handle the new price structure with meal plans
              if (typeof selectedConfig.price === "object") {
                if (
                  selectedConfig.price.meal_plans &&
                  selectedConfig.price.meal_plans[boardType]
                ) {
                  // Use the total price for the selected meal plan
                  const baseAmount =
                    selectedConfig.price.meal_plans[boardType].total_amount;
                  // Multiply by number of rooms
                  form.setValue("total_amount", baseAmount * numberOfRooms);
                } else if (selectedConfig.price.total_amount) {
                  // Fallback to the default total price
                  const baseAmount = selectedConfig.price.total_amount;
                  form.setValue("total_amount", baseAmount * numberOfRooms);
                } else if (selectedConfig.price.amount) {
                  // If no total_amount, use amount (which might be per night) multiplied by nights
                  const nights =
                    data.nights ||
                    differenceInDays(watchCheckOutDate, watchCheckInDate);
                  const baseAmount = selectedConfig.price.amount * nights;
                  form.setValue("total_amount", baseAmount * numberOfRooms);
                }
              } else if (typeof selectedConfig.price === "number") {
                const baseAmount = selectedConfig.price;
                form.setValue("total_amount", baseAmount * numberOfRooms);
              }
            }

            // No need to fetch available rooms anymore
          } else {
            toast.error("Not Available", {
              description: `${
                selectedConfig.title || selectedConfig.name
              } is not available for the selected dates`,
            });
          }
        }
      } else {
        // If no room configuration is selected, check if any are available
        const availableConfigs = data.available_rooms?.filter(
          (config: any) => config.available
        );

        if (availableConfigs && availableConfigs.length > 0) {
          toast.success("Rooms Available", {
            description: `${availableConfigs.length} room types are available for the selected dates`,
          });
        } else {
          toast.error("No Availability", {
            description: "No rooms are available for the selected dates",
          });
        }
      }
    } catch (error) {
      console.error("Error checking availability:", error);
      toast.error("Error", {
        description: "Failed to check availability",
      });
    } finally {
      setIsCheckingAvailability(false);
    }
  };

  // Form submission
  const onSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      // Check if we have availability data and validate number of rooms
      if (availabilityResult && values.room_config_id) {
        const selectedConfig = availabilityResult.available_rooms?.find(
          (config: any) => config.id === values.room_config_id
        );

        if (selectedConfig) {
          // If the room is not available, show error and stop submission
          if (!selectedConfig.available) {
            toast.error("Room Not Available", {
              description:
                "The selected room is not available for the chosen dates. Please select another room or change dates.",
            });
            setIsSubmitting(false);
            return;
          }

          // If the requested number of rooms exceeds availability, show error and stop submission
          if (
            selectedConfig.available_rooms &&
            values.number_of_rooms > selectedConfig.available_rooms
          ) {
            toast.error("Room Booked Out", {
              description:
                selectedConfig.available_rooms === 0
                  ? "This room is fully booked for the selected dates."
                  : `Only ${selectedConfig.available_rooms} room(s) available. Please adjust your selection.`,
            });
            setIsSubmitting(false);
            return;
          }
        }
      }

      // Format dates and ensure all required fields are present
      const formattedValues = {
        ...values,
        check_in_date: format(values.check_in_date, "yyyy-MM-dd"),
        check_out_date: format(values.check_out_date, "yyyy-MM-dd"),
        // Ensure these required fields are present
        room_config_id: values.room_config_id, // Keep room_config_id
        room_type: values.room_type || "", // Include room_type
        adults: values.adults || 2,
        children: values.children || 0,
        infants: values.infants || 0,
        extra_beds: values.extra_beds || 0,
        number_of_rooms: values.number_of_rooms || 1,
        board_type: values.board_type || "none",
        currency_code: values.currency_code || "USD",
        region_id: values.region_id || "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
        total_amount: values.total_amount || 100, // Default value if not set
      };

      console.log("Formatted values:", formattedValues);

      // Ensure metadata is properly formatted and cast to allow adding properties
      const metadata: Record<string, any> = form.getValues("metadata") || {};

      // Get add-ons and custom line items
      const addOns = (form.getValues("add_ons") || []) as AddOnItem[];
      const customLineItems = (form.getValues("custom_line_items") ||
        []) as CustomLineItem[];

      // Calculate the room-only total amount (without add-ons)
      const roomTotal =
        formattedValues.total_amount -
        addOns.reduce((sum, item) => sum + item.total_price / 100, 0) -
        customLineItems.reduce((sum, item) => sum + item.total_price, 0);

      // Prepare line items for the order
      const lineItems = [
        // Room booking as the main line item
        {
          title: `Room booking: ${
            formattedValues.room_type || "Standard Room"
          }`,
          quantity: formattedValues.number_of_rooms,
          unit_price: roomTotal / formattedValues.number_of_rooms,
          variant_id: formattedValues.room_id || formattedValues.room_config_id,
          metadata: {
            room_id: formattedValues.room_id,
            room_config_id: formattedValues.room_config_id,
            check_in_date: formattedValues.check_in_date,
            check_out_date: formattedValues.check_out_date,
            number_of_rooms: formattedValues.number_of_rooms,
            item_type: "room",
          },
        },

        // Add-on services as line items
        ...addOns.map((addOn) => ({
          title: `Add-on: ${addOn.name}`,
          quantity: addOn.adult_quantity + addOn.child_quantity,
          unit_price:
            addOn.total_price /
            100 /
            (addOn.adult_quantity + addOn.child_quantity || 1),
          metadata: {
            service_id: addOn.service_id,
            adult_quantity: addOn.adult_quantity,
            child_quantity: addOn.child_quantity,
            adult_price: addOn.adult_price / 100,
            child_price: addOn.child_price ? addOn.child_price / 100 : null,
            item_type: "add_on",
          },
        })),

        // Custom line items
        ...customLineItems.map((item) => ({
          title: item.name,
          quantity: item.quantity,
          unit_price: item.unit_price,
          metadata: {
            item_type: "custom",
          },
        })),
      ];

      // Add line items to metadata
      metadata.line_items = lineItems;

      // Add add-ons to metadata for reference
      if (addOns.length > 0) {
        metadata.add_ons = addOns.map((addOn) => ({
          ...addOn,
          adult_price: addOn.adult_price / 100,
          child_price: addOn.child_price ? addOn.child_price / 100 : null,
          total_price: addOn.total_price / 100,
        }));
      }

      // Add custom line items to metadata for reference
      if (customLineItems.length > 0) {
        metadata.custom_line_items = customLineItems;
      }

      // Get child ages from the form
      const childAges = form.getValues("child_ages") || [];

      // Booking data includes formatted values, metadata, child_ages, and line items
      const bookingData = {
        ...formattedValues,
        metadata,
        line_items: lineItems,
        child_ages: childAges,
      };

      // Determine API endpoint and method
      const endpoint = isEdit
        ? `/admin/hotel-management/bookings/${bookingId}`
        : "/admin/hotel-management/bookings";
      const method = isEdit ? "PUT" : "POST";

      console.log(
        `Sending ${method} request to ${endpoint} with data:`,
        bookingData
      );

      const response = await fetch(endpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(bookingData),
      });

      console.log(`Response status: ${response.status}`);

      // Log response headers
      const headers: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });
      console.log("Response headers:", headers);

      // Handle response
      if (!response.ok) {
        // Try to get error details from the response
        let errorMessage = `Failed to ${isEdit ? "update" : "create"} booking`;

        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
          console.error("API error details:", errorData);

          // If missing fields, show them in the UI
          if (errorMessage.includes("Missing required fields")) {
            const missingFields = errorMessage
              .replace("Missing required fields: ", "")
              .split(", ");
            missingFields.forEach((field) => {
              form.setError(field as any, {
                type: "manual",
                message: "This field is required",
              });
            });
          }
        } catch (parseError) {
          console.error("Could not parse error response:", parseError);
        }

        // Show error toast
        toast.error("Error", {
          description: errorMessage,
        });

        // Don't throw error, just return to keep the form open
        setIsSubmitting(false);
        return;
      }

      const data = await response.json();
      const newBookingId = data.order.id;

      toast.success("Success", {
        description: `Booking ${isEdit ? "updated" : "created"} successfully`,
      });

      // Navigate to booking details
      navigate(`/hotel-management/bookings/${newBookingId}`);
    } catch (error) {
      console.error(
        `Error ${isEdit ? "updating" : "creating"} booking:`,
        error
      );
      toast.error("Error", {
        description: `Failed to ${isEdit ? "update" : "create"} booking`,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Cancel form
  const handleCancel = () => {
    if (isEdit && bookingId) {
      navigate(`/hotel-management/bookings/${bookingId}`);
    } else {
      navigate("/hotel-management/bookings");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="medium" />
        <div className="ml-4">Loading booking details...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Toaster />

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Hotel and Room Selection */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm space-y-4">
            <div className="mb-4">
              <h3 className="text-lg font-medium">Hotel and Room</h3>
              <p className="text-sm text-gray-500">
                Select the hotel and room for this booking
              </p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Hotel</label>
                  <Controller
                    control={form.control}
                    name="hotel_id"
                    render={({ field }) => (
                      <>
                        <Select
                          disabled={isEdit}
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <Select.Trigger>
                            <Select.Value placeholder="Select a hotel" />
                          </Select.Trigger>
                          <Select.Content>
                            {hotels.map((hotel) => (
                              <Select.Item key={hotel.id} value={hotel.id}>
                                {hotel.name}
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select>
                        {form.formState.errors.hotel_id && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.hotel_id.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Adults</label>
                    <Controller
                      control={form.control}
                      name="adults"
                      render={({ field }) => (
                        <>
                          <Input
                            type="number"
                            min={1}
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value) || 1)
                            }
                          />
                          {form.formState.errors.adults && (
                            <p className="text-sm text-red-500">
                              {form.formState.errors.adults.message}
                            </p>
                          )}
                        </>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Children</label>
                    <Controller
                      control={form.control}
                      name="children"
                      render={({ field }) => (
                        <>
                          <Input
                            type="number"
                            min={0}
                            {...field}
                            onChange={(e) => {
                              const value = parseInt(e.target.value) || 0;
                              field.onChange(value);

                              // Update child_ages array when number of children changes
                              const currentChildAges =
                                (form.getValues("child_ages") as any[]) || [];
                              let newChildAges = [
                                ...currentChildAges,
                              ] as Array<{ age: number | string }>;

                              // Adjust the array size based on the new number of children
                              if (newChildAges.length < value) {
                                // Add more child ages if needed
                                while (newChildAges.length < value) {
                                  newChildAges.push({ age: 10 }); // Default age of 10
                                }
                              } else if (newChildAges.length > value) {
                                // Remove extra child ages if needed
                                newChildAges = newChildAges.slice(0, value);
                              }

                              // Update the form
                              form.setValue("child_ages", newChildAges as any);
                            }}
                          />
                          {form.formState.errors.children && (
                            <p className="text-sm text-red-500">
                              {form.formState.errors.children.message}
                            </p>
                          )}
                        </>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Infants</label>
                    <Controller
                      control={form.control}
                      name="infants"
                      render={({ field }) => (
                        <>
                          <Input
                            type="number"
                            min={0}
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value) || 0)
                            }
                          />
                          {form.formState.errors.infants && (
                            <p className="text-sm text-red-500">
                              {form.formState.errors.infants.message}
                            </p>
                          )}
                        </>
                      )}
                    />
                  </div>

                  {/* Child Ages Section - Only show if there are children */}
                  {watchChildren > 0 && (
                    <div className="col-span-5 mt-4 p-3 border border-gray-200 rounded-md">
                      <h4 className="font-medium mb-3">Child Ages</h4>
                      <p className="text-sm text-gray-500 mb-3">
                        Please enter the age of each child for accurate pricing
                      </p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {Array.from({ length: watchChildren }).map(
                          (_, index) => (
                            <div
                              key={`child-age-${index}`}
                              className="space-y-1"
                            >
                              <label className="text-xs font-medium">
                                Child {index + 1} Age
                              </label>
                              <Select
                                value={(() => {
                                  const childAges =
                                    (form.getValues("child_ages") as any[]) ||
                                    [];
                                  return String(childAges[index]?.age || 10);
                                })()}
                                onValueChange={(value) => {
                                  const ageValue = parseInt(value);
                                  const childAges = [
                                    ...(form.getValues("child_ages") || []),
                                  ] as Array<{ age: number }>;

                                  // Ensure the array has enough elements
                                  while (childAges.length <= index) {
                                    childAges.push({ age: 10 });
                                  }

                                  // Update the specific child's age
                                  childAges[index] = { age: ageValue };

                                  // Update the form
                                  form.setValue("child_ages", childAges as any);

                                  // Also update the travelers metadata
                                  const metadataValue =
                                    form.getValues("metadata") || {};
                                  const travelers = metadataValue.travelers || {
                                    adults: [],
                                    children: [],
                                    infants: [],
                                  };
                                  const childrenArray = [
                                    ...(travelers.children || []),
                                  ] as Traveler[];

                                  // Ensure the children array has enough elements
                                  while (childrenArray.length <= index) {
                                    childrenArray.push({ name: "", age: 10 });
                                  }

                                  // Update the specific child's age
                                  childrenArray[index] = {
                                    name: childrenArray[index]?.name || "",
                                    age: ageValue,
                                  };

                                  // Update the metadata
                                  const updatedMetadata = {
                                    ...metadataValue,
                                    travelers: {
                                      ...travelers,
                                      children: childrenArray,
                                    },
                                  };
                                  form.setValue(
                                    "metadata",
                                    updatedMetadata as any
                                  );
                                }}
                              >
                                <Select.Trigger className="w-full">
                                  <Select.Value placeholder="Select age" />
                                </Select.Trigger>
                                <Select.Content>
                                  {Array.from(
                                    { length: 16 },
                                    (_, i) => i + 2
                                  ).map((age) => (
                                    <Select.Item key={age} value={String(age)}>
                                      {age} years
                                    </Select.Item>
                                  ))}
                                </Select.Content>
                              </Select>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                  {/* <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Number of Rooms
                    </label>
                    <Controller
                      control={form.control}
                      name="number_of_rooms"
                      render={({ field }) => (
                        <>
                          <Input
                            type="number"
                            min={1}
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value) || 1)
                            }
                          />
                          {form.formState.errors.number_of_rooms && (
                            <p className="text-sm text-red-500">
                              {form.formState.errors.number_of_rooms.message}
                            </p>
                          )}
                        </>
                      )}
                    />
                  </div> */}
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Board Type</label>
                  <Controller
                    control={form.control}
                    name="board_type"
                    render={({ field }) => (
                      <>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <Select.Trigger>
                            <Select.Value placeholder="Select board type" />
                          </Select.Trigger>
                          <Select.Content>
                            <Select.Item value="none">Room Only</Select.Item>
                            <Select.Item value="bb">
                              Bed & Breakfast
                            </Select.Item>
                            <Select.Item value="hb">Half Board</Select.Item>
                            <Select.Item value="fb">Full Board</Select.Item>
                          </Select.Content>
                        </Select>
                        {form.formState.errors.board_type && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.board_type.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Check-in Date</label>
                  <Controller
                    control={form.control}
                    name="check_in_date"
                    render={({ field }) => (
                      <>
                        <DatePicker
                          value={field.value}
                          onChange={field.onChange}
                        />
                        {form.formState.errors.check_in_date && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.check_in_date.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Check-out Date</label>
                  <Controller
                    control={form.control}
                    name="check_out_date"
                    render={({ field }) => (
                      <>
                        <DatePicker
                          value={field.value}
                          onChange={field.onChange}
                        />
                        {form.formState.errors.check_out_date && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.check_out_date.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={checkAvailability}
                  disabled={
                    isCheckingAvailability ||
                    !watchHotelId ||
                    !watchCheckInDate ||
                    !watchCheckOutDate
                  }
                >
                  {isCheckingAvailability
                    ? "Checking..."
                    : "Check Availability"}
                </Button>
              </div>

              {availabilityResult && (
                <div className="mt-4 space-y-4">
                  <div className="p-4 rounded-md bg-blue-50 border border-blue-200">
                    <h4 className="font-medium text-blue-700 mb-2">
                      Availability for{" "}
                      {format(watchCheckInDate, "MMM dd, yyyy")} to{" "}
                      {format(watchCheckOutDate, "MMM dd, yyyy")}
                    </h4>
                    <p className="text-sm text-blue-600">
                      {availabilityResult.nights} night
                      {availabilityResult.nights !== 1 ? "s" : ""} ·{" "}
                      {availabilityResult.adults} adult
                      {availabilityResult.adults !== 1 ? "s" : ""}
                      {availabilityResult.children > 0 &&
                        ` · ${availabilityResult.children} child${
                          availabilityResult.children !== 1 ? "ren" : ""
                        }`}
                      {availabilityResult.infants > 0 &&
                        ` · ${availabilityResult.infants} infant${
                          availabilityResult.infants !== 1 ? "s" : ""
                        }`}
                      {availabilityResult.extra_beds > 0 &&
                        ` · ${availabilityResult.extra_beds} extra bed${
                          availabilityResult.extra_beds !== 1 ? "s" : ""
                        }`}
                    </p>
                  </div>

                  {availabilityResult.available_rooms &&
                  availabilityResult.available_rooms.length > 0 ? (
                    <div className="space-y-3">
                      {availabilityResult.available_rooms.map((room: any) => (
                        <div
                          key={room.id}
                          className={`p-4 rounded-md border ${
                            room.available &&
                            (!room.available_rooms ||
                              room.available_rooms >=
                                form.getValues("number_of_rooms"))
                              ? watchRoomConfigId === room.id
                                ? "bg-green-50 border-green-300"
                                : "bg-white border-gray-200 hover:border-blue-300 cursor-pointer"
                              : "bg-gray-50 border-gray-200 opacity-70"
                          }`}
                          onClick={() => {
                            // Only allow selection if room is available AND has enough rooms
                            if (
                              room.available &&
                              !isEdit &&
                              (!room.available_rooms ||
                                room.available_rooms >=
                                  form.getValues("number_of_rooms"))
                            ) {
                              form.setValue("room_config_id", room.id);
                              form.setValue(
                                "room_type",
                                room.title || room.name || ""
                              );

                              // Store the selected room with tax information
                              setSelectedRoomWithTax(room);

                              // Get the selected board type
                              const boardType =
                                form.getValues("board_type") || "none";

                              // Get number of rooms
                              const numberOfRooms =
                                form.getValues("number_of_rooms") || 1;

                              if (room.price) {
                                // Handle the new price structure with meal plans
                                if (typeof room.price === "object") {
                                  if (
                                    room.price.meal_plans &&
                                    room.price.meal_plans[boardType]
                                  ) {
                                    // Use the total price with tax for the selected meal plan if available
                                    const baseAmount =
                                      room.price.meal_plans[boardType]
                                        .total_amount_with_tax ||
                                      room.price.meal_plans[boardType]
                                        .total_amount;
                                    form.setValue(
                                      "total_amount",
                                      baseAmount * numberOfRooms
                                    );
                                  } else if (room.price.total_amount_with_tax) {
                                    // Use the total price with tax if available
                                    const baseAmount =
                                      room.price.total_amount_with_tax;
                                    form.setValue(
                                      "total_amount",
                                      baseAmount * numberOfRooms
                                    );
                                  } else if (room.price.total_amount) {
                                    // Fallback to the default total price
                                    const baseAmount = room.price.total_amount;
                                    form.setValue(
                                      "total_amount",
                                      baseAmount * numberOfRooms
                                    );
                                  } else if (room.price.amount) {
                                    // If no total_amount, use amount (which might be per night)
                                    const baseAmount =
                                      room.price.amount *
                                      availabilityResult.nights;
                                    form.setValue(
                                      "total_amount",
                                      baseAmount * numberOfRooms
                                    );
                                  }
                                } else if (typeof room.price === "number") {
                                  const baseAmount = room.price;
                                  form.setValue(
                                    "total_amount",
                                    baseAmount * numberOfRooms
                                  );
                                }
                              }
                            } else if (
                              room.available_rooms &&
                              room.available_rooms <
                                form.getValues("number_of_rooms")
                            ) {
                              // Show a message if trying to select a room with insufficient availability
                              toast.error("Cannot Select Room", {
                                description:
                                  "This room doesn't have enough availability for your requested number of rooms.",
                              });
                            }
                          }}
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium text-lg">
                                {room.title || room.name}
                              </h4>
                              <p className="text-sm font-medium text-blue-600">
                                Room Type
                              </p>
                              {room.description && (
                                <p className="text-sm text-gray-600 mt-1">
                                  {room.description}
                                </p>
                              )}
                              {room.room_size && (
                                <p className="text-xs text-gray-500 mt-1">
                                  Room size: {room.room_size} m²
                                </p>
                              )}
                              {room.bed_type && (
                                <p className="text-xs text-gray-500">
                                  Bed type:{" "}
                                  {room.bed_type.charAt(0).toUpperCase() +
                                    room.bed_type.slice(1)}
                                </p>
                              )}
                            </div>
                            <div className="text-right">
                              {/* Only show price details if room is available and has enough rooms */}
                              {room.price &&
                              room.available &&
                              (!room.available_rooms ||
                                room.available_rooms >=
                                  form.getValues("number_of_rooms")) ? (
                                <div>
                                  {(() => {
                                    // Get the selected board type
                                    const boardType = watchBoardType || "none";
                                    const currencyCode =
                                      (typeof room.price === "object" &&
                                        room.price.currency_code) ||
                                      availabilityResult.currency_code ||
                                      "USD";

                                    // Format total price
                                    const formatPrice = (amount: number) => {
                                      return new Intl.NumberFormat("en-US", {
                                        style: "currency",
                                        currency: currencyCode,
                                      }).format(amount);
                                    };

                                    let totalPrice = 0;
                                    let perNightPrice = 0;

                                    if (typeof room.price === "object") {
                                      // Check if we have meal plans and the selected board type
                                      if (
                                        room.price.meal_plans &&
                                        room.price.meal_plans[boardType]
                                      ) {
                                        const mealPlanPrice =
                                          room.price.meal_plans[boardType];
                                        totalPrice =
                                          mealPlanPrice.total_amount ||
                                          mealPlanPrice.amount *
                                            availabilityResult.nights;
                                        perNightPrice =
                                          mealPlanPrice.per_night_amount ||
                                          mealPlanPrice.amount;
                                      } else {
                                        // Fallback to the default price
                                        totalPrice =
                                          room.price.total_amount ||
                                          room.price.amount *
                                            availabilityResult.nights;
                                        perNightPrice =
                                          room.price.per_night_amount ||
                                          room.price.amount;
                                      }
                                    } else if (
                                      typeof room.price === "number" &&
                                      !isNaN(parseFloat(room.price.toString()))
                                    ) {
                                      totalPrice =
                                        parseFloat(room.price.toString()) *
                                        availabilityResult.nights;
                                      perNightPrice = parseFloat(
                                        room.price.toString()
                                      );
                                    }

                                    return (
                                      <>
                                        <div className="font-bold text-lg">
                                          {formatPrice(totalPrice)}
                                        </div>
                                        <div className="text-sm text-gray-600">
                                          {formatPrice(perNightPrice)} per night
                                        </div>
                                      </>
                                    );
                                  })()}
                                </div>
                              ) : (
                                <div className="text-gray-500">
                                  {!room.available ||
                                  (room.available_rooms &&
                                    room.available_rooms <
                                      form.getValues("number_of_rooms"))
                                    ? "Room booked out"
                                    : "Price not available"}
                                </div>
                              )}
                              {/* Only show board type info if room is available */}
                              {room.available &&
                                (!room.available_rooms ||
                                  room.available_rooms >=
                                    form.getValues("number_of_rooms")) && (
                                  <div className="text-xs text-gray-500">
                                    {(() => {
                                      const boardType =
                                        watchBoardType || "none";
                                      const boardTypeText = (() => {
                                        switch (boardType) {
                                          case "none":
                                            return "Room Only";
                                          case "bb":
                                            return "Bed & Breakfast";
                                          case "hb":
                                            return "Half Board";
                                          case "fb":
                                            return "Full Board";
                                          default:
                                            return boardType;
                                        }
                                      })();

                                      return `${
                                        availabilityResult.nights
                                      } night${
                                        availabilityResult.nights !== 1
                                          ? "s"
                                          : ""
                                      } · ${boardTypeText}`;
                                    })()}
                                  </div>
                                )}
                            </div>
                          </div>
                          <div className="mt-3 border-t pt-2 border-gray-100">
                            <div className="flex justify-between items-center mb-2">
                              <div className="text-xs text-gray-500">
                                Max occupancy:
                                {room.max_adults > 0 &&
                                  ` ${room.max_adults} adult${
                                    room.max_adults !== 1 ? "s" : ""
                                  }`}
                                {room.max_children > 0 &&
                                  `, ${room.max_children} child${
                                    room.max_children !== 1 ? "ren" : ""
                                  }`}
                                {room.max_infants > 0 &&
                                  `, ${room.max_infants} infant${
                                    room.max_infants !== 1 ? "s" : ""
                                  }`}
                              </div>
                              <div className="text-xs">
                                {room.available_rooms > 0 ? (
                                  <span
                                    className={`${
                                      room.available_rooms <
                                      form.getValues("number_of_rooms")
                                        ? "text-red-600"
                                        : "text-green-600"
                                    } font-medium`}
                                  >
                                    {room.available_rooms <
                                    form.getValues("number_of_rooms")
                                      ? "Room booked out"
                                      : `${room.available_rooms} room${
                                          room.available_rooms !== 1 ? "s" : ""
                                        } left`}
                                  </span>
                                ) : (
                                  <span className="text-red-600 font-medium">
                                    Room booked out
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex justify-between items-center">
                              <div className="text-sm">
                                {room.available ? (
                                  <span className="text-green-600 font-medium">
                                    Available
                                  </span>
                                ) : (
                                  <span className="text-red-600 font-medium">
                                    Not available
                                  </span>
                                )}
                              </div>
                              {watchRoomConfigId === room.id && (
                                <div className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-sm font-medium">
                                  Selected
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 rounded-md bg-red-50 border border-red-200">
                      <p className="text-red-700">
                        No rooms available for the selected dates
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm space-y-4">
            <div className="mb-4">
              <h3 className="text-lg font-medium">Customer Information</h3>
              <p className="text-sm text-gray-500">
                Search for an existing customer or enter new customer details
              </p>
            </div>
            <div className="space-y-4">
              {/* Customer Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Search Customer</label>
                <div className="flex gap-2">
                  <div className="flex-1">
                    <Input
                      placeholder="Search by name or email"
                      value={customerSearchQuery}
                      onChange={(e) => setCustomerSearchQuery(e.target.value)}
                    />
                  </div>
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => searchCustomers(customerSearchQuery)}
                    disabled={isSearching}
                  >
                    {isSearching ? "Searching..." : "Search"}
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  Search for an existing customer or enter details below to
                  create a new one
                </p>

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <div className="mt-2 border rounded-md overflow-hidden">
                    <div className="bg-gray-50 px-4 py-2 text-sm font-medium">
                      Search Results
                    </div>
                    <div className="divide-y">
                      {searchResults.map((customer) => (
                        <div
                          key={customer.id}
                          className="p-3 hover:bg-gray-50 cursor-pointer flex justify-between items-center"
                          onClick={() => selectCustomer(customer)}
                        >
                          <div>
                            <div className="font-medium">
                              {customer.first_name} {customer.last_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {customer.email}
                            </div>
                          </div>
                          <Button size="small" variant="secondary">
                            Select
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Customer Name</label>
                  <Controller
                    control={form.control}
                    name="guest_name"
                    render={({ field }) => (
                      <>
                        <Input {...field} placeholder="John Doe" />
                        {form.formState.errors.guest_name && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.guest_name.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Customer Email</label>
                  <Controller
                    control={form.control}
                    name="guest_email"
                    render={({ field }) => (
                      <>
                        <Input {...field} placeholder="<EMAIL>" />
                        {form.formState.errors.guest_email && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.guest_email.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Customer Phone</label>
                  <Controller
                    control={form.control}
                    name="guest_phone"
                    render={({ field }) => (
                      <>
                        <Input {...field} placeholder="****** 456 7890" />
                        {form.formState.errors.guest_phone && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.guest_phone.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>

              {/* Travelers Information */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <h4 className="text-md font-medium mb-2">
                  Travelers Information
                </h4>
                <p className="text-sm text-gray-500 mb-4">
                  Enter details for all travelers
                </p>

                {/* Adults */}
                {Array.from({ length: form.getValues("adults") || 0 }).map(
                  (_, index) => (
                    <div
                      key={`adult-${index}`}
                      className="mb-4 p-3 border border-gray-200 rounded-md"
                    >
                      <h5 className="font-medium mb-2">Adult {index + 1}</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="text-sm font-medium">
                            Full Name
                          </label>
                          <Input
                            placeholder="Full Name"
                            onChange={(e) =>
                              updateTravelerMetadata("adults", index, {
                                name: e.target.value,
                              })
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )
                )}

                {/* Children */}
                {Array.from({ length: form.getValues("children") || 0 }).map(
                  (_, index) => (
                    <div
                      key={`child-${index}`}
                      className="mb-4 p-3 border border-gray-200 rounded-md"
                    >
                      <h5 className="font-medium mb-2">Child {index + 1}</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="text-sm font-medium">
                            Full Name
                          </label>
                          <Input
                            placeholder="Full Name"
                            onChange={(e) =>
                              updateTravelerMetadata("children", index, {
                                name: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Age</label>
                          <Input
                            type="number"
                            min="2"
                            max="17"
                            placeholder="Age"
                            onChange={(e) =>
                              updateTravelerMetadata("children", index, {
                                age: parseInt(e.target.value) || 0,
                              })
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )
                )}

                {/* Infants */}
                {Array.from({ length: form.getValues("infants") || 0 }).map(
                  (_, index) => (
                    <div
                      key={`infant-${index}`}
                      className="mb-4 p-3 border border-gray-200 rounded-md"
                    >
                      <h5 className="font-medium mb-2">Infant {index + 1}</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="text-sm font-medium">
                            Full Name
                          </label>
                          <Input
                            placeholder="Full Name"
                            onChange={(e) =>
                              updateTravelerMetadata("infants", index, {
                                name: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">
                            Age (months)
                          </label>
                          <Input
                            type="number"
                            min="0"
                            max="23"
                            placeholder="Age in months"
                            onChange={(e) =>
                              updateTravelerMetadata("infants", index, {
                                age: parseInt(e.target.value) || 0,
                              })
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Special Requests</label>
                <Controller
                  control={form.control}
                  name="special_requests"
                  render={({ field }) => (
                    <>
                      <Textarea
                        {...field}
                        placeholder="Any special requests or requirements"
                      />
                      {form.formState.errors.special_requests && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.special_requests.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Booking Summary */}
          {watchRoomConfigId && (
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Booking Summary</h3>
                <p className="text-sm text-gray-500">
                  Summary of your booking details
                </p>
              </div>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 border border-blue-100 rounded-md">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h4 className="font-medium text-lg">
                        {form.getValues("room_type")}
                      </h4>
                      <div className="mt-1 mb-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Room Type
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-2">
                        <span className="font-medium">Stay dates:</span>{" "}
                        {format(watchCheckInDate, "MMM dd, yyyy")} to{" "}
                        {format(watchCheckOutDate, "MMM dd, yyyy")}
                      </p>

                      {/* Find the selected room to display additional details */}
                      {(() => {
                        if (
                          !availabilityResult ||
                          !availabilityResult.available_rooms
                        )
                          return null;

                        const selectedRoom =
                          availabilityResult.available_rooms.find(
                            (r: any) => r.id === watchRoomConfigId
                          );

                        if (!selectedRoom) return null;

                        return (
                          <>
                            {selectedRoom.room_size && (
                              <p className="text-xs text-gray-500 mt-1">
                                Room size: {selectedRoom.room_size} m²
                              </p>
                            )}
                            {selectedRoom.bed_type && (
                              <p className="text-xs text-gray-500">
                                Bed type:{" "}
                                {selectedRoom.bed_type.charAt(0).toUpperCase() +
                                  selectedRoom.bed_type.slice(1)}
                              </p>
                            )}
                          </>
                        );
                      })()}
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-lg">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: form.getValues("currency_code") || "USD",
                        }).format(form.getValues("total_amount"))}
                      </div>
                      <div className="text-sm text-gray-600">
                        {(() => {
                          const nights = differenceInDays(
                            watchCheckOutDate,
                            watchCheckInDate
                          );
                          const perNightAmount =
                            form.getValues("total_amount") / nights;
                          const formattedPerNight = new Intl.NumberFormat(
                            "en-US",
                            {
                              style: "currency",
                              currency:
                                form.getValues("currency_code") || "USD",
                            }
                          ).format(perNightAmount);

                          return `${formattedPerNight} per night`;
                        })()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {(() => {
                          const nights = differenceInDays(
                            watchCheckOutDate,
                            watchCheckInDate
                          );
                          const boardType =
                            form.getValues("board_type") || "none";
                          const boardTypeText = (() => {
                            switch (boardType) {
                              case "none":
                                return "Room Only";
                              case "bb":
                                return "Bed & Breakfast";
                              case "hb":
                                return "Half Board";
                              case "fb":
                                return "Full Board";
                              default:
                                return boardType;
                            }
                          })();

                          return `${nights} night${
                            nights !== 1 ? "s" : ""
                          } · ${boardTypeText}`;
                        })()}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 pt-3 border-t border-blue-200">
                    <h5 className="font-medium text-blue-800 mb-2">
                      Occupancy Details
                    </h5>
                    <div className="grid grid-cols-4 gap-2">
                      <div className="bg-white rounded p-2 text-center">
                        <div className="text-lg font-bold">
                          {form.getValues("adults")}
                        </div>
                        <div className="text-xs text-gray-600">
                          Adult{form.getValues("adults") !== 1 ? "s" : ""}
                        </div>
                      </div>

                      <div className="bg-white rounded p-2 text-center">
                        <div className="text-lg font-bold">
                          {form.getValues("children")}
                        </div>
                        <div className="text-xs text-gray-600">
                          Child{form.getValues("children") !== 1 ? "ren" : ""}
                        </div>
                      </div>

                      <div className="bg-white rounded p-2 text-center">
                        <div className="text-lg font-bold">
                          {form.getValues("infants")}
                        </div>
                        <div className="text-xs text-gray-600">
                          Infant{form.getValues("infants") !== 1 ? "s" : ""}
                        </div>
                      </div>

                      <div className="bg-white rounded p-2 text-center">
                        <div className="text-lg font-bold">
                          {form.getValues("number_of_rooms")}
                        </div>
                        <div className="text-xs text-gray-600">
                          Room
                          {form.getValues("number_of_rooms") !== 1 ? "s" : ""}
                        </div>
                      </div>
                    </div>

                    {/* Display max occupancy if available */}
                    {(() => {
                      if (
                        !availabilityResult ||
                        !availabilityResult.available_rooms
                      )
                        return null;

                      const selectedRoom =
                        availabilityResult.available_rooms.find(
                          (r: any) => r.id === watchRoomConfigId
                        );

                      if (
                        !selectedRoom ||
                        (!selectedRoom.max_adults &&
                          !selectedRoom.max_children &&
                          !selectedRoom.max_infants)
                      )
                        return null;

                      return (
                        <div className="mt-2 text-xs text-gray-500">
                          <span className="font-medium">
                            Maximum occupancy:
                          </span>
                          {selectedRoom.max_adults > 0 &&
                            ` ${selectedRoom.max_adults} adult${
                              selectedRoom.max_adults !== 1 ? "s" : ""
                            }`}
                          {selectedRoom.max_children > 0 &&
                            `, ${selectedRoom.max_children} child${
                              selectedRoom.max_children !== 1 ? "ren" : ""
                            }`}
                          {selectedRoom.max_infants > 0 &&
                            `, ${selectedRoom.max_infants} infant${
                              selectedRoom.max_infants !== 1 ? "s" : ""
                            }`}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Payment Information */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm space-y-4">
            <div className="mb-4">
              <h3 className="text-lg font-medium">Payment Information</h3>
              <p className="text-sm text-gray-500">
                Enter the payment details for this booking
              </p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Total Amount</label>
                  <Controller
                    control={form.control}
                    name="total_amount"
                    render={({ field }) => (
                      <>
                        <Input
                          {...field}
                          type="number"
                          min="0"
                          step="0.01"
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value))
                          }
                        />
                        {form.formState.errors.total_amount && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.total_amount.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Currency</label>
                  <Controller
                    control={form.control}
                    name="currency_code"
                    render={({ field }) => (
                      <>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <Select.Trigger>
                            <Select.Value placeholder="Select currency" />
                          </Select.Trigger>
                          <Select.Content>
                            <Select.Item value="USD">USD</Select.Item>
                            <Select.Item value="EUR">EUR</Select.Item>
                            <Select.Item value="GBP">GBP</Select.Item>
                            <Select.Item value="JPY">JPY</Select.Item>
                            <Select.Item value="CAD">CAD</Select.Item>
                            <Select.Item value="AUD">AUD</Select.Item>
                          </Select.Content>
                        </Select>
                        {form.formState.errors.currency_code && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.currency_code.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Notes</label>
                <Controller
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <>
                      <Textarea
                        {...field}
                        placeholder="Any additional notes for this booking"
                      />
                      {form.formState.errors.notes && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.notes.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Add-on Services Section - Only show if hotel is selected and availability has been checked */}
          {watchHotelId && availabilityResult && (
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Add-on Services</h3>
                <p className="text-sm text-gray-500">
                  Add extra services or custom items to this booking
                </p>
              </div>

              <AddOnServiceSelector
                hotelId={watchHotelId}
                adults={watchAdults}
                children={watchChildren}
                currencyCode={form.getValues("currency_code")}
              />

              {/* Summary of add-ons and total */}
              <div className="mt-4 p-4 rounded-md bg-blue-50 border border-blue-200">
                <h4 className="font-medium text-blue-700 mb-2">
                  Booking Summary
                </h4>
                <div className="space-y-2">
                  {/* Room Total */}
                  <div className="flex justify-between">
                    <span>Room Total (excl. tax):</span>
                    <span>
                      {(() => {
                        // Get the selected room configuration
                        const roomConfigId = form.getValues("room_config_id");

                        if (!selectedRoomWithTax || !roomConfigId) {
                          return new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: form.getValues("currency_code"),
                          }).format(form.getValues("total_amount"));
                        }

                        // Get the room price without tax
                        const boardType =
                          form.getValues("board_type") || "none";
                        const numberOfRooms =
                          form.getValues("number_of_rooms") || 1;

                        let priceWithoutTax = 0;

                        if (
                          selectedRoomWithTax.price.meal_plans &&
                          selectedRoomWithTax.price.meal_plans[boardType] &&
                          selectedRoomWithTax.price.meal_plans[boardType]
                            .total_amount_without_tax
                        ) {
                          priceWithoutTax =
                            selectedRoomWithTax.price.meal_plans[boardType]
                              .total_amount_without_tax * numberOfRooms;
                        } else if (
                          selectedRoomWithTax.price.total_amount_without_tax
                        ) {
                          priceWithoutTax =
                            selectedRoomWithTax.price.total_amount_without_tax *
                            numberOfRooms;
                        } else {
                          priceWithoutTax =
                            (form.getValues("total_amount") || 0) -
                            (
                              (form.getValues("add_ons") || []) as AddOnItem[]
                            ).reduce(
                              (sum, item) => sum + item.total_price / 100,
                              0
                            ) -
                            (
                              (form.getValues("custom_line_items") ||
                                []) as CustomLineItem[]
                            ).reduce((sum, item) => sum + item.total_price, 0);
                        }

                        return new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: form.getValues("currency_code"),
                        }).format(priceWithoutTax);
                      })()}
                    </span>
                  </div>

                  {/* Tax */}
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>
                      {(() => {
                        // Get the selected room configuration
                        const roomConfigId = form.getValues("room_config_id");

                        if (!selectedRoomWithTax || !roomConfigId) {
                          return "N/A";
                        }

                        // Get the tax amount
                        const boardType =
                          form.getValues("board_type") || "none";
                        const numberOfRooms =
                          form.getValues("number_of_rooms") || 1;

                        let taxAmount = 0;
                        let taxRate = 0;

                        if (
                          selectedRoomWithTax.price.meal_plans &&
                          selectedRoomWithTax.price.meal_plans[boardType] &&
                          selectedRoomWithTax.price.meal_plans[boardType]
                            .tax_amount
                        ) {
                          taxAmount =
                            selectedRoomWithTax.price.meal_plans[boardType]
                              .tax_amount * numberOfRooms;
                          taxRate =
                            selectedRoomWithTax.price.meal_plans[boardType]
                              .tax_rate || 0;
                        } else if (selectedRoomWithTax.price.tax_amount) {
                          taxAmount =
                            selectedRoomWithTax.price.tax_amount *
                            numberOfRooms;
                          taxRate = selectedRoomWithTax.price.tax_rate || 0;
                        }

                        return `${new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: form.getValues("currency_code"),
                        }).format(taxAmount)} (${taxRate}%)`;
                      })()}
                    </span>
                  </div>

                  {/* Add-ons */}
                  {form.getValues("add_ons")?.length > 0 && (
                    <div className="flex justify-between">
                      <span>Add-on Services:</span>
                      <span>
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: form.getValues("currency_code"),
                        }).format(
                          (
                            (form.getValues("add_ons") || []) as AddOnItem[]
                          ).reduce(
                            (sum, item) => sum + item.total_price / 100,
                            0
                          )
                        )}
                      </span>
                    </div>
                  )}

                  {/* Custom Items */}
                  {form.getValues("custom_line_items")?.length > 0 && (
                    <div className="flex justify-between">
                      <span>Custom Items:</span>
                      <span>
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: form.getValues("currency_code"),
                        }).format(
                          (
                            (form.getValues("custom_line_items") ||
                              []) as CustomLineItem[]
                          ).reduce((sum, item) => sum + item.total_price, 0)
                        )}
                      </span>
                    </div>
                  )}

                  {/* Total */}
                  <div className="flex justify-between font-bold pt-2 border-t border-blue-200">
                    <span>Total (incl. tax):</span>
                    <span>
                      {(() => {
                        // Get the selected room configuration
                        const roomConfigId = form.getValues("room_config_id");

                        if (!selectedRoomWithTax || !roomConfigId) {
                          return new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: form.getValues("currency_code"),
                          }).format(form.getValues("total_amount"));
                        }

                        // Calculate the total with tax
                        const addOnsTotal = (
                          (form.getValues("add_ons") || []) as AddOnItem[]
                        ).reduce(
                          (sum, item) => sum + item.total_price / 100,
                          0
                        );

                        const customItemsTotal = (
                          (form.getValues("custom_line_items") ||
                            []) as CustomLineItem[]
                        ).reduce((sum, item) => sum + item.total_price, 0);

                        const boardType =
                          form.getValues("board_type") || "none";
                        const numberOfRooms =
                          form.getValues("number_of_rooms") || 1;

                        let roomTotalWithTax = 0;

                        if (
                          selectedRoomWithTax.price.meal_plans &&
                          selectedRoomWithTax.price.meal_plans[boardType] &&
                          selectedRoomWithTax.price.meal_plans[boardType]
                            .total_amount_with_tax
                        ) {
                          roomTotalWithTax =
                            selectedRoomWithTax.price.meal_plans[boardType]
                              .total_amount_with_tax * numberOfRooms;
                        } else if (
                          selectedRoomWithTax.price.total_amount_with_tax
                        ) {
                          roomTotalWithTax =
                            selectedRoomWithTax.price.total_amount_with_tax *
                            numberOfRooms;
                        } else {
                          roomTotalWithTax = form.getValues("total_amount");
                        }

                        const totalWithTax =
                          roomTotalWithTax + addOnsTotal + customItemsTotal;

                        return new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: form.getValues("currency_code"),
                        }).format(totalWithTax);
                      })()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="secondary" onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? isEdit
                  ? "Updating..."
                  : "Creating..."
                : isEdit
                ? "Update Booking"
                : "Create Booking"}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default BookingForm;
