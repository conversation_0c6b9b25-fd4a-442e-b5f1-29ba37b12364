import {
  Input,
  FocusModal,
  Text,
  Label,
  Textarea,
  Heading,
  Button,
  Select,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import { useState } from "react";
import {
  FileText,
  Calendar,
  DollarSign,
  X,
} from "lucide-react";

export interface ContractFormData {
  contract_number: string;
  title: string;
  description?: string;
  start_date: string;
  end_date: string;
  status: "draft" | "active" | "expired" | "terminated" | "pending_renewal";
  terms_and_conditions?: string;
  commission_rate?: number;
  payment_terms?: string;
  metadata?: Record<string, any>;
}

interface ContractFormProps {
  formData: ContractFormData;
  setFormData: (data: ContractFormData) => void;
  onSubmit: (data: ContractFormData) => void;
  closeModal: () => void;
  isEditing?: boolean;
}

const ContractForm = ({
  formData,
  setFormData,
  onSubmit,
  closeModal,
  isEditing = false,
}: ContractFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContractFormData>({
    defaultValues: formData,
  });

  const handleSubmit = async (data: ContractFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof ContractFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <FocusModal.Header>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FileText className="h-6 w-6 text-blue-600" />
            <div>
              <Heading level="h2" className="text-xl font-semibold">
                {isEditing ? "Edit Contract" : "Add New Contract"}
              </Heading>
              <Text className="text-gray-600 text-sm">
                {isEditing ? "Update contract information" : "Create a new vendor contract"}
              </Text>
            </div>
          </div>
          <Button variant="secondary" onClick={closeModal}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </FocusModal.Header>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Contract Details
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contract_number">Contract Number *</Label>
                <div className="relative">
                  <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="contract_number"
                    placeholder="Enter contract number"
                    value={formData.contract_number}
                    onChange={(e) => handleInputChange("contract_number", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select status" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="draft">Draft</Select.Item>
                    <Select.Item value="active">Active</Select.Item>
                    <Select.Item value="expired">Expired</Select.Item>
                    <Select.Item value="terminated">Terminated</Select.Item>
                    <Select.Item value="pending_renewal">Pending Renewal</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="title">Contract Title *</Label>
              <Input
                id="title"
                placeholder="Enter contract title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Brief description of the contract"
                value={formData.description || ""}
                onChange={(e) => handleInputChange("description", e.target.value)}
                rows={3}
              />
            </div>
          </div>

          {/* Contract Dates */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Contract Period
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start_date">Start Date *</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => handleInputChange("start_date", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="end_date">End Date *</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="end_date"
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => handleInputChange("end_date", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Financial Terms */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Financial Terms
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="commission_rate">Commission Rate (%)</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="commission_rate"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    placeholder="0.00"
                    value={formData.commission_rate || ""}
                    onChange={(e) => handleInputChange("commission_rate", parseFloat(e.target.value) || undefined)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="payment_terms">Payment Terms</Label>
                <Input
                  id="payment_terms"
                  placeholder="e.g., Net 30 days"
                  value={formData.payment_terms || ""}
                  onChange={(e) => handleInputChange("payment_terms", e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="terms_and_conditions">Terms and Conditions</Label>
              <Textarea
                id="terms_and_conditions"
                placeholder="Enter contract terms and conditions"
                value={formData.terms_and_conditions || ""}
                onChange={(e) => handleInputChange("terms_and_conditions", e.target.value)}
                rows={4}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={closeModal}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? "Saving..." : isEditing ? "Update Contract" : "Create Contract"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContractForm;
