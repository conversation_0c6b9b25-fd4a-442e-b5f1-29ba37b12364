import {
  Input,
  FocusModal,
  Text,
  Label,
  Textarea,
  Heading,
  Button,
  Select,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import { useState } from "react";
import {
  MessageSquare,
  Calendar,
  User,
  X,
} from "lucide-react";

export interface CommunicationFormData {
  contact_id?: string;
  communication_type: "email" | "phone_call" | "meeting" | "video_call" | "chat" | "document" | "other";
  direction: "inbound" | "outbound";
  subject: string;
  content: string;
  status: "pending" | "completed" | "follow_up_required" | "cancelled";
  priority: "low" | "medium" | "high" | "urgent";
  internal_participant: string;
  external_participant?: string;
  scheduled_at?: string;
  completed_at?: string;
  follow_up_date?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

interface CommunicationFormProps {
  formData: CommunicationFormData;
  setFormData: (data: CommunicationFormData) => void;
  onSubmit: (data: CommunicationFormData) => void;
  closeModal: () => void;
  isEditing?: boolean;
  vendorContacts?: Array<{ id: string; name: string; email: string; role: string }>;
}

const CommunicationForm = ({
  formData,
  setFormData,
  onSubmit,
  closeModal,
  isEditing = false,
  vendorContacts = [],
}: CommunicationFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CommunicationFormData>({
    defaultValues: formData,
  });

  const handleSubmit = async (data: CommunicationFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof CommunicationFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <FocusModal.Header>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MessageSquare className="h-6 w-6 text-blue-600" />
            <div>
              <Heading level="h2" className="text-xl font-semibold">
                {isEditing ? "Edit Communication" : "Log New Communication"}
              </Heading>
              <Text className="text-gray-600 text-sm">
                {isEditing ? "Update communication record" : "Record a new communication with vendor"}
              </Text>
            </div>
          </div>
          <Button variant="secondary" onClick={closeModal}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </FocusModal.Header>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Communication Details
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="communication_type">Type *</Label>
                <Select
                  value={formData.communication_type}
                  onValueChange={(value) => handleInputChange("communication_type", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select type" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="email">Email</Select.Item>
                    <Select.Item value="phone_call">Phone Call</Select.Item>
                    <Select.Item value="meeting">Meeting</Select.Item>
                    <Select.Item value="video_call">Video Call</Select.Item>
                    <Select.Item value="chat">Chat</Select.Item>
                    <Select.Item value="document">Document</Select.Item>
                    <Select.Item value="other">Other</Select.Item>
                  </Select.Content>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="direction">Direction *</Label>
                <Select
                  value={formData.direction}
                  onValueChange={(value) => handleInputChange("direction", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select direction" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="inbound">Inbound (From Vendor)</Select.Item>
                    <Select.Item value="outbound">Outbound (To Vendor)</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select status" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="pending">Pending</Select.Item>
                    <Select.Item value="completed">Completed</Select.Item>
                    <Select.Item value="follow_up_required">Follow-up Required</Select.Item>
                    <Select.Item value="cancelled">Cancelled</Select.Item>
                  </Select.Content>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => handleInputChange("priority", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select priority" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="low">Low</Select.Item>
                    <Select.Item value="medium">Medium</Select.Item>
                    <Select.Item value="high">High</Select.Item>
                    <Select.Item value="urgent">Urgent</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="subject">Subject *</Label>
              <Input
                id="subject"
                placeholder="Enter communication subject"
                value={formData.subject}
                onChange={(e) => handleInputChange("subject", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="content">Content *</Label>
              <Textarea
                id="content"
                placeholder="Enter communication content or notes"
                value={formData.content}
                onChange={(e) => handleInputChange("content", e.target.value)}
                rows={4}
                required
              />
            </div>
          </div>

          {/* Participants */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Participants
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="internal_participant">Internal Participant *</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="internal_participant"
                    placeholder="Your name or team member"
                    value={formData.internal_participant}
                    onChange={(e) => handleInputChange("internal_participant", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="external_participant">External Participant</Label>
                <Input
                  id="external_participant"
                  placeholder="Vendor contact person"
                  value={formData.external_participant || ""}
                  onChange={(e) => handleInputChange("external_participant", e.target.value)}
                />
              </div>
            </div>

            {vendorContacts.length > 0 && (
              <div>
                <Label htmlFor="contact_id">Related Contact</Label>
                <Select
                  value={formData.contact_id || ""}
                  onValueChange={(value) => handleInputChange("contact_id", value || undefined)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select related contact" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="">No specific contact</Select.Item>
                    {vendorContacts.map((contact) => (
                      <Select.Item key={contact.id} value={contact.id}>
                        {contact.name} ({contact.role})
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>
            )}
          </div>

          {/* Timing */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Timing
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="scheduled_at">Scheduled At</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="scheduled_at"
                    type="datetime-local"
                    value={formData.scheduled_at || ""}
                    onChange={(e) => handleInputChange("scheduled_at", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="completed_at">Completed At</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="completed_at"
                    type="datetime-local"
                    value={formData.completed_at || ""}
                    onChange={(e) => handleInputChange("completed_at", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="follow_up_date">Follow-up Date</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="follow_up_date"
                    type="date"
                    value={formData.follow_up_date || ""}
                    onChange={(e) => handleInputChange("follow_up_date", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={closeModal}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? "Saving..." : isEditing ? "Update Communication" : "Log Communication"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CommunicationForm;
