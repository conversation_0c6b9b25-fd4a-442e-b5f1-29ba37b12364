import {
  Input,
  FocusModal,
  Text,
  Label,
  Heading,
  Button,
  Select,
  Checkbox,
  Switch,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { User, Mail, Phone, X } from "lucide-react";

export interface ContactFormData {
  name: string;
  email: string;
  phone_number?: string;
  is_whatsapp: boolean;
  role: string;
  is_primary: boolean;
  metadata?: Record<string, any>;
}

interface ContactFormProps {
  formData: ContactFormData;
  setFormData: (data: ContactFormData) => void;
  onSubmit: (data: ContactFormData) => void;
  closeModal: () => void;
  isEditing?: boolean;
}

const ContactForm = ({
  formData,
  setFormData,
  onSubmit,
  closeModal,
  isEditing = false,
}: ContactFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContactFormData>({
    defaultValues: formData,
  });

  const handleSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof ContactFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <FocusModal.Header>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <User className="h-6 w-6 text-blue-600" />
            <div>
              <Heading level="h2" className="text-xl font-semibold">
                {isEditing ? "Edit Contact" : "Add New Contact"}
              </Heading>
              <Text className="text-gray-600 text-sm">
                {isEditing
                  ? "Update contact information"
                  : "Create a new vendor contact"}
              </Text>
            </div>
          </div>
          <Button variant="secondary" onClick={closeModal}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </FocusModal.Header>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Contact Information
            </Heading>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Full Name *</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="name"
                    placeholder="Enter full name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email">Email Address *</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter email address"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone_number">Phone Number</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="phone_number"
                    placeholder="Enter phone number"
                    value={formData.phone_number || ""}
                    onChange={(e) =>
                      handleInputChange("phone_number", e.target.value)
                    }
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="role">Role *</Label>
                <Select
                  value={formData.role}
                  onValueChange={(value) => handleInputChange("role", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select role" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="primary_contact">
                      Primary Contact
                    </Select.Item>
                    <Select.Item value="billing">Billing Contact</Select.Item>
                    <Select.Item value="operations">
                      Operations Contact
                    </Select.Item>
                    <Select.Item value="support">Support Contact</Select.Item>
                    <Select.Item value="sales">Sales Contact</Select.Item>
                    <Select.Item value="technical">
                      Technical Contact
                    </Select.Item>
                    <Select.Item value="management">Management</Select.Item>
                    <Select.Item value="other">Other</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_primary"
                  checked={formData.is_primary}
                  onCheckedChange={(checked) =>
                    handleInputChange("is_primary", checked)
                  }
                />
                <Label htmlFor="is_primary" className="text-sm font-medium">
                  Set as primary contact
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_whatsapp"
                  checked={formData.is_whatsapp}
                  onCheckedChange={(checked) =>
                    handleInputChange("is_whatsapp", checked)
                  }
                />
                <Label htmlFor="is_whatsapp" className="text-sm font-medium">
                  WhatsApp Available
                </Label>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={closeModal}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting
                ? "Saving..."
                : isEditing
                ? "Update Contact"
                : "Create Contact"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContactForm;
