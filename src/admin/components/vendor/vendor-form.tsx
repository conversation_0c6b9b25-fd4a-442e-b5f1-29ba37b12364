import {
  Input,
  FocusModal,
  Text,
  Label,
  Textarea,
  Heading,
  Button,
  Select,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import { useState } from "react";
import {
  Building2,
  Globe,
  MapPin,
  Mail,
  Phone,
  FileText,
  X,
} from "lucide-react";
import { VendorFormData } from "../../routes/vendor-management/vendors/page";

interface VendorFormProps {
  formData: VendorFormData;
  setFormData: (data: VendorFormData) => void;
  onSubmit: (data: VendorFormData) => void;
  closeModal: () => void;
  isEditing?: boolean;
}

const VendorForm = ({
  formData,
  setFormData,
  onSubmit,
  closeModal,
  isEditing = false,
}: VendorFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<VendorFormData>({
    defaultValues: formData,
  });

  const handleSubmit = async (data: VendorFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof VendorFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleAddressChange = (field: keyof NonNullable<VendorFormData['address']>, value: string) => {
    setFormData({
      ...formData,
      address: {
        ...formData.address,
        [field]: value,
      } as NonNullable<VendorFormData['address']>,
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <FocusModal.Header>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-blue-600" />
            <div>
              <Heading level="h2" className="text-xl font-semibold">
                {isEditing ? "Edit Vendor" : "Add New Vendor"}
              </Heading>
              <Text className="text-gray-600 text-sm">
                {isEditing ? "Update vendor information" : "Create a new vendor profile"}
              </Text>
            </div>
          </div>
          <Button variant="secondary" onClick={closeModal}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </FocusModal.Header>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Basic Information
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Vendor Name *</Label>
                <Input
                  id="name"
                  placeholder="Enter vendor name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="type">Vendor Type *</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange("type", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select vendor type" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="hotel_partner">Hotel Partner</Select.Item>
                    <Select.Item value="service_provider">Service Provider</Select.Item>
                    <Select.Item value="transport_provider">Transport Provider</Select.Item>
                    <Select.Item value="activity_provider">Activity Provider</Select.Item>
                    <Select.Item value="tour_operator">Tour Operator</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select status" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="pending_approval">Pending Approval</Select.Item>
                    <Select.Item value="active">Active</Select.Item>
                    <Select.Item value="inactive">Inactive</Select.Item>
                    <Select.Item value="suspended">Suspended</Select.Item>
                    <Select.Item value="terminated">Terminated</Select.Item>
                  </Select.Content>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="website">Website</Label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="website"
                    placeholder="https://example.com"
                    value={formData.website || ""}
                    onChange={(e) => handleInputChange("website", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Brief description of the vendor"
                value={formData.description || ""}
                onChange={(e) => handleInputChange("description", e.target.value)}
                rows={3}
              />
            </div>
          </div>

          {/* Legal Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Legal Information
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="tax_id">Tax ID</Label>
                <div className="relative">
                  <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="tax_id"
                    placeholder="Tax identification number"
                    value={formData.tax_id || ""}
                    onChange={(e) => handleInputChange("tax_id", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="registration_number">Registration Number</Label>
                <div className="relative">
                  <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="registration_number"
                    placeholder="Business registration number"
                    value={formData.registration_number || ""}
                    onChange={(e) => handleInputChange("registration_number", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Address Information
            </Heading>
            
            <div>
              <Label htmlFor="street">Street Address</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="street"
                  placeholder="Street address"
                  value={formData.address?.street || ""}
                  onChange={(e) => handleAddressChange("street", e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  placeholder="City"
                  value={formData.address?.city || ""}
                  onChange={(e) => handleAddressChange("city", e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="state">State/Province</Label>
                <Input
                  id="state"
                  placeholder="State or Province"
                  value={formData.address?.state || ""}
                  onChange={(e) => handleAddressChange("state", e.target.value)}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  placeholder="Country"
                  value={formData.address?.country || ""}
                  onChange={(e) => handleAddressChange("country", e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="postal_code">Postal Code</Label>
                <Input
                  id="postal_code"
                  placeholder="Postal/ZIP code"
                  value={formData.address?.postal_code || ""}
                  onChange={(e) => handleAddressChange("postal_code", e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={closeModal}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? "Saving..." : isEditing ? "Update Vendor" : "Create Vendor"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VendorForm;
