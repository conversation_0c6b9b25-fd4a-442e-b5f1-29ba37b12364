import React, { useState } from "react";
import { Box } from "@mui/material";
import { ChevronDown, ChevronUp, Check } from "lucide-react";

interface Option {
  id: string;
  label: string;
  subtitle?: string;
}

interface MultiSelectProps {
  options: Option[];
  selectedValues: string[];
  onChange: (selectedValues: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  maxHeight?: string;
  emptyMessage?: string;
}

export const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  selectedValues,
  onChange,
  placeholder = "Select options...",
  disabled = false,
  loading = false,
  maxHeight = "200px",
  emptyMessage = "No options available",
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = () => {
    if (!disabled && !loading) {
      setIsOpen(!isOpen);
    }
  };

  const handleOptionClick = (optionId: string) => {
    if (disabled) return;

    const newSelectedValues = selectedValues.includes(optionId)
      ? selectedValues.filter((id) => id !== optionId)
      : [...selectedValues, optionId];

    onChange(newSelectedValues);
  };

  const getDisplayText = () => {
    if (selectedValues.length === 0) {
      return placeholder;
    }
    if (selectedValues.length === 1) {
      const option = options.find((opt) => opt.id === selectedValues[0]);
      return option?.label || placeholder;
    }
    return `${selectedValues.length} selected`;
  };

  return (
    <Box sx={{ position: "relative", width: "100%" }}>
      {/* Trigger */}
      <Box
        onClick={handleToggle}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "0.75rem",
          border: "1px solid #d1d5db",
          borderRadius: "0.375rem",
          backgroundColor: disabled ? "#f9fafb" : "white",
          cursor: disabled || loading ? "not-allowed" : "pointer",
          "&:hover": {
            borderColor: disabled || loading ? "#d1d5db" : "#9ca3af",
          },
          "&:focus": {
            outline: "none",
            borderColor: "#3b82f6",
            boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
          },
        }}
      >
        <span
          style={{
            color: selectedValues.length === 0 ? "#9ca3af" : "#374151",
            fontSize: "0.875rem",
          }}
        >
          {loading ? "Loading..." : getDisplayText()}
        </span>
        {!loading &&
          (isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />)}
      </Box>

      {/* Dropdown */}
      {isOpen && !loading && (
        <Box
          sx={{
            position: "absolute",
            bottom: "100%",
            left: 0,
            right: 0,
            zIndex: 1000,
            backgroundColor: "white",
            border: "1px solid #d1d5db",
            borderRadius: "0.375rem",
            boxShadow:
              "0 -10px 15px -3px rgba(0, 0, 0, 0.1), 0 -4px 6px -2px rgba(0, 0, 0, 0.05)",
            marginBottom: "0.25rem",
            maxHeight,
            overflowY: "auto",
          }}
        >
          {options.length === 0 ? (
            <Box
              sx={{
                padding: "0.75rem",
                textAlign: "center",
                color: "#6b7280",
                fontSize: "0.875rem",
              }}
            >
              {emptyMessage}
            </Box>
          ) : (
            options.map((option) => {
              const isSelected = selectedValues.includes(option.id);
              return (
                <Box
                  key={option.id}
                  onClick={() => handleOptionClick(option.id)}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    padding: "0.75rem",
                    cursor: "pointer",
                    backgroundColor: isSelected ? "#eff6ff" : "white",
                    "&:hover": {
                      backgroundColor: isSelected ? "#dbeafe" : "#f9fafb",
                    },
                    borderBottom: "1px solid #f3f4f6",
                    "&:last-child": {
                      borderBottom: "none",
                    },
                  }}
                >
                  <Box sx={{ flex: 1 }}>
                    <div
                      style={{
                        fontSize: "0.875rem",
                        fontWeight: isSelected ? 500 : 400,
                        color: "#374151",
                      }}
                    >
                      {option.label}
                    </div>
                    {option.subtitle && (
                      <div
                        style={{
                          fontSize: "0.75rem",
                          color: "#6b7280",
                          marginTop: "0.125rem",
                        }}
                      >
                        {option.subtitle}
                      </div>
                    )}
                  </Box>
                  {isSelected && (
                    <Check size={16} style={{ color: "#3b82f6" }} />
                  )}
                </Box>
              );
            })
          )}
        </Box>
      )}

      {/* Backdrop to close dropdown */}
      {isOpen && (
        <Box
          onClick={() => setIsOpen(false)}
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999,
          }}
        />
      )}
    </Box>
  );
};
