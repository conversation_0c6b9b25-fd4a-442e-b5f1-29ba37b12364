import React from "react";
import { Button } from "@camped-ai/ui";
import "../../styles/button-styles.css";

// This component creates a clean outline button with visible borders
// that works well in both light and dark themes
type OutlineButtonProps = React.ComponentProps<typeof Button> & {
  /**
   * Whether to use a more prominent outline style
   * @default false
   */
  prominent?: boolean;
};

const OutlineButton: React.FC<OutlineButtonProps> = ({
  children,
  className = "",
  variant = "secondary",
  prominent = false,
  ...props
}) => {
  const outlineClass = prominent
    ? "outline-button outline-button--prominent"
    : "outline-button";

  return (
    <Button
      variant={variant}
      className={`${outlineClass} ${className}`}
      {...props}
    >
      {children}
    </Button>
  );
};

export default OutlineButton;
