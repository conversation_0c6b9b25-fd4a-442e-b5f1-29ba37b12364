import {
  Input,
  FocusModal,
  Text,
  Label,
  Textarea,
  Heading,
  Button,
  Select,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import { useState } from "react";
import {
  MapPin,
  Calendar,
  Users,
  DollarSign,
  X,
} from "lucide-react";
import { ItineraryFormData } from "../../routes/fulfillment-operations/itineraries/page";

interface ItineraryFormProps {
  formData: ItineraryFormData;
  setFormData: (data: ItineraryFormData) => void;
  onSubmit: (data: ItineraryFormData) => void;
  closeModal: () => void;
  isEditing?: boolean;
}

const ItineraryForm = ({
  formData,
  setFormData,
  onSubmit,
  closeModal,
  isEditing = false,
}: ItineraryFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ItineraryFormData>({
    defaultValues: formData,
  });

  const handleSubmit = async (data: ItineraryFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof ItineraryFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <FocusModal.Header>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MapPin className="h-6 w-6 text-blue-600" />
            <div>
              <Heading level="h2" className="text-xl font-semibold">
                {isEditing ? "Edit Itinerary" : "Create New Itinerary"}
              </Heading>
              <Text className="text-gray-600 text-sm">
                {isEditing ? "Update itinerary information" : "Create a new customer itinerary"}
              </Text>
            </div>
          </div>
          <Button variant="secondary" onClick={closeModal}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </FocusModal.Header>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Itinerary Information
            </Heading>
            
            <div>
              <Label htmlFor="title">Itinerary Title *</Label>
              <Input
                id="title"
                placeholder="Enter itinerary title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe the itinerary details"
                value={formData.description || ""}
                onChange={(e) => handleInputChange("description", e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="booking_id">Booking ID *</Label>
                <Input
                  id="booking_id"
                  placeholder="Enter booking ID"
                  value={formData.booking_id}
                  onChange={(e) => handleInputChange("booking_id", e.target.value)}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="customer_id">Customer ID *</Label>
                <div className="relative">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="customer_id"
                    placeholder="Enter customer ID"
                    value={formData.customer_id}
                    onChange={(e) => handleInputChange("customer_id", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="destination">Destination *</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="destination"
                    placeholder="Enter destination"
                    value={formData.destination}
                    onChange={(e) => handleInputChange("destination", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <Select.Item value="draft">Draft</Select.Item>
                  <Select.Item value="pending_approval">Pending Approval</Select.Item>
                  <Select.Item value="approved">Approved</Select.Item>
                  <Select.Item value="in_progress">In Progress</Select.Item>
                  <Select.Item value="completed">Completed</Select.Item>
                  <Select.Item value="cancelled">Cancelled</Select.Item>
                </Select>
              </div>
            </div>
          </div>

          {/* Date Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Travel Dates
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start_date">Start Date *</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => handleInputChange("start_date", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="end_date">End Date *</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="end_date"
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => handleInputChange("end_date", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Cost Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Cost Information
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="total_cost">Total Cost</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="total_cost"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={formData.total_cost || ""}
                    onChange={(e) => handleInputChange("total_cost", parseFloat(e.target.value) || 0)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={formData.currency || "USD"}
                  onValueChange={(value) => handleInputChange("currency", value)}
                >
                  <Select.Item value="USD">USD - US Dollar</Select.Item>
                  <Select.Item value="EUR">EUR - Euro</Select.Item>
                  <Select.Item value="GBP">GBP - British Pound</Select.Item>
                  <Select.Item value="JPY">JPY - Japanese Yen</Select.Item>
                  <Select.Item value="CAD">CAD - Canadian Dollar</Select.Item>
                  <Select.Item value="AUD">AUD - Australian Dollar</Select.Item>
                  <Select.Item value="INR">INR - Indian Rupee</Select.Item>
                </Select>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Additional Information
            </Heading>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <Text className="text-sm text-gray-600 mb-2">
                Itinerary metadata and additional notes can be added here. This information will be stored as JSON metadata.
              </Text>
              <Textarea
                placeholder="Add any additional notes or metadata (JSON format)"
                value={formData.metadata ? JSON.stringify(formData.metadata, null, 2) : ""}
                onChange={(e) => {
                  try {
                    const metadata = e.target.value ? JSON.parse(e.target.value) : {};
                    handleInputChange("metadata", metadata);
                  } catch (error) {
                    // Invalid JSON, keep the string for now
                  }
                }}
                rows={4}
                className="font-mono text-sm"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={closeModal}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? "Saving..." : isEditing ? "Update Itinerary" : "Create Itinerary"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ItineraryForm;
