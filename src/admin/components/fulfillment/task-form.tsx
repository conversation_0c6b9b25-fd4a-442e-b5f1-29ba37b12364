import {
  Input,
  FocusModal,
  Text,
  Label,
  Textarea,
  Heading,
  Button,
  Select,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import { useState } from "react";
import {
  Clock,
  User,
  Calendar,
  AlertTriangle,
  X,
} from "lucide-react";
import { TaskFormData } from "../../routes/fulfillment-operations/tasks/page";

interface TaskFormProps {
  formData: TaskFormData;
  setFormData: (data: TaskFormData) => void;
  onSubmit: (data: TaskFormData) => void;
  closeModal: () => void;
  isEditing?: boolean;
}

const TaskForm = ({
  formData,
  setFormData,
  onSubmit,
  closeModal,
  isEditing = false,
}: TaskFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<TaskFormData>({
    defaultValues: formData,
  });

  const handleSubmit = async (data: TaskFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof TaskFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <FocusModal.Header>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Clock className="h-6 w-6 text-blue-600" />
            <div>
              <Heading level="h2" className="text-xl font-semibold">
                {isEditing ? "Edit Task" : "Create New Task"}
              </Heading>
              <Text className="text-gray-600 text-sm">
                {isEditing ? "Update task information" : "Create a new fulfillment task"}
              </Text>
            </div>
          </div>
          <Button variant="secondary" onClick={closeModal}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </FocusModal.Header>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Task Information
            </Heading>
            
            <div>
              <Label htmlFor="title">Task Title *</Label>
              <Input
                id="title"
                placeholder="Enter task title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe the task details"
                value={formData.description || ""}
                onChange={(e) => handleInputChange("description", e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">Task Type *</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange("type", value)}
                >
                  <Select.Item value="booking_confirmation">Booking Confirmation</Select.Item>
                  <Select.Item value="vendor_assignment">Vendor Assignment</Select.Item>
                  <Select.Item value="itinerary_creation">Itinerary Creation</Select.Item>
                  <Select.Item value="customer_communication">Customer Communication</Select.Item>
                  <Select.Item value="payment_processing">Payment Processing</Select.Item>
                  <Select.Item value="document_generation">Document Generation</Select.Item>
                  <Select.Item value="quality_check">Quality Check</Select.Item>
                  <Select.Item value="follow_up">Follow Up</Select.Item>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <Select.Item value="pending">Pending</Select.Item>
                  <Select.Item value="in_progress">In Progress</Select.Item>
                  <Select.Item value="completed">Completed</Select.Item>
                  <Select.Item value="failed">Failed</Select.Item>
                  <Select.Item value="cancelled">Cancelled</Select.Item>
                  <Select.Item value="on_hold">On Hold</Select.Item>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="priority">Priority</Label>
                <div className="relative">
                  <AlertTriangle className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Select
                    value={formData.priority}
                    onValueChange={(value) => handleInputChange("priority", value)}
                  >
                    <Select.Item value="low">Low</Select.Item>
                    <Select.Item value="medium">Medium</Select.Item>
                    <Select.Item value="high">High</Select.Item>
                    <Select.Item value="urgent">Urgent</Select.Item>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="due_date">Due Date</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="due_date"
                    type="datetime-local"
                    value={formData.due_date || ""}
                    onChange={(e) => handleInputChange("due_date", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Assignment Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Assignment & References
            </Heading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="assigned_to">Assigned To</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="assigned_to"
                    placeholder="Enter assignee name or ID"
                    value={formData.assigned_to || ""}
                    onChange={(e) => handleInputChange("assigned_to", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="booking_id">Booking ID</Label>
                <Input
                  id="booking_id"
                  placeholder="Related booking ID"
                  value={formData.booking_id || ""}
                  onChange={(e) => handleInputChange("booking_id", e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="vendor_id">Vendor ID</Label>
              <Input
                id="vendor_id"
                placeholder="Related vendor ID"
                value={formData.vendor_id || ""}
                onChange={(e) => handleInputChange("vendor_id", e.target.value)}
              />
            </div>
          </div>

          {/* Additional Notes */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium text-gray-900">
              Additional Information
            </Heading>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <Text className="text-sm text-gray-600 mb-2">
                Task metadata and additional notes can be added here. This information will be stored as JSON metadata.
              </Text>
              <Textarea
                placeholder="Add any additional notes or metadata (JSON format)"
                value={formData.metadata ? JSON.stringify(formData.metadata, null, 2) : ""}
                onChange={(e) => {
                  try {
                    const metadata = e.target.value ? JSON.parse(e.target.value) : {};
                    handleInputChange("metadata", metadata);
                  } catch (error) {
                    // Invalid JSON, keep the string for now
                  }
                }}
                rows={4}
                className="font-mono text-sm"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={closeModal}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? "Saving..." : isEditing ? "Update Task" : "Create Task"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskForm;
