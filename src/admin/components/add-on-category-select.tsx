import React from "react";
import { useAdminAddOnCategories } from "../hooks/add-on-categories/use-admin-add-on-categories";
import CustomDropdown from "./custom-dropdown";

interface AddOnCategorySelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  className?: string;
}

const AddOnCategorySelect: React.FC<AddOnCategorySelectProps> = ({
  value,
  onChange,
  placeholder = "Select a category",
  label,
  required = false,
  error,
  disabled = false,
  className,
}) => {
  const { data: categoriesData, isLoading } = useAdminAddOnCategories();

  const categories = (categoriesData as any)?.categories || [];
  const activeCategories = categories.filter((cat: any) => cat.is_active);

  // Prepare options for the dropdown
  const options = [
    {
      value: "",
      label: "No category",
      description: "",
    },
    ...activeCategories.map((category: any) => ({
      value: category.id,
      label: category.name,
      description: category.description || undefined,
    })),
  ];

  return (
    <CustomDropdown
      value={value || ""}
      onChange={onChange}
      options={options}
      placeholder={placeholder}
      label={label}
      required={required}
      error={error}
      disabled={disabled}
      loading={isLoading}
      className={className}
    />
  );
};

export default AddOnCategorySelect;
