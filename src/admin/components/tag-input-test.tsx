import React, { useState } from "react";
import { Input, Label, Text, Badge } from "@camped-ai/ui";
import { X } from "lucide-react";

/**
 * Simple test component to demonstrate the tag input functionality
 * This shows how the hotel form now handles tags using standard Camped UI components
 */
const TagInputTest: React.FC = () => {
  const [tags, setTags] = useState<string[]>(["existing-tag", "sample"]);
  const [tagInput, setTagInput] = useState("");

  // Helper functions for tag management
  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag]);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addTag(tagInput);
    } else if (e.key === "Backspace" && tagInput === "" && tags.length > 0) {
      // Remove last tag when backspace is pressed on empty input
      removeTag(tags[tags.length - 1]);
    }
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Handle comma-separated input
    if (value.includes(",")) {
      const newTags = value.split(",").map(tag => tag.trim()).filter(tag => tag);
      newTags.forEach(tag => {
        if (!tags.includes(tag)) {
          addTag(tag);
        }
      });
      setTagInput("");
    } else {
      setTagInput(value);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Tag Input Test</h1>
        <Text className="text-gray-600 mt-2">
          Testing the new tag input functionality using standard Camped UI components.
        </Text>
      </div>

      <div>
        <Label htmlFor="tags" className="block mb-1 font-medium">
          Tags
        </Label>
        
        {/* Tags Display */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-2 p-2 border border-gray-200 rounded-md bg-gray-50">
            {tags.map((tag, index) => (
              <Badge
                key={index}
                className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(tag)}
                  className="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                  aria-label={`Remove ${tag} tag`}
                >
                  <X size={12} />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Input Field */}
        <Input
          id="tags"
          value={tagInput}
          onChange={handleTagInputChange}
          onKeyDown={handleTagInputKeyDown}
          placeholder="Type a tag and press Enter"
          className="w-full"
        />
        <Text className="text-xs text-gray-500 mt-1">
          Press Enter to add a tag, or separate multiple tags with commas
        </Text>
      </div>

      {/* Current State Display */}
      <div className="bg-gray-50 p-3 rounded">
        <Text className="text-sm font-medium">Current tags array:</Text>
        <Text className="text-sm text-gray-600 font-mono">
          {JSON.stringify(tags)}
        </Text>
      </div>

      {/* Test Submit Button */}
      <button
        onClick={() => {
          const payload = { tags, timestamp: new Date().toISOString() };
          console.log("Form submission payload:", payload);
          alert("Check console for payload - tags should be an array!");
        }}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Test Submit (Check Console)
      </button>
    </div>
  );
};

export default TagInputTest;
