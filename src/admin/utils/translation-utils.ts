/**
 * Translation utilities for generating Tolgee keys and managing translations
 */

export type EntityType =
  | "destination"
  | "hotel"
  | "room"
  | "add_on"
  | "cancellation_policy"
  | "add_on_category";
export type ArrayFieldType = "tags" | "amenities" | "rules" | "safety_measures";
export type NestedObjectFieldType = "faqs";
export type FaqSubFieldType = "question" | "answer";

/**
 * Generate a translation key for Tolgee
 * Pattern: {entity}.{entity_id}.{field_name}
 */
export const generateTranslationKey = (
  entity: EntityType,
  entityId: string,
  fieldName: string
): string => {
  return `${entity}.${entityId}.${fieldName}`;
};

/**
 * Generate translation keys for all translatable fields of an entity
 */
export const generateTranslationKeys = (
  entity: EntityType,
  entityId: string,
  translatableFields: readonly string[]
): Record<string, string> => {
  const keys: Record<string, string> = {};

  translatableFields.forEach((fieldName) => {
    keys[fieldName] = generateTranslationKey(entity, entityId, fieldName);
  });

  return keys;
};

/**
 * Extract entity ID and field name from a translation key
 */
export const parseTranslationKey = (
  key: string
): {
  entity: string;
  entityId: string;
  fieldName: string;
} | null => {
  const parts = key.split(".");
  if (parts.length !== 3) return null;

  return {
    entity: parts[0],
    entityId: parts[1],
    fieldName: parts[2],
  };
};

/**
 * Get all translation keys for a specific entity
 */
export const getEntityKeyPrefix = (
  entity: EntityType,
  entityId: string
): string => {
  return `${entity}.${entityId}.`;
};

/**
 * Check if a key belongs to a specific entity
 */
export const isEntityKey = (
  key: string,
  entity: EntityType,
  entityId: string
): boolean => {
  return key.startsWith(getEntityKeyPrefix(entity, entityId));
};

/**
 * Filter translation keys by entity
 */
export const filterKeysByEntity = (
  keys: string[],
  entity: EntityType,
  entityId: string
): string[] => {
  const prefix = getEntityKeyPrefix(entity, entityId);
  return keys.filter((key) => key.startsWith(prefix));
};

/**
 * Generate a translation key for an array field item
 * Pattern: {entity}.{entity_id}.{field_name}.{index}
 */
export const generateArrayItemTranslationKey = (
  entity: EntityType,
  entityId: string,
  fieldName: ArrayFieldType,
  index: number
): string => {
  return `${entity}.${entityId}.${fieldName}.${index}`;
};

/**
 * Generate translation keys for all items in an array field
 */
export const generateArrayFieldTranslationKeys = (
  entity: EntityType,
  entityId: string,
  fieldName: ArrayFieldType,
  arrayLength: number
): Record<number, string> => {
  const keys: Record<number, string> = {};

  for (let i = 0; i < arrayLength; i++) {
    keys[i] = generateArrayItemTranslationKey(entity, entityId, fieldName, i);
  }

  return keys;
};

/**
 * Parse an array field translation key to extract components
 */
export const parseArrayTranslationKey = (
  key: string
): {
  entity: string;
  entityId: string;
  fieldName: string;
  index: number;
} | null => {
  const parts = key.split(".");
  if (parts.length !== 4) return null;

  const index = parseInt(parts[3], 10);
  if (isNaN(index)) return null;

  return {
    entity: parts[0],
    entityId: parts[1],
    fieldName: parts[2],
    index: index,
  };
};

/**
 * Get all array field translation keys for a specific entity and field
 */
export const getArrayFieldKeyPrefix = (
  entity: EntityType,
  entityId: string,
  fieldName: ArrayFieldType
): string => {
  return `${entity}.${entityId}.${fieldName}.`;
};

/**
 * Check if a key belongs to a specific array field
 */
export const isArrayFieldKey = (
  key: string,
  entity: EntityType,
  entityId: string,
  fieldName: ArrayFieldType
): boolean => {
  return key.startsWith(getArrayFieldKeyPrefix(entity, entityId, fieldName));
};

/**
 * Filter translation keys by array field
 */
export const filterKeysByArrayField = (
  keys: string[],
  entity: EntityType,
  entityId: string,
  fieldName: ArrayFieldType
): string[] => {
  const prefix = getArrayFieldKeyPrefix(entity, entityId, fieldName);
  return keys.filter((key) => key.startsWith(prefix));
};

/**
 * Convert array field translations to ordered array
 * Takes translation keys and values, returns ordered array based on index
 */
export const arrayTranslationsToOrderedArray = (
  translations: Record<string, string>,
  entity: EntityType,
  entityId: string,
  fieldName: ArrayFieldType
): string[] => {
  const relevantKeys = filterKeysByArrayField(
    Object.keys(translations),
    entity,
    entityId,
    fieldName
  );

  const indexedItems: { index: number; value: string }[] = [];

  relevantKeys.forEach((key) => {
    const parsed = parseArrayTranslationKey(key);
    if (parsed) {
      indexedItems.push({
        index: parsed.index,
        value: translations[key],
      });
    }
  });

  // Sort by index and extract values
  return indexedItems
    .sort((a, b) => a.index - b.index)
    .map((item) => item.value);
};

/**
 * Convert ordered array to translation keys and values
 * Takes an array and returns translation key-value pairs
 */
export const orderedArrayToTranslations = (
  array: string[],
  entity: EntityType,
  entityId: string,
  fieldName: ArrayFieldType
): Record<string, string> => {
  const translations: Record<string, string> = {};

  array.forEach((value, index) => {
    const key = generateArrayItemTranslationKey(
      entity,
      entityId,
      fieldName,
      index
    );
    translations[key] = value;
  });

  return translations;
};

/**
 * Generate a translation key for a nested object field item with subfield
 * Pattern: {entity}.{entity_id}.{field_name}.{index}.{subfield}
 */
export const generateNestedObjectTranslationKey = (
  entity: EntityType,
  entityId: string,
  fieldName: NestedObjectFieldType,
  index: number,
  subField: FaqSubFieldType
): string => {
  return `${entity}.${entityId}.${fieldName}.${index}.${subField}`;
};

/**
 * Generate translation keys for all items in a nested object field
 */
export const generateNestedObjectFieldTranslationKeys = (
  entity: EntityType,
  entityId: string,
  fieldName: NestedObjectFieldType,
  arrayLength: number
): Record<string, Record<FaqSubFieldType, string>> => {
  const keys: Record<string, Record<FaqSubFieldType, string>> = {};

  for (let i = 0; i < arrayLength; i++) {
    keys[i] = {
      question: generateNestedObjectTranslationKey(
        entity,
        entityId,
        fieldName,
        i,
        "question"
      ),
      answer: generateNestedObjectTranslationKey(
        entity,
        entityId,
        fieldName,
        i,
        "answer"
      ),
    };
  }

  return keys;
};

/**
 * Parse a nested object field translation key to extract components
 */
export const parseNestedObjectTranslationKey = (
  key: string
): {
  entity: string;
  entityId: string;
  fieldName: string;
  index: number;
  subField: string;
} | null => {
  const parts = key.split(".");
  if (parts.length !== 5) return null;

  const index = parseInt(parts[3], 10);
  if (isNaN(index)) return null;

  return {
    entity: parts[0],
    entityId: parts[1],
    fieldName: parts[2],
    index: index,
    subField: parts[4],
  };
};

/**
 * Get all nested object field translation keys for a specific entity and field
 */
export const getNestedObjectFieldKeyPrefix = (
  entity: EntityType,
  entityId: string,
  fieldName: NestedObjectFieldType
): string => {
  return `${entity}.${entityId}.${fieldName}.`;
};

/**
 * Check if a key belongs to a specific nested object field
 */
export const isNestedObjectFieldKey = (
  key: string,
  entity: EntityType,
  entityId: string,
  fieldName: NestedObjectFieldType
): boolean => {
  return key.startsWith(
    getNestedObjectFieldKeyPrefix(entity, entityId, fieldName)
  );
};

/**
 * Filter translation keys by nested object field
 */
export const filterKeysByNestedObjectField = (
  keys: string[],
  entity: EntityType,
  entityId: string,
  fieldName: NestedObjectFieldType
): string[] => {
  const prefix = getNestedObjectFieldKeyPrefix(entity, entityId, fieldName);
  return keys.filter((key) => key.startsWith(prefix));
};

/**
 * Convert nested object field translations to ordered array of objects
 * Takes translation keys and values, returns ordered array based on index
 */
export const nestedObjectTranslationsToOrderedArray = (
  translations: Record<string, string>,
  entity: EntityType,
  entityId: string,
  fieldName: NestedObjectFieldType
): Array<{ question: string; answer: string }> => {
  const relevantKeys = filterKeysByNestedObjectField(
    Object.keys(translations),
    entity,
    entityId,
    fieldName
  );

  const indexedItems: Record<number, { question?: string; answer?: string }> =
    {};

  relevantKeys.forEach((key) => {
    const parsed = parseNestedObjectTranslationKey(key);
    if (
      parsed &&
      (parsed.subField === "question" || parsed.subField === "answer")
    ) {
      if (!indexedItems[parsed.index]) {
        indexedItems[parsed.index] = {};
      }
      indexedItems[parsed.index][parsed.subField as FaqSubFieldType] =
        translations[key];
    }
  });

  // Convert to ordered array, ensuring both question and answer exist
  const result: Array<{ question: string; answer: string }> = [];
  const sortedIndices = Object.keys(indexedItems)
    .map((index) => parseInt(index, 10))
    .sort((a, b) => a - b);

  sortedIndices.forEach((index) => {
    const item = indexedItems[index];
    if (item.question && item.answer) {
      result.push({
        question: item.question,
        answer: item.answer,
      });
    }
  });

  return result;
};

/**
 * Convert ordered array of objects to translation keys and values
 * Takes an array of FAQ objects and returns translation key-value pairs
 */
export const orderedNestedObjectArrayToTranslations = (
  array: Array<{ question: string; answer: string }>,
  entity: EntityType,
  entityId: string,
  fieldName: NestedObjectFieldType
): Record<string, string> => {
  const translations: Record<string, string> = {};

  array.forEach((item, index) => {
    const questionKey = generateNestedObjectTranslationKey(
      entity,
      entityId,
      fieldName,
      index,
      "question"
    );
    const answerKey = generateNestedObjectTranslationKey(
      entity,
      entityId,
      fieldName,
      index,
      "answer"
    );

    translations[questionKey] = item.question;
    translations[answerKey] = item.answer;
  });

  return translations;
};
