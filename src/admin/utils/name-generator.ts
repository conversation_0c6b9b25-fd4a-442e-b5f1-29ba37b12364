import { DynamicFieldSchema } from "../components/supplier-management/dynamic-field-renderer";

/**
 * Generates a product/service name based on category and mandatory dynamic fields
 * This is the frontend version of the name generator utility
 * @param categoryName - The name of the category
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns Generated name string
 */
export function generateProductServiceName(
  categoryName: string,
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): string {
  if (!categoryName) {
    return "";
  }

  const nameParts: string[] = [categoryName];

  // Get mandatory fields that should be used in product/service names
  const mandatoryFields = dynamicFieldSchema.filter(
    field => field.required && field.used_in_product
  );

  // Add values from mandatory fields
  for (const field of mandatoryFields) {
    const value = customFields[field.key];
    if (value !== undefined && value !== null && value !== '') {
      // Handle different field types
      let displayValue: string;
      
      switch (field.type) {
        case 'multi-select':
          displayValue = Array.isArray(value) ? value.join(', ') : String(value);
          break;
        case 'number-range':
          if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
            displayValue = `${value.min}-${value.max}`;
          } else {
            displayValue = String(value);
          }
          break;
        case 'boolean':
          displayValue = value ? 'Yes' : 'No';
          break;
        case 'date':
          if (value instanceof Date) {
            displayValue = value.toLocaleDateString();
          } else if (typeof value === 'string') {
            // Handle date strings
            const date = new Date(value);
            displayValue = isNaN(date.getTime()) ? value : date.toLocaleDateString();
          } else {
            displayValue = String(value);
          }
          break;
        default:
          displayValue = String(value);
      }
      
      if (displayValue.trim()) {
        nameParts.push(displayValue.trim());
      }
    }
  }

  return nameParts.join(' – ');
}

/**
 * Checks if all required fields for name generation are filled
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns True if all required fields are filled
 */
export function areRequiredFieldsForNameFilled(
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): boolean {
  const mandatoryFields = dynamicFieldSchema.filter(
    field => field.required && field.used_in_product
  );

  return mandatoryFields.every(field => {
    const value = customFields[field.key];
    return value !== undefined && value !== null && value !== '';
  });
}

/**
 * Gets the list of required fields that are missing for name generation
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns Array of missing field labels
 */
export function getMissingRequiredFields(
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): string[] {
  const mandatoryFields = dynamicFieldSchema.filter(
    field => field.required && field.used_in_product
  );

  return mandatoryFields
    .filter(field => {
      const value = customFields[field.key];
      return value === undefined || value === null || value === '';
    })
    .map(field => field.label);
}
