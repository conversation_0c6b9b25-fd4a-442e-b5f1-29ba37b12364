import { DollarSign, Euro, PoundSterling } from "lucide-react";
import type { Currency } from "../hooks/use-admin-currencies";

/**
 * Get currency symbol for a given currency code
 * Note: This is a fallback function. Use Currency object from API when available.
 */
export function getCurrencySymbol(currencyCode: string): string {
  // This should only be used as a last resort fallback
  // Prefer using the Currency object from useAdminCurrencies hook
  return currencyCode.toUpperCase();
}

/**
 * Format currency for dropdown display
 */
export function formatCurrencyOption(currency: Currency): string {
  return `${currency.currency_code} (${currency.symbol})`;
}

/**
 * Get appropriate Lucide icon component for currency
 */
export function getCurrencyIcon(currencyCode: string) {
  const code = currencyCode.toUpperCase();
  
  switch (code) {
    case "USD":
    case "CAD":
    case "AUD":
    case "NZD":
    case "HKD":
    case "SGD":
    case "MXN":
      return DollarSign;
    case "EUR":
      return Euro;
    case "GBP":
    case "EGP":
      return PoundSterling;
    default:
      return DollarSign; // Default fallback
  }
}

/**
 * Format amount with currency symbol and proper decimal places
 */
export function formatCurrencyAmount(
  amount: number,
  currency: Currency,
  options: {
    showSymbol?: boolean;
    showCode?: boolean;
    symbolPosition?: "before" | "after";
  } = {}
): string {
  const {
    showSymbol = true,
    showCode = false,
    symbolPosition = "before",
  } = options;

  const formattedAmount = amount.toFixed(currency.decimal_digits);
  
  let result = formattedAmount;
  
  if (showSymbol && symbolPosition === "before") {
    result = `${currency.symbol}${formattedAmount}`;
  } else if (showSymbol && symbolPosition === "after") {
    result = `${formattedAmount} ${currency.symbol}`;
  }
  
  if (showCode) {
    result = `${result} ${currency.currency_code}`;
  }
  
  return result;
}

/**
 * Parse amount from string, handling different currency formats
 */
export function parseCurrencyAmount(value: string, currency: Currency): number {
  // Remove currency symbols and non-numeric characters except decimal point
  const cleanValue = value
    .replace(new RegExp(`[${currency.symbol}]`, "g"), "")
    .replace(/[^\d.-]/g, "");
  
  const parsed = parseFloat(cleanValue);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Convert amount to smallest currency unit (e.g., cents for USD)
 */
export function toSmallestUnit(amount: number, currency: Currency): number {
  return Math.round(amount * Math.pow(10, currency.decimal_digits));
}

/**
 * Convert amount from smallest currency unit to standard unit
 */
export function fromSmallestUnit(amount: number, currency: Currency): number {
  return amount / Math.pow(10, currency.decimal_digits);
}

/**
 * Validate currency code
 */
export function isValidCurrencyCode(code: string): boolean {
  return /^[A-Z]{3}$/.test(code.toUpperCase());
}

/**
 * Get currency display name
 * Note: This is a fallback function. Use Currency object from API when available.
 */
export function getCurrencyName(currencyCode: string): string {
  // This should only be used as a last resort fallback
  // Prefer using the Currency object from useAdminCurrencies hook
  return currencyCode.toUpperCase();
}
