/**
 * AI Translation Service for array fields using OpenAI API
 * Handles translation of hotel/destination array fields like tags, amenities, rules, safety measures
 */

export interface TranslationRequest {
  values: string[];
  targetLanguage: string;
  fieldType: "tags" | "amenities" | "rules" | "safety_measures";
  context?: {
    entityType?: "hotel" | "destination";
    entityName?: string;
    location?: string;
  };
}

export interface TranslationResponse {
  translatedValues: string[];
  success: boolean;
  error?: string;
}

/**
 * Get language name mapping for better AI context
 */
const getLanguageName = (languageCode: string): string => {
  const languageMap: Record<string, string> = {
    en: "English",
    de: "German",
    ja: "Japanese",
    fr: "French",
    es: "Spanish",
    it: "Italian",
    pt: "Portuguese",
    ru: "Russian",
    zh: "Chinese",
    ko: "Korean",
    ar: "Arabic",
    hi: "Hindi",
  };
  return languageMap[languageCode] || languageCode;
};

/**
 * Get field-specific translation instructions
 */
const getFieldInstructions = (fieldType: string): string => {
  const instructions: Record<string, string> = {
    tags: "These are categorization tags for the property. Keep them concise and relevant for search and filtering.",
    amenities:
      "These are facilities and services available at the property. Use standard hospitality terminology.",
    rules:
      "These are policies and regulations for guests. Maintain the authoritative tone while being clear.",
    safety_measures:
      "These are health and safety protocols. Keep the serious, professional tone and ensure accuracy.",
  };
  return (
    instructions[fieldType] ||
    "Translate these items accurately while maintaining their meaning and context."
  );
};

/**
 * Create system prompt for array field translation
 */
const createSystemPrompt = (request: TranslationRequest): string => {
  const { targetLanguage, fieldType, context } = request;
  const languageName = getLanguageName(targetLanguage);
  const fieldInstructions = getFieldInstructions(fieldType);
  const entityType = context?.entityType || "property";

  return `You are a professional translator specializing in hospitality and travel content. Your task is to translate ${fieldType} from English to ${languageName}.

Context:
- Entity type: ${entityType}
- Field type: ${fieldType}
- Target language: ${languageName} (${targetLanguage})
${context?.entityName ? `- Property name: ${context.entityName}` : ""}
${context?.location ? `- Location: ${context.location}` : ""}

Instructions:
${fieldInstructions}

Requirements:
1. Translate each item accurately while preserving meaning
2. Use appropriate terminology for the hospitality industry
3. Keep translations concise and clear
4. Maintain consistency in style and tone
5. Consider cultural context and local preferences
6. Return ONLY the translated items as a JSON array of strings
7. Do not add explanations, comments, or additional text
8. Ensure the number of translated items matches the input exactly

Example format: ["translated item 1", "translated item 2", "translated item 3"]`;
};

/**
 * Create user prompt with the values to translate
 */
const createUserPrompt = (values: string[]): string => {
  return `Translate the following ${values.length} items to the target language:

${JSON.stringify(values, null, 2)}

Return only the translated items as a JSON array.`;
};

/**
 * Call the AI translation API
 */
export const translateArrayValues = async (
  request: TranslationRequest
): Promise<TranslationResponse> => {
  try {
    const { values, targetLanguage } = request;

    if (!values || values.length === 0) {
      return {
        translatedValues: [],
        success: true,
      };
    }

    // Validate target language
    if (!targetLanguage || targetLanguage === "en") {
      throw new Error(
        "Target language must be specified and cannot be English"
      );
    }

    const systemPrompt = createSystemPrompt(request);
    const userPrompt = createUserPrompt(values);

    // Call the existing AI generation API
    const response = await fetch("/admin/ai-generate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "general",
        prompt: userPrompt,
        context: {
          systemPrompt,
          fieldType: request.fieldType,
          targetLanguage,
          entityType: request.context?.entityType,
          entityName: request.context?.entityName,
          location: request.context?.location,
        },
        maxLength: 1000, // Reasonable limit for array translations
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || `Translation API error: ${response.status}`
      );
    }

    const data = await response.json();
    const content = data.content || data.message || "";

    // Parse the AI response as JSON array
    let translatedValues: string[];
    try {
      // Try to extract JSON array from the response
      const jsonMatch = content.match(/\[.*\]/s);
      if (jsonMatch) {
        translatedValues = JSON.parse(jsonMatch[0]);
      } else {
        // Fallback: split by lines and clean up
        translatedValues = content
          .split("\n")
          .map((line: string) => line.trim())
          .filter(
            (line: string) =>
              line && !line.startsWith("[") && !line.startsWith("]")
          )
          .map((line: string) =>
            line.replace(/^["']|["']$/g, "").replace(/^-\s*/, "")
          );
      }
    } catch (parseError) {
      console.error("Failed to parse AI translation response:", parseError);
      throw new Error(
        "Failed to parse translation response. Please try again."
      );
    }

    // Validate the response
    if (!Array.isArray(translatedValues)) {
      throw new Error("Invalid translation response format");
    }

    if (translatedValues.length !== values.length) {
      console.warn(
        `Translation count mismatch: expected ${values.length}, got ${translatedValues.length}`
      );
      // Truncate or pad as needed
      if (translatedValues.length > values.length) {
        translatedValues = translatedValues.slice(0, values.length);
      } else {
        // Pad with original values if needed
        while (translatedValues.length < values.length) {
          const missingIndex = translatedValues.length;
          translatedValues.push(values[missingIndex]);
        }
      }
    }

    // Clean up translated values
    const cleanedValues = translatedValues
      .map((value) =>
        typeof value === "string" ? value.trim() : String(value).trim()
      )
      .filter((value) => value.length > 0);

    return {
      translatedValues: cleanedValues,
      success: true,
    };
  } catch (error) {
    console.error("AI translation error:", error);
    return {
      translatedValues: [],
      success: false,
      error: error instanceof Error ? error.message : "Translation failed",
    };
  }
};
