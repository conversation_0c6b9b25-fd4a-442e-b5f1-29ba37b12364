# Sidebar Navigation Configuration

This directory contains configuration files for customizing the admin UI.

## Hiding Sidebar Navigation Items

You can hide specific sidebar navigation items by modifying the `HIDDEN_SIDEBAR_ITEMS` array in the `sidebar-config.ts` file. This is useful for hiding menu items that you don't want users to access or that aren't relevant to your application.

### How to Use

1. Open the `sidebar-config.ts` file
2. Modify the `HIDDEN_SIDEBAR_ITEMS` array to include the labels of the items you want to hide
3. The items will be hidden when the admin UI loads

### Example

```typescript
// Hide developer-related and specific business settings items
export const HIDDEN_SIDEBAR_ITEMS: string[] = [
  "Publishable API Keys",
  "Secret API Keys",
  "Workflows",
  "Webhooks",
  "Return Reasons",
  "Sales Channels",
  "Product Types",
  "Product Tags",
  "Locations & Shipping",
];
```

### Notes

- The comparison is case-insensitive, so "products" will match "Products"
- The items are matched by both exact and partial text, so "Product" will hide both "Products" and "Product Categories"
- You can enable or disable debug mode by setting `DEBUG_MODE` in the `sidebar-config.ts` file
- If you need to temporarily show all items, you can empty the array:

```typescript
export const HIDDEN_SIDEBAR_ITEMS: string[] = [];
```

### Troubleshooting

If items are not being hidden:

1. Make sure the labels in the array match the text shown in the sidebar (or part of the text)
2. Check the browser console for any errors
3. Try refreshing the page after making changes
4. The solution uses a simple React widget that runs once per page load
5. If the sidebar items have a different structure than expected, you may need to modify the selectors in `simple-sidebar-hider.tsx`

### Technical Details

This feature works by using DOM manipulation to hide specific elements in the sidebar. It uses a MutationObserver to ensure items are hidden even if they are dynamically loaded after the initial page load.

### Implementation Details

- The solution consists of these main files:

  - `src/admin/config/sidebar-config.ts`: Contains the configuration for which items to hide
  - `src/admin/widgets/simple-sidebar-hider.tsx`: A simple React widget that hides sidebar items

- The widget is configured to run on multiple pages (product list, order list, customer list)
- The solution uses a simple approach that runs once per page load without infinite loops or multiple observers
- It includes proper cleanup to prevent memory leaks
