import { useQuery } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";
import { updateCurrencyCache } from "../../modules/store-analytics/utils/currency";

export interface Currency {
  currency_code: string;
  is_default?: boolean;
  symbol: string;
  name: string;
  decimal_digits: number;
}

export interface CurrencyOption {
  value: string;
  label: string;
  symbol: string;
  decimal_digits: number;
}

// Minimal fallback for extreme edge cases only
const EMERGENCY_FALLBACK = {
  currency_code: "USD",
  is_default: true,
  symbol: "$",
  name: "US Dollar",
  decimal_digits: 2,
};





/**
 * Hook to fetch store's configured currencies (not all available currencies)
 */
export const useAdminCurrencies = () => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ["admin", "store-currencies"],
    queryFn: async () => {
      try {
        // First, get the store's configured currencies
        const storeResponse = await sdk.client.fetch("/admin/stores") as any;
        const store = storeResponse?.stores?.[0];

        if (!store || !store.supported_currencies?.length) {
          // Return emergency fallback if no store currencies configured
          return [EMERGENCY_FALLBACK];
        }

        // Get detailed currency information from Medusa's currencies API
        const currencyResponse = await sdk.client.fetch("/admin/currencies") as any;
        const allCurrencies = currencyResponse?.currencies || [];

        // Update currency cache for other parts of the app
        if (allCurrencies.length) {
          updateCurrencyCache(allCurrencies);
        }

        // Filter to only include store's configured currencies with detailed info
        const storeCurrencies = store.supported_currencies.map((storeCurrency: any) => {
          const currencyCode = storeCurrency.currency_code.toUpperCase();

          // Find detailed info from Medusa's currencies API
          const detailedCurrency = allCurrencies.find((c: any) =>
            c.code?.toUpperCase() === currencyCode
          );

          return {
            currency_code: currencyCode,
            is_default: storeCurrency.is_default || false,
            symbol: detailedCurrency?.symbol || currencyCode,
            name: detailedCurrency?.name || currencyCode,
            decimal_digits: detailedCurrency?.decimal_digits ?? 2,
          };
        });

        return storeCurrencies;
      } catch (error) {
        console.error("Failed to fetch store currencies:", error);
        // Return emergency fallback on error
        return [EMERGENCY_FALLBACK];
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 1,
  });

  // Transform currencies to options format for dropdowns
  const currencyOptions: CurrencyOption[] = (data || []).map((currency: Currency) => ({
    value: currency.currency_code,
    label: `${currency.currency_code} (${currency.symbol})`,
    symbol: currency.symbol,
    decimal_digits: currency.decimal_digits,
  }));

  // Get default currency (is_default is already set in the main query)
  const defaultCurrency = data?.find((c: Currency) => c.is_default) || data?.[0];

  return {
    currencies: data || [],
    currencyOptions,
    defaultCurrency,
    isLoading,
    isError,
    error,
    refetch,
  };
};

/**
 * Hook to get a specific currency by code
 */
export const useAdminCurrency = (currencyCode?: string) => {
  const { currencies, isLoading } = useAdminCurrencies();
  
  const currency = currencies.find(
    (c: Currency) => c.currency_code === currencyCode?.toUpperCase()
  );

  return {
    currency,
    isLoading,
  };
};
