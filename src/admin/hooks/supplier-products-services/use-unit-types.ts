import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

export interface UnitType {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateUnitTypeInput {
  name: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdateUnitTypeInput {
  name?: string;
  description?: string;
  is_active?: boolean;
}

export interface UnitTypeFilters {
  name?: string;
  is_active?: boolean;
  limit?: number;
  offset?: number;
}

export interface UnitTypeListResponse {
  unit_types: UnitType[];
  count: number;
  limit: number;
  offset: number;
}

const QUERY_KEYS = {
  all: ["supplier-products-services", "unit-types"] as const,
  lists: () => [...QUERY_KEYS.all, "list"] as const,
  list: (filters: UnitTypeFilters) => [...QUERY_KEYS.lists(), filters] as const,
  details: () => [...QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...QUERY_KEYS.details(), id] as const,
};

export const useUnitTypes = (filters: UnitTypeFilters = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.list(filters),
    queryFn: async (): Promise<UnitTypeListResponse> => {
      const params = new URLSearchParams();

      if (filters.name) params.append("name", filters.name);
      if (filters.is_active !== undefined) params.append("is_active", filters.is_active.toString());
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const response = await fetch(
        `/admin/supplier-management/products-services/unit-types?${params.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch unit types");
      }

      return response.json();
    },
  });
};

export const useUnitType = (id: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.detail(id),
    queryFn: async (): Promise<{ unit_type: UnitType }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/unit-types/${id}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch unit type");
      }

      return response.json();
    },
    enabled: !!id,
  });
};

export const useCreateUnitType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateUnitTypeInput): Promise<{ unit_type: UnitType }> => {
      console.log("Creating unit type with data:", data);

      const response = await fetch(
        "/admin/supplier-management/products-services/unit-types",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          credentials: "include",
        }
      );

      console.log("Create unit type response status:", response.status);

      if (!response.ok) {
        const error = await response.json();
        console.error("Create unit type error:", error);
        throw new Error(error.message || "Failed to create unit type");
      }

      const result = await response.json();
      console.log("Create unit type success:", result);
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Unit type created successfully");
    },
    onError: (error: Error) => {
      console.error("Create unit type mutation error:", error);
      toast.error(error.message);
    },
  });
};

export const useUpdateUnitType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateUnitTypeInput }): Promise<{ unit_type: UnitType }> => {
      console.log("Updating unit type with data:", { id, data });

      const response = await fetch(
        `/admin/supplier-management/products-services/unit-types/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          credentials: "include",
        }
      );

      console.log("Update unit type response status:", response.status);

      if (!response.ok) {
        const error = await response.json();
        console.error("Update unit type error:", error);
        throw new Error(error.message || "Failed to update unit type");
      }

      const result = await response.json();
      console.log("Update unit type success:", result);
      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.detail(id) });
      toast.success("Unit type updated successfully");
    },
    onError: (error: Error) => {
      console.error("Update unit type mutation error:", error);
      toast.error(error.message);
    },
  });
};

export const useDeleteUnitType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ deleted: boolean; id: string }> => {
      console.log("Deleting unit type with id:", id);

      const response = await fetch(
        `/admin/supplier-management/products-services/unit-types/${id}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      console.log("Delete unit type response status:", response.status);

      if (!response.ok) {
        const error = await response.json();
        console.error("Delete unit type error:", error);
        throw new Error(error.message || "Failed to delete unit type");
      }

      const result = await response.json();
      console.log("Delete unit type success:", result);
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Unit type deleted successfully");
    },
    onError: (error: Error) => {
      console.error("Delete unit type mutation error:", error);
      toast.error(error.message);
    },
  });
};
