import { useState, useCallback, useMemo, useRef } from "react";
import {
  generateTranslationKey,
  generateTranslationKeys,
} from "../../utils/translation-utils";

// Note: Using flexible typing for Tolgee API response since structure can vary

// Define translatable fields for cancellation policies
const CANCELLATION_POLICY_TRANSLATABLE_FIELDS = [
  "name",
  "description",
] as const;

type CancellationPolicyTranslatableField =
  (typeof CANCELLATION_POLICY_TRANSLATABLE_FIELDS)[number];

interface CancellationPolicyTranslations {
  [fieldName: string]: string;
}

interface UseCancellationPolicyTranslationsReturn {
  translations: Record<string, CancellationPolicyTranslations>;
  loading: boolean;
  error: string | null;
  getTranslation: (
    fieldName: CancellationPolicyTranslatableField,
    language: string
  ) => string;
  saveTranslations: (
    language: string,
    translationsToSave: Record<string, string>
  ) => Promise<void>;
  loadTranslations: (language: string) => Promise<Record<string, string>>;
  getTranslationKeys: () => Record<string, string>;
}

/**
 * Hook for managing cancellation policy translations with Tolgee integration
 */
export const useCancellationPolicyTranslations = (
  policyId: string
): UseCancellationPolicyTranslationsReturn => {
  const [translations, setTranslations] = useState<
    Record<string, CancellationPolicyTranslations>
  >({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadingLanguagesRef = useRef<Set<string>>(new Set());

  /**
   * Get translation keys for this cancellation policy - memoized for performance
   */
  const translationKeys = useMemo(() => {
    return generateTranslationKeys(
      "cancellation_policy" as any,
      policyId,
      CANCELLATION_POLICY_TRANSLATABLE_FIELDS
    );
  }, [policyId]);

  const getTranslationKeys = useCallback(() => {
    return translationKeys;
  }, [translationKeys]);

  /**
   * Get a translation value for a specific field and language
   */
  const getTranslation = useCallback(
    (
      fieldName: CancellationPolicyTranslatableField,
      language: string
    ): string => {
      const key = generateTranslationKey(
        "cancellation_policy" as any,
        policyId,
        fieldName
      );
      return translations[language]?.[key] || "";
    },
    [translations, policyId]
  );

  /**
   * Load translations from Tolgee API for a specific language
   * Uses GET request to fetch existing translations
   */
  const loadTranslations = useCallback(
    async (language: string): Promise<Record<string, string>> => {
      // Prevent duplicate API calls for the same language
      if (loadingLanguagesRef.current.has(language)) {
        console.log(
          `🔄 Already loading translations for ${language}, skipping duplicate call`
        );
        return {};
      }

      // Skip loading for base language (English)
      if (language === "en") {
        console.log("🏠 Base language (English) - no translations to load");
        return {};
      }

      loadingLanguagesRef.current.add(language);
      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        if (!apiKey || !projectId) {
          console.warn("Tolgee API configuration missing");
          return {};
        }

        // Use the simpler tree endpoint with filter for cancellation policy keys
        const filterKeyName = `cancellation_policy.${policyId}`;
        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations/${language}?filterKeyName=${filterKeyName}`;
        console.log("🔗 Loading cancellation policy translations from:", url);
        console.log("🔍 Looking for policy:", policyId);
        console.log("🔍 Filter key name:", filterKeyName);

        const response = await fetch(url, {
          method: "GET",
          headers: {
            Accept: "application/json",
            "X-API-Key": apiKey,
          },
        });

        if (!response.ok) {
          if (response.status === 404) {
            console.log(
              `📭 No translations found for ${language} - this is normal for new languages`
            );
            return {};
          }
          throw new Error(
            `Failed to load translations: ${response.status} ${response.statusText}`
          );
        }

        const data: any = await response.json();
        console.log("📥 Raw Tolgee response:", data);

        // The response structure is: { [language]: { cancellation_policy: { [policyId]: { name: "...", description: "..." } } } }
        // We need to extract the data from the language wrapper
        const languageData = data[language] || data;
        const policyTranslations =
          languageData.cancellation_policy?.[policyId] || {};
        console.log(
          `📋 Extracted policy translations for ${policyId}:`,
          policyTranslations
        );

        // Convert to the format expected by our hook
        const formattedTranslations: Record<string, string> = {};
        Object.entries(policyTranslations).forEach(([fieldName, value]) => {
          const key = generateTranslationKey(
            "cancellation_policy" as any,
            policyId,
            fieldName
          );
          if (typeof value === "string") {
            formattedTranslations[key] = value;
            console.log(`✅ Formatted translation: ${key} = ${value}`);
          }
        });

        // Update state with the loaded translations
        setTranslations((prev) => ({
          ...prev,
          [language]: formattedTranslations,
        }));

        console.log(
          `🎯 Successfully loaded ${
            Object.keys(formattedTranslations).length
          } translations for ${language}`
        );
        return formattedTranslations;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        console.error(
          "❌ Error loading cancellation policy translations:",
          errorMessage
        );
        setError(errorMessage);
        return {};
      } finally {
        setLoading(false);
        loadingLanguagesRef.current.delete(language);
      }
    },
    [policyId]
  );

  /**
   * Save translations to Tolgee API - ONLY called on explicit form submission
   * Uses POST for upsert behavior (creates keys if they don't exist, updates if they do)
   */
  const saveTranslations = useCallback(
    async (language: string, translationsToSave: Record<string, string>) => {
      if (!translationsToSave || Object.keys(translationsToSave).length === 0) {
        console.log("No translations to save");
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        if (!apiKey || !projectId) {
          throw new Error("Tolgee API configuration missing");
        }

        console.log(
          `🚀 Saving cancellation policy translations for ${language}:`,
          translationsToSave
        );

        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;

        // Process each translation key individually using the correct Tolgee format
        for (const [key, value] of Object.entries(translationsToSave)) {
          const payload = {
            key: key,
            translations: {
              [language]: value,
            },
          };

          console.log(`📤 Saving translation for key: ${key}`, payload);

          const response = await fetch(url, {
            method: "POST", // Always use POST for upsert behavior
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
              "X-API-Key": apiKey,
            },
            body: JSON.stringify(payload),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(
              `❌ Failed to save translation for key ${key}:`,
              errorText
            );
            throw new Error(
              `HTTP ${response.status}: Failed to save translation for key ${key}. ${errorText}`
            );
          }

          console.log(`✅ Successfully saved translation for key: ${key}`);
        }

        console.log(
          "✅ All cancellation policy translations saved successfully"
        );

        // Update local state with saved translations
        setTranslations((prev) => ({
          ...prev,
          [language]: {
            ...prev[language],
            ...translationsToSave,
          },
        }));
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        console.error(
          "❌ Error saving cancellation policy translations:",
          errorMessage
        );
        setError(errorMessage);
        throw err; // Re-throw to allow form to handle the error
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return {
    translations,
    loading,
    error,
    getTranslation,
    saveTranslations,
    loadTranslations,
    getTranslationKeys,
  };
};
