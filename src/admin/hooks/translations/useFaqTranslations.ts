import { useState, useCallback, useEffect, useRef } from 'react';
import { useDestinationTranslations } from './useDestinationTranslations';

export interface FaqItem {
  question: string;
  answer: string;
}

export interface UseFaqTranslationsReturn {
  faqs: FaqItem[];
  translatedFaqs: FaqItem[];
  loading: boolean;
  error: string | null;
  setFaqs: (faqs: FaqItem[]) => void;
  setTranslatedFaqs: (faqs: FaqItem[]) => void;
  saveFaqs: (language: string) => Promise<void>;
  loadFaqs: (language: string) => Promise<void>;
  isBaseLanguage: boolean;
  hasUnsavedChanges: boolean;
  getAllLanguageTranslations: () => Record<string, FaqItem[]>;
  hasTranslationsForLanguage: (language: string) => boolean;
}

/**
 * Specialized hook for managing FAQ translations
 * Provides a simplified interface for FAQ-specific operations
 */
export const useFaqTranslations = (
  destinationId: string,
  selectedLanguage: string,
  baseFaqs: FaqItem[] = []
): UseFaqTranslationsReturn => {
  const {
    nestedObjectTranslations,
    loading,
    error,
    getNestedObjectTranslation,
    saveNestedObjectTranslations,
    loadTranslations,
    setNestedObjectTranslations
  } = useDestinationTranslations(destinationId);

  // Local state for FAQs
  const [faqs, setFaqs] = useState<FaqItem[]>(baseFaqs);
  const [translatedFaqs, setTranslatedFaqs] = useState<FaqItem[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Store all language translations locally to persist across language switches
  const allLanguageTranslations = useRef<Record<string, FaqItem[]>>({});

  // Determine if current language is base language
  const isBaseLanguage = selectedLanguage === 'en';

  // Track if we've already loaded translations for this language to prevent infinite loops
  const loadedLanguages = useRef<Set<string>>(new Set());
  const loadingLanguages = useRef<Set<string>>(new Set());

  // Simple language change handler - no API calls, just state management
  useEffect(() => {
    console.log(`❓ useFaqTranslations language changed to: ${selectedLanguage}`);
    console.log(`🏠 isBaseLanguage: ${isBaseLanguage}`);

    if (!isBaseLanguage) {
      // Check if we have local translations for this language
      const localTranslations = allLanguageTranslations.current[selectedLanguage];
      console.log(`💾 Local FAQ translations for ${selectedLanguage}:`, localTranslations);

      if (localTranslations && localTranslations.length > 0) {
        console.log(`🔄 Loading local FAQ translations for ${selectedLanguage}:`, localTranslations);
        setTranslatedFaqs(localTranslations);
        setHasUnsavedChanges(false);
      } else {
        // Initialize with base language values if no translations exist
        console.log(`🆕 No FAQ translations found for ${selectedLanguage}, initializing with base values`);
        setTranslatedFaqs(baseFaqs);
        if (baseFaqs.length > 0) {
          allLanguageTranslations.current[selectedLanguage] = baseFaqs;
        }
        setHasUnsavedChanges(false);
      }
    } else {
      // For base language, clear translated FAQs
      console.log(`🏠 Base language selected, clearing translated FAQs`);
      setTranslatedFaqs([]);
      setHasUnsavedChanges(false);
    }
  }, [selectedLanguage, isBaseLanguage]); // Only depend on language changes

  // Update local state when base FAQs change
  useEffect(() => {
    console.log(`📝 Base FAQs changed:`, baseFaqs);
    setFaqs(baseFaqs);
  }, [baseFaqs]);

  // Listen for changes in nestedObjectTranslations state (when translations are loaded from API)
  useEffect(() => {
    console.log(`🔄 nestedObjectTranslations effect triggered for ${selectedLanguage}`);
    console.log(`📊 Current nestedObjectTranslations:`, nestedObjectTranslations);
    console.log(`🎯 Looking for ${selectedLanguage} FAQs in:`, nestedObjectTranslations[selectedLanguage]);

    if (!isBaseLanguage && nestedObjectTranslations[selectedLanguage]?.faqs) {
      const loadedFaqs = nestedObjectTranslations[selectedLanguage].faqs;
      console.log(`🔄 API translations loaded for ${selectedLanguage} FAQs:`, loadedFaqs);
      console.log(`📏 Loaded FAQs length:`, loadedFaqs.length);

      if (loadedFaqs && loadedFaqs.length > 0) {
        // Only update if we don't already have user-modified translations
        const currentLocal = allLanguageTranslations.current[selectedLanguage];
        const hasUserModifications = hasUnsavedChanges;

        console.log(`🔍 Update check:`, {
          hasUserModifications,
          currentLocal,
          currentLocalLength: currentLocal?.length || 0,
          baseFaqsLength: baseFaqs.length,
          shouldUpdate: !hasUserModifications && (!currentLocal || currentLocal.length === 0 || JSON.stringify(currentLocal) === JSON.stringify(baseFaqs))
        });

        // Always update with API translations if they exist and are different from current
        const isDifferent = !currentLocal ||
          currentLocal.length !== loadedFaqs.length ||
          JSON.stringify(currentLocal) !== JSON.stringify(loadedFaqs);

        if (isDifferent) {
          console.log(`✅ Updating FAQ translations from API for ${selectedLanguage}:`, loadedFaqs);
          setTranslatedFaqs(loadedFaqs);
          allLanguageTranslations.current[selectedLanguage] = loadedFaqs;
          setHasUnsavedChanges(false);
        } else {
          console.log(`ℹ️ FAQ translations for ${selectedLanguage} are already up to date`);
        }
      } else {
        console.log(`⚠️ No valid FAQs found for ${selectedLanguage}`);
      }
    } else {
      console.log(`ℹ️ No FAQ data found for ${selectedLanguage} or is base language`);
    }
  }, [nestedObjectTranslations, selectedLanguage, isBaseLanguage, hasUnsavedChanges, baseFaqs]);

  // Track changes to translated FAQs
  useEffect(() => {
    if (!isBaseLanguage) {
      // Store current translations in local cache
      allLanguageTranslations.current[selectedLanguage] = translatedFaqs;
      console.log(`💾 Stored FAQ translations for ${selectedLanguage}:`, translatedFaqs);
      console.log(`📊 All FAQ translations cache:`, allLanguageTranslations.current);
    }
  }, [translatedFaqs, selectedLanguage, isBaseLanguage]);

  // Custom setter for translated FAQs that marks as unsaved
  const setTranslatedFaqsWithUnsaved = useCallback((newFaqs: FaqItem[]) => {
    console.log(`📝 Setting translated FAQs for ${selectedLanguage}:`, newFaqs);
    setTranslatedFaqs(newFaqs);
    setHasUnsavedChanges(true);
    
    // Update local cache
    allLanguageTranslations.current[selectedLanguage] = newFaqs;
  }, [selectedLanguage]);

  // Save FAQs for current language
  const saveFaqs = useCallback(async (language: string) => {
    if (isBaseLanguage) {
      console.log('❌ Cannot save FAQ translations for base language');
      return;
    }

    const faqsToSave = language === selectedLanguage ? translatedFaqs : allLanguageTranslations.current[language];
    
    if (!faqsToSave || faqsToSave.length === 0) {
      console.log(`ℹ️ No FAQ translations to save for ${language}`);
      return;
    }

    try {
      console.log(`💾 Saving FAQ translations for ${language}:`, faqsToSave);
      await saveNestedObjectTranslations(language, 'faqs', faqsToSave);
      
      // Update the hook's state to reflect the saved translations
      setNestedObjectTranslations(prev => ({
        ...prev,
        [language]: {
          ...prev[language],
          faqs: faqsToSave
        }
      }));
      
      setHasUnsavedChanges(false);
      console.log(`✅ FAQ translations saved successfully for ${language}`);
    } catch (error) {
      console.error(`❌ Error saving FAQ translations for ${language}:`, error);
      throw error;
    }
  }, [isBaseLanguage, selectedLanguage, translatedFaqs, saveNestedObjectTranslations, setNestedObjectTranslations]);

  // Load FAQs for current language
  const loadFaqs = useCallback(async (language: string) => {
    if (isBaseLanguage) {
      return;
    }

    try {
      await loadTranslations(language);
      console.log(`FAQ translations loaded for language: ${language}`);
    } catch (error) {
      console.error('Error loading FAQ translations:', error);
      throw error;
    }
  }, [isBaseLanguage, loadTranslations]);

  // Helper function to get all language translations
  const getAllLanguageTranslations = useCallback(() => {
    const result = { ...allLanguageTranslations.current };
    console.log('📋 getAllLanguageTranslations called, returning:', result);
    return result;
  }, []);

  // Helper function to check if a language has translations
  const hasTranslationsForLanguage = useCallback((language: string) => {
    const translations = allLanguageTranslations.current[language];
    return translations && translations.length > 0;
  }, []);

  return {
    faqs,
    translatedFaqs,
    loading,
    error,
    setFaqs,
    setTranslatedFaqs: setTranslatedFaqsWithUnsaved,
    saveFaqs,
    loadFaqs,
    isBaseLanguage,
    hasUnsavedChanges,
    getAllLanguageTranslations,
    hasTranslationsForLanguage
  };
};
