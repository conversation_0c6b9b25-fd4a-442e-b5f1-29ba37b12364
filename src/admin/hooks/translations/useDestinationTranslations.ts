import { useState, useCallback, useMemo } from 'react';
import {
  generateTranslationKey,
  generateTranslationKeys,
  generateArrayFieldTranslationKeys,
  arrayTranslationsToOrderedArray,
  orderedArrayToTranslations,
  ArrayFieldType,
  nestedObjectTranslationsToOrderedArray,
  orderedNestedObjectArrayToTranslations,
  NestedObjectFieldType
} from '../../utils/translation-utils';

// Tolgee tree response interface
interface TolgeeTreeResponse {
  [language: string]: {
    destination?: {
      [destinationId: string]: {
        [fieldName: string]: string;
      };
    };
    hotel?: {
      [hotelId: string]: {
        [fieldName: string]: string;
      };
    };
  };
}

// Define translatable fields for destinations
const DESTINATION_TRANSLATABLE_FIELDS = [
  'name',
  'description',
  'location'
] as const;

// Define translatable array fields for destinations
const DESTINATION_TRANSLATABLE_ARRAY_FIELDS = [
  'tags'
] as const;

// Define translatable nested object fields for destinations
const DESTINATION_TRANSLATABLE_NESTED_OBJECT_FIELDS = [
  'faqs'
] as const;

export type DestinationTranslatableField = typeof DESTINATION_TRANSLATABLE_FIELDS[number];
export type DestinationTranslatableArrayField = typeof DESTINATION_TRANSLATABLE_ARRAY_FIELDS[number];
export type DestinationTranslatableNestedObjectField = typeof DESTINATION_TRANSLATABLE_NESTED_OBJECT_FIELDS[number];

export interface DestinationTranslations {
  [language: string]: {
    [key: string]: string;
  };
}

export interface DestinationArrayTranslations {
  [language: string]: {
    [fieldName: string]: string[];
  };
}

export interface DestinationNestedObjectTranslations {
  [language: string]: {
    [fieldName: string]: Array<{ question: string; answer: string }>;
  };
}

export interface UseDestinationTranslationsReturn {
  translations: DestinationTranslations;
  arrayTranslations: DestinationArrayTranslations;
  nestedObjectTranslations: DestinationNestedObjectTranslations;
  loading: boolean;
  error: string | null;
  getTranslation: (fieldName: DestinationTranslatableField, language: string) => string;
  getArrayTranslation: (fieldName: DestinationTranslatableArrayField, language: string) => string[];
  getNestedObjectTranslation: (fieldName: DestinationTranslatableNestedObjectField, language: string) => Array<{ question: string; answer: string }>;
  saveTranslations: (language: string, translationsToSave: Record<string, string>, strictMode?: boolean) => Promise<void>;
  saveArrayTranslations: (language: string, fieldName: DestinationTranslatableArrayField, arrayValues: string[]) => Promise<void>;
  saveNestedObjectTranslations: (language: string, fieldName: DestinationTranslatableNestedObjectField, objectValues: Array<{ question: string; answer: string }>) => Promise<void>;
  loadTranslations: (language: string) => Promise<void>;
  getTranslationKeys: () => Record<string, string>;
  getArrayTranslationKeys: (fieldName: DestinationTranslatableArrayField, arrayLength: number) => Record<number, string>;
  removeTranslations: (language: string) => void;
  clearError: () => void;
  setArrayTranslations: React.Dispatch<React.SetStateAction<DestinationArrayTranslations>>;
}

/**
 * Hook for managing destination translations with Tolgee integration
 */
export const useDestinationTranslations = (destinationId: string): UseDestinationTranslationsReturn => {
  const [translations, setTranslations] = useState<DestinationTranslations>({});
  const [arrayTranslations, setArrayTranslations] = useState<DestinationArrayTranslations>({});
  const [nestedObjectTranslations, setNestedObjectTranslations] = useState<DestinationNestedObjectTranslations>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Get translation keys for this destination - memoized for performance
   */
  const translationKeys = useMemo(() => {
    return generateTranslationKeys('destination', destinationId, DESTINATION_TRANSLATABLE_FIELDS);
  }, [destinationId]);

  const getTranslationKeys = useCallback(() => {
    return translationKeys;
  }, [translationKeys]);

  /**
   * Get a translation value for a specific field and language
   */
  const getTranslation = useCallback((fieldName: DestinationTranslatableField, language: string): string => {
    const key = generateTranslationKey('destination', destinationId, fieldName);
    return translations[language]?.[key] || '';
  }, [translations, destinationId]);

  /**
   * Get array translation keys for a specific field and array length
   */
  const getArrayTranslationKeys = useCallback((fieldName: DestinationTranslatableArrayField, arrayLength: number) => {
    return generateArrayFieldTranslationKeys('destination', destinationId, fieldName as ArrayFieldType, arrayLength);
  }, [destinationId]);

  /**
   * Get array translation values for a specific field and language
   */
  const getArrayTranslation = useCallback((fieldName: DestinationTranslatableArrayField, language: string): string[] => {
    console.log(`🔍 getArrayTranslation called for ${fieldName} in ${language}`);
    console.log(`📊 Current arrayTranslations state:`, arrayTranslations);
    console.log(`🎯 Looking for: arrayTranslations[${language}]?.[${fieldName}]`);

    const result = arrayTranslations[language]?.[fieldName] || [];
    console.log(`📤 getArrayTranslation returning:`, result);

    return result;
  }, [arrayTranslations]);

  /**
   * Get nested object translation values for a specific field and language
   */
  const getNestedObjectTranslation = useCallback((fieldName: DestinationTranslatableNestedObjectField, language: string): Array<{ question: string; answer: string }> => {
    console.log(`🔍 getNestedObjectTranslation called for ${fieldName} in ${language}`);
    console.log(`📊 Current nestedObjectTranslations state:`, nestedObjectTranslations);
    console.log(`🎯 Looking for: nestedObjectTranslations[${language}]?.[${fieldName}]`);

    const result = nestedObjectTranslations[language]?.[fieldName] || [];
    console.log(`📤 getNestedObjectTranslation returning:`, result);

    return result;
  }, [nestedObjectTranslations]);



  /**
   * Save translations to Tolgee API - ONLY called on explicit form submission
   * Uses POST for upsert behavior (creates keys if they don't exist, updates if they do)
   * Alternative: Use PUT method to only update existing keys (will error if key doesn't exist)
   */
  const saveTranslations = useCallback(async (language: string, translationsToSave: Record<string, string>, strictMode = false) => {
    if (!translationsToSave || Object.keys(translationsToSave).length === 0) {
      console.log('No translations to save');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
      const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

      if (!apiKey || !projectId) {
        throw new Error('Tolgee API configuration missing');
      }

      // Save translations one by one using the correct API format
      const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;
      console.log('Saving translations to:', url);
      console.log('Translations to save:', translationsToSave);

      // Process each translation key individually
      for (const [keyName, value] of Object.entries(translationsToSave)) {
        const payload = {
          key: keyName,
          translations: {
            [language]: value
          }
        };

        console.log(`Saving translation for key: ${keyName}`, payload);

        const response = await fetch(url, {
          method: strictMode ? 'PUT' : 'POST', // POST for upsert, PUT for strict update only
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-API-Key': apiKey,
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to save translation for key ${keyName}:`, errorText);
          throw new Error(`HTTP ${response.status}: Failed to save translation for key ${keyName}. ${errorText}`);
        }

        console.log(`Successfully saved translation for key: ${keyName}`);
      }

      console.log(`Using ${strictMode ? 'PUT (strict update)' : 'POST (upsert)'} method for translations`);
      console.log(`All translations saved for destination ${destinationId} in language ${language}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save translations';
      setError(errorMessage);
      console.error('Error saving translations:', err);
    } finally {
      setLoading(false);
    }
  }, [destinationId]);

  /**
   * Save array translations to Tolgee API
   */
  const saveArrayTranslations = useCallback(async (
    language: string,
    fieldName: DestinationTranslatableArrayField,
    arrayValues: string[]
  ) => {
    console.group(`🏷️ saveArrayTranslations called for ${fieldName} in ${language}`);
    console.log(`📋 Input parameters:`, {
      language,
      fieldName,
      arrayValues,
      destinationId,
      arrayLength: arrayValues?.length || 0
    });
    console.log(`📊 Current arrayTranslations state:`, arrayTranslations);
    console.log(`🎯 Current state for this language/field:`, arrayTranslations[language]?.[fieldName]);

    // Allow saving empty arrays to clear all translations
    if (!arrayValues) {
      console.log('❌ arrayValues is null/undefined');
      return;
    }

    // Handle empty array case (clear all translations)
    if (arrayValues.length === 0) {
      console.log(`🗑️ Empty array provided - clearing all ${fieldName} translations for ${language}`);

      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        if (!apiKey || !projectId) {
          throw new Error('Tolgee API configuration missing');
        }

        // Get current translations to see what keys need to be deleted
        const currentTranslations = arrayTranslations[language]?.[fieldName] || [];
        const currentLength = currentTranslations.length;

        console.log(`🗑️ Clearing ${currentLength} existing ${fieldName} translations`);

        // Clear all existing translations
        for (let i = 0; i < currentLength; i++) {
          const keyToClear = `destination.${destinationId}.${fieldName}.${i}`;
          try {
            console.log(`🗑️ Clearing translation for key: ${keyToClear}`);

            const clearUrl = `https://app.tolgee.io/v2/projects/${projectId}/translations`;
            const clearPayload = {
              key: keyToClear,
              translations: {
                [language]: null // Set to null to clear the translation
              }
            };

            const clearResponse = await fetch(clearUrl, {
              method: 'POST',
              headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(clearPayload)
            });

            if (clearResponse.ok) {
              console.log(`✅ Cleared translation: ${keyToClear}`);
            } else {
              console.warn(`⚠️ Failed to clear translation ${keyToClear}: ${clearResponse.status}`);
            }
          } catch (clearError) {
            console.warn(`⚠️ Error clearing translation ${keyToClear}:`, clearError);
          }
        }

        // Update local state to empty array
        setArrayTranslations(prev => ({
          ...prev,
          [language]: {
            ...prev[language],
            [fieldName]: []
          }
        }));

        console.log(`✅ Cleared all ${fieldName} translations for ${language}`);
        return;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to clear array translations';
        setError(errorMessage);
        console.error('Error clearing array translations:', err);
        throw err;
      } finally {
        setLoading(false);
      }
    }

    setLoading(true);
    setError(null);

    try {
      const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
      const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

      console.log('🔑 API Configuration:', {
        hasApiKey: !!apiKey,
        hasProjectId: !!projectId,
        projectId: projectId
      });

      if (!apiKey || !projectId) {
        throw new Error('Tolgee API configuration missing');
      }

      // STEP 1: Clean up old translation keys first (if array got shorter)
      console.log(`🧹 Step 1: Cleaning up old ${fieldName} translations for ${language}...`);

      // Get current translations to see how many keys exist
      const currentTranslations = arrayTranslations[language]?.[fieldName] || [];
      const currentLength = currentTranslations.length;
      const newLength = arrayValues.length;

      console.log(`📊 Array length change: ${currentLength} → ${newLength}`);

      // If the new array is shorter, we need to delete the orphaned keys
      if (currentLength > newLength) {
        const keysToDelete: string[] = [];

        // Generate keys for indices that will be orphaned
        for (let i = newLength; i < currentLength; i++) {
          const keyToDelete = `destination.${destinationId}.${fieldName}.${i}`;
          keysToDelete.push(keyToDelete);
        }

        console.log(`🗑️ Deleting ${keysToDelete.length} orphaned keys:`, keysToDelete);

        // Clear orphaned translations by setting them to null
        // Since Tolgee DELETE endpoint requires key IDs (not key names),
        // we'll use the translation update endpoint to clear the values
        for (const keyToDelete of keysToDelete) {
          try {
            console.log(`🗑️ Clearing translation for key: ${keyToDelete}`);

            // Use the translations endpoint to set the value to null/empty
            const clearUrl = `https://app.tolgee.io/v2/projects/${projectId}/translations`;
            const clearPayload = {
              key: keyToDelete,
              translations: {
                [language]: null // Set to null to clear the translation
              }
            };

            const clearResponse = await fetch(clearUrl, {
              method: 'POST',
              headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(clearPayload)
            });

            if (clearResponse.ok) {
              console.log(`✅ Successfully cleared orphaned translation: ${keyToDelete}`);
            } else {
              console.warn(`⚠️ Failed to clear translation ${keyToDelete}: ${clearResponse.status} ${clearResponse.statusText}`);
            }
          } catch (clearError) {
            console.warn(`⚠️ Error clearing translation ${keyToDelete}:`, clearError);
            // Continue with other operations even if one fails
          }
        }
      } else if (currentLength === 0) {
        console.log(`ℹ️ No existing ${fieldName} translations to clean up (first time saving)`);
      } else {
        console.log(`ℹ️ Array grew or stayed same size (${currentLength} → ${newLength}), no cleanup needed`);
      }

      // STEP 2: Save new translations
      console.log(`💾 Step 2: Saving ${arrayValues.length} new ${fieldName} translations...`);

      // Convert array to translation key-value pairs
      const translationsToSave = orderedArrayToTranslations(
        arrayValues,
        'destination',
        destinationId,
        fieldName as ArrayFieldType
      );

      const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;
      console.log('🌐 Saving array translations to:', url);
      console.log('📝 Array translations to save:', translationsToSave);
      console.log('🎯 Number of keys to save:', Object.keys(translationsToSave).length);

      // Process each translation key individually
      let successCount = 0;
      let errorCount = 0;

      for (const [keyName, value] of Object.entries(translationsToSave)) {
        const payload = {
          key: keyName,
          translations: {
            [language]: value
          }
        };

        console.log(`🚀 [${successCount + errorCount + 1}/${Object.keys(translationsToSave).length}] Saving array translation for key: ${keyName}`, payload);

        try {
          const response = await fetch(url, {
            method: 'POST', // Use POST for upsert behavior
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'X-API-Key': apiKey,
            },
            body: JSON.stringify(payload)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ Failed to save array translation for key ${keyName}:`, {
              status: response.status,
              statusText: response.statusText,
              errorText
            });
            errorCount++;
            throw new Error(`HTTP ${response.status}: Failed to save array translation for key ${keyName}. ${errorText}`);
          }

          const responseData = await response.json();
          console.log(`✅ Successfully saved array translation for key: ${keyName}`, responseData);
          successCount++;
        } catch (fetchError) {
          console.error(`❌ Network/API error for key ${keyName}:`, fetchError);
          errorCount++;
          throw fetchError;
        }
      }

      console.log(`📊 Array translation save summary: ${successCount} successful, ${errorCount} failed`);

      // Update local state
      setArrayTranslations(prev => ({
        ...prev,
        [language]: {
          ...prev[language],
          [fieldName]: arrayValues
        }
      }));

      console.log(`✅ All array translations saved for destination ${destinationId}, field ${fieldName} in language ${language}`);
      console.log(`🔄 Final arrayTranslations state:`, arrayTranslations);
      console.groupEnd();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save array translations';
      setError(errorMessage);
      console.error('❌ Error saving array translations:', err);
      console.groupEnd();
      throw err; // Re-throw to ensure calling code knows about the error
    } finally {
      setLoading(false);
    }
  }, [destinationId, arrayTranslations]);

  /**
   * Save nested object translations to Tolgee API (for FAQs)
   */
  const saveNestedObjectTranslations = useCallback(async (
    language: string,
    fieldName: DestinationTranslatableNestedObjectField,
    objectValues: Array<{ question: string; answer: string }>
  ) => {
    console.group(`❓ saveNestedObjectTranslations called for ${fieldName} in ${language}`);
    console.log(`📋 Input parameters:`, {
      language,
      fieldName,
      objectValues,
      destinationId,
      objectLength: objectValues?.length || 0
    });
    console.log(`📊 Current nestedObjectTranslations state:`, nestedObjectTranslations);
    console.log(`🎯 Current state for this language/field:`, nestedObjectTranslations[language]?.[fieldName]);

    // Allow saving empty arrays to clear all translations
    if (!objectValues) {
      console.log('❌ objectValues is null/undefined');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
      const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

      console.log('🔑 API Configuration:', {
        hasApiKey: !!apiKey,
        hasProjectId: !!projectId,
        projectId: projectId
      });

      if (!apiKey || !projectId) {
        throw new Error('Tolgee API configuration missing');
      }

      // STEP 1: Clean up old translation keys first (if array got shorter)
      console.log(`🧹 Step 1: Cleaning up old ${fieldName} translations for ${language}...`);

      // Get current translations to see how many keys exist
      const currentTranslations = nestedObjectTranslations[language]?.[fieldName] || [];
      const currentLength = currentTranslations.length;
      const newLength = objectValues.length;

      console.log(`📊 Object array length change: ${currentLength} → ${newLength}`);

      // If the new array is shorter, we need to delete the orphaned keys
      if (currentLength > newLength) {
        const keysToDelete: string[] = [];

        // Generate keys for indices that will be orphaned (both question and answer)
        for (let i = newLength; i < currentLength; i++) {
          const questionKey = `destination.${destinationId}.${fieldName}.${i}.question`;
          const answerKey = `destination.${destinationId}.${fieldName}.${i}.answer`;
          keysToDelete.push(questionKey, answerKey);
        }

        console.log(`🗑️ Deleting ${keysToDelete.length} orphaned keys:`, keysToDelete);

        // Clear orphaned translations by setting them to null
        for (const keyToDelete of keysToDelete) {
          try {
            console.log(`🗑️ Clearing translation for key: ${keyToDelete}`);

            const clearUrl = `https://app.tolgee.io/v2/projects/${projectId}/translations`;
            const clearPayload = {
              key: keyToDelete,
              translations: {
                [language]: null // Set to null to clear the translation
              }
            };

            const clearResponse = await fetch(clearUrl, {
              method: 'POST',
              headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(clearPayload)
            });

            if (clearResponse.ok) {
              console.log(`✅ Successfully cleared orphaned translation: ${keyToDelete}`);
            } else {
              console.warn(`⚠️ Failed to clear translation ${keyToDelete}: ${clearResponse.status} ${clearResponse.statusText}`);
            }
          } catch (clearError) {
            console.warn(`⚠️ Error clearing translation ${keyToDelete}:`, clearError);
            // Continue with other operations even if one fails
          }
        }
      } else if (currentLength === 0) {
        console.log(`ℹ️ No existing ${fieldName} translations to clean up (first time saving)`);
      } else {
        console.log(`ℹ️ Array grew or stayed same size (${currentLength} → ${newLength}), no cleanup needed`);
      }

      // STEP 2: Save new translations
      console.log(`💾 Step 2: Saving ${objectValues.length} new ${fieldName} translations...`);

      // Convert object array to translation key-value pairs
      const translationsToSave = orderedNestedObjectArrayToTranslations(
        objectValues,
        'destination',
        destinationId,
        fieldName as NestedObjectFieldType
      );

      const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;
      console.log('🌐 Saving nested object translations to:', url);
      console.log('📝 Nested object translations to save:', translationsToSave);
      console.log('🎯 Number of keys to save:', Object.keys(translationsToSave).length);

      // Process each translation key individually
      let successCount = 0;
      let errorCount = 0;

      for (const [keyName, value] of Object.entries(translationsToSave)) {
        const payload = {
          key: keyName,
          translations: {
            [language]: value
          }
        };

        console.log(`🚀 [${successCount + errorCount + 1}/${Object.keys(translationsToSave).length}] Saving nested object translation for key: ${keyName}`, payload);

        try {
          const response = await fetch(url, {
            method: 'POST', // Use POST for upsert behavior
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'X-API-Key': apiKey,
            },
            body: JSON.stringify(payload)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ Failed to save nested object translation for key ${keyName}:`, {
              status: response.status,
              statusText: response.statusText,
              errorText
            });
            errorCount++;
            throw new Error(`HTTP ${response.status}: Failed to save nested object translation for key ${keyName}. ${errorText}`);
          }

          const responseData = await response.json();
          console.log(`✅ Successfully saved nested object translation for key: ${keyName}`, responseData);
          successCount++;
        } catch (fetchError) {
          console.error(`❌ Network/API error for key ${keyName}:`, fetchError);
          errorCount++;
          throw fetchError;
        }
      }

      console.log(`📊 Nested object translation save summary: ${successCount} successful, ${errorCount} failed`);

      // Update local state
      setNestedObjectTranslations(prev => ({
        ...prev,
        [language]: {
          ...prev[language],
          [fieldName]: objectValues
        }
      }));

      console.log(`✅ All nested object translations saved for destination ${destinationId}, field ${fieldName} in language ${language}`);
      console.log(`🔄 Final nestedObjectTranslations state:`, nestedObjectTranslations);
      console.groupEnd();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save nested object translations';
      setError(errorMessage);
      console.error('❌ Error saving nested object translations:', err);
      console.groupEnd();
      throw err; // Re-throw to ensure calling code knows about the error
    } finally {
      setLoading(false);
    }
  }, [destinationId, nestedObjectTranslations]);

  /**
   * Load translations from Tolgee API - ONLY makes GET requests to fetch existing translations
   */
  const loadTranslations = useCallback(async (language: string) => {
    console.log(`\n🌐 ===== LOAD TRANSLATIONS START =====`);
    console.log(`🔄 loadTranslations called for language: ${language}`);
    console.log(`🏠 destinationId: ${destinationId}`);

    setLoading(true);
    setError(null);

    try {
      const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
      const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

      console.log(`🔑 API Key exists: ${!!apiKey}`);
      console.log(`🆔 Project ID: ${projectId}`);

      if (!apiKey || !projectId) {
        throw new Error('Tolgee API configuration missing');
      }

      const keysList = Object.values(getTranslationKeys());
      console.log(`🔑 Translation keys to load:`, keysList);

      if (keysList.length === 0) {
        console.log('No translation keys to load');
        console.log(`🌐 ===== LOAD TRANSLATIONS END (NO KEYS) =====\n`);
        return;
      }

      // Use the simpler tree endpoint with filter for destination keys
      const filterKeyName = `destination.${destinationId}`;
      const url = `https://app.tolgee.io/v2/projects/${projectId}/translations/${language}?filterKeyName=${filterKeyName}`;
      console.log('🔗 Loading existing translations from:', url);
      console.log('🔍 Looking for destination:', destinationId);
      console.log('🔍 Filter key name:', filterKeyName);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'X-API-Key': apiKey,
        }
      });

      console.log(`📡 API Response status: ${response.status}`);
      console.log(`📡 API Response ok: ${response.ok}`);

      if (!response.ok) {
        if (response.status === 404) {
          console.log(`ℹ️ No translations found for language ${language} (404)`);
          // Set empty translations for this language and continue
          setTranslations(prev => ({ ...prev, [language]: {} }));
          console.log(`🌐 ===== LOAD TRANSLATIONS END (404) =====\n`);
          return;
        }
        throw new Error(`Load failed: ${response.status}`);
      }

      // Parse the tree response format
      const responseData: TolgeeTreeResponse = await response.json();
      console.log(`📥 Raw Tolgee response for ${language}:`, responseData);
      console.log(`📥 Response data type:`, typeof responseData);
      console.log(`📥 Response data keys:`, Object.keys(responseData || {}));

      // Extract translations from the nested tree structure
      const transformedData: Record<string, string> = {};
      const transformedArrayData: Record<string, string[]> = {};
      const transformedNestedObjectData: Record<string, Array<{ question: string; answer: string }>> = {};

      // Navigate through the nested structure: language -> destination -> destinationId -> field
      if (responseData[language]?.destination?.[destinationId]) {
        const destinationTranslations = responseData[language].destination[destinationId];
        console.log(`🔍 Raw destination translations for ${destinationId}:`, destinationTranslations);

        // Convert nested structure to flat keys for regular fields
        DESTINATION_TRANSLATABLE_FIELDS.forEach(fieldName => {
          if (destinationTranslations[fieldName]) {
            const fullKey = `destination.${destinationId}.${fieldName}`;
            transformedData[fullKey] = destinationTranslations[fieldName];
            console.log(`Found translation for ${fullKey}: ${destinationTranslations[fieldName]}`);
          }
        });

        // Handle array fields (like tags)
        DESTINATION_TRANSLATABLE_ARRAY_FIELDS.forEach(fieldName => {
          // Check if the field exists as a nested object (new format)
          if (destinationTranslations[fieldName] && typeof destinationTranslations[fieldName] === 'object') {
            console.log(`📦 Found nested ${fieldName} object:`, destinationTranslations[fieldName]);

            // Convert nested object to ordered array
            const nestedObject = destinationTranslations[fieldName] as Record<string, string>;
            const orderedArray: string[] = [];

            // Sort by numeric keys and extract values, filtering out null/empty values
            Object.keys(nestedObject)
              .map(key => parseInt(key, 10))
              .filter(index => !isNaN(index))
              .sort((a, b) => a - b)
              .forEach(index => {
                const value = nestedObject[index.toString()];
                // Only add non-null, non-empty values
                if (value !== null && value !== undefined && value !== '') {
                  orderedArray.push(value);
                }
              });

            if (orderedArray.length > 0) {
              transformedArrayData[fieldName] = orderedArray;
              console.log(`✅ Converted nested ${fieldName} to array:`, orderedArray);

              // Also add to flat structure for consistency
              orderedArray.forEach((value, index) => {
                const fullKey = `destination.${destinationId}.${fieldName}.${index}`;
                transformedData[fullKey] = value;
              });
            }
          } else {
            // Handle flat key format (legacy format)
            const arrayKeys = Object.keys(destinationTranslations).filter(key =>
              key.startsWith(`${fieldName}.`)
            );

            if (arrayKeys.length > 0) {
              console.log(`📋 Found flat ${fieldName} keys:`, arrayKeys);

              // Convert array field translations to ordered array, filtering out null values
              const arrayFieldTranslations: Record<string, string> = {};
              arrayKeys.forEach(key => {
                const value = destinationTranslations[key];
                // Only include non-null, non-empty values
                if (value !== null && value !== undefined && value !== '') {
                  const fullKey = `destination.${destinationId}.${key}`;
                  arrayFieldTranslations[fullKey] = value;
                  transformedData[fullKey] = value; // Also add to flat structure
                }
              });

              // Convert to ordered array
              const orderedArray = arrayTranslationsToOrderedArray(
                arrayFieldTranslations,
                'destination',
                destinationId,
                fieldName as ArrayFieldType
              );

              if (orderedArray.length > 0) {
                transformedArrayData[fieldName] = orderedArray;
                console.log(`✅ Converted flat ${fieldName} to array:`, orderedArray);
              }
            }
          }
        });

        // Handle nested object fields (like FAQs)
        DESTINATION_TRANSLATABLE_NESTED_OBJECT_FIELDS.forEach(fieldName => {
          // Check if the field exists as a nested object
          if (destinationTranslations[fieldName] && typeof destinationTranslations[fieldName] === 'object') {
            console.log(`📦 Found nested ${fieldName} object:`, destinationTranslations[fieldName]);

            // Convert nested object to ordered array of FAQ objects
            const nestedObject = destinationTranslations[fieldName] as Record<string, any>;
            const orderedFaqs: Array<{ question: string; answer: string }> = [];

            console.log(`🔍 Processing ${fieldName} nested object keys:`, Object.keys(nestedObject));

            // Sort by numeric keys and extract question/answer pairs
            Object.keys(nestedObject)
              .map(key => parseInt(key, 10))
              .filter(index => !isNaN(index))
              .sort((a, b) => a - b)
              .forEach(index => {
                const faqData = nestedObject[index.toString()];
                console.log(`🔍 Processing FAQ index ${index}:`, faqData);

                if (faqData && typeof faqData === 'object' && faqData.question && faqData.answer) {
                  // Filter out null values and ensure we have valid strings
                  if (faqData.question !== null && faqData.answer !== null &&
                      faqData.question.trim() !== '' && faqData.answer.trim() !== '') {
                    orderedFaqs.push({
                      question: faqData.question,
                      answer: faqData.answer
                    });
                    console.log(`✅ Added FAQ ${index}:`, { question: faqData.question, answer: faqData.answer });
                  } else {
                    console.log(`⚠️ Skipping FAQ ${index} - null or empty values:`, faqData);
                  }
                } else {
                  console.log(`⚠️ Skipping FAQ ${index} - invalid structure:`, faqData);
                }
              });

            if (orderedFaqs.length > 0) {
              transformedNestedObjectData[fieldName] = orderedFaqs;
              console.log(`✅ Converted nested ${fieldName} to FAQ array:`, orderedFaqs);

              // Also add to flat structure for consistency
              orderedFaqs.forEach((faq, index) => {
                const questionKey = `destination.${destinationId}.${fieldName}.${index}.question`;
                const answerKey = `destination.${destinationId}.${fieldName}.${index}.answer`;
                transformedData[questionKey] = faq.question;
                transformedData[answerKey] = faq.answer;
              });
            } else {
              console.log(`⚠️ No valid FAQs found in ${fieldName} object`);
            }
          } else {
            // Handle flat key format (legacy format) for nested objects
            const nestedObjectKeys = Object.keys(destinationTranslations).filter(key =>
              key.startsWith(`${fieldName}.`) && (key.includes('.question') || key.includes('.answer'))
            );

            if (nestedObjectKeys.length > 0) {
              console.log(`📋 Found flat ${fieldName} keys:`, nestedObjectKeys);

              // Convert nested object field translations to ordered array
              const nestedObjectFieldTranslations: Record<string, string> = {};
              nestedObjectKeys.forEach(key => {
                const value = destinationTranslations[key];
                if (value !== null && value !== undefined && value !== '') {
                  const fullKey = `destination.${destinationId}.${key}`;
                  nestedObjectFieldTranslations[fullKey] = value;
                  transformedData[fullKey] = value; // Also add to flat structure
                }
              });

              // Convert to ordered array of FAQ objects
              const orderedFaqs = nestedObjectTranslationsToOrderedArray(
                nestedObjectFieldTranslations,
                'destination',
                destinationId,
                fieldName as NestedObjectFieldType
              );

              if (orderedFaqs.length > 0) {
                transformedNestedObjectData[fieldName] = orderedFaqs;
                console.log(`✅ Converted flat ${fieldName} to FAQ array:`, orderedFaqs);
              }
            }
          }
        });
      }

      setTranslations(prev => ({
        ...prev,
        [language]: transformedData
      }));

      setArrayTranslations(prev => {
        const newState = {
          ...prev,
          [language]: transformedArrayData
        };
        console.log(`🔄 Setting arrayTranslations state:`, newState);
        return newState;
      });

      setNestedObjectTranslations(prev => {
        const newState = {
          ...prev,
          [language]: transformedNestedObjectData
        };
        console.log(`\n🔥 ===== SETTING NESTED OBJECT TRANSLATIONS =====`);
        console.log(`🔄 Setting nestedObjectTranslations state for ${language}:`);
        console.log(`📊 Previous state:`, prev);
        console.log(`📊 New data for ${language}:`, transformedNestedObjectData);
        console.log(`📊 Complete new state:`, newState);
        console.log(`❓ FAQ translations specifically for ${language}:`, transformedNestedObjectData.faqs);
        console.log(`❓ FAQ translations type:`, typeof transformedNestedObjectData.faqs);
        console.log(`❓ FAQ translations length:`, transformedNestedObjectData.faqs?.length || 0);
        if (transformedNestedObjectData.faqs && transformedNestedObjectData.faqs.length > 0) {
          console.log(`❓ First FAQ:`, transformedNestedObjectData.faqs[0]);
        }
        console.log(`🔥 ===== SETTING NESTED OBJECT TRANSLATIONS END =====\n`);
        return newState;
      });

      console.log(`✅ Existing translations loaded for destination ${destinationId} in language ${language}:`, transformedData);
      console.log(`✅ Existing array translations loaded for destination ${destinationId} in language ${language}:`, transformedArrayData);
      console.log(`✅ Existing nested object translations loaded for destination ${destinationId} in language ${language}:`, transformedNestedObjectData);
      console.log(`🌐 ===== LOAD TRANSLATIONS SUCCESS =====\n`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load translations';
      setError(errorMessage);
      console.error(`❌ Error loading translations for ${language}:`, err);
      console.log(`🌐 ===== LOAD TRANSLATIONS ERROR =====\n`);
    } finally {
      setLoading(false);
    }
  }, [destinationId, getTranslationKeys]);

  /**
   * Remove translations for a specific language
   */
  const removeTranslations = useCallback((language: string) => {
    setTranslations(prev => {
      const updated = { ...prev };
      delete updated[language];
      return updated;
    });
    setArrayTranslations(prev => {
      const updated = { ...prev };
      delete updated[language];
      return updated;
    });
    setNestedObjectTranslations(prev => {
      const updated = { ...prev };
      delete updated[language];
      return updated;
    });
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    translations,
    arrayTranslations,
    nestedObjectTranslations,
    loading,
    error,
    getTranslation,
    getArrayTranslation,
    getNestedObjectTranslation,
    saveTranslations,
    saveArrayTranslations,
    saveNestedObjectTranslations,
    loadTranslations,
    getTranslationKeys,
    getArrayTranslationKeys,
    removeTranslations,
    clearError,
    setArrayTranslations // Expose for state synchronization
  };
};
