import { useState, useCallback, useEffect, useRef } from 'react';
import { useDestinationTranslations } from './useDestinationTranslations';

export interface UseTagTranslationsReturn {
  tags: string[];
  translatedTags: string[];
  loading: boolean;
  error: string | null;
  setTags: (tags: string[]) => void;
  setTranslatedTags: (tags: string[]) => void;
  saveTags: (language: string) => Promise<void>;
  loadTags: (language: string) => Promise<void>;
  isBaseLanguage: boolean;
  hasUnsavedChanges: boolean;
  getAllLanguageTranslations: () => Record<string, string[]>;
  hasTranslationsForLanguage: (language: string) => boolean;
}

/**
 * Specialized hook for managing tag translations
 * Provides a simplified interface for tag-specific operations
 */
export const useTagTranslations = (
  destinationId: string,
  selectedLanguage: string,
  baseTags: string[] = []
): UseTagTranslationsReturn => {
  const {
    arrayTranslations,
    loading,
    error,
    getArrayTranslation,
    saveArrayTranslations,
    loadTranslations,
    setArrayTranslations
  } = useDestinationTranslations(destinationId);

  // Local state for tags
  const [tags, setTags] = useState<string[]>(baseTags);
  const [translatedTags, setTranslatedTags] = useState<string[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Store all language translations locally to persist across language switches
  const allLanguageTranslations = useRef<Record<string, string[]>>({});

  // Determine if current language is base language
  const isBaseLanguage = selectedLanguage === 'en';

  // Load translated tags when language changes
  useEffect(() => {
    console.log(`🏷️ useTagTranslations effect triggered for language: ${selectedLanguage}`);
    console.log(`🏠 isBaseLanguage: ${isBaseLanguage}`);

    if (!isBaseLanguage) {
      // First check if we have local translations for this language
      const localTranslations = allLanguageTranslations.current[selectedLanguage];
      console.log(`💾 Local translations for ${selectedLanguage}:`, localTranslations);

      if (localTranslations && localTranslations.length > 0) {
        console.log(`🔄 Loading local translations for ${selectedLanguage}:`, localTranslations);
        setTranslatedTags(localTranslations);
        setHasUnsavedChanges(false);
      } else {
        // Check if we have API translations
        console.log(`🌐 Checking API translations for ${selectedLanguage}...`);
        const apiTranslations = getArrayTranslation('tags', selectedLanguage);
        console.log(`📥 API translations result for ${selectedLanguage}:`, apiTranslations);

        if (apiTranslations.length > 0) {
          console.log(`✅ Found API translations for ${selectedLanguage}:`, apiTranslations);
          setTranslatedTags(apiTranslations);
          allLanguageTranslations.current[selectedLanguage] = apiTranslations;
          setHasUnsavedChanges(false);
        } else {
          // Start with empty translations for new languages - don't copy base tags
          console.log(`🆕 Starting with empty translations for ${selectedLanguage} (no API translations found)`);
          setTranslatedTags([]);
          setHasUnsavedChanges(false);

          // Try to load translations from API
          console.log(`🔄 Attempting to load translations from API for ${selectedLanguage}...`);
          loadTranslations(selectedLanguage).catch(error => {
            console.error(`❌ Failed to load translations for ${selectedLanguage}:`, error);
          });
        }
      }
    } else {
      console.log(`🏠 Base language selected, clearing translated tags`);
      setTranslatedTags([]);
      setHasUnsavedChanges(false);
    }
  }, [selectedLanguage, isBaseLanguage, baseTags, getArrayTranslation, loadTranslations]);

  // Update base tags when they change
  useEffect(() => {
    setTags(baseTags);
  }, [baseTags]);

  // Track unsaved changes for translated tags
  const handleSetTranslatedTags = useCallback((newTags: string[]) => {
    console.log(`🏷️ Setting translated tags for ${selectedLanguage}:`, newTags);
    setTranslatedTags(newTags);

    if (!isBaseLanguage) {
      // Store in local state to persist across language switches
      allLanguageTranslations.current[selectedLanguage] = newTags;
      console.log(`💾 Stored local translations for ${selectedLanguage}:`, newTags);
      console.log(`📊 All local translations:`, allLanguageTranslations.current);

      // CRITICAL FIX: Immediately sync the arrayTranslations state
      // This ensures saveArrayTranslations uses the correct current state for cleanup
      console.log(`🔄 Syncing arrayTranslations state for ${selectedLanguage}...`);
      setArrayTranslations(prev => {
        const newState = {
          ...prev,
          [selectedLanguage]: {
            ...prev[selectedLanguage],
            tags: newTags
          }
        };
        console.log(`✅ arrayTranslations state synced for ${selectedLanguage}:`, newState);
        return newState;
      });

      // Check if there are unsaved changes compared to API
      const apiTags = getArrayTranslation('tags', selectedLanguage);
      const hasChanges = JSON.stringify(newTags) !== JSON.stringify(apiTags);
      setHasUnsavedChanges(hasChanges);
      console.log(`🔄 Unsaved changes for ${selectedLanguage}:`, hasChanges);
    }
  }, [isBaseLanguage, selectedLanguage, getArrayTranslation, setArrayTranslations]);

  // Save tags for current language
  const saveTags = useCallback(async (language: string) => {
    if (isBaseLanguage) {
      console.log('Cannot save translations for base language');
      return;
    }

    try {
      await saveArrayTranslations(language, 'tags', translatedTags);
      setHasUnsavedChanges(false);
      console.log(`Tags saved for language: ${language}`);
    } catch (error) {
      console.error('Error saving tags:', error);
      throw error;
    }
  }, [isBaseLanguage, translatedTags, saveArrayTranslations]);

  // Load tags for current language
  const loadTags = useCallback(async (language: string) => {
    if (isBaseLanguage) {
      return;
    }

    try {
      await loadTranslations(language);
      console.log(`Tags loaded for language: ${language}`);
    } catch (error) {
      console.error('Error loading tags:', error);
      throw error;
    }
  }, [isBaseLanguage, loadTranslations]);

  // Helper function to get all language translations
  const getAllLanguageTranslations = useCallback(() => {
    return { ...allLanguageTranslations.current };
  }, []);

  // Helper function to check if we have translations for a specific language
  const hasTranslationsForLanguage = useCallback((language: string) => {
    const translations = allLanguageTranslations.current[language];
    return translations && translations.length > 0;
  }, []);

  return {
    tags,
    translatedTags,
    loading,
    error,
    setTags,
    setTranslatedTags: handleSetTranslatedTags,
    saveTags,
    loadTags,
    isBaseLanguage,
    hasUnsavedChanges,
    getAllLanguageTranslations,
    hasTranslationsForLanguage
  };
};
