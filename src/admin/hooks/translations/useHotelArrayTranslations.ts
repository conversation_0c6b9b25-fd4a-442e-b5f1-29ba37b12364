import { useState, useCallback, useEffect, useRef } from "react";
import { HotelArrayTranslations } from "./useHotelTranslations";

export interface UseHotelArrayTranslationsReturn {
  translatedValues: string[];
  loading: boolean;
  error: string | null;
  setTranslatedValues: (values: string[]) => void;
  isBaseLanguage: boolean;
  hasUnsavedChanges: boolean;
  getAllLanguageTranslations: () => Record<string, string[]>;
  hasTranslationsForLanguage: (language: string) => boolean;
  saveTranslations: () => Promise<void>;
}

/**
 * Specialized hook for managing hotel array field translations (tags, amenities, rules, safety_measures)
 * Provides a simplified interface for array-specific operations with multi-language persistence
 *
 * IMPORTANT: This hook now receives shared state from the main useHotelTranslations instance
 * to ensure all hooks work with the same arrayTranslations state
 */
export const useHotelArrayTranslations = (
  hotelId: string,
  selectedLanguage: string,
  fieldName: "tags" | "amenities" | "rules" | "safety_measures",
  baseValues: string[] = [],
  // Shared state from main useHotelTranslations instance
  arrayTranslations: HotelArrayTranslations,
  loading: boolean,
  error: string | null,
  getArrayTranslation: (
    fieldName: "tags" | "amenities" | "rules" | "safety_measures",
    language: string
  ) => string[],
  setArrayTranslations: React.Dispatch<
    React.SetStateAction<HotelArrayTranslations>
  >,
  saveArrayTranslations: (
    language: string,
    fieldName: string,
    values: string[]
  ) => Promise<void>
): UseHotelArrayTranslationsReturn => {
  // Local state for translated values
  const [translatedValues, setTranslatedValues] = useState<string[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Store all language translations locally to persist across language switches
  const allLanguageTranslations = useRef<Record<string, string[]>>({});

  // Determine if current language is base language
  const isBaseLanguage = selectedLanguage === "en";

  // Load translated values when language changes or arrayTranslations updates
  useEffect(() => {
    console.log(
      `🏷️ useHotelArrayTranslations effect triggered for ${fieldName} in language: ${selectedLanguage}`
    );
    console.log(`🏠 isBaseLanguage: ${isBaseLanguage}`);
    console.log(`📊 Current arrayTranslations state:`, arrayTranslations);

    if (!isBaseLanguage) {
      // First check if we have local translations for this language
      const localTranslations =
        allLanguageTranslations.current[selectedLanguage];
      console.log(
        `💾 Local ${fieldName} translations for ${selectedLanguage}:`,
        localTranslations
      );

      // Check if we have API translations (this will be updated when loadTranslations completes)
      console.log(
        `🌐 Checking API ${fieldName} translations for ${selectedLanguage}...`
      );
      console.log(
        `🔍 arrayTranslations[${selectedLanguage}]:`,
        arrayTranslations[selectedLanguage]
      );
      console.log(
        `🔍 arrayTranslations[${selectedLanguage}]?.[${fieldName}]:`,
        arrayTranslations[selectedLanguage]?.[fieldName]
      );

      const apiTranslations = getArrayTranslation(fieldName, selectedLanguage);
      console.log(
        `📥 API ${fieldName} translations result for ${selectedLanguage}:`,
        apiTranslations
      );

      // Prioritize API translations over local cache if they exist
      if (apiTranslations.length > 0) {
        console.log(
          `✅ Found API ${fieldName} translations for ${selectedLanguage}:`,
          apiTranslations
        );
        setTranslatedValues(apiTranslations);
        allLanguageTranslations.current[selectedLanguage] = apiTranslations;
        setHasUnsavedChanges(false);
      } else if (localTranslations && localTranslations.length > 0) {
        console.log(
          `🔄 Loading local ${fieldName} translations for ${selectedLanguage}:`,
          localTranslations
        );
        setTranslatedValues(localTranslations);
        setHasUnsavedChanges(false);
      } else {
        // Start with empty translations for new languages - don't copy base values
        console.log(
          `🆕 Starting with empty ${fieldName} translations for ${selectedLanguage} (no API translations found)`
        );
        setTranslatedValues([]);
        setHasUnsavedChanges(false);
      }
    } else {
      console.log(
        `🏠 Base language selected, clearing translated ${fieldName}`
      );
      setTranslatedValues([]);
      setHasUnsavedChanges(false);
    }
  }, [
    selectedLanguage,
    isBaseLanguage,
    fieldName,
    getArrayTranslation,
    arrayTranslations, // This will trigger re-run when loadTranslations updates the state
  ]);

  // Track unsaved changes for translated values
  const handleSetTranslatedValues = useCallback(
    (newValues: string[]) => {
      console.log(
        `🏷️ Setting translated ${fieldName} for ${selectedLanguage}:`,
        newValues
      );
      setTranslatedValues(newValues);

      if (!isBaseLanguage) {
        // Store in local state to persist across language switches
        allLanguageTranslations.current[selectedLanguage] = newValues;
        console.log(
          `💾 Stored local ${fieldName} translations for ${selectedLanguage}:`,
          newValues
        );
        console.log(
          `📊 All local ${fieldName} translations:`,
          allLanguageTranslations.current
        );

        // CRITICAL FIX: Immediately sync the arrayTranslations state
        // This ensures saveArrayTranslations uses the correct current state for cleanup
        console.log(
          `🔄 Syncing arrayTranslations state for ${selectedLanguage} ${fieldName}...`
        );
        setArrayTranslations((prev) => {
          const newState = {
            ...prev,
            [selectedLanguage]: {
              ...prev[selectedLanguage],
              [fieldName]: newValues,
            },
          };
          console.log(
            `✅ arrayTranslations state synced for ${selectedLanguage} ${fieldName}:`,
            newState
          );
          return newState;
        });

        // Check if there are unsaved changes compared to API
        const apiValues = getArrayTranslation(fieldName, selectedLanguage);
        const hasChanges =
          JSON.stringify(newValues) !== JSON.stringify(apiValues);
        setHasUnsavedChanges(hasChanges);
        console.log(
          `🔄 Unsaved changes for ${selectedLanguage} ${fieldName}:`,
          hasChanges
        );
      }
    },
    [
      isBaseLanguage,
      selectedLanguage,
      fieldName,
      getArrayTranslation,
      setArrayTranslations,
    ]
  );

  // Helper function to get all language translations
  const getAllLanguageTranslations = useCallback(() => {
    return { ...allLanguageTranslations.current };
  }, []);

  // Helper function to check if we have translations for a specific language
  const hasTranslationsForLanguage = useCallback((language: string) => {
    const translations = allLanguageTranslations.current[language];
    return translations && translations.length > 0;
  }, []);

  // Save translations to API
  const saveTranslations = useCallback(async () => {
    if (!hasUnsavedChanges || isBaseLanguage) return;

    try {
      console.log(
        `💾 Saving ${fieldName} translations for ${selectedLanguage}:`,
        translatedValues
      );

      // Save to API using the passed function
      await saveArrayTranslations(
        selectedLanguage,
        fieldName,
        translatedValues
      );

      setHasUnsavedChanges(false);
      console.log(
        `✅ Successfully saved ${fieldName} translations for ${selectedLanguage}`
      );
    } catch (error) {
      console.error(
        `❌ Failed to save ${fieldName} translations for ${selectedLanguage}:`,
        error
      );
    }
  }, [
    hasUnsavedChanges,
    isBaseLanguage,
    fieldName,
    selectedLanguage,
    translatedValues,
    saveArrayTranslations,
  ]);

  return {
    translatedValues,
    loading,
    error,
    setTranslatedValues: handleSetTranslatedValues,
    isBaseLanguage,
    hasUnsavedChanges,
    getAllLanguageTranslations,
    hasTranslationsForLanguage,
    saveTranslations,
  };
};
