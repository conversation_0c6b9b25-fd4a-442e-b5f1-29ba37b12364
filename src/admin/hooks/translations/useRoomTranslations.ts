import { useState, useCallback, useMemo } from "react";
import {
  generateTranslationKey,
  generateTranslationKeys,
  generateArrayFieldTranslationKeys,
  orderedArrayToTranslations,
  ArrayFieldType,
} from "../../utils/translation-utils";

// Tolgee tree response interface
interface TolgeeTreeResponse {
  [language: string]: {
    room?: {
      [roomId: string]: {
        [fieldName: string]: string;
      };
    };
  };
}

// Define translatable fields for rooms
const ROOM_TRANSLATABLE_FIELDS = ["name", "description"] as const;

// Define translatable array fields for rooms
const ROOM_TRANSLATABLE_ARRAY_FIELDS = ["amenities"] as const;

export type RoomTranslatableField = (typeof ROOM_TRANSLATABLE_FIELDS)[number];
export type RoomTranslatableArrayField =
  (typeof ROOM_TRANSLATABLE_ARRAY_FIELDS)[number];

export interface RoomTranslations {
  [language: string]: {
    [key: string]: string;
  };
}

export interface RoomArrayTranslations {
  [language: string]: {
    [fieldName in RoomTranslatableArrayField]?: string[];
  };
}

export interface UseRoomTranslationsReturn {
  translations: RoomTranslations;
  arrayTranslations: RoomArrayTranslations;
  loading: boolean;
  error: string | null;
  getTranslation: (
    fieldName: RoomTranslatableField,
    language: string
  ) => string;
  getArrayTranslation: (
    fieldName: RoomTranslatableArrayField,
    language: string
  ) => string[];
  saveTranslations: (
    language: string,
    translationsToSave: Record<string, string>,
    strictMode?: boolean
  ) => Promise<void>;
  saveArrayTranslations: (
    language: string,
    fieldName: RoomTranslatableArrayField,
    arrayValues: string[]
  ) => Promise<void>;
  loadTranslations: (language: string) => Promise<void>;
  getTranslationKeys: () => Record<string, string>;
  getArrayTranslationKeys: (
    fieldName: RoomTranslatableArrayField,
    arrayLength: number
  ) => Record<number, string>;
  removeTranslations: (language: string) => void;
  clearError: () => void;
  setArrayTranslations: React.Dispatch<
    React.SetStateAction<RoomArrayTranslations>
  >;
}

/**
 * Hook for managing room translations with Tolgee integration
 */
export const useRoomTranslations = (
  roomId: string
): UseRoomTranslationsReturn => {
  const [translations, setTranslations] = useState<RoomTranslations>({});
  const [arrayTranslations, setArrayTranslations] =
    useState<RoomArrayTranslations>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Get translation keys for this room - memoized for performance
   */
  const translationKeys = useMemo(() => {
    return generateTranslationKeys("room", roomId, ROOM_TRANSLATABLE_FIELDS);
  }, [roomId]);

  const getTranslationKeys = useCallback(() => {
    return translationKeys;
  }, [translationKeys]);

  /**
   * Get a translation value for a specific field and language
   */
  const getTranslation = useCallback(
    (fieldName: RoomTranslatableField, language: string): string => {
      const key = generateTranslationKey("room", roomId, fieldName);
      return translations[language]?.[key] || "";
    },
    [translations, roomId]
  );

  /**
   * Get array translation keys for a specific field and array length
   */
  const getArrayTranslationKeys = useCallback(
    (fieldName: RoomTranslatableArrayField, arrayLength: number) => {
      return generateArrayFieldTranslationKeys(
        "room",
        roomId,
        fieldName as ArrayFieldType,
        arrayLength
      );
    },
    [roomId]
  );

  /**
   * Get array translation values for a specific field and language
   */
  const getArrayTranslation = useCallback(
    (fieldName: RoomTranslatableArrayField, language: string): string[] => {
      console.log(
        `🔍 getArrayTranslation called for ${fieldName} in ${language}`
      );
      console.log(`📊 Current arrayTranslations state:`, arrayTranslations);
      console.log(
        `🎯 Looking for: arrayTranslations[${language}]?.[${fieldName}]`
      );

      const result = arrayTranslations[language]?.[fieldName] || [];
      console.log(`📤 getArrayTranslation returning:`, result);

      return result;
    },
    [arrayTranslations]
  );

  /**
   * Save translations to Tolgee API - ONLY called on explicit form submission
   * Uses POST for upsert behavior (creates keys if they don't exist, updates if they do)
   * Alternative: Use PUT method to only update existing keys (will error if key doesn't exist)
   */
  const saveTranslations = useCallback(
    async (
      language: string,
      translationsToSave: Record<string, string>,
      strictMode = false
    ) => {
      if (!translationsToSave || Object.keys(translationsToSave).length === 0) {
        console.log("No translations to save");
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        if (!apiKey || !projectId) {
          throw new Error("Tolgee API configuration missing");
        }

        console.log(
          `🌐 Saving ${
            Object.keys(translationsToSave).length
          } room translations for language: ${language}`
        );
        console.log("📝 Translations to save:", translationsToSave);

        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;

        // Process each translation key individually
        for (const [keyName, value] of Object.entries(translationsToSave)) {
          const payload = {
            key: keyName,
            translations: {
              [language]: value,
            },
          };

          console.log(`Saving translation for key: ${keyName}`, payload);

          const response = await fetch(url, {
            method: strictMode ? "PUT" : "POST", // POST for upsert, PUT for strict update only
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
              "X-API-Key": apiKey,
            },
            body: JSON.stringify(payload),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(
              `Failed to save translation for key ${keyName}:`,
              errorText
            );
            throw new Error(
              `HTTP ${response.status}: Failed to save translation for key ${keyName}. ${errorText}`
            );
          }

          const result = await response.json();
          console.log(
            `✅ Successfully saved translation for key: ${keyName}`,
            result
          );
        }

        // Update local state with saved translations
        setTranslations((prev) => ({
          ...prev,
          [language]: {
            ...prev[language],
            ...translationsToSave,
          },
        }));

        console.log("✅ All room translations saved successfully");
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        console.error("❌ Error saving room translations:", errorMessage);
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * Save array translations to Tolgee API
   */
  const saveArrayTranslations = useCallback(
    async (
      language: string,
      fieldName: RoomTranslatableArrayField,
      arrayValues: string[]
    ) => {
      console.group(
        `🏷️ saveArrayTranslations called for ${fieldName} in ${language}`
      );
      console.log(`📋 Input parameters:`, {
        language,
        fieldName,
        arrayValues,
        roomId,
        arrayLength: arrayValues?.length || 0,
      });
      console.log(`📊 Current arrayTranslations state:`, arrayTranslations);
      console.log(
        `🎯 Current state for this language/field:`,
        arrayTranslations[language]?.[fieldName]
      );

      // Allow saving empty arrays to clear all translations
      if (!arrayValues) {
        console.log("❌ arrayValues is null/undefined");
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        console.log("🔑 API Configuration:", {
          hasApiKey: !!apiKey,
          hasProjectId: !!projectId,
          projectId: projectId,
        });

        if (!apiKey || !projectId) {
          throw new Error("Tolgee API configuration missing");
        }

        // STEP 1: Clean up old translation keys first (if array got shorter)
        console.log(
          `🧹 Step 1: Cleaning up old ${fieldName} translations for ${language}...`
        );

        // Get current translations to see how many keys exist
        const currentTranslations =
          arrayTranslations[language]?.[fieldName] || [];
        const currentLength = currentTranslations.length;
        const newLength = arrayValues.length;

        console.log(`📊 Array length change: ${currentLength} → ${newLength}`);

        // If array got shorter, delete the extra keys
        if (newLength < currentLength) {
          console.log(
            `🗑️ Array got shorter, need to delete ${
              currentLength - newLength
            } keys`
          );

          const deleteUrl = `https://app.tolgee.io/v2/projects/${projectId}/translations`;

          for (let i = newLength; i < currentLength; i++) {
            const keyToDelete = `room.${roomId}.${fieldName}.${i}`;
            console.log(`🗑️ Deleting key: ${keyToDelete}`);

            try {
              const deleteResponse = await fetch(
                `${deleteUrl}/${encodeURIComponent(keyToDelete)}`,
                {
                  method: "DELETE",
                  headers: {
                    Accept: "application/json",
                    "X-API-Key": apiKey,
                  },
                }
              );

              if (deleteResponse.ok) {
                console.log(`✅ Successfully deleted key: ${keyToDelete}`);
              } else {
                console.log(
                  `⚠️ Key ${keyToDelete} might not exist (${deleteResponse.status})`
                );
              }
            } catch (deleteError) {
              console.log(`⚠️ Error deleting key ${keyToDelete}:`, deleteError);
            }
          }
        }

        // STEP 2: Save new translations
        console.log(
          `💾 Step 2: Saving ${arrayValues.length} new ${fieldName} translations...`
        );

        // Convert array to translation key-value pairs
        const translationsToSave = orderedArrayToTranslations(
          arrayValues,
          "room",
          roomId,
          fieldName as ArrayFieldType
        );

        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;
        console.log("🌐 Saving array translations to:", url);
        console.log("📝 Array translations to save:", translationsToSave);
        console.log(
          "🎯 Number of keys to save:",
          Object.keys(translationsToSave).length
        );

        // Process each translation key individually
        let successCount = 0;
        let errorCount = 0;

        for (const [keyName, value] of Object.entries(translationsToSave)) {
          const payload = {
            key: keyName,
            translations: {
              [language]: value,
            },
          };

          console.log(
            `💾 Saving array translation for key: ${keyName} = "${value}"`
          );

          try {
            const response = await fetch(url, {
              method: "POST", // Always use POST for upsert behavior
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
                "X-API-Key": apiKey,
              },
              body: JSON.stringify(payload),
            });

            if (!response.ok) {
              const errorText = await response.text();
              console.error(
                `❌ Failed to save array translation for key ${keyName}:`,
                errorText
              );
              errorCount++;
            } else {
              const result = await response.json();
              console.log(
                `✅ Successfully saved array translation for key: ${keyName}`,
                result
              );
              successCount++;
            }
          } catch (keyError) {
            console.error(
              `❌ Error saving array translation for key ${keyName}:`,
              keyError
            );
            errorCount++;
          }
        }

        console.log(
          `📊 Array translation save summary: ${successCount} success, ${errorCount} errors`
        );

        if (errorCount > 0) {
          throw new Error(
            `Failed to save ${errorCount} out of ${
              Object.keys(translationsToSave).length
            } array translations`
          );
        }

        // Update local state with saved array translations
        setArrayTranslations((prev) => ({
          ...prev,
          [language]: {
            ...prev[language],
            [fieldName]: arrayValues,
          },
        }));

        console.log(
          `✅ All ${fieldName} array translations saved successfully for ${language}`
        );
        console.log(
          `🔄 Updated local arrayTranslations state for ${language}.${fieldName}:`,
          arrayValues
        );
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        console.error(
          `❌ Error saving ${fieldName} array translations:`,
          errorMessage
        );
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
        console.groupEnd();
      }
    },
    [arrayTranslations, roomId]
  );

  /**
   * Load translations from Tolgee API for a specific language
   * Uses the tree endpoint with filter for room keys
   */
  const loadTranslations = useCallback(
    async (language: string) => {
      console.log(`🌐 ===== LOAD ROOM TRANSLATIONS START =====`);
      console.log(
        `🔍 Loading translations for room ${roomId} in language: ${language}`
      );

      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        console.log(`🔑 API Key exists: ${!!apiKey}`);
        console.log(`🆔 Project ID: ${projectId}`);

        if (!apiKey || !projectId) {
          throw new Error("Tolgee API configuration missing");
        }

        const keysList = Object.values(getTranslationKeys());
        console.log(`🔑 Translation keys to load:`, keysList);

        if (keysList.length === 0) {
          console.log("No translation keys to load");
          console.log(`🌐 ===== LOAD TRANSLATIONS END (NO KEYS) =====\n`);
          return;
        }

        // Use the simpler tree endpoint with filter for room keys
        const filterKeyName = `room.${roomId}`;
        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations/${language}?filterKeyName=${filterKeyName}`;
        console.log("🔗 Loading existing translations from:", url);
        console.log("🔍 Looking for room:", roomId);
        console.log("🔍 Filter key name:", filterKeyName);

        const response = await fetch(url, {
          method: "GET",
          headers: {
            Accept: "application/json",
            "X-API-Key": apiKey,
          },
        });

        if (!response.ok) {
          if (response.status === 404) {
            console.log(
              `ℹ️ No translations found for room ${roomId} in ${language} (404)`
            );
            console.log(`🌐 ===== LOAD TRANSLATIONS END (404) =====\n`);
            return;
          }
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData: TolgeeTreeResponse = await response.json();
        console.log(`📥 Raw API response for ${language}:`, responseData);

        // Transform nested structure to flat keys
        const transformedData: Record<string, string> = {};

        // Navigate through the nested structure: language -> room -> roomId -> field
        if (responseData[language]?.room?.[roomId]) {
          const roomTranslations = responseData[language].room[roomId];
          console.log(
            `🔍 Raw room translations for ${roomId}:`,
            roomTranslations
          );

          // Convert nested structure to flat keys for regular fields
          ROOM_TRANSLATABLE_FIELDS.forEach((fieldName) => {
            if (roomTranslations[fieldName]) {
              const fullKey = `room.${roomId}.${fieldName}`;
              transformedData[fullKey] = roomTranslations[fieldName];
              console.log(
                `Found translation for ${fullKey}: ${roomTranslations[fieldName]}`
              );
            }
          });

          // Handle array fields (amenities)
          ROOM_TRANSLATABLE_ARRAY_FIELDS.forEach((fieldName) => {
            console.log(`🔍 Processing array field: ${fieldName}`);

            // Check if the field exists as an object with numeric keys
            const arrayFieldData = roomTranslations[fieldName];
            console.log(`🔍 Raw ${fieldName} data:`, arrayFieldData);

            if (arrayFieldData && typeof arrayFieldData === "object") {
              console.log(
                `📋 Found ${fieldName} object with keys:`,
                Object.keys(arrayFieldData)
              );

              // Convert object with numeric keys to ordered array
              const orderedArray: string[] = [];
              const numericKeys = Object.keys(arrayFieldData)
                .filter((key) => /^\d+$/.test(key)) // Only numeric keys
                .map((key) => parseInt(key, 10))
                .sort((a, b) => a - b); // Sort numerically

              console.log(`🔢 Numeric keys for ${fieldName}:`, numericKeys);

              numericKeys.forEach((index) => {
                const value = arrayFieldData[index.toString()];
                if (value !== null && value !== undefined && value !== "") {
                  orderedArray.push(value);
                }
              });

              console.log(
                `📋 Ordered ${fieldName} array for ${language}:`,
                orderedArray
              );

              // Update array translations state
              setArrayTranslations((prev) => ({
                ...prev,
                [language]: {
                  ...prev[language],
                  [fieldName]: orderedArray,
                },
              }));

              // Also add individual items to flat structure for consistency
              orderedArray.forEach((value, index) => {
                const fullKey = `room.${roomId}.${fieldName}.${index}`;
                transformedData[fullKey] = value;
              });
            } else {
              console.log(`ℹ️ No ${fieldName} data found or not an object`);
            }
          });
        } else {
          console.log(
            `ℹ️ No room translations found in response structure for ${roomId}`
          );
        }

        console.log(
          `📊 Final transformed data for ${language}:`,
          transformedData
        );

        // Update translations state
        setTranslations((prev) => ({
          ...prev,
          [language]: transformedData,
        }));

        console.log(
          `✅ Successfully loaded ${
            Object.keys(transformedData).length
          } room translations for ${language}`
        );
        console.log(`🌐 ===== LOAD ROOM TRANSLATIONS END =====\n`);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        console.error("❌ Error loading room translations:", errorMessage);
        setError(errorMessage);
        console.log(`🌐 ===== LOAD ROOM TRANSLATIONS END (ERROR) =====\n`);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [roomId, getTranslationKeys]
  );

  /**
   * Remove translations for a specific language
   */
  const removeTranslations = useCallback((language: string) => {
    setTranslations((prev) => {
      const updated = { ...prev };
      delete updated[language];
      return updated;
    });
    setArrayTranslations((prev) => {
      const updated = { ...prev };
      delete updated[language];
      return updated;
    });
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    translations,
    arrayTranslations,
    loading,
    error,
    getTranslation,
    getArrayTranslation,
    saveTranslations,
    saveArrayTranslations,
    loadTranslations,
    getTranslationKeys,
    getArrayTranslationKeys,
    removeTranslations,
    clearError,
    setArrayTranslations, // Expose for state synchronization
  };
};
