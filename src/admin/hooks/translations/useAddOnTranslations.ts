import { useState, useCallback, useMemo, useRef } from "react";
import { generateTranslationKeys } from "../../utils/translation-utils";

// Tolgee tree response interface
interface TolgeeTreeResponse {
  [language: string]: {
    add_on?: {
      [addOnId: string]: {
        [fieldName: string]: string;
      };
    };
  };
}

// Define translatable fields for add-ons
const ADD_ON_TRANSLATABLE_FIELDS = ["name", "description"] as const;

interface AddOnTranslations {
  [fieldName: string]: string;
}

interface UseAddOnTranslationsProps {
  addOnId: string;
}

interface UseAddOnTranslationsReturn {
  translations: Record<string, AddOnTranslations>;
  translationKeys: Record<string, string>;
  loading: boolean;
  error: string | null;
  loadTranslations: (language: string) => Promise<void>;
  saveTranslations: (
    language: string,
    translationsToSave: Record<string, string>,
    strictMode?: boolean
  ) => Promise<void>;
  getTranslatedValue: (
    fieldName: string,
    language: string,
    fallbackValue?: string
  ) => string;
}

/**
 * Hook for managing add-on translations with Tolgee API
 *
 * This hook provides functionality to:
 * 1. Load translations for a specific add-on and language from Tolgee
 * 2. Save translations to Tolgee API
 * 3. Get translated values with fallback support
 *
 * @param addOnId - The ID of the add-on to manage translations for
 * @returns Translation management functions and state
 */
export const useAddOnTranslations = ({
  addOnId,
}: UseAddOnTranslationsProps): UseAddOnTranslationsReturn => {
  const [translations, setTranslations] = useState<
    Record<string, AddOnTranslations>
  >({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadingLanguagesRef = useRef<Set<string>>(new Set());

  // Generate translation keys for all translatable fields
  const translationKeys = useMemo(() => {
    return generateTranslationKeys(
      "add_on",
      addOnId,
      ADD_ON_TRANSLATABLE_FIELDS
    );
  }, [addOnId, translations]);

  /**
   * Load translations for a specific language from Tolgee API
   * This function fetches translations and stores them in component state
   */
  const loadTranslations = useCallback(
    async (language: string) => {
      // Check if translations for this language are already loaded
      if (translations[language] !== undefined) {
        console.log(
          `Translations for ${language} already loaded, skipping API call`
        );
        return;
      }

      // Check if this language is currently being loaded
      if (loadingLanguagesRef.current.has(language)) {
        console.log(
          `Translations for ${language} already being loaded, skipping API call`
        );
        return;
      }

      console.log(`Loading translations for ${language}...`);
      loadingLanguagesRef.current.add(language);
      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        if (!apiKey || !projectId) {
          throw new Error("Tolgee API configuration missing");
        }

        // Use the tree endpoint with filter for add-on keys (same format as destinations)
        const filterKeyName = `add_on.${addOnId}`;
        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations/${language}?filterKeyName=${filterKeyName}`;


        const response = await fetch(url, {
          method: "GET",
          headers: {
            Accept: "application/json",
            "X-API-Key": apiKey,
          },
        });

        console.log(`📡 API Response status: ${response.status}`);
        console.log(`📡 API Response ok: ${response.ok}`);

        if (!response.ok) {
          if (response.status === 404) {
            // No translations found for this language - this is normal
            setTranslations((prev) => ({
              ...prev,
              [language]: {},
            }));
            return;
          }
          throw new Error(`Tolgee API error: ${response.status}`);
        }

        const responseData: TolgeeTreeResponse = await response.json();
        console.log(`📥 Raw Tolgee response for ${language}:`, responseData);
        console.log(`📥 Response data type:`, typeof responseData);
        console.log(`📥 Response data keys:`, Object.keys(responseData || {}));

        // Transform the nested response to flat structure
        const languageTranslations: AddOnTranslations = {};

        // Navigate through the nested structure: language -> add_on -> addOnId -> field
        if (responseData[language]?.add_on?.[addOnId]) {
          const addOnTranslations = responseData[language].add_on[addOnId];

          // Process regular translatable fields
          ADD_ON_TRANSLATABLE_FIELDS.forEach((fieldName) => {
            if (
              addOnTranslations[fieldName] &&
              typeof addOnTranslations[fieldName] === "string"
            ) {
              languageTranslations[fieldName] = addOnTranslations[
                fieldName
              ] as string;
            }
          });
        }

        // Store translations in state
        setTranslations((prev) => ({
          ...prev,
          [language]: languageTranslations,
        }));

        console.log(
          `✅ Add-on translations loaded for ${addOnId} in language ${language}:`,
          languageTranslations
        );
        console.log(`🌐 ===== LOAD ADD-ON TRANSLATIONS SUCCESS =====\n`);
      } catch (err) {
        console.error("Error loading add-on translations:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load translations"
        );

        // Set empty translations for this language on error
        setTranslations((prev) => ({
          ...prev,
          [language]: {},
        }));
      } finally {
        loadingLanguagesRef.current.delete(language);
        setLoading(false);
      }
    },
    [addOnId]
  );

  /**
   * Save translations to Tolgee API - ONLY called on explicit form submission
   * Uses POST for upsert behavior (creates keys if they don't exist, updates if they do)
   * Alternative: Use PUT method to only update existing keys (will error if key doesn't exist)
   */
  const saveTranslations = useCallback(
    async (language: string, translationsToSave: Record<string, string>) => {
      if (!translationsToSave || Object.keys(translationsToSave).length === 0) {
        console.log("No translations to save");
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        if (!apiKey || !projectId) {
          throw new Error("Tolgee API configuration missing");
        }

        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;

        console.log(`Saving add-on translations:`, translationsToSave);

        // Process each translation key individually using the correct Tolgee format
        for (const [key, value] of Object.entries(translationsToSave)) {
          const payload = {
            key: key,
            translations: {
              [language]: value,
            },
          };

          console.log(`Saving translation for key: ${key}`, payload);

          const response = await fetch(url, {
            method: "POST", // Always use POST for upsert behavior
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
              "X-API-Key": apiKey,
            },
            body: JSON.stringify(payload),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(
              `Failed to save translation for key ${key}:`,
              errorText
            );
            throw new Error(
              `HTTP ${response.status}: Failed to save translation for key ${key}. ${errorText}`
            );
          }
        }

        console.log("Add-on translations saved successfully");

        // Update local state with saved translations
        setTranslations((prev) => ({
          ...prev,
          [language]: {
            ...prev[language],
            ...Object.fromEntries(
              Object.entries(translationsToSave).map(([key, value]) => {
                // Extract field name from translation key (e.g., "add_on.123.name" -> "name")
                const fieldName = key.split(".").pop() || key;
                return [fieldName, value];
              })
            ),
          },
        }));
      } catch (err) {
        console.error("Error saving add-on translations:", err);
        setError(
          err instanceof Error ? err.message : "Failed to save translations"
        );
        throw err; // Re-throw to allow caller to handle
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * Get translated value for a field in a specific language
   * Falls back to provided fallback value if translation not found
   */
  const getTranslatedValue = useCallback(
    (fieldName: string, language: string, fallbackValue = "") => {
      return translations[language]?.[fieldName] || fallbackValue;
    },
    [translations]
  );

  return {
    translations,
    translationKeys,
    loading,
    error,
    loadTranslations,
    saveTranslations,
    getTranslatedValue,
  };
};
