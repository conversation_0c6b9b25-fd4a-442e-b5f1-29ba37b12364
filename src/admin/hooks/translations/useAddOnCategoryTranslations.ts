import { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { generateTranslationKeys } from "../../utils/translation-utils";

// Tolgee tree response interface
interface TolgeeTreeResponse {
  [language: string]: {
    add_on_category?: {
      [categoryId: string]: {
        [fieldName: string]: string;
      };
    };
  };
}

// Define translatable fields for add-on categories
const ADD_ON_CATEGORY_TRANSLATABLE_FIELDS = ["name", "description"] as const;

interface AddOnCategoryTranslations {
  [fieldName: string]: string;
}

interface UseAddOnCategoryTranslationsProps {
  categoryId: string;
}

interface UseAddOnCategoryTranslationsReturn {
  translations: Record<string, AddOnCategoryTranslations>;
  translationKeys: Record<string, string>;
  loading: boolean;
  error: string | null;
  loadTranslations: (language: string) => Promise<void>;
  saveTranslations: (
    language: string,
    translationsToSave: Record<string, string>
  ) => Promise<void>;
  getTranslatedValue: (
    fieldName: string,
    language: string,
    fallbackValue?: string
  ) => string;
}

/**
 * Hook for managing add-on category translations with Tolgee API
 *
 * This hook provides functionality to:
 * 1. Load translations for a specific category and language from Tolgee
 * 2. Save translations to Tolgee API
 * 3. Get translated values with fallback support
 *
 * @param categoryId - The ID of the category to manage translations for
 * @returns Translation management functions and state
 */
export const useAddOnCategoryTranslations = ({
  categoryId,
}: UseAddOnCategoryTranslationsProps): UseAddOnCategoryTranslationsReturn => {
  const [translations, setTranslations] = useState<
    Record<string, AddOnCategoryTranslations>
  >({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadingLanguagesRef = useRef<Set<string>>(new Set());

  // Generate translation keys for all translatable fields
  const translationKeys = useMemo(() => {
    return generateTranslationKeys(
      "add_on_category",
      categoryId,
      ADD_ON_CATEGORY_TRANSLATABLE_FIELDS
    );
  }, [categoryId, translations]);

  // Reset translations when category changes
  useEffect(() => {
    setTranslations({});
  }, [categoryId]);

  /**
   * Load translations for a specific language from Tolgee API
   * This function fetches translations and stores them in component state
   */
  const loadTranslations = useCallback(
    async (language: string, forceReload = false) => {
      // Check if translations for this language are already loaded (unless force reload)
      if (!forceReload && translations[language] !== undefined) {
        console.log(
          `Translations for ${language} already loaded, skipping API call`
        );
        return;
      }

      // Check if this language is currently being loaded
      if (loadingLanguagesRef.current.has(language)) {
        console.log(
          `Translations for ${language} already being loaded, skipping API call`
        );
        return;
      }

      console.log(`Loading translations for ${language}...`);

      loadingLanguagesRef.current.add(language);
      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        if (!apiKey || !projectId) {
          throw new Error("Tolgee API configuration missing");
        }

        // Use the tree endpoint with filter for category keys
        const filterKeyName = `add_on_category.${categoryId}`;
        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations/${language}?filterKeyName=${filterKeyName}`;

        const response = await fetch(url, {
          method: "GET",
          headers: {
            Accept: "application/json",
            "X-API-Key": apiKey,
          },
        });

        console.log(`📡 API Response status: ${response.status}`);
        console.log(`📡 API Response ok: ${response.ok}`);

        if (!response.ok) {
          if (response.status === 404) {
            // No translations found for this language - this is normal
            setTranslations((prev) => ({
              ...prev,
              [language]: {},
            }));
            return;
          }
          throw new Error(`Tolgee API error: ${response.status}`);
        }

        const responseData: TolgeeTreeResponse = await response.json();
        console.log(`📥 Raw Tolgee response for ${language}:`, responseData);
        console.log(`📥 Response data type:`, typeof responseData);
        console.log(`📥 Response data keys:`, Object.keys(responseData || {}));

        // Transform the nested structure to our flat structure
        const categoryTranslations: AddOnCategoryTranslations = {};

        // Navigate through the nested structure: language -> add_on_category -> categoryId -> field
        if (responseData[language]?.add_on_category?.[categoryId]) {
          const categoryData =
            responseData[language].add_on_category[categoryId];

          // Process each field in the category data
          ADD_ON_CATEGORY_TRANSLATABLE_FIELDS.forEach((fieldName) => {
            if (
              categoryData[fieldName] &&
              typeof categoryData[fieldName] === "string"
            ) {
              categoryTranslations[fieldName] = categoryData[
                fieldName
              ] as string;
            }
          });
        }

        // Store translations in state
        setTranslations((prev) => ({
          ...prev,
          [language]: categoryTranslations,
        }));

        console.log(
          `✅ Add-on category translations loaded for ${categoryId} in language ${language}:`,
          categoryTranslations
        );
        console.log(
          `🌐 ===== LOAD ADD-ON CATEGORY TRANSLATIONS SUCCESS =====\n`
        );
      } catch (err) {
        console.error("Error loading add-on category translations:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load translations"
        );

        // Set empty translations for this language on error
        setTranslations((prev) => ({
          ...prev,
          [language]: {},
        }));
      } finally {
        loadingLanguagesRef.current.delete(language);
        setLoading(false);
      }
    },
    [categoryId]
  );

  /**
   * Save translations to Tolgee API - ONLY called on explicit form submission
   * Uses POST for upsert behavior (creates keys if they don't exist, updates if they do)
   */
  const saveTranslations = useCallback(
    async (language: string, translationsToSave: Record<string, string>) => {
      if (!translationsToSave || Object.keys(translationsToSave).length === 0) {
        console.log("No translations to save");
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
        const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

        if (!apiKey || !projectId) {
          throw new Error("Tolgee API configuration missing");
        }

        const url = `https://app.tolgee.io/v2/projects/${projectId}/translations`;

        // Process each translation key individually using the correct Tolgee format
        for (const [key, value] of Object.entries(translationsToSave)) {
          const payload = {
            key: key,
            translations: {
              [language]: value,
            },
          };

          console.log(`Saving translation for key: ${key}`, payload);

          const response = await fetch(url, {
            method: "POST", // Always use POST for upsert behavior
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
              "X-API-Key": apiKey,
            },
            body: JSON.stringify(payload),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(
              `Failed to save translation for key ${key}:`,
              errorText
            );
            throw new Error(
              `HTTP ${response.status}: Failed to save translation for key ${key}. ${errorText}`
            );
          }
        }

        console.log(
          `Successfully saved ${
            Object.keys(translationsToSave).length
          } translations for language: ${language}`
        );

        // Update local state with saved translations
        setTranslations((prev) => ({
          ...prev,
          [language]: {
            ...prev[language],
            ...Object.fromEntries(
              Object.entries(translationsToSave).map(([key, value]) => {
                // Extract field name from key
                const fieldName = key.split(".").pop() || key;
                return [fieldName, value];
              })
            ),
          },
        }));
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to save translations";
        console.error(
          `Error saving translations for language ${language}:`,
          err
        );
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [categoryId]
  );

  /**
   * Get translated value for a field in a specific language
   * Falls back to provided fallback value if translation not found
   */
  const getTranslatedValue = useCallback(
    (fieldName: string, language: string, fallbackValue = "") => {
      return translations[language]?.[fieldName] || fallbackValue;
    },
    [translations]
  );

  return {
    translations,
    translationKeys,
    loading,
    error,
    loadTranslations,
    saveTranslations,
    getTranslatedValue,
  };
};

export default useAddOnCategoryTranslations;
