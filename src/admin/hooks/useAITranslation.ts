import { useState, useCallback } from 'react';
import { translateArrayValues, TranslationRequest, TranslationResponse } from '../services/aiTranslationService';

export interface UseAITranslationOptions {
  onSuccess?: (translatedValues: string[]) => void;
  onError?: (error: string) => void;
}

export interface UseAITranslationReturn {
  translateValues: (request: TranslationRequest) => Promise<void>;
  isTranslating: boolean;
  error: string | null;
  lastTranslation: string[] | null;
}

/**
 * Hook for AI-powered translation of array values
 * 
 * @example
 * const { translateValues, isTranslating, error } = useAITranslation({
 *   onSuccess: (translatedValues) => setTranslatedTags(translatedValues),
 *   onError: (error) => console.error('Translation failed:', error)
 * });
 * 
 * // Usage
 * await translateValues({
 *   values: ['luxury', 'business', 'downtown'],
 *   targetLanguage: 'de',
 *   fieldType: 'tags',
 *   context: {
 *     entityType: 'hotel',
 *     entityName: 'Grand Hotel',
 *     location: 'Berlin, Germany'
 *   }
 * });
 */
export const useAITranslation = (options: UseAITranslationOptions = {}): UseAITranslationReturn => {
  const [isTranslating, setIsTranslating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastTranslation, setLastTranslation] = useState<string[] | null>(null);

  const { onSuccess, onError } = options;

  const translateValues = useCallback(async (request: TranslationRequest) => {
    setIsTranslating(true);
    setError(null);

    try {
      console.log('🤖 Starting AI translation:', {
        fieldType: request.fieldType,
        targetLanguage: request.targetLanguage,
        valueCount: request.values.length,
        values: request.values
      });

      const response: TranslationResponse = await translateArrayValues(request);

      if (response.success && response.translatedValues) {
        console.log('✅ AI translation successful:', {
          originalCount: request.values.length,
          translatedCount: response.translatedValues.length,
          translatedValues: response.translatedValues
        });

        setLastTranslation(response.translatedValues);
        onSuccess?.(response.translatedValues);
      } else {
        const errorMessage = response.error || 'Translation failed';
        console.error('❌ AI translation failed:', errorMessage);
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Translation failed';
      console.error('❌ AI translation error:', err);
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsTranslating(false);
    }
  }, [onSuccess, onError]);

  return {
    translateValues,
    isTranslating,
    error,
    lastTranslation
  };
};
