import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { useRbac } from "./use-rbac";
import { createDeactivationMetadata, createActivationMetadata } from "../utils/userStatus";

/**
 * Hook for deactivating a user
 */
export const useDeactivateUser = () => {
  const { currentUser } = useRbac();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, reason }: { userId: string; reason?: string }) => {
      if (!currentUser?.id) {
        throw new Error("No current user found");
      }

      const metadata = createDeactivationMetadata(currentUser.id, reason);
      
      const response = await fetch(`/admin/users/${userId}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ metadata }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to deactivate user");
      }
      
      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate users queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["rbac"] });
      
      toast.success("User deactivated successfully");
    },
    onError: (error: Error) => {
      console.error("Failed to deactivate user:", error);
      toast.error(`Failed to deactivate user: ${error.message}`);
    },
  });
};

/**
 * Hook for activating a user
 */
export const useActivateUser = () => {
  const { currentUser } = useRbac();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userId: string) => {
      if (!currentUser?.id) {
        throw new Error("No current user found");
      }

      const metadata = createActivationMetadata(currentUser.id);
      
      const response = await fetch(`/admin/users/${userId}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ metadata }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to activate user");
      }
      
      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate users queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["rbac"] });
      
      toast.success("User activated successfully");
    },
    onError: (error: Error) => {
      console.error("Failed to activate user:", error);
      toast.error(`Failed to activate user: ${error.message}`);
    },
  });
};

/**
 * Hook for bulk user operations (future enhancement)
 */
export const useBulkUserOperation = () => {
  const { currentUser } = useRbac();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      userIds, 
      operation, 
      reason 
    }: { 
      userIds: string[]; 
      operation: "activate" | "deactivate"; 
      reason?: string;
    }) => {
      if (!currentUser?.id) {
        throw new Error("No current user found");
      }

      const promises = userIds.map(async (userId) => {
        const metadata = operation === "deactivate" 
          ? createDeactivationMetadata(currentUser.id, reason)
          : createActivationMetadata(currentUser.id);
        
        const response = await fetch(`/admin/users/${userId}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({ metadata }),
        });
        
        if (!response.ok) {
          throw new Error(`Failed to ${operation} user ${userId}`);
        }
        
        return response.json();
      });

      return Promise.all(promises);
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["rbac"] });
      
      const { userIds, operation } = variables;
      const action = operation === "activate" ? "activated" : "deactivated";
      toast.success(`${userIds.length} user(s) ${action} successfully`);
    },
    onError: (error: Error) => {
      console.error("Bulk user operation failed:", error);
      toast.error(`Bulk operation failed: ${error.message}`);
    },
  });
};
