import { useMutation, useQueryClient } from "@tanstack/react-query";

type DeleteSeasonalPricingData = {
  name: string;
  start_date: string;
  end_date: string;
};

type DeleteSeasonalPricingResponse = {
  message: string;
  deleted_count: number;
  deleted_rule_ids: string[];
  season: {
    name: string;
    start_date: string;
    end_date: string;
  };
};

// Hook to delete all seasonal pricing rules for a specific season
export const useAdminDeleteSeasonalPricing = (hotelId: string) => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (data: DeleteSeasonalPricingData): Promise<DeleteSeasonalPricingResponse> => {
      const response = await fetch(
        `/admin/hotel-management/hotels/${hotelId}/pricing/seasonal/delete-by-season`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to delete seasonal pricing: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ["hotel-pricing", hotelId] });
      queryClient.invalidateQueries({ queryKey: ["room-config-seasonal-pricing"] });
      queryClient.invalidateQueries({ queryKey: ["hotel-seasonal-periods", hotelId] });
    },
  });

  return mutation;
};

export default useAdminDeleteSeasonalPricing;
