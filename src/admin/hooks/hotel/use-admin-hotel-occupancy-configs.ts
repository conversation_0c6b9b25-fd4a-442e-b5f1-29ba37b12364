import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";
import { cacheSettings } from "../../lib/react-query-config";

type OccupancyConfig = {
  id: string;
  name: string;
  type: string;
  min_age: number | null;
  max_age: number | null;
  min_occupancy: number;
  max_occupancy: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
};

type OccupancyConfigsResponse = {
  occupancy_configs: OccupancyConfig[];
};

export const useAdminHotelOccupancyConfigs = (hotelId: string) => {
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["hotel-occupancy-configs", hotelId],
    queryFn: async (): Promise<OccupancyConfigsResponse> => {
      const response = await sdk.client.fetch(
        `/admin/hotel-management/hotels/${hotelId}/pricing/occupancy-config`
      );
      return response as OccupancyConfigsResponse;
    },
    enabled: !!hotelId,
    retry: 2,
    // Use optimized cache settings for configurations
    ...cacheSettings.configurations,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  return {
    occupancyConfigs: data?.occupancy_configs || [],
    isLoading,
    isError,
    refetch,
  };
};

export const useAdminCreateHotelOccupancyConfig = () => {
  const createOccupancyConfig = async (
    hotelId: string,
    data: {
      name: string;
      type: string;
      min_age?: number | null;
      max_age?: number | null;
      min_occupancy?: number;
      max_occupancy?: number;
      is_default?: boolean;
    }
  ) => {
    const response = await sdk.client.fetch(
      `/admin/hotel-management/hotels/${hotelId}/pricing/occupancy-config`,
      {
        method: "POST",
        body: data,
      }
    );
    return (response as any).data;
  };

  return {
    createOccupancyConfig,
  };
};

export const useAdminUpdateHotelOccupancyConfig = () => {
  const updateOccupancyConfig = async (
    hotelId: string,
    occupancyConfigId: string,
    data: {
      name?: string;
      type?: string;
      min_age?: number | null;
      max_age?: number | null;
      min_occupancy?: number;
      max_occupancy?: number;
      is_default?: boolean;
    }
  ) => {
    const response = await sdk.client.fetch(
      `/admin/hotel-management/hotels/${hotelId}/pricing/occupancy-config/${occupancyConfigId}`,
      {
        method: "PUT",
        body: data,
      }
    );
    return (response as any).data;
  };

  return {
    updateOccupancyConfig,
  };
};

export const useAdminDeleteHotelOccupancyConfig = () => {
  const deleteOccupancyConfig = async (
    hotelId: string,
    occupancyConfigId: string
  ) => {
    try {
      // Use a direct fetch call to handle 204 responses properly
      const response = await fetch(
        `/admin/hotel-management/hotels/${hotelId}/pricing/occupancy-config/${occupancyConfigId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // For 204 responses, just return a success object without trying to parse JSON
      if (response.status === 204 || response.status === 200) {
        return { success: true };
      }

      // Only try to parse JSON for non-204 responses
      if (response.ok) {
        try {
          const data = await response.json();
          return data.data || { success: true };
        } catch (e) {
          // If JSON parsing fails, still return success for successful requests
          return { success: true };
        }
      }

      throw new Error(
        `Failed to delete occupancy config: ${response.statusText}`
      );
    } catch (error) {
      console.error("Error deleting occupancy config:", error);
      throw error;
    }
  };

  return {
    deleteOccupancyConfig,
  };
};
