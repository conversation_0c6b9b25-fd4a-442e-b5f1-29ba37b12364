import { useQuery } from "@tanstack/react-query";

export interface TolgeeLanguage {
  id: number;
  tag: string;
  name: string;
  originalName: string;
  flagEmoji: string;
  base: boolean;
}

// Query key factory for project languages
export const projectLanguagesKeys = {
  all: ["project-languages"] as const,
  list: () => [...projectLanguagesKeys.all, "list"] as const,
};

// Fetch function for project languages
const fetchProjectLanguages = async (): Promise<TolgeeLanguage[]> => {
  const apiKey = import.meta.env.VITE_TOLGEE_API_KEY;
  const projectId = import.meta.env.VITE_TOLGEE_PROJECT_ID;

  if (!projectId) {
    throw new Error("Missing VITE_TOLGEE_PROJECT_ID in environment");
  }
  if (!apiKey) {
    throw new Error("Missing VITE_TOLGEE_API_KEY in environment");
  }

  const url = `https://app.tolgee.io/v2/projects/${projectId}/languages`;
  const response = await fetch(url, {
    headers: {
      Accept: "application/json",
      "X-API-Key": apiKey,
    },
  });

  if (!response.ok) {
    throw new Error(
      `HTTP ${response.status}: Failed to fetch project languages`
    );
  }

  const data = (await response.json()) as TolgeeLanguage[];
  console.log("Fetched languages:", data);

  return Array.isArray(data) ? data : [];
};

export function useProjectLanguages() {
  const {
    data: languages = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: projectLanguagesKeys.list(),
    queryFn: fetchProjectLanguages,
    // Cache for 24 hours as requested
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    // Don't refetch on window focus since languages rarely change
    refetchOnWindowFocus: false,
    // Don't refetch on reconnect since this is static configuration data
    refetchOnReconnect: false,
    // Retry failed requests 2 times
    retry: 2,
    // Retry delay with exponential backoff
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return {
    languages,
    loading,
    error: error as Error | null,
  };
}
