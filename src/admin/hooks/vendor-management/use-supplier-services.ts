import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

export interface SupplierService {
  id: string;
  supplier_id: string;
  name: string;
  description?: string;
  category: string;
  duration_minutes?: number;
  capacity?: number;
  is_available: boolean;
  availability_schedule?: Record<string, any>;
  requirements?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CreateSupplierServiceData {
  supplier_id: string;
  name: string;
  description?: string;
  category: string;
  duration_minutes?: number;
  capacity?: number;
  is_available?: boolean;
  availability_schedule?: Record<string, any>;
  requirements?: Record<string, any>;
}

export interface UpdateSupplierServiceData extends Partial<CreateSupplierServiceData> {
  id: string;
}

export interface SupplierServiceFilters {
  supplier_id?: string;
  category?: string;
  is_available?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface SupplierServicesResponse {
  services: SupplierService[];
  count: number;
  offset: number;
  limit: number;
}

export interface SupplierServiceResponse {
  service: SupplierService;
}

// Query Keys
export const supplierServiceKeys = {
  all: ["supplier-services"] as const,
  lists: () => [...supplierServiceKeys.all, "list"] as const,
  list: (filters: SupplierServiceFilters) => [...supplierServiceKeys.lists(), filters] as const,
  details: () => [...supplierServiceKeys.all, "detail"] as const,
  detail: (id: string) => [...supplierServiceKeys.details(), id] as const,
};

// Hooks
export const useSupplierServices = (filters: SupplierServiceFilters = {}) => {
  return useQuery({
    queryKey: supplierServiceKeys.list(filters),
    queryFn: async (): Promise<SupplierServicesResponse> => {
      const params = new URLSearchParams();

      if (filters.supplier_id) params.append("supplier_id", filters.supplier_id);
      if (filters.category) params.append("category", filters.category);
      if (filters.is_available !== undefined) params.append("is_available", filters.is_available.toString());
      if (filters.search) params.append("search", filters.search);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const url = `/admin/supplier-management/services${params.toString() ? `?${params.toString()}` : ''}`;

      const response = await fetch(url, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    },
  });
};

export const useSupplierService = (id: string) => {
  return useQuery({
    queryKey: supplierServiceKeys.detail(id),
    queryFn: async (): Promise<SupplierServiceResponse> => {
      const response = await fetch(`/admin/supplier-management/services/${id}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    },
    enabled: !!id,
  });
};

export const useCreateSupplierService = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: CreateSupplierServiceData): Promise<SupplierServiceResponse> => {
      const response = await fetch("/admin/supplier-management/services", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create supplier service: ${response.statusText} - ${errorText}`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: supplierServiceKeys.lists() });
      toast.success(`Service "${data.service.name}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create service: ${error.message}`);
    },
  });
};

export const useUpdateSupplierService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateSupplierServiceData): Promise<SupplierServiceResponse> => {
      const { id, ...updateData } = data;
      const response = await fetch(`/admin/supplier-management/services/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to update service: ${response.statusText}`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: supplierServiceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierServiceKeys.detail(data.service.id) });
      toast.success(`Service "${data.service.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update service: ${error.message}`);
    },
  });
};

export const useDeleteSupplierService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ results: any }> => {
      const response = await fetch(`/admin/supplier-management/services/${id}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to delete service: ${response.statusText}`);
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierServiceKeys.lists() });
      toast.success("Service deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete service: ${error.message}`);
    },
  });
};

export const useUpdateSupplierServiceAvailability = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, is_available }: { id: string; is_available: boolean }): Promise<SupplierServiceResponse> => {
      const response = await fetch(`/admin/supplier-management/services/${id}/availability`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ is_available }),
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to update availability: ${response.statusText}`);
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: supplierServiceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierServiceKeys.detail(data.service.id) });
      toast.success(`Service ${variables.is_available ? 'enabled' : 'disabled'} successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update availability: ${error.message}`);
    },
  });
};
