import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";
import { toast } from "@camped-ai/ui";
import { suppliersListKeys } from "../supplier-management/use-suppliers-list";

export interface Supplier {
  id: string;
  name: string;
  handle: string;
  description?: string;
  business_type: string;
  preference?: "Preferred" | "Backup";
  status: string;
  verification_status: string;
  primary_contact_name: string;
  primary_contact_email: string;
  primary_contact_phone?: string;
  secondary_contact_name?: string;
  secondary_contact_email?: string;
  secondary_contact_phone?: string;
  business_registration_number?: string;
  tax_id?: string;
  website?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  categories?: string[];
  rating?: number;
  total_orders: number;
  completed_orders: number;
  created_at: string;
  updated_at: string;
}

export interface CreateSupplierData {
  name: string;
  handle?: string;
  description?: string;
  supplier_type?: "Company" | "Individual";
  business_type: string;
  status?:
    | "Active"
    | "Inactive"
    | "Pending Approval"
    | "Suspended"
    | "Terminated";
  verification_status?: "verified" | "unverified" | "in_review";

  // Main Contact Information
  email: string;
  phone?: string;

  // Business & Region Information
  region?: string;
  timezone?: string;
  language_preference?: string[];

  // Financial Information
  payment_method?: string;
  payout_terms?: string;
  bank_account_details?: string;

  // Categories
  categories?: string[];

  // Business Details
  business_registration_number?: string;
  tax_id?: string;
  website?: string;
  default_currency?: string;

  // Address Information
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;

  // Supplier Contacts
  contacts?: Array<{
    name: string;
    email: string;
    phone_number?: string;
    is_whatsapp?: boolean;
    is_primary?: boolean;
  }>;

  metadata?: Record<string, any>;
}

export interface UpdateSupplierData extends Partial<CreateSupplierData> {
  id: string;
}

export interface SupplierFilters {
  business_type?: string;
  status?: string;
  verification_status?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface SuppliersResponse {
  suppliers: Supplier[];
  count: number;
  offset: number;
  limit: number;
}

export interface SupplierResponse {
  supplier: Supplier;
  performance_metrics?: {
    completion_rate: number;
    average_rating: number;
    total_revenue: number;
    on_time_delivery_rate: number;
  };
}

export interface SupplierDocument {
  id: string;
  name: string;
  url: string;
  rank: number;
  metadata: {
    originalName?: string;
    mimeType?: string;
    size?: number;
    uploadedAt?: string;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}

export interface SupplierDocumentsResponse {
  supplier_documents: SupplierDocument[];
}

// Query Keys
export const supplierKeys = {
  all: ["suppliers"] as const,
  lists: () => [...supplierKeys.all, "list"] as const,
  list: (filters: SupplierFilters) =>
    [...supplierKeys.lists(), filters] as const,
  details: () => [...supplierKeys.all, "detail"] as const,
  detail: (id: string) => [...supplierKeys.details(), id] as const,
  documents: () => [...supplierKeys.all, "documents"] as const,
  supplierDocuments: (id: string) => [...supplierKeys.documents(), id] as const,
};

// Hooks
export const useSuppliers = (filters: SupplierFilters = {}) => {
  return useQuery({
    queryKey: supplierKeys.list(filters),
    queryFn: async (): Promise<SuppliersResponse> => {
      const params = new URLSearchParams();

      if (filters.business_type)
        params.append("business_type", filters.business_type);
      if (filters.status) params.append("status", filters.status);
      if (filters.verification_status)
        params.append("verification_status", filters.verification_status);
      if (filters.search) params.append("search", filters.search);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const url = `/admin/supplier-management/suppliers${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      try {
        const result = (await sdk.client.fetch(url)) as any;

        return {
          suppliers: result.suppliers || [],
          count: result.count || 0,
          limit: result.limit || 20,
          offset: result.offset || 0,
        };
      } catch (error) {
        throw error;
      }
    },
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export const useSupplier = (id: string) => {
  return useQuery({
    queryKey: supplierKeys.detail(id),
    queryFn: async (): Promise<SupplierResponse> => {
      const result = await sdk.client.fetch(
        `/admin/supplier-management/suppliers/${id}`
      );

      return result as SupplierResponse;
    },
    enabled: !!id,
  });
};

export const useCreateSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateSupplierData): Promise<SupplierResponse> => {
      const result = await sdk.client.fetch(
        "/admin/supplier-management/suppliers",
        {
          method: "POST",
          body: data,
        }
      );

      return result as SupplierResponse;
    },
    onMutate: async (newSupplier: CreateSupplierData) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: suppliersListKeys.lists() });

      // Snapshot the previous value
      const previousSuppliers = queryClient.getQueriesData({
        queryKey: suppliersListKeys.lists(),
      });

      // Create an optimistic supplier object
      const optimisticSupplier = {
        id: `temp-${Date.now()}`, // Temporary ID
        name: newSupplier.name,
        email: newSupplier.email,
        business_type: newSupplier.business_type,
        status: newSupplier.status || "Active",
        supplier_type: newSupplier.supplier_type || "Company",
        phone: newSupplier.phone || "",
        website: newSupplier.website || "",
        handle: newSupplier.handle || "",
        region: newSupplier.region || "",
        timezone: newSupplier.timezone || "",
        language_preference: newSupplier.language_preference || [],
        payment_method: newSupplier.payment_method || "",
        payout_terms: newSupplier.payout_terms || "",
        tax_id: newSupplier.tax_id || "",
        default_currency: newSupplier.default_currency || "CHF",
        bank_account_details: newSupplier.bank_account_details || "",
        address_line_1: newSupplier.address_line_1 || "",
        address_line_2: newSupplier.address_line_2 || "",
        city: newSupplier.city || "",
        state: newSupplier.state || "",
        postal_code: newSupplier.postal_code || "",
        country: newSupplier.country || "",
        contacts: newSupplier.contacts || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        verification_status: "unverified",
        primary_contact_name: newSupplier.contacts?.[0]?.name || "",
        primary_contact_email:
          newSupplier.contacts?.[0]?.email || newSupplier.email,
        primary_contact_phone:
          newSupplier.contacts?.[0]?.phone_number || newSupplier.phone,
        rating: 0,
        total_orders: 0,
        completed_orders: 0,
      };

      // Optimistically add the supplier to all cached queries
      queryClient.setQueriesData(
        { queryKey: suppliersListKeys.lists() },
        (old: any) => {
          if (!old?.suppliers) return old;
          return {
            ...old,
            suppliers: [optimisticSupplier, ...old.suppliers],
            count: (old.count || 0) + 1,
          };
        }
      );

      // Return a context object with the snapshotted value
      return { previousSuppliers, optimisticSupplier };
    },
    onSuccess: (data) => {
      // Invalidate both the old supplier keys and the new suppliers list keys
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: suppliersListKeys.lists() });
      toast.success(`Supplier "${data.supplier.name}" created successfully`);
    },
    onError: (error: Error, newSupplier: CreateSupplierData, context: any) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousSuppliers) {
        context.previousSuppliers.forEach(([queryKey, data]: [any, any]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
      toast.error(`Failed to create supplier: ${error.message}`);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: suppliersListKeys.lists() });
    },
  });
};

export const useUpdateSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateSupplierData): Promise<SupplierResponse> => {
      const { id, ...updateData } = data;
      const result = await sdk.client.fetch(
        `/admin/supplier-management/suppliers/${id}`,
        {
          method: "PUT",
          body: updateData,
        }
      );

      return result as SupplierResponse;
    },
    onMutate: async (updateData: UpdateSupplierData) => {
      const { id, ...updates } = updateData;

      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: suppliersListKeys.lists() });
      await queryClient.cancelQueries({ queryKey: supplierKeys.detail(id) });

      // Snapshot the previous values
      const previousSuppliers = queryClient.getQueriesData({
        queryKey: suppliersListKeys.lists(),
      });
      const previousSupplier = queryClient.getQueryData(
        supplierKeys.detail(id)
      );

      // Optimistically update the supplier in all cached queries
      queryClient.setQueriesData(
        { queryKey: suppliersListKeys.lists() },
        (old: any) => {
          if (!old?.suppliers) return old;
          return {
            ...old,
            suppliers: old.suppliers.map((supplier: any) =>
              supplier.id === id
                ? {
                    ...supplier,
                    ...updates,
                    updated_at: new Date().toISOString(),
                  }
                : supplier
            ),
          };
        }
      );

      // Also update the individual supplier detail if it exists
      queryClient.setQueryData(supplierKeys.detail(id), (old: any) => {
        if (!old?.supplier) return old;
        return {
          ...old,
          supplier: {
            ...old.supplier,
            ...updates,
            updated_at: new Date().toISOString(),
          },
        };
      });

      // Return a context object with the snapshotted values
      return { previousSuppliers, previousSupplier, supplierId: id };
    },
    onSuccess: (data) => {
      // Invalidate both the old supplier keys and the new suppliers list keys
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: suppliersListKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: supplierKeys.detail(data.supplier.id),
      });
      toast.success(`Supplier "${data.supplier.name}" updated successfully`);
    },
    onError: (error: Error, updateData: UpdateSupplierData, context: any) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousSuppliers) {
        context.previousSuppliers.forEach(([queryKey, data]: [any, any]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
      if (context?.previousSupplier && context?.supplierId) {
        queryClient.setQueryData(
          supplierKeys.detail(context.supplierId),
          context.previousSupplier
        );
      }
      toast.error(`Failed to update supplier: ${error.message}`);
    },
    onSettled: (data, error, updateData) => {
      // Always refetch after error or success to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: suppliersListKeys.lists() });
      if (updateData?.id) {
        queryClient.invalidateQueries({
          queryKey: supplierKeys.detail(updateData.id),
        });
      }
    },
  });
};

export const useActivateSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      activate,
    }: {
      id: string;
      activate: boolean;
    }): Promise<SupplierResponse> => {
      const result = await sdk.client.fetch(
        "/admin/supplier-management/suppliers",
        {
          method: "PATCH",
          body: { id, activate },
        }
      );

      return result as SupplierResponse;
    },
    onSuccess: (data, variables) => {
      // Invalidate both the old supplier keys and the new suppliers list keys
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: suppliersListKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: supplierKeys.detail(data.supplier.id),
      });
      toast.success(
        `Supplier ${
          variables.activate ? "activated" : "deactivated"
        } successfully`
      );
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDeleteSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ results: any }> => {
      // First, check if supplier has any associated supplier offerings
      const offeringsResponse = await fetch(
        `/admin/supplier-management/supplier-offerings?supplier_id=${id}&limit=1`,
        {
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!offeringsResponse.ok) {
        throw new Error("Failed to check supplier offerings");
      }

      const offeringsData = await offeringsResponse.json();

      // If supplier has offerings, prevent deletion
      if (offeringsData.supplier_offerings && offeringsData.supplier_offerings.length > 0) {
        const error = new Error(
          "Cannot delete supplier: This supplier has active offerings."
        );
        (error as any).type = "reference_constraint_error";
        (error as any).status = 409;
        throw error;
      }

      // If no offerings found, proceed with deletion
      const result = await sdk.client.fetch(
        `/admin/supplier-management/suppliers/${id}`,
        {
          method: "DELETE",
        }
      );

      return result as { results: any };
    },
    onMutate: async (supplierId: string) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: suppliersListKeys.lists() });

      // Snapshot the previous value
      const previousSuppliers = queryClient.getQueriesData({
        queryKey: suppliersListKeys.lists(),
      });

      // Optimistically remove the supplier from all cached queries
      queryClient.setQueriesData(
        { queryKey: suppliersListKeys.lists() },
        (old: any) => {
          if (!old?.suppliers) return old;
          return {
            ...old,
            suppliers: old.suppliers.filter(
              (supplier: any) => supplier.id !== supplierId
            ),
            count: Math.max(0, (old.count || 0) - 1),
          };
        }
      );

      // Return a context object with the snapshotted value
      return { previousSuppliers };
    },
    onSuccess: () => {
      // Invalidate both the old supplier keys and the new suppliers list keys
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: suppliersListKeys.lists() });
      toast.success("Supplier deleted successfully");
    },
    onError: (error: Error, supplierId: string, context: any) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousSuppliers) {
        context.previousSuppliers.forEach(([queryKey, data]: [any, any]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      // Handle specific error types
      const errorType = (error as any).type;
      const status = (error as any).status;

      if (status === 409 && errorType === "reference_constraint_error") {
        // This is our custom validation error for supplier offerings
        toast.error(error.message);
      } else {
        toast.error(`Failed to delete supplier: ${error.message}`);
      }
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: suppliersListKeys.lists() });
    },
  });
};

// Document Hooks
export const useSupplierDocuments = (supplierId: string) => {
  return useQuery({
    queryKey: supplierKeys.supplierDocuments(supplierId),
    queryFn: async (): Promise<SupplierDocumentsResponse> => {
      const result = await sdk.client.fetch(
        `/admin/supplier-management/suppliers/${supplierId}/documents`
      );
      return result as SupplierDocumentsResponse;
    },
    enabled: !!supplierId,
  });
};

export const useUploadSupplierDocuments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      supplierId,
      files,
    }: {
      supplierId: string;
      files: File[];
    }): Promise<any> => {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });

      // Use native fetch to avoid SDK issues with FormData
      const response = await fetch(
        `/admin/supplier-management/suppliers/${supplierId}/documents`,
        {
          method: "POST",
          body: formData,
          credentials: "include", // if your app uses cookies/session
        }
      );

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || "Failed to upload documents");
      }

      return response.json();
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: supplierKeys.supplierDocuments(variables.supplierId),
      });
      toast.success("Documents uploaded successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to upload documents: ${error.message}`);
    },
  });
};

export const useDeleteSupplierDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      documentId,
      supplierId,
      documentName,
    }: {
      documentId: string;
      supplierId: string;
      documentName?: string;
    }): Promise<any> => {
      const result = await sdk.client.fetch(
        `/admin/supplier-management/documents/${documentId}`,
        {
          method: "DELETE",
        }
      );

      return result;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: supplierKeys.supplierDocuments(variables.supplierId),
      });
      const message = variables.documentName
        ? `"${variables.documentName}" deleted successfully`
        : "Document deleted successfully";
      toast.success(message);
    },
    onError: (error: Error, variables) => {
      const message = variables.documentName
        ? `Failed to delete "${variables.documentName}": ${error.message}`
        : `Failed to delete document: ${error.message}`;
      toast.error(message);
    },
  });
};
