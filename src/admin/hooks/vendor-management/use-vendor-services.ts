import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAdminClient } from "../use-admin-client";
import { sdk } from "../../lib/sdk";
import { toast } from "@camped-ai/ui";

export interface VendorService {
  id: string;
  vendor_id: string;
  name: string;
  description?: string;
  category: string;
  duration_minutes?: number;
  capacity?: number;
  is_available: boolean;
  availability_schedule?: Record<string, any>;
  requirements?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CreateVendorServiceData {
  vendor_id: string;
  name: string;
  description?: string;
  category: string;
  duration_minutes?: number;
  capacity?: number;
  is_available?: boolean;
  availability_schedule?: Record<string, any>;
  requirements?: Record<string, any>;
}

export interface UpdateVendorServiceData extends Partial<CreateVendorServiceData> {
  id: string;
}

export interface VendorServiceFilters {
  vendor_id?: string;
  category?: string;
  is_available?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface VendorServicesResponse {
  services: VendorService[];
  count: number;
  offset: number;
  limit: number;
}

export interface VendorServiceResponse {
  service: VendorService;
}

// Query Keys
export const vendorServiceKeys = {
  all: ["vendor-services"] as const,
  lists: () => [...vendorServiceKeys.all, "list"] as const,
  list: (filters: VendorServiceFilters) => [...vendorServiceKeys.lists(), filters] as const,
  details: () => [...vendorServiceKeys.all, "detail"] as const,
  detail: (id: string) => [...vendorServiceKeys.details(), id] as const,
  byVendor: (vendorId: string) => [...vendorServiceKeys.all, "vendor", vendorId] as const,
};

// Hooks
export const useVendorServices = (filters: VendorServiceFilters = {}) => {
  return useQuery({
    queryKey: vendorServiceKeys.list(filters),
    queryFn: async (): Promise<VendorServicesResponse> => {
      const params = new URLSearchParams();
      
      if (filters.vendor_id) params.append("vendor_id", filters.vendor_id);
      if (filters.category) params.append("category", filters.category);
      if (filters.is_available !== undefined) params.append("is_available", filters.is_available.toString());
      if (filters.search) params.append("search", filters.search);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      
      const endpoint = filters.vendor_id 
        ? `/admin/vendor_management/vendors/${filters.vendor_id}/services?${params.toString()}`
        : `/admin/vendor_management/services?${params.toString()}`;
      
      const response = await sdk.client.fetch(endpoint);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch vendor services: ${response.statusText}`);
      }
      
      return response.json();
    },
  });
};

export const useVendorService = (id: string) => {
  return useQuery({
    queryKey: vendorServiceKeys.detail(id),
    queryFn: async (): Promise<VendorServiceResponse> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/services/${id}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch vendor service: ${response.statusText}`);
      }

      return response.json();
    },
    enabled: !!id,
  });
};

export const useCreateVendorService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateVendorServiceData): Promise<VendorServiceResponse> => {
      const endpoint = `/admin/vendor_management/vendors/${data.vendor_id}/services`;

      const response = await sdk.client.fetch(endpoint, {
        method: "POST",
        body: data,
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to create service: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.byVendor(data.service.vendor_id) });
      toast.success(`Service "${data.service.name}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create service: ${error.message}`);
    },
  });
};

export const useUpdateVendorService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateVendorServiceData): Promise<VendorServiceResponse> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/services/${data.id}`, {
        method: "PUT",
        body: data,
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to update service: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.detail(data.service.id) });
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.byVendor(data.service.vendor_id) });
      toast.success(`Service "${data.service.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update service: ${error.message}`);
    },
  });
};

export const useDeleteVendorService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ results: any }> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/services/${id}`, {
        method: "DELETE",
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to delete service: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.lists() });
      toast.success("Service deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete service: ${error.message}`);
    },
  });
};

// Availability management
export const useUpdateServiceAvailability = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      is_available,
      availability_schedule
    }: {
      id: string;
      is_available: boolean;
      availability_schedule?: Record<string, any>;
    }): Promise<VendorServiceResponse> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/services/${id}`, {
        method: "PUT",
        body: {
          id,
          is_available,
          availability_schedule,
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to update availability: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.detail(data.service.id) });
      queryClient.invalidateQueries({ queryKey: vendorServiceKeys.byVendor(data.service.vendor_id) });
      toast.success("Service availability updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update availability: ${error.message}`);
    },
  });
};
