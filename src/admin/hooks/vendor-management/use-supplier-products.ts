import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAdminClient } from "../use-admin-client";
import { sdk } from "../../lib/sdk";
import { toast } from "@camped-ai/ui";

export interface SupplierProduct {
  id: string;
  supplier_id: string;
  name: string;
  description?: string;
  sku?: string;
  category: string;
  unit_of_measure: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock?: number;
  specifications?: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateSupplierProductData {
  supplier_id: string;
  name: string;
  description?: string;
  sku?: string;
  category: string;
  unit_of_measure: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock?: number;
  specifications?: Record<string, any>;
  is_active?: boolean;
}

export interface UpdateSupplierProductData
  extends Partial<CreateSupplierProductData> {
  id: string;
}

export interface SupplierProductFilters {
  supplier_id?: string;
  category?: string;
  is_active?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface SupplierProductsResponse {
  products: SupplierProduct[];
  count: number;
  offset: number;
  limit: number;
}

export interface SupplierProductResponse {
  product: SupplierProduct;
}

// Query Keys
export const supplierProductKeys = {
  all: ["supplier-products"] as const,
  lists: () => [...supplierProductKeys.all, "list"] as const,
  list: (filters: SupplierProductFilters) =>
    [...supplierProductKeys.lists(), filters] as const,
  details: () => [...supplierProductKeys.all, "detail"] as const,
  detail: (id: string) => [...supplierProductKeys.details(), id] as const,
};

// Hooks
export const useSupplierProducts = (filters: SupplierProductFilters = {}) => {
  return useQuery({
    queryKey: supplierProductKeys.list(filters),
    queryFn: async (): Promise<SupplierProductsResponse> => {
      const params = new URLSearchParams();

      if (filters.supplier_id)
        params.append("supplier_id", filters.supplier_id);
      if (filters.category) params.append("category", filters.category);
      if (filters.is_active !== undefined)
        params.append("is_active", filters.is_active.toString());
      if (filters.search) params.append("search", filters.search);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const url = `/admin/supplier-management/products${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      try {
        const result = (await sdk.client.fetch(url)) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
  });
};

export const useSupplierProduct = (id: string) => {
  return useQuery({
    queryKey: supplierProductKeys.detail(id),
    queryFn: async (): Promise<SupplierProductResponse> => {
      try {
        const result = (await sdk.client.fetch(
          `/admin/supplier-management/products/${id}`
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!id,
  });
};

export const useCreateSupplierProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateSupplierProductData
    ): Promise<SupplierProductResponse> => {
      try {
        const result = (await sdk.client.fetch(
          "/admin/supplier-management/products",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
          }
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: supplierProductKeys.lists() });
      toast.success(`Product "${data.product.name}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create product: ${error.message}`);
    },
  });
};

export const useUpdateSupplierProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: UpdateSupplierProductData
    ): Promise<SupplierProductResponse> => {
      const { id, ...updateData } = data;
      try {
        const result = (await sdk.client.fetch(
          `/admin/supplier-management/products/${id}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(updateData),
          }
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: supplierProductKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: supplierProductKeys.detail(data.product.id),
      });
      toast.success(`Product "${data.product.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update product: ${error.message}`);
    },
  });
};

export const useDeleteSupplierProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ results: any }> => {
      try {
        const result = (await sdk.client.fetch(
          `/admin/supplier-management/products/${id}`,
          {
            method: "DELETE",
          }
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierProductKeys.lists() });
      toast.success("Product deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete product: ${error.message}`);
    },
  });
};

export const useUpdateSupplierProductStock = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      stock,
    }: {
      id: string;
      stock: {
        current_stock: number;
        minimum_stock?: number;
        maximum_stock?: number;
      };
    }): Promise<SupplierProductResponse> => {
      try {
        const result = (await sdk.client.fetch(
          `/admin/supplier-management/products/${id}/stock`,
          {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(stock),
          }
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: supplierProductKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: supplierProductKeys.detail(data.product.id),
      });
      toast.success("Stock updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update stock: ${error.message}`);
    },
  });
};
