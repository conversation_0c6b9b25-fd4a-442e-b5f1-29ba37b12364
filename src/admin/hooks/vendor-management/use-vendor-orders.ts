import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAdminClient } from "../use-admin-client";
import { sdk } from "../../lib/sdk";
import { toast } from "@camped-ai/ui";

export interface VendorOrderItem {
  id: string;
  order_id: string;
  item_type: "product" | "service";
  item_id: string;
  item_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  metadata?: Record<string, any>;
}

export interface VendorOrder {
  id: string;
  vendor_id: string;
  order_number: string;
  status: string;
  order_type: "product" | "service" | "mixed";
  items: VendorOrderItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  currency_code: string;
  customer_name?: string;
  customer_email?: string;
  requested_delivery_date?: string;
  actual_delivery_date?: string;
  notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CreateVendorOrderData {
  vendor_id: string;
  order_type: "product" | "service" | "mixed";
  items: {
    item_type: "product" | "service";
    item_id: string;
    quantity: number;
    unit_price: number;
    metadata?: Record<string, any>;
  }[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  currency_code: string;
  customer_name?: string;
  customer_email?: string;
  requested_delivery_date?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface UpdateVendorOrderData {
  id: string;
  status?: string;
  actual_delivery_date?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface VendorOrderFilters {
  vendor_id?: string;
  status?: string;
  order_type?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
}

export interface VendorOrdersResponse {
  orders: VendorOrder[];
  count: number;
  offset: number;
  limit: number;
}

export interface VendorOrderResponse {
  order: VendorOrder;
  items: VendorOrderItem[];
}

// Query Keys
export const vendorOrderKeys = {
  all: ["vendor-orders"] as const,
  lists: () => [...vendorOrderKeys.all, "list"] as const,
  list: (filters: VendorOrderFilters) => [...vendorOrderKeys.lists(), filters] as const,
  details: () => [...vendorOrderKeys.all, "detail"] as const,
  detail: (id: string) => [...vendorOrderKeys.details(), id] as const,
  byVendor: (vendorId: string) => [...vendorOrderKeys.all, "vendor", vendorId] as const,
};

// Hooks
export const useVendorOrders = (filters: VendorOrderFilters = {}) => {
  return useQuery({
    queryKey: vendorOrderKeys.list(filters),
    queryFn: async (): Promise<VendorOrdersResponse> => {
      const params = new URLSearchParams();
      
      if (filters.vendor_id) params.append("vendor_id", filters.vendor_id);
      if (filters.status) params.append("status", filters.status);
      if (filters.order_type) params.append("order_type", filters.order_type);
      if (filters.search) params.append("search", filters.search);
      if (filters.date_from) params.append("date_from", filters.date_from);
      if (filters.date_to) params.append("date_to", filters.date_to);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      
      const endpoint = filters.vendor_id 
        ? `/admin/vendor_management/vendors/${filters.vendor_id}/orders?${params.toString()}`
        : `/admin/vendor_management/orders?${params.toString()}`;
      
      const response = await sdk.client.fetch(endpoint);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch vendor orders: ${response.statusText}`);
      }
      
      return response.json();
    },
  });
};

export const useVendorOrder = (id: string) => {
  return useQuery({
    queryKey: vendorOrderKeys.detail(id),
    queryFn: async (): Promise<VendorOrderResponse> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/orders/${id}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch vendor order: ${response.statusText}`);
      }
      
      return response.json();
    },
    enabled: !!id,
  });
};

export const useCreateVendorOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateVendorOrderData): Promise<VendorOrderResponse> => {
      const endpoint = `/admin/vendor_management/vendors/${data.vendor_id}/orders`;

      const response = await sdk.client.fetch(endpoint, {
        method: "POST",
        body: data,
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to create order: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorOrderKeys.byVendor(data.order.vendor_id) });
      toast.success(`Order ${data.order.order_number} created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create order: ${error.message}`);
    },
  });
};

export const useUpdateVendorOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateVendorOrderData): Promise<VendorOrderResponse> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/orders/${data.id}`, {
        method: "PUT",
        body: data,
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to update order: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorOrderKeys.detail(data.order.id) });
      queryClient.invalidateQueries({ queryKey: vendorOrderKeys.byVendor(data.order.vendor_id) });
      toast.success(`Order ${data.order.order_number} updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update order: ${error.message}`);
    },
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      status,
      actual_delivery_date
    }: {
      id: string;
      status: string;
      actual_delivery_date?: string;
    }): Promise<VendorOrderResponse> => {
      const response = await sdk.client.fetch(`/admin/vendor_management/orders/${id}`, {
        method: "PUT",
        body: {
          id,
          status,
          actual_delivery_date,
        },
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || `Failed to update order status: ${response.statusText}`);
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: vendorOrderKeys.detail(data.order.id) });
      queryClient.invalidateQueries({ queryKey: vendorOrderKeys.byVendor(data.order.vendor_id) });
      toast.success(`Order status updated to ${data.order.status}`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update order status: ${error.message}`);
    },
  });
};
