import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAdminClient } from "../use-admin-client";
import { sdk } from "../../lib/sdk";
import { toast } from "@camped-ai/ui";

export interface Vendor {
  id: string;
  name: string;
  handle: string;
  description?: string;
  business_type: string;
  status: string;
  verification_status: string;
  primary_contact_name: string;
  primary_contact_email: string;
  primary_contact_phone?: string;
  secondary_contact_name?: string;
  secondary_contact_email?: string;
  secondary_contact_phone?: string;
  business_registration_number?: string;
  tax_id?: string;
  website?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  rating?: number;
  total_orders: number;
  completed_orders: number;
  created_at: string;
  updated_at: string;
}

export interface CreateVendorData {
  name: string;
  handle?: string;
  description?: string;
  business_type: string;
  primary_contact_name: string;
  primary_contact_email: string;
  primary_contact_phone?: string;
  secondary_contact_name?: string;
  secondary_contact_email?: string;
  secondary_contact_phone?: string;
  business_registration_number?: string;
  tax_id?: string;
  website?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export interface UpdateVendorData extends Partial<CreateVendorData> {
  id: string;
}

export interface VendorFilters {
  business_type?: string;
  status?: string;
  verification_status?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface VendorsResponse {
  vendors: Vendor[];
  count: number;
  offset: number;
  limit: number;
}

export interface VendorResponse {
  vendor: Vendor;
  performance_metrics?: {
    completion_rate: number;
    average_rating: number;
    total_revenue: number;
    on_time_delivery_rate: number;
  };
}

// Query Keys
export const vendorKeys = {
  all: ["vendors"] as const,
  lists: () => [...vendorKeys.all, "list"] as const,
  list: (filters: VendorFilters) => [...vendorKeys.lists(), filters] as const,
  details: () => [...vendorKeys.all, "detail"] as const,
  detail: (id: string) => [...vendorKeys.details(), id] as const,
};

// Hooks
export const useVendors = (filters: VendorFilters = {}) => {
  console.log("Hook Debug - useVendors called with filters:", filters);
  return useQuery({
    queryKey: vendorKeys.list(filters),
    queryFn: async (): Promise<VendorsResponse> => {
      const params = new URLSearchParams();

      if (filters.business_type)
        params.append("business_type", filters.business_type);
      if (filters.status) params.append("status", filters.status);
      if (filters.verification_status)
        params.append("verification_status", filters.verification_status);
      if (filters.search) params.append("search", filters.search);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const url = `/admin/vendor_management/vendors${
        params.toString() ? `?${params.toString()}` : ""
      }`;
      console.log("Hook Debug - Making request to:", url);

      try {
        const result = (await sdk.client.fetch(url)) as any;
        console.log("Hook Debug - SDK fetch result:", result);
        console.log("Hook Debug - Result type:", typeof result);
        console.log("Hook Debug - Result keys:", Object.keys(result || {}));
        console.log("Hook Debug - Result vendors:", result?.vendors);
        return result;
      } catch (error) {
        console.error("Hook Debug - SDK fetch error:", error);
        throw error;
      }
    },
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export const useVendor = (id: string) => {
  return useQuery({
    queryKey: vendorKeys.detail(id),
    queryFn: async (): Promise<VendorResponse> => {
      return sdk.client.fetch(`/admin/vendor_management/vendors/${id}`);
    },
    enabled: !!id,
  });
};

export const useCreateVendor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateVendorData): Promise<VendorResponse> => {
      console.log("Original data:", data);

      // Use native fetch like the successful booking form
      const response = await fetch("/admin/vendor_management/vendors", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        credentials: "include",
      });

      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.log("Error response:", errorText);
        throw new Error(
          `Failed to create vendor: ${response.statusText} - ${errorText}`
        );
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorKeys.lists() });
      toast.success(`Vendor "${data.vendor.name}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create vendor: ${error.message}`);
    },
  });
};

export const useUpdateVendor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateVendorData): Promise<VendorResponse> => {
      const response = await sdk.client.fetch(
        "/admin/vendor_management/vendors",
        {
          method: "PUT",
          body: data,
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(
          error.message || `Failed to update vendor: ${response.statusText}`
        );
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: vendorKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: vendorKeys.detail(data.vendor.id),
      });
      toast.success(`Vendor "${data.vendor.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update vendor: ${error.message}`);
    },
  });
};

export const useActivateVendor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      activate,
    }: {
      id: string;
      activate: boolean;
    }): Promise<VendorResponse> => {
      const response = await sdk.client.fetch(
        "/admin/vendor_management/vendors",
        {
          method: "PATCH",
          body: { id, activate },
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(
          error.message ||
            `Failed to ${activate ? "activate" : "deactivate"} vendor: ${
              response.statusText
            }`
        );
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: vendorKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: vendorKeys.detail(data.vendor.id),
      });
      toast.success(
        `Vendor ${
          variables.activate ? "activated" : "deactivated"
        } successfully`
      );
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDeleteVendor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ results: any }> => {
      const response = await sdk.client.fetch(
        "/admin/vendor_management/vendors",
        {
          method: "DELETE",
          body: { ids: id },
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(
          error.message || `Failed to delete vendor: ${response.statusText}`
        );
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: vendorKeys.lists() });
      toast.success("Vendor deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete vendor: ${error.message}`);
    },
  });
};
