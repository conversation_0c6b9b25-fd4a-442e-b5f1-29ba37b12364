import { useState, useCallback } from "react";

export interface TextTranslationRequest {
  text: string;
  targetLanguage: string;
  fieldType: "name" | "description" | "title" | "content";
  context?: {
    entityType?: "hotel" | "destination" | "cancellation_policy";
    entityName?: string;
    location?: string;
  };
}

export interface TextTranslationResponse {
  translatedText: string;
  success: boolean;
  error?: string;
}

export interface UseAITextTranslationOptions {
  onSuccess?: (translatedText: string) => void;
  onError?: (error: string) => void;
}

export interface UseAITextTranslationReturn {
  translateText: (request: TextTranslationRequest) => Promise<void>;
  isTranslating: boolean;
  error: string | null;
  lastTranslation: string | null;
}

/**
 * Get language name mapping for better AI context
 */
const getLanguageName = (languageCode: string): string => {
  const languageMap: Record<string, string> = {
    en: "English",
    de: "German",
    ja: "Japanese",
    fr: "French",
    es: "Spanish",
    it: "Italian",
    pt: "Portuguese",
    ru: "Russian",
    zh: "Chinese",
    ko: "Korean",
    ar: "Arabic",
    hi: "Hindi",
  };
  return languageMap[languageCode] || languageCode;
};

/**
 * Get field-specific translation instructions
 */
const getFieldInstructions = (fieldType: string): string => {
  const instructions: Record<string, string> = {
    name: "This is a name/title field. Keep it concise, clear, and appropriate for the context.",
    description:
      "This is a description field. Maintain the tone and style while ensuring clarity and accuracy.",
    title:
      "This is a title field. Keep it engaging and appropriate for the target audience.",
    content:
      "This is general content. Translate accurately while maintaining the original meaning and tone.",
  };
  return (
    instructions[fieldType] ||
    "Translate this text accurately while maintaining its meaning and context."
  );
};

/**
 * Create system prompt for text field translation
 */
const createSystemPrompt = (request: TextTranslationRequest): string => {
  const { targetLanguage, fieldType, context } = request;
  const languageName = getLanguageName(targetLanguage);
  const fieldInstructions = getFieldInstructions(fieldType);
  const entityType = context?.entityType || "content";

  return `You are a professional translator specializing in hospitality and travel content. Your task is to translate ${fieldType} text from English to ${languageName}.

Context:
- Entity type: ${entityType}
- Field type: ${fieldType}
- Target language: ${languageName} (${targetLanguage})
${context?.entityName ? `- Entity name: ${context.entityName}` : ""}
${context?.location ? `- Location: ${context.location}` : ""}

Instructions:
${fieldInstructions}

Requirements:
1. Translate the text accurately while preserving meaning
2. Use appropriate terminology for the hospitality industry
3. Keep the translation natural and fluent
4. Maintain the original tone and style
5. Consider cultural context and local preferences
6. Return ONLY the translated text
7. Do not add explanations, comments, or additional formatting
8. Ensure the translation is appropriate for the target audience`;
};

/**
 * Clean up translated text by removing quotes and extra whitespace
 */
const cleanTranslatedText = (text: string): string => {
  if (!text) return text;

  // Remove leading and trailing whitespace
  let cleaned = text.trim();

  // Remove quotes from both ends if they exist
  // Handle both single and double quotes
  if (
    (cleaned.startsWith('"') && cleaned.endsWith('"')) ||
    (cleaned.startsWith("'") && cleaned.endsWith("'"))
  ) {
    cleaned = cleaned.slice(1, -1).trim();
  }

  // Remove any remaining quotes that might be at the beginning or end
  // This handles cases where AI might add quotes inconsistently
  cleaned = cleaned.replace(/^["']|["']$/g, "").trim();

  return cleaned;
};

/**
 * Hook for AI-powered translation of individual text fields
 */
export const useAITextTranslation = (
  options: UseAITextTranslationOptions = {}
): UseAITextTranslationReturn => {
  const [isTranslating, setIsTranslating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastTranslation, setLastTranslation] = useState<string | null>(null);

  const { onSuccess, onError } = options;

  const translateText = useCallback(
    async (request: TextTranslationRequest) => {
      setIsTranslating(true);
      setError(null);

      try {
        const { text, targetLanguage } = request;

        if (!text || text.trim().length === 0) {
          throw new Error("Text to translate cannot be empty");
        }

        // Validate target language
        if (!targetLanguage || targetLanguage === "en") {
          throw new Error(
            "Target language must be specified and cannot be English"
          );
        }

        console.log("🤖 Starting AI text translation:", {
          fieldType: request.fieldType,
          targetLanguage: request.targetLanguage,
          textLength: text.length,
          text: text.substring(0, 100) + (text.length > 100 ? "..." : ""),
        });

        const systemPrompt = createSystemPrompt(request);

        // Call the existing AI generation API
        const response = await fetch("/admin/ai-generate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            type: "general",
            prompt: `Translate the following ${
              request.fieldType
            } to ${getLanguageName(targetLanguage)}:\n\n"${text}"`,
            context: {
              systemPrompt,
              fieldType: request.fieldType,
              targetLanguage,
              entityType: request.context?.entityType,
              entityName: request.context?.entityName,
              location: request.context?.location,
            },
            maxLength: Math.max(text.length * 2, 500), // Allow for expansion in translation
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message || `Translation API error: ${response.status}`
          );
        }

        const data = await response.json();
        const rawTranslatedText = (data.content || data.message || "").trim();

        if (!rawTranslatedText) {
          throw new Error("Empty translation response");
        }

        // Clean up the translated text by removing quotes
        const translatedText = cleanTranslatedText(rawTranslatedText);

        console.log("✅ AI text translation successful:", {
          originalLength: text.length,
          rawTranslatedLength: rawTranslatedText.length,
          cleanedTranslatedLength: translatedText.length,
          rawTranslatedText:
            rawTranslatedText.substring(0, 100) +
            (rawTranslatedText.length > 100 ? "..." : ""),
          cleanedTranslatedText:
            translatedText.substring(0, 100) +
            (translatedText.length > 100 ? "..." : ""),
        });

        setLastTranslation(translatedText);
        onSuccess?.(translatedText);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Translation failed";
        console.error("❌ AI text translation error:", err);
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setIsTranslating(false);
      }
    },
    [onSuccess, onError]
  );

  return {
    translateText,
    isTranslating,
    error,
    lastTranslation,
  };
};
