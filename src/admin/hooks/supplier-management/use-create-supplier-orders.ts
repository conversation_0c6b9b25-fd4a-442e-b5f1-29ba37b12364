import { useMutation, useQueryClient } from "@tanstack/react-query";
import { BookingAddon } from "./use-booking-addons";

export interface CreateSupplierOrdersInput {
  supplier_groups: {
    supplier_id: string;
    supplier_name: string;
    addons: BookingAddon[];
  }[];
}

export interface CreateSupplierOrdersResponse {
  success: boolean;
  orders_created: number;
  orders: {
    supplier_id: string;
    supplier_name: string;
    order_id: string;
    order_number: string;
    total_amount: number;
    items_count: number;
  }[];
  errors?: string[];
}

const createSupplierOrdersFromAddons = async (
  input: CreateSupplierOrdersInput
): Promise<CreateSupplierOrdersResponse> => {
  console.log("🔍 Creating supplier orders from booking add-ons...", input);

  const response = await fetch(
    "/admin/supplier-management/create-orders-from-addons",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(input),
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to create supplier orders");
  }

  const data = await response.json();
  console.log("✅ Supplier orders created successfully:", data);
  return data;
};

export const useCreateSupplierOrders = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createSupplierOrdersFromAddons,
    onSuccess: (data) => {
      console.log(
        `✅ Successfully created ${data.orders_created} supplier orders`
      );

      // Invalidate booking add-ons queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ["booking-addons"] });

      // Also invalidate supplier orders queries if they exist
      queryClient.invalidateQueries({ queryKey: ["supplier-orders"] });

      console.log("🔄 Invalidated booking add-ons and supplier orders queries");
    },
    onError: (error) => {
      console.error("❌ Failed to create supplier orders:", error);
    },
  });
};

// Helper function to group booking add-ons by supplier
export const groupAddonsBySupplier = (addons: BookingAddon[]) => {
  const supplierGroups = addons.reduce((groups, addon) => {
    const supplierId = addon.add_on?.metadata?.supplier_id || "unknown";
    const supplierName =
      addon.add_on?.metadata?.supplier_name || "Unknown Supplier";

    if (!groups[supplierId]) {
      groups[supplierId] = {
        supplier_id: supplierId,
        supplier_name: supplierName,
        addons: [],
      };
    }

    groups[supplierId].addons.push(addon);
    return groups;
  }, {} as Record<string, { supplier_id: string; supplier_name: string; addons: BookingAddon[] }>);

  return Object.values(supplierGroups);
};

// Helper function to transform booking add-on to supplier order item
export const transformAddonToOrderItem = (addon: BookingAddon) => {
  return {
    item_type: "service" as const,
    item_id: addon.add_on_id,
    item_name: addon.add_on_name,
    item_description: `Booking add-on service for order ${
      addon.order?.display_id || addon.order_id
    }`,
    quantity: addon.quantity,
    unit_price: addon.unit_price,
    service_date: addon.order?.metadata?.check_in_date
      ? new Date(addon.order.metadata.check_in_date)
      : undefined,
    specifications: addon.customer_field_responses,
    notes: `Customer: ${
      addon.order?.metadata?.customer_name || addon.order?.email || "Unknown"
    }`,
  };
};
