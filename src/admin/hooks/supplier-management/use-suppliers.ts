import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

export interface Supplier {
  id: string;
  name: string;
  email: string;
  phone?: string;
  status:
    | "Active"
    | "Inactive"
    | "Pending Approval"
    | "Suspended"
    | "Terminated";
  business_type: string;
  categories: string[];
  metadata?: Record<string, any>;
}

export interface SuppliersResponse {
  suppliers: Supplier[];
  count: number;
  offset: number;
  limit: number;
}

const fetchSuppliers = async (params?: {
  limit?: number;
  offset?: number;
  status?: string;
  search?: string;
}): Promise<SuppliersResponse> => {
  const searchParams = new URLSearchParams();

  if (params?.limit) searchParams.append("limit", params.limit.toString());
  if (params?.offset) searchParams.append("offset", params.offset.toString());
  if (params?.status) searchParams.append("status", params.status);
  if (params?.search) searchParams.append("search", params.search);

  try {
    const result = (await sdk.client.fetch(
      `/admin/supplier-management/suppliers?${searchParams.toString()}`
    )) as any;
    return result;
  } catch (error) {
    throw error;
  }
};

export const useSuppliers = (params?: {
  limit?: number;
  offset?: number;
  status?: string;
  search?: string;
}) => {
  return useQuery({
    queryKey: ["suppliers", params],
    queryFn: () => fetchSuppliers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook specifically for getting active suppliers for dropdowns
export const useActiveSuppliersForDropdown = () => {
  return useQuery({
    queryKey: ["suppliers", "Active", "dropdown"],
    queryFn: () => fetchSuppliers({ status: "Active", limit: 100 }),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) =>
      data.suppliers.map((supplier) => ({
        id: supplier.id,
        name: supplier.name,
        business_type: supplier.business_type,
        categories: supplier.categories,
      })),
  });
};
