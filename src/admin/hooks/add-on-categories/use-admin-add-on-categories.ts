import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

export interface AddOnCategory {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateAddOnCategoryData {
  name: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdateAddOnCategoryData {
  name?: string;
  description?: string;
  is_active?: boolean;
}

// Hook to fetch all add-on categories
export const useAdminAddOnCategories = (params?: {
  limit?: number;
  offset?: number;
}) => {
  return useQuery({
    queryKey: ["admin", "add-on-categories", params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (params?.limit) searchParams.set("limit", params.limit.toString());
      if (params?.offset) searchParams.set("offset", params.offset.toString());

      const url = `/admin/add-on-categories${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      return sdk.client.fetch(url);
    },
  });
};

// Hook to fetch a single add-on category
export const useAdminAddOnCategory = (id: string) => {
  return useQuery({
    queryKey: ["admin", "add-on-category", id],
    queryFn: async () => {
      return sdk.client.fetch(`/admin/add-on-categories/${id}`);
    },
    enabled: !!id,
  });
};

// Hook to create a new add-on category
export const useAdminCreateAddOnCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateAddOnCategoryData) => {
      return sdk.client.fetch("/admin/add-on-categories", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: data,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["admin", "add-on-categories"],
      });
    },
  });
};

// Hook to update an add-on category
export const useAdminUpdateAddOnCategory = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateAddOnCategoryData) => {
      return sdk.client.fetch(`/admin/add-on-categories/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: data,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["admin", "add-on-categories"],
      });
      queryClient.invalidateQueries({
        queryKey: ["admin", "add-on-category", id],
      });
    },
  });
};

// Hook to delete an add-on category
export const useAdminDeleteAddOnCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return sdk.client.fetch(`/admin/add-on-categories/${id}`, {
        method: "DELETE",
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["admin", "add-on-categories"],
      });
    },
  });
};
