import { useState } from "react";
import { useProjectLanguages } from "./languages/useProjectLanguages";

export interface AITranslateOptions {
  entityType: "destination" | "hotel" | "room" | "add_on";
  entityId: string;
  entityData: Record<string, any>;
  sourceLanguage?: string;
  onSuccess?: (targetLanguages: string[]) => void;
  onError?: (error: Error) => void;
}

export interface UseAITranslateReturn {
  translateToAllLanguages: () => Promise<void>;
  translateToLanguage: (targetLanguage: string) => Promise<void>;
  isTranslating: boolean;
  error: string | null;
  availableLanguages: Array<{ tag: string; name: string; flagEmoji: string }>;
}

/**
 * Hook for AI-powered translation to multiple languages
 * 
 * @example
 * const { translateToAllLanguages, isTranslating, error } = useAITranslate({
 *   entityType: "add_on",
 *   entityId: "addon_123",
 *   entityData: { name: "Ski Lessons", description: "Professional ski instruction" },
 *   onSuccess: (languages) => console.log(`Translated to: ${languages.join(', ')}`),
 * });
 * 
 * // Then in your component:
 * <button onClick={translateToAllLanguages} disabled={isTranslating}>
 *   {isTranslating ? "Translating..." : "Translate with AI"}
 * </button>
 */
export function useAITranslate(options: AITranslateOptions): UseAITranslateReturn {
  const [isTranslating, setIsTranslating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { languages } = useProjectLanguages();
  
  const {
    entityType,
    entityId,
    entityData,
    sourceLanguage = "en",
    onSuccess,
    onError,
  } = options;

  // Get available target languages (exclude source language)
  const availableLanguages = languages.filter(lang => lang.tag !== sourceLanguage);

  const translateToLanguage = async (targetLanguage: string) => {
    try {
      setIsTranslating(true);
      setError(null);

      const response = await fetch("/admin/ai-translate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          entityType,
          entityId,
          entityData,
          targetLanguages: [targetLanguage],
          sourceLanguage,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `Translation failed: ${response.statusText}`);
      }

      if (data.success) {
        onSuccess?.([targetLanguage]);
        return data;
      } else {
        throw new Error(data.message || "Translation failed");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Translation failed";
      setError(errorMessage);
      onError?.(new Error(errorMessage));
      throw err;
    } finally {
      setIsTranslating(false);
    }
  };

  const translateToAllLanguages = async () => {
    try {
      setIsTranslating(true);
      setError(null);

      const targetLanguages = availableLanguages.map(lang => lang.tag);
      
      if (targetLanguages.length === 0) {
        throw new Error("No target languages available for translation");
      }

      const response = await fetch("/admin/ai-translate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          entityType,
          entityId,
          entityData,
          targetLanguages,
          sourceLanguage,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `Translation failed: ${response.statusText}`);
      }

      if (data.success) {
        onSuccess?.(targetLanguages);
        return data;
      } else {
        throw new Error(data.message || "Translation failed");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Translation failed";
      setError(errorMessage);
      onError?.(new Error(errorMessage));
      throw err;
    } finally {
      setIsTranslating(false);
    }
  };

  return {
    translateToAllLanguages,
    translateToLanguage,
    isTranslating,
    error,
    availableLanguages,
  };
}

export default useAITranslate;
