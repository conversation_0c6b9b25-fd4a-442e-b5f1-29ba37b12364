import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";

export enum UserRole {
  ADMIN = "admin",
}

// Re-export types from the backend
export { ScreenPermission } from "../../modules/rbac/types";
export type {
  Role,
  PermissionGroup,
  RoleTemplate,
} from "../../modules/rbac/types";

export interface RbacMetadata {
  role?: UserRole;
  role_id?: string;
  is_active: boolean;
  custom_permissions?: string[];
  created_by?: string;
  updated_by?: string;
  updated_at?: string;
}

export interface UserWithRole {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  rbac?: RbacMetadata;
  permissions?: {
    canCreateHotels: boolean;
    canManageUsers: boolean;
  };
}

export interface RbacSetupStatus {
  needs_setup: boolean;
  admin_count: number;
  message: string;
}

// Query Keys
export const rbacQueryKeys = {
  all: ["rbac"] as const,
  currentUser: () => [...rbacQueryKeys.all, "currentUser"] as const,
  userRole: (userId: string) =>
    [...rbacQueryKeys.all, "userRole", userId] as const,
  rolePermissions: (roleId: string) =>
    [...rbacQueryKeys.all, "rolePermissions", roleId] as const,
  roles: () => [...rbacQueryKeys.all, "roles"] as const,
  permissions: () => [...rbacQueryKeys.all, "permissions"] as const,
  roleTemplates: () => [...rbacQueryKeys.all, "roleTemplates"] as const,
  setupStatus: () => [...rbacQueryKeys.all, "setupStatus"] as const,
};

// API Functions
const rbacApi = {
  getCurrentUser: async (): Promise<UserWithRole> => {
    const userData = (await sdk.client.fetch("/admin/users/me")) as any;
    const user = userData?.user || userData;

    if (!user?.id) {
      throw new Error("No authenticated user found");
    }

    const rbacData = user.metadata?.rbac;

    // Calculate permissions based on role
    let permissions = {
      canCreateHotels: false,
      canManageUsers: false,
    };

    if (rbacData?.role === UserRole.ADMIN && rbacData?.is_active) {
      permissions = {
        canCreateHotels: true,
        canManageUsers: true,
      };
    }

    return {
      ...user,
      rbac: rbacData,
      permissions,
    };
  },

  getRolePermissions: async (roleId: string): Promise<string[]> => {
    const response = await fetch(`/admin/roles/${roleId}`, {
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch role permissions: ${response.status}`);
    }

    const data = await response.json();
    const role = data.role || data;
    return role?.permissions || [];
  },

  getUserRole: async (userId: string) => {
    const response = await fetch(`/admin/users/${userId}/role`, {
      method: "GET",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`Failed to get user role: ${response.status}`);
    }

    return response.json();
  },

  getRoles: async () => {
    const response = await fetch("/admin/roles", {
      method: "GET",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch roles: ${response.status}`);
    }

    return response.json();
  },

  getPermissions: async () => {
    const response = await fetch("/admin/permissions", {
      method: "GET",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch permissions: ${response.status}`);
    }

    return response.json();
  },

  getRoleTemplates: async () => {
    const response = await fetch("/admin/role-templates", {
      method: "GET",
      headers: { "Content-Type": "application/json" },
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch role templates: ${response.status}`);
    }

    return response.json();
  },

  getSetupStatus: async (): Promise<RbacSetupStatus> => {
    const response = (await sdk.client.fetch("/admin/rbac/setup")) as any;
    if (!response.ok) {
      throw new Error("Failed to check setup status");
    }
    return response.json();
  },
};

export const useRbac = () => {
  const queryClient = useQueryClient();

  // Current user query
  const {
    data: currentUser,
    isLoading: loading,
    error,
    refetch: getCurrentUser,
  } = useQuery({
    queryKey: rbacQueryKeys.currentUser(),
    queryFn: rbacApi.getCurrentUser,
    staleTime: 60 * 60 * 1000, // 1 hour - user permissions rarely change
    gcTime: 2 * 60 * 60 * 1000, // Keep in cache for 2 hours
    retry: 1,
  });

  // Role permissions query (only when user has a role_id)
  const {
    data: rolePermissions = [],
    isLoading: rolePermissionsLoading,
    isFetching: rolePermissionsFetching,
  } = useQuery({
    queryKey: rbacQueryKeys.rolePermissions(currentUser?.rbac?.role_id || ""),
    queryFn: () => rbacApi.getRolePermissions(currentUser!.rbac!.role_id!),
    enabled: !!currentUser?.rbac?.role_id,
    staleTime: 60 * 60 * 1000, // 1 hour - role permissions rarely change
    gcTime: 2 * 60 * 60 * 1000, // Keep in cache for 2 hours
  });

  // Mutations for role management
  const setUserRoleMutation = useMutation({
    mutationFn: async ({
      userId,
      roleOrRoleId,
      customPermissions = [],
    }: {
      userId: string;
      roleOrRoleId: UserRole | string;
      customPermissions?: string[];
    }) => {
      const isSystemRole = Object.values(UserRole).includes(
        roleOrRoleId as UserRole
      );

      const requestBody = {
        ...(isSystemRole ? { role: roleOrRoleId } : { role_id: roleOrRoleId }),
        custom_permissions: customPermissions,
        is_active: true,
      };

      const response = await fetch(`/admin/users/${userId}/role`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Failed to set user role: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: async (data, variables) => {
      // Invalidate current user query
      await queryClient.invalidateQueries({
        queryKey: rbacQueryKeys.currentUser(),
      });

      // Invalidate the specific user's role query
      await queryClient.invalidateQueries({
        queryKey: rbacQueryKeys.userRole(variables.userId),
      });

      // Invalidate all user role queries to ensure consistency
      await queryClient.invalidateQueries({
        queryKey: [...rbacQueryKeys.all, "userRole"],
      });

      // Also invalidate all RBAC-related queries to be extra sure
      await queryClient.invalidateQueries({ queryKey: rbacQueryKeys.all });

      // Force refetch of the specific user role query
      await queryClient.refetchQueries({
        queryKey: rbacQueryKeys.userRole(variables.userId),
      });
    },
  });

  const removeUserRoleMutation = useMutation({
    mutationFn: async (userId: string) => {
      const response = (await sdk.client.fetch(`/admin/users/${userId}/role`, {
        method: "DELETE",
      })) as any;

      if (!response.ok) {
        throw new Error("Failed to remove user role");
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate current user query
      queryClient.invalidateQueries({ queryKey: rbacQueryKeys.currentUser() });
      // Invalidate the specific user's role query
      queryClient.invalidateQueries({
        queryKey: rbacQueryKeys.userRole(variables),
      });
      // Invalidate all user role queries to ensure consistency
      queryClient.invalidateQueries({
        queryKey: [...rbacQueryKeys.all, "userRole"],
      });
    },
  });

  // Additional queries for data fetching
  const useUserRole = (userId: string) => {
    return useQuery({
      queryKey: rbacQueryKeys.userRole(userId),
      queryFn: () => rbacApi.getUserRole(userId),
      enabled: !!userId,
      staleTime: 60 * 60 * 1000, // 1 hour
      gcTime: 2 * 60 * 60 * 1000, // 2 hours
    });
  };

  const useRoles = () => {
    return useQuery({
      queryKey: rbacQueryKeys.roles(),
      queryFn: rbacApi.getRoles,
      staleTime: 60 * 60 * 1000, // 1 hour - roles don't change often
      gcTime: 2 * 60 * 60 * 1000, // 2 hours
    });
  };

  const usePermissions = () => {
    return useQuery({
      queryKey: rbacQueryKeys.permissions(),
      queryFn: rbacApi.getPermissions,
      staleTime: 2 * 60 * 60 * 1000, // 2 hours - system permissions rarely change
      gcTime: 4 * 60 * 60 * 1000, // 4 hours
    });
  };

  const useRoleTemplates = () => {
    return useQuery({
      queryKey: rbacQueryKeys.roleTemplates(),
      queryFn: rbacApi.getRoleTemplates,
      staleTime: 2 * 60 * 60 * 1000, // 2 hours - templates rarely change
      gcTime: 4 * 60 * 60 * 1000, // 4 hours
    });
  };

  const useSetupStatus = () => {
    return useQuery({
      queryKey: rbacQueryKeys.setupStatus(),
      queryFn: rbacApi.getSetupStatus,
      staleTime: 30 * 60 * 1000, // 30 minutes - setup status changes less frequently
      gcTime: 60 * 60 * 1000, // 1 hour
    });
  };

  // More mutations for role and user management
  const setupInitialAdminMutation = useMutation({
    mutationFn: async (userId: string) => {
      const response = (await sdk.client.fetch("/admin/rbac/setup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ user_id: userId }),
      })) as any;

      if (!response.ok) {
        throw new Error("Failed to setup initial admin");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: rbacQueryKeys.currentUser() });
      queryClient.invalidateQueries({ queryKey: rbacQueryKeys.setupStatus() });
    },
  });

  const createRoleMutation = useMutation({
    mutationFn: async (roleData: {
      name: string;
      description: string;
      permissions: string[];
    }) => {
      const response = await fetch("/admin/roles", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(roleData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create role: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: rbacQueryKeys.roles() });
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: async ({
      roleId,
      roleData,
    }: {
      roleId: string;
      roleData: {
        name?: string;
        description?: string;
        permissions?: string[];
        is_active?: boolean;
      };
    }) => {
      const response = await fetch(`/admin/roles/${roleId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(roleData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update role: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: rbacQueryKeys.roles() });
      queryClient.invalidateQueries({ queryKey: rbacQueryKeys.currentUser() });
    },
  });

  const deleteRoleMutation = useMutation({
    mutationFn: async (roleId: string) => {
      const response = await fetch(`/admin/roles/${roleId}`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to delete role: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: rbacQueryKeys.roles() });
    },
  });

  // Permission helpers
  const hasRole = (role: UserRole): boolean => {
    return (
      currentUser?.rbac?.role === role && currentUser?.rbac?.is_active === true
    );
  };

  const isAdmin = (): boolean => {
    return hasRole(UserRole.ADMIN);
  };

  const isHotelManager = (): boolean => {
    // Hotel manager is now a custom role, not a system role
    // This function is deprecated - use permission-based checks instead
    return false;
  };

  const canAccessHotel = (_hotelId: string): boolean => {
    if (!currentUser?.rbac) return false;
    if (isAdmin()) return true;
    // Hotel access is now permission-based, not assignment-based
    return hasPermission("hotel_management:view");
  };

  const canCreateHotels = (): boolean => {
    return isAdmin();
  };

  const canManageUsers = (): boolean => {
    return isAdmin();
  };

  // ===== NEW PERMISSION METHODS =====

  /**
   * Check if user has a specific screen permission
   */
  const hasPermission = (permission: string): boolean => {
    if (!currentUser?.rbac) {
      return false;
    }

    // Admins have all permissions
    if (isAdmin()) {
      return true;
    }

    // For custom roles, check against the loaded role permissions
    if (currentUser.rbac.role_id) {
      // If role permissions are still loading, be optimistic only for view permissions
      // This prevents the race condition where permissions haven't loaded yet
      if (rolePermissionsLoading || rolePermissionsFetching) {
        // Only grant optimistic access for view permissions to avoid security issues
        const isViewPermission =
          permission.includes(":view") || permission.includes(":read");
        return isViewPermission; // Optimistically allow view access while loading
      }

      if (rolePermissions.length > 0) {
        return rolePermissions.includes(permission);
      } else {
        // If permissions are loaded but empty, deny access
        return false;
      }
    }

    // Check custom permissions (user-specific overrides)
    if (currentUser.rbac.custom_permissions) {
      return currentUser.rbac.custom_permissions.includes(permission);
    }

    // For users without roles or permissions, deny access
    return false;
  };

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some((permission) => hasPermission(permission));
  };

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every((permission) => hasPermission(permission));
  };

  return {
    // Current user data
    currentUser,
    loading: loading || rolePermissionsLoading, // Include role permissions loading
    error,
    refetchCurrentUser: getCurrentUser,

    // Mutations
    setUserRole: setUserRoleMutation.mutateAsync,
    removeUserRole: removeUserRoleMutation.mutateAsync,
    setupInitialAdmin: setupInitialAdminMutation.mutateAsync,
    createRole: createRoleMutation.mutateAsync,
    updateRole: updateRoleMutation.mutateAsync,
    deleteRole: deleteRoleMutation.mutateAsync,

    // Query hooks (for components to use directly)
    useUserRole,
    useRoles,
    usePermissions,
    useRoleTemplates,
    useSetupStatus,

    // Legacy permission helpers
    hasRole,
    isAdmin,
    isHotelManager,
    canAccessHotel,
    canCreateHotels,
    canManageUsers,

    // New permission system
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,

    // Role permissions data
    rolePermissions,
  };
};

// Separate hooks for specific data fetching (can be used independently)
export const useCurrentUser = () => {
  return useQuery({
    queryKey: rbacQueryKeys.currentUser(),
    queryFn: rbacApi.getCurrentUser,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
    retry: 1,
  });
};

export const useUserRole = (userId: string) => {
  return useQuery({
    queryKey: rbacQueryKeys.userRole(userId),
    queryFn: () => rbacApi.getUserRole(userId),
    enabled: !!userId,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  });
};

export const useRoles = () => {
  return useQuery({
    queryKey: rbacQueryKeys.roles(),
    queryFn: rbacApi.getRoles,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  });
};

export const usePermissions = () => {
  return useQuery({
    queryKey: rbacQueryKeys.permissions(),
    queryFn: rbacApi.getPermissions,
    staleTime: 2 * 60 * 60 * 1000, // 2 hours - system permissions rarely change
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  });
};

export const useRoleTemplates = () => {
  return useQuery({
    queryKey: rbacQueryKeys.roleTemplates(),
    queryFn: rbacApi.getRoleTemplates,
    staleTime: 2 * 60 * 60 * 1000, // 2 hours - templates rarely change
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
  });
};

export const useSetupStatus = () => {
  return useQuery({
    queryKey: rbacQueryKeys.setupStatus(),
    queryFn: rbacApi.getSetupStatus,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};

// Mutation hooks
export const useSetUserRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      userId,
      roleOrRoleId,
      customPermissions = [],
    }: {
      userId: string;
      roleOrRoleId: UserRole | string;
      customPermissions?: string[];
    }) => {
      const isSystemRole = Object.values(UserRole).includes(
        roleOrRoleId as UserRole
      );

      const requestBody = {
        ...(isSystemRole ? { role: roleOrRoleId } : { role_id: roleOrRoleId }),
        custom_permissions: customPermissions,
        is_active: true,
      };

      const response = await fetch(`/admin/users/${userId}/role`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Failed to set user role: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: async (data, variables) => {
      // Invalidate current user query
      await queryClient.invalidateQueries({
        queryKey: rbacQueryKeys.currentUser(),
      });

      // Invalidate the specific user's role query
      await queryClient.invalidateQueries({
        queryKey: rbacQueryKeys.userRole(variables.userId),
      });

      // Invalidate all user role queries to ensure consistency
      await queryClient.invalidateQueries({
        queryKey: [...rbacQueryKeys.all, "userRole"],
      });

      // Also invalidate all RBAC-related queries to be extra sure
      await queryClient.invalidateQueries({ queryKey: rbacQueryKeys.all });

      // Force refetch of the specific user role query
      await queryClient.refetchQueries({
        queryKey: rbacQueryKeys.userRole(variables.userId),
      });
    },
  });
};

export const useCreateRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (roleData: {
      name: string;
      description: string;
      permissions: string[];
    }) => {
      const response = await fetch("/admin/roles", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(roleData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create role: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: rbacQueryKeys.roles() });
    },
  });
};
