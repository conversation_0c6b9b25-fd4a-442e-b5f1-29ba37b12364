import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

// Types
export interface RoomConfig {
  id: string;
  name: string;
  type: string;
  description?: string;
  room_size?: string;
  amenities?: string[];
  bed_type?: string;
  max_extra_beds: number;
  max_cots: number;
  max_adults: number;
  max_adults_beyond_capacity: number;
  max_children: number;
  max_infants: number;
  max_occupancy: number;
  hotel_id: string;
  images?: any[];
  thumbnail?: string;
}

export interface Room {
  id: string;
  name: string;
  room_number: string;
  status: string;
  floor: string;
  notes: string;
  is_active: boolean;
  room_config_id: string;
  hotel_id: string;
  options: Record<string, string>;
  inventory_quantity: number;
  created_at?: string;
  updated_at?: string;
  room_config?: RoomConfig;
}

export interface RoomsResponse {
  rooms: Room[];
  count: number;
}

export interface RoomConfigsResponse {
  roomConfigs: RoomConfig[];
  count: number;
}

// Hooks for Room Configs
export const useRoomConfigs = (hotelId: string) => {
  return useQuery({
    queryKey: ["room-configs", hotelId],
    queryFn: async (): Promise<RoomConfigsResponse> => {
      const timestamp = new Date().getTime();
      const response = await sdk.client.fetch(
        `/admin/direct-room-configs?hotel_id=${hotelId}&_=${timestamp}`,
        {
          headers: {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
        }
      );
      return response as RoomConfigsResponse;
    },
    enabled: !!hotelId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hooks for Rooms
export const useRooms = (hotelId: string, roomConfigId?: string) => {
  return useQuery({
    queryKey: ["rooms", hotelId, roomConfigId],
    queryFn: async (): Promise<RoomsResponse> => {
      let url = `/admin/direct-rooms?hotel_id=${hotelId}`;
      if (roomConfigId && roomConfigId !== "all") {
        url += `&room_config_id=${roomConfigId}`;
      }
      
      const response = await sdk.client.fetch(url, {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });
      return response as RoomsResponse;
    },
    enabled: !!hotelId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Combined hook for rooms with room configs
export const useRoomsWithConfigs = (hotelId: string, roomConfigId?: string) => {
  const {
    data: roomsData,
    isLoading: roomsLoading,
    error: roomsError,
    refetch: refetchRooms,
  } = useRooms(hotelId, roomConfigId);

  const {
    data: roomConfigsData,
    isLoading: roomConfigsLoading,
    error: roomConfigsError,
    refetch: refetchRoomConfigs,
  } = useRoomConfigs(hotelId);

  return {
    rooms: roomsData?.rooms || [],
    roomConfigs: roomConfigsData?.roomConfigs || [],
    isLoading: roomsLoading || roomConfigsLoading,
    error: roomsError || roomConfigsError,
    refetch: () => {
      refetchRooms();
      refetchRoomConfigs();
    },
    roomsCount: roomsData?.count || 0,
    roomConfigsCount: roomConfigsData?.count || 0,
  };
};

// Create room mutation
export const useCreateRoom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (roomData: Partial<Room>) => {
      const response = await sdk.client.fetch("/admin/direct-rooms", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(roomData),
      });
      return response;
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch rooms for the hotel
      queryClient.invalidateQueries({
        queryKey: ["rooms", variables.hotel_id],
      });
    },
  });
};

// Update room mutation
export const useUpdateRoom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...roomData }: Partial<Room> & { id: string }) => {
      const response = await sdk.client.fetch(`/admin/direct-rooms/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(roomData),
      });
      return response;
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch rooms for the hotel
      queryClient.invalidateQueries({
        queryKey: ["rooms", variables.hotel_id],
      });
    },
  });
};

// Delete room mutation
export const useDeleteRoom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, hotelId }: { id: string; hotelId: string }) => {
      const response = await sdk.client.fetch(`/admin/direct-rooms/${id}`, {
        method: "DELETE",
      });
      return response;
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch rooms for the hotel
      queryClient.invalidateQueries({
        queryKey: ["rooms", variables.hotelId],
      });
    },
  });
};
