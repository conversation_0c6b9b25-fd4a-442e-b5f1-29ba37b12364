/**
 * CSS Custom Properties for Light and Dark Theme Support
 * These variables are used by Tailwind CSS for consistent theming
 */

:root {
  /* Light theme colors */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 35%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  /* Dark theme colors - shades of black and grey */
  --background: 0 0% 7%;        /* Very dark grey background */
  --foreground: 0 0% 95%;       /* Light grey text */
  --card: 0 0% 12%;             /* Dark grey cards */
  --card-foreground: 0 0% 95%;  /* Light grey text on cards */
  --popover: 0 0% 12%;          /* Dark grey popovers */
  --popover-foreground: 0 0% 95%; /* Light grey text on popovers */
  --primary: 217.2 91.2% 59.8%; /* Keep primary color for buttons */
  --primary-foreground: 0 0% 7%; /* Dark text on primary */
  --secondary: 0 0% 18%;        /* Medium dark grey for secondary elements */
  --secondary-foreground: 0 0% 95%; /* Light grey text on secondary */
  --muted: 0 0% 15%;            /* Muted dark grey */
  --muted-foreground: 0 0% 65%; /* Medium grey text */
  --accent: 0 0% 18%;           /* Accent dark grey */
  --accent-foreground: 0 0% 95%; /* Light grey text on accent */
  --destructive: 0 62.8% 30.6%; /* Keep destructive color */
  --destructive-foreground: 0 0% 95%; /* Light grey text on destructive */
  --border: 0 0% 20%;           /* Border grey */
  --input: 0 0% 15%;            /* Input background grey */
  --ring: 217.2 91.2% 59.8%;   /* Focus ring color */
}

/* Additional theme-aware styles for better dark mode support */
.dark .shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.2);
}

.dark .shadow-md {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
}

/* Ensure proper contrast for form elements in dark mode */
/* .dark input,
.dark textarea,
.dark select {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
} */

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Ensure proper contrast for disabled elements */
.dark input:disabled,
.dark textarea:disabled,
.dark select:disabled {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  opacity: 0.7;
}

/* Better scrollbar styling for dark mode */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Ensure proper modal overlay colors */
.dark [data-focus-modal-overlay] {
  background-color: rgb(0 0 0 / 0.8);
}

/* Better loading spinner visibility in dark mode */
.dark .animate-spin {
  border-color: hsl(var(--muted-foreground));
  border-top-color: hsl(var(--primary));
}

/* Ensure proper icon visibility in both themes */
.text-muted-foreground {
  color: hsl(var(--muted-foreground)) !important;
}

/* Ensure icons in navigation tabs are visible */
button .lucide,
button svg {
  color: inherit;
}

/* Specific styling for form input icons */
.relative svg.absolute {
  color: hsl(var(--muted-foreground));
}

/* Ensure tooltip icons are visible */
[data-tooltip] svg,
.tooltip svg {
  color: hsl(var(--muted-foreground));
}

/* Dark theme specific overrides for hardcoded colors */
.dark .bg-white {
  background-color: hsl(var(--card)) !important;
}

.dark .border-gray-200 {
  border-color: hsl(var(--border)) !important;
}

.dark .border-gray-300 {
  border-color: hsl(var(--border)) !important;
}

.dark .text-gray-500 {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .text-gray-400 {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .text-gray-600 {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .text-gray-900 {
  color: hsl(var(--foreground)) !important;
}

.dark .bg-gray-100 {
  background-color: hsl(var(--muted)) !important;
}

.dark .bg-gray-50 {
  background-color: hsl(var(--muted)) !important;
}

.dark .hover\\:bg-gray-50:hover {
  background-color: hsl(var(--accent)) !important;
}

/* Keep icon backgrounds colorful but containers dark */
.dark .bg-blue-50 {
  background-color: hsl(var(--muted)) !important;
}

.dark .bg-green-50 {
  background-color: hsl(var(--muted)) !important;
}

.dark .bg-purple-50 {
  background-color: hsl(var(--muted)) !important;
}

.dark .bg-amber-50 {
  background-color: hsl(var(--muted)) !important;
}

/* Ensure loading states work in dark mode */
.dark .animate-pulse .bg-gray-100 {
  background-color: hsl(var(--muted)) !important;
}

/* Table component dark theme support */
.dark table {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

.dark table th {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dark table td {
  border-color: hsl(var(--border)) !important;
  color: hsl(var(--card-foreground)) !important;
}

.dark table tr:hover {
  background-color: hsl(var(--accent)) !important;
}

/* Dropdown menu dark theme support */
.dark [data-dropdown-menu-content] {
  background-color: hsl(var(--popover)) !important;
  border-color: hsl(var(--border)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

.dark [data-dropdown-menu-item] {
  color: hsl(var(--popover-foreground)) !important;
}

.dark [data-dropdown-menu-item]:hover {
  background-color: hsl(var(--accent)) !important;
}

/* Badge component dark theme support */
.dark .badge {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
  border-color: hsl(var(--border)) !important;
}
