/* Custom button styles to enhance outline appearance with theme support */
.outline-button {
  border: 1.5px solid #d1d5db !important; /* Clear visible border */
  background-color: transparent !important;
  color: #374151 !important; /* Dark gray text */
  border-radius: 6px !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease-in-out !important;
  position: relative !important;
  /* Override any conflicting styles */
  box-shadow: none !important;
}

.outline-button:hover {
  border-color: #9ca3af !important; /* Darker border on hover */
  background-color: #f9fafb !important; /* Very light gray background */
  color: #111827 !important; /* Darker text */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.outline-button:active {
  border-color: #6b7280 !important; /* Even darker border when pressed */
  background-color: #f3f4f6 !important; /* Slightly darker background */
  color: #111827 !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.outline-button:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  border-color: #3b82f6 !important;
}

/* Dark mode specific adjustments */
.dark .outline-button {
  border-color: #6b7280 !important; /* Lighter border for dark mode */
  color: #d1d5db !important; /* Light text */
  background-color: transparent !important;
}

.dark .outline-button:hover {
  border-color: #9ca3af !important; /* Even lighter border on hover */
  background-color: #374151 !important; /* Dark background */
  color: #f3f4f6 !important; /* Very light text */
}

.dark .outline-button:active {
  border-color: #d1d5db !important;
  background-color: #4b5563 !important;
  color: #f9fafb !important;
}

/* Prominent outline button variant */
.outline-button--prominent {
  border-width: 2px !important;
  font-weight: 600 !important;
  padding: 0.625rem 1.25rem !important;
}

.outline-button--prominent:hover {
  border-width: 2px !important;
}

.dark .outline-button--prominent {
  border-width: 2px !important;
}
