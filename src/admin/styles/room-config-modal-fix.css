/**
 * Aggressive fixes for room-config modal scrolling issues
 */

/* Force the modal to be scrollable */
/* .camped-ui-focus-modal-content,
[data-focus-modal-content],
div[role="dialog"] {
  max-height: 90vh !important;
  overflow-y: auto !important;
} */

/* Fix for SimpleRoomConfigForm */
.room-config-form-container {
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
  padding-bottom: 100px !important; /* Extra padding at bottom */
}

/* Fix for form content */
.room-config-form-scrollable {
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

/* Fix for media section */
.room-config-media-section {
  overflow: visible !important;
  max-height: none !important;
}

/* Fix for footer */
.room-config-form-footer {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: white !important;
  padding: 16px !important;
  border-top: 1px solid #e5e7eb !important;
  z-index: 100 !important;
  width: 100% !important;
  display: flex !important;
  justify-content: flex-end !important;
}

/* Fix for modal body */
.camped-ui-focus-modal-body,
[data-focus-modal-body] {
  overflow-y: auto !important;
  height: auto !important;
  max-height: none !important;
  padding-bottom: 80px !important;
}

/* Fix for modal header */
.camped-ui-focus-modal-header,
[data-focus-modal-header] {
  position: sticky !important;
  top: 0 !important;
  background: white !important;
  z-index: 100 !important;
  border-bottom: 1px solid #e5e7eb !important;
}
