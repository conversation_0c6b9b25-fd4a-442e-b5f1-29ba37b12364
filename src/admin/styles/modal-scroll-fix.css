/**
 * CSS fixes for modal scrolling issues
 * This ensures modals have proper scrolling behavior and don't overflow the viewport
 */

/* Fix for FocusModal content scrolling */
/* .camped-ui-focus-modal-content,
[data-focus-modal-content],
div[role="dialog"] {
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
} */

/* Fix for FocusModal body scrolling */
.camped-ui-focus-modal-body,
[data-focus-modal-body] {
  flex: 1 1 auto !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding-bottom: 20px !important;
}

/* Fix for SimpleRoomConfigForm scrolling */
.room-config-form-container {
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
  padding-bottom: 80px !important; /* Add extra padding at the bottom */
}

/* Fix for nested scrollable areas */
.room-config-form-scrollable {
  overflow-y: visible !important;
  padding-bottom: 20px !important;
}

/* Fix for media section scrolling */
.room-config-media-section {
  overflow: visible !important;
  max-height: none !important;
}

/* Ensure modal headers stay at the top */
.camped-ui-focus-modal-header,
[data-focus-modal-header] {
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
  background-color: white !important;
  border-bottom: 1px solid #e5e7eb !important;
}

/* Ensure modal footers stay at the bottom */
.room-config-form-footer {
  position: sticky !important;
  bottom: 0 !important;
  z-index: 10 !important;
  background-color: white !important;
  border-top: 1px solid #e5e7eb !important;
  padding: 16px !important;
  margin-top: 16px !important;
}
