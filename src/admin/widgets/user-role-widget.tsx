import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { Container, Badge, Button, Alert } from "@camped-ai/ui";
import { Box } from "@mui/material";
import { useRbac } from "../hooks/use-rbac";
import { RoleBadge, UserRoleManager } from "../components/rbac";
import { useState, useEffect } from "react";

const UserRoleWidget = () => {
  const { currentUser, isAdmin } = useRbac();
  const [showRoleManager, setShowRoleManager] = useState(false);

  // Only show to admins
  if (!isAdmin()) {
    return null;
  }

  return (
    <Container className="p-4">
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <h3 style={{ fontWeight: 500 }}>Role Management</h3>
          <Button
            size="small"
            onClick={() => setShowRoleManager(!showRoleManager)}
          >
            {showRoleManager ? "Hide" : "Manage Roles"}
          </Button>
        </Box>
        
        <Box sx={{ fontSize: '0.875rem', color: 'text.secondary' }}>
          Assign roles and permissions to users
        </Box>

        {showRoleManager && (
          <Box sx={{ mt: 2 }}>
            <Alert variant="info">
              Use the native Medusa user management to create users, then assign roles here.
            </Alert>
            <Box sx={{ mt: 2 }}>
              <Button
                onClick={() => window.location.href = "/admin/settings/users"}
              >
                Open Role Management
              </Button>
            </Box>
          </Box>
        )}
      </Box>
    </Container>
  );
};

export const config = defineWidgetConfig({
  zone: "user.details.side",
});

export default UserRoleWidget;
