import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { HIDDEN_SIDEBAR_ITEMS } from "../config/sidebar-config";

/**
 * Simple sidebar hider widget that runs once per page load
 * No infinite loops, no multiple observers, no intervals
 */
const SimpleSidebarHider = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    let hasRun = false;

    // Handle redirection from orders to bookings
    const handleRedirection = () => {
      const currentPath = location.pathname;

      // If user is on orders page, redirect to bookings
      if (currentPath === "/orders" || currentPath.startsWith("/orders/")) {
        navigate("/hotel-management/bookings", { replace: true });
        return;
      }

      // If user is on the root path, redirect to bookings
      if (
        currentPath === "/" ||
        currentPath === "/app" ||
        currentPath === "/app/" ||
        currentPath === "" ||
        currentPath === "/app/orders" ||
        currentPath.startsWith("/app/orders/")
      ) {
        navigate("/hotel-management/bookings", { replace: true });
        return;
      }
    };

    // Run redirection check first
    handleRedirection();

    const hideSidebarItems = () => {
      if (hasRun) return;
      hasRun = true;

      // Hide sidebar items
      const normalizedItems = HIDDEN_SIDEBAR_ITEMS.map((item) =>
        item.toLowerCase()
      );
      let hiddenCount = 0;

      // Target navigation links first (most specific)
      const navLinks = document.querySelectorAll(
        'nav a, aside a, [role="navigation"] a'
      );

      navLinks.forEach((link) => {
        const text = link.textContent?.trim() || "";
        const normalizedText = text.toLowerCase();
        const href = (link as HTMLAnchorElement).href || "";

        // Check if this link is a supplier management submenu
        const isSupplierManagementSubmenu = href.includes(
          "/supplier-management/"
        );

        const shouldHide = normalizedItems.some((item) => {
          return normalizedText === item || normalizedText.includes(item);
        });

        // Don't hide supplier management submenus even if they match hidden items
        if (shouldHide && !isSupplierManagementSubmenu) {
          // Hide the parent list item if it exists, otherwise hide the link
          const listItem = link.closest("li");
          const targetElement = listItem || link;

          (targetElement as HTMLElement).style.display = "none";
          hiddenCount++;
        } else if (shouldHide && isSupplierManagementSubmenu) {
          // Skipped hiding supplier submenu
        }
      });

      // Special handling for search - look for elements containing "Search" text
      const allElements = document.querySelectorAll(
        'nav *, aside *, [role="navigation"] *'
      );

      allElements.forEach((element) => {
        const text = element.textContent?.trim() || "";
        const normalizedText = text.toLowerCase();

        // Only target elements that contain exactly "search" and are likely navigation items
        if (
          normalizedText === "search" &&
          (element.tagName === "SPAN" ||
            element.tagName === "DIV" ||
            element.tagName === "A")
        ) {
          const listItem = element.closest("li");
          const targetElement = listItem || element.parentElement || element;

          (targetElement as HTMLElement).style.display = "none";
          hiddenCount++;
        }
      });

      // Handle duplicate "Supplier Management" items - keep only the first one
      const supplierMgmtLinks = document.querySelectorAll(
        'nav a, aside a, [role="navigation"] a'
      );
      let supplierMgmtCount = 0;

      supplierMgmtLinks.forEach((link) => {
        const text = link.textContent?.trim() || "";
        if (text === "Supplier Management") {
          supplierMgmtCount++;
          if (supplierMgmtCount > 1) {
            // Hide duplicate supplier management items (keep only the first one)
            const listItem = link.closest("li");
            const targetElement = listItem || link;
            (targetElement as HTMLElement).style.display = "none";
            hiddenCount++;
          }
        }
      });
    };

    // Re-enabled with specific logic for supplier management duplicates
    hideSidebarItems();

    // Run once more after a short delay to catch late-loading content
    const timeoutId = setTimeout(hideSidebarItems, 1000);

    // Cleanup
    return () => {
      clearTimeout(timeoutId);
    };
  }, [navigate, location.pathname]);

  return null;
};

export const config = defineWidgetConfig({
  zone: [
    "product.list.before",
    "order.list.before",
    "customer.list.before",
    "store.details.before",
    "inventory_item.list.before",
    "promotion.list.before",
    "price_list.list.before",
  ],
});

export default SimpleSidebarHider;
