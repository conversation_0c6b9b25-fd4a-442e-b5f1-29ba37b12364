import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  ArrowLeft,
  Package,
  Calendar,
  DollarSign,
  Info,
  Users,
} from "lucide-react";
import { Container, Heading, Text, Button, Badge } from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";
import { useInventoryAddOn } from "../../../hooks/use-inventory-add-ons";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../hooks/use-rbac";
import { ComponentType } from "react";

// Create icon component for route config
const AddOnViewIcon: ComponentType = (props: any) => (
  <Package {...props} className="h-4 w-4" />
);

const InventoryAddOnViewPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();

  // Fetch add-on data
  const { addOn, loading, error, refetch } = useInventoryAddOn(id!);

  // Format currency
  const formatCurrency = (amount: number, currency: string = "CHF") => {
    return new Intl.NumberFormat("en-CH", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-CH", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Format custom field values
  const formatCustomFieldValue = (value: any): string => {
    if (value === null || value === undefined) {
      return "N/A";
    }

    // Handle boolean values
    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    // Handle arrays
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return "None";
      }
      return value.join(", ");
    }

    // Handle objects (like age_range: {min: 3, max: 12})
    if (typeof value === "object") {
      const entries = Object.entries(value);
      if (entries.length === 0) {
        return "{}";
      }

      // Format as key-value pairs
      return entries
        .map(([key, val]) => {
          const formattedKey = key
            .replace(/_/g, " ")
            .replace(/\b\w/g, (l) => l.toUpperCase());
          return `${formattedKey}: ${val}`;
        })
        .join(", ");
    }

    // Handle primitive values (string, number)
    return String(value);
  };

  // Check permissions
  if (!hasPermission("addons:view")) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <Package className="h-12 w-12 text-ui-fg-muted mx-auto mb-4" />
            <Heading level="h1">Access Denied</Heading>
            <Text className="mt-2">
              You don't have permission to view add-ons inventory.
            </Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate("/add-ons")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Add-ons
          </Button>

          <div className="flex-1">
            {loading ? (
              <div className="h-8 w-64 bg-ui-bg-subtle animate-pulse rounded" />
            ) : (
              <Heading level="h1">{addOn?.title || "Add-on Details"}</Heading>
            )}
            <Text className="text-ui-fg-subtle mt-1">
              View detailed information about this add-on
            </Text>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="space-y-6">
            <Container className="p-6 border border-ui-border-base rounded-lg">
              <div className="space-y-4">
                <div className="h-6 w-48 bg-ui-bg-subtle animate-pulse rounded" />
                <div className="h-4 w-full bg-ui-bg-subtle animate-pulse rounded" />
                <div className="h-4 w-3/4 bg-ui-bg-subtle animate-pulse rounded" />
              </div>
            </Container>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Container className="p-6 border border-ui-border-base rounded-lg">
                <div className="h-6 w-32 mb-4 bg-ui-bg-subtle animate-pulse rounded" />
                <div className="space-y-3">
                  <div className="h-4 w-full bg-ui-bg-subtle animate-pulse rounded" />
                  <div className="h-4 w-2/3 bg-ui-bg-subtle animate-pulse rounded" />
                </div>
              </Container>
              <Container className="p-6 border border-ui-border-base rounded-lg">
                <div className="h-6 w-32 mb-4 bg-ui-bg-subtle animate-pulse rounded" />
                <div className="space-y-3">
                  <div className="h-4 w-full bg-ui-bg-subtle animate-pulse rounded" />
                  <div className="h-4 w-2/3 bg-ui-bg-subtle animate-pulse rounded" />
                </div>
              </Container>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Container className="p-8 text-center border border-ui-border-base rounded-lg">
            <Package className="h-12 w-12 text-ui-fg-muted mx-auto mb-4" />
            <Heading level="h3" className="mb-2">
              Error Loading Add-on
            </Heading>
            <Text className="text-ui-fg-error mb-4">{error}</Text>
            <Button variant="secondary" size="small" onClick={refetch}>
              Retry
            </Button>
          </Container>
        )}

        {/* Add-on Details */}
        {!loading && !error && addOn && (
          <div className="space-y-6">
            {/* Basic Information */}
            <Container className="p-6 border border-ui-border-base rounded-lg">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Info className="h-5 w-5 text-ui-fg-subtle" />
                  <Heading level="h2">Basic Information</Heading>
                </div>
                <Badge color={addOn.is_active ? "green" : "grey"}>
                  {addOn.status}
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Text className="text-ui-fg-subtle text-sm mb-1">
                    Add-on Name
                  </Text>
                  <Text className="font-medium">{addOn.title}</Text>
                </div>

                {addOn.service_level && (
                  <div>
                    <Text className="text-ui-fg-subtle text-sm mb-1">
                      Service Level
                    </Text>
                    <Text className="font-medium">
                      {addOn.service_level.charAt(0).toUpperCase() +
                        addOn.service_level.slice(1)}
                    </Text>
                  </div>
                )}

                {addOn.hotel_name && (
                  <div>
                    <Text className="text-ui-fg-subtle text-sm mb-1">
                      Hotel
                    </Text>
                    <Text className="font-medium">{addOn.hotel_name}</Text>
                  </div>
                )}

                {addOn.destination_name && (
                  <div>
                    <Text className="text-ui-fg-subtle text-sm mb-1">
                      Destination
                    </Text>
                    <Text className="font-medium">
                      {addOn.destination_name}
                    </Text>
                  </div>
                )}

                {addOn.max_capacity && (
                  <div>
                    <Text className="text-ui-fg-subtle text-sm mb-1">
                      Max Capacity
                    </Text>
                    <Text className="font-medium">{addOn.max_capacity}</Text>
                  </div>
                )}
              </div>

              {addOn.description && (
                <div className="mt-6">
                  <Text className="text-ui-fg-subtle text-sm mb-2">
                    Description
                  </Text>
                  <Text className="leading-relaxed">{addOn.description}</Text>
                </div>
              )}

              {addOn.custom_fields &&
                Object.keys(addOn.custom_fields).length > 0 && (
                  <div className="mt-6">
                    <Text className="text-ui-fg-subtle text-sm mb-2">
                      Custom Fields
                    </Text>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(addOn.custom_fields).map(
                        ([key, value]) => (
                          <div key={key}>
                            <Text className="text-ui-fg-subtle text-xs mb-1">
                              {key
                                .replace(/_/g, " ")
                                .replace(/\b\w/g, (l) => l.toUpperCase())}
                            </Text>
                            <Text className="font-medium">
                              {formatCustomFieldValue(value)}
                            </Text>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
            </Container>

            {/* Customer Fields */}
            <Container className="p-6 border border-ui-border-base rounded-lg">
              <div className="flex items-center gap-3 mb-4">
                <Users className="h-5 w-5 text-ui-fg-subtle" />
                <Heading level="h2">Customer Fields</Heading>
              </div>

              {/* Display customer fields from metadata */}
              {addOn.metadata?.customer_fields &&
              Array.isArray(addOn.metadata.customer_fields) &&
              addOn.metadata.customer_fields.length > 0 ? (
                <div className="space-y-3">
                  <Text className="text-ui-fg-subtle text-sm">
                    These fields will be displayed to customers during the
                    booking process:
                  </Text>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {addOn.metadata.customer_fields.map(
                      (field: any, index: number) => (
                        <div
                          key={index}
                          className="p-4 border border-ui-border-base rounded-lg bg-ui-bg-subtle"
                        >
                          <div className="flex items-start justify-between mb-2">
                            <Text className="font-medium">
                              {field.field_name || field.label}
                            </Text>
                            <Badge size="small">
                              {field.field_type || field.type}
                            </Badge>
                          </div>
                          <div className="space-y-1">
                            <Text className="text-ui-fg-subtle text-sm">
                              Required:{" "}
                              {field.is_required || field.required
                                ? "Yes"
                                : "No"}
                            </Text>
                            {field.options && Array.isArray(field.options) && (
                              <div>
                                <Text className="text-ui-fg-subtle text-sm">
                                  Options: {field.options.join(", ")}
                                </Text>
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-ui-border-base rounded-lg bg-ui-bg-subtle">
                  <Text className="text-ui-fg-subtle text-sm">
                    No customer fields configured for this add-on. Customer
                    fields are automatically synced from the product service
                    configuration.
                  </Text>
                </div>
              )}
            </Container>

            {/* Pricing Information */}
            <Container className="p-6 border border-ui-border-base rounded-lg">
              <div className="flex items-center gap-3 mb-4">
                <DollarSign className="h-5 w-5 text-ui-fg-subtle" />
                <Heading level="h2">Pricing Information</Heading>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <Text className="text-ui-fg-subtle text-sm mb-1">Cost</Text>
                  <Text className="font-medium text-lg">
                    {formatCurrency(addOn.cost, addOn.cost_currency)}
                  </Text>
                  <Text className="text-ui-fg-subtle text-xs">
                    {addOn.cost_currency}
                  </Text>
                </div>

                <div>
                  <Text className="text-ui-fg-subtle text-sm mb-1">
                    Selling Margin
                  </Text>
                  <Text className="font-medium text-lg text-ui-fg-success">
                    {formatPercentage(addOn.selling_margin)}
                  </Text>
                </div>

                <div>
                  <Text className="text-ui-fg-subtle text-sm mb-1">
                    Selling Price (Cost Currency)
                  </Text>
                  <Text className="font-medium text-lg">
                    {formatCurrency(
                      addOn.selling_price_cost_currency,
                      addOn.cost_currency
                    )}
                  </Text>
                  <Text className="text-ui-fg-subtle text-xs">
                    {addOn.cost_currency}
                  </Text>
                </div>

                <div>
                  <Text className="text-ui-fg-subtle text-sm mb-1">
                    Selling Price
                  </Text>
                  <Text className="font-medium text-lg">
                    {formatCurrency(
                      addOn.selling_price,
                      addOn.selling_currency
                    )}
                  </Text>
                  <Text className="text-ui-fg-subtle text-xs">
                    {addOn.selling_currency}
                  </Text>
                </div>
              </div>
            </Container>

            {/* Metadata */}
            <Container className="p-6 border border-ui-border-base rounded-lg">
              <div className="flex items-center gap-3 mb-4">
                <Calendar className="h-5 w-5 text-ui-fg-subtle" />
                <Heading level="h2">Metadata</Heading>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Text className="text-ui-fg-subtle text-sm mb-1">
                    Created At
                  </Text>
                  <Text className="font-medium">
                    {formatDate(addOn.created_at)}
                  </Text>
                </div>

                <div>
                  <Text className="text-ui-fg-subtle text-sm mb-1">
                    Last Updated
                  </Text>
                  <Text className="font-medium">
                    {formatDate(addOn.updated_at)}
                  </Text>
                </div>

                <div>
                  <Text className="text-ui-fg-subtle text-sm mb-1">
                    Add-on ID
                  </Text>
                  <Text className="font-mono text-sm">{addOn.id}</Text>
                </div>
              </div>
            </Container>
          </div>
        )}
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "View Add-on",
  icon: AddOnViewIcon,
});

export default InventoryAddOnViewPage;
