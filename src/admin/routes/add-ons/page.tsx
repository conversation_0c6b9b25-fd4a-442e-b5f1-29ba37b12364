import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Settings,
  Package,
  Search,
  Filter,
  Download,
  Eye,
  Edit2,
  Check,
  X,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Select,
  IconButton,
  Tooltip,
} from "@camped-ai/ui";
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../hooks/use-rbac";
import { useInventoryAddOns } from "../../hooks/use-inventory-add-ons";
import { useAdminCurrencies } from "../../hooks/use-admin-currencies";
import { ComponentType } from "react";

// Create icon component for route config
const AddOnsIcon: ComponentType = (props: any) => (
  <Settings {...props} className="h-4 w-4" />
);

const InventoryAddOnsPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const currencyOptions = [
    { value: "CHF", label: "CHF - Swiss Franc" },
    { value: "GBP", label: "GBP - British Pound" },
    { value: "EUR", label: "EUR - Euro" },
    { value: "USD", label: "USD - US Dollar" },
  ];

  // State for filters and pagination
  const [filters, setFilters] = useState({
    search: "",
    supplier_id: "",
    category: "",
    is_active: undefined as boolean | undefined,
    sort_by: "title" as "title" | "created_at" | "category" | "status",
    sort_order: "asc" as "asc" | "desc",
  });

  const [pagination, setPagination] = useState({
    limit: 10,
    offset: 0,
  });

  // State for inline editing
  const [editingCell, setEditingCell] = useState<{
    addOnId: string;
    field: string;
  } | null>(null);
  const [editingValues, setEditingValues] = useState<Record<string, any>>({});
  const [updating, setUpdating] = useState<string | null>(null);

  // Fetch add-ons data
  const { addOns, loading, error, summary, filterOptions, refetch } =
    useInventoryAddOns({
      ...filters,
      ...pagination,
    });

  // Handle filter changes
  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPagination((prev) => ({ ...prev, offset: 0 })); // Reset to first page
  };

  // Handle pagination
  const handlePageChange = (newOffset: number) => {
    setPagination((prev) => ({ ...prev, offset: newOffset }));
  };

  // Handle items per page change
  const handleLimitChange = (newLimit: number) => {
    setPagination((prev) => ({ ...prev, limit: newLimit, offset: 0 })); // Reset to first page
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    setFilters((prev) => ({
      ...prev,
      sort_by: field as any,
      sort_order:
        prev.sort_by === field && prev.sort_order === "asc" ? "desc" : "asc",
    }));
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string = "CHF") => {
    return new Intl.NumberFormat("en-CH", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Inline editing functions
  const startEditing = (addOnId: string, field: string, currentValue: any) => {
    setEditingCell({ addOnId, field });
    setEditingValues({ [`${addOnId}_${field}`]: String(currentValue) });
  };

  const cancelEditing = () => {
    setEditingCell(null);
    setEditingValues({});
  };

  const saveEdit = async (addOnId: string, field: string) => {
    const key = `${addOnId}_${field}`;
    const newValue = editingValues[key];

    if (newValue === undefined) return;

    setUpdating(addOnId);

    try {
      // Convert text input to number for numeric fields
      const numericFields = [
        "cost",
        "selling_margin",
        "selling_price_cost_currency",
        "selling_price",
      ];
      const valueToSend = numericFields.includes(field)
        ? parseFloat(newValue) || 0
        : newValue;

      // Find the current add-on data
      const currentAddOn = addOns?.find((addon) => addon.id === addOnId);
      if (!currentAddOn) return;

      // Prepare the update payload - backend will handle all price calculations
      const updatePayload: Record<string, any> = {
        [field]: valueToSend,
      };

      const response = await fetch(`/admin/inventory/add-ons/${addOnId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "x-publishable-api-key":
            "pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b",
        },
        body: JSON.stringify(updatePayload),
      });

      if (!response.ok) {
        throw new Error("Failed to update add-on");
      }

      // Refresh the data
      refetch();

      // Clear editing state
      setEditingCell(null);
      setEditingValues({});
    } catch (error) {
      console.error("Error updating add-on:", error);
      // You might want to show a toast notification here
    } finally {
      setUpdating(null);
    }
  };

  const handleEditingValueChange = (
    addOnId: string,
    field: string,
    value: any
  ) => {
    const key = `${addOnId}_${field}`;
    setEditingValues((prev) => ({ ...prev, [key]: value }));
  };

  const isEditing = (addOnId: string, field: string) => {
    return editingCell?.addOnId === addOnId && editingCell?.field === field;
  };

  const getEditingValue = (addOnId: string, field: string) => {
    const key = `${addOnId}_${field}`;
    return editingValues[key];
  };

  // Editable cell component
  const EditableCell = ({
    addOn,
    field,
    value,
    displayValue,
  }: {
    addOn: any;
    field: string;
    value: any;
    displayValue: string;
  }) => {
    const editing = isEditing(addOn.id, field);
    const isUpdatingThis = updating === addOn.id;
    const inputRef = useRef<HTMLInputElement>(null);
    const selectRef = useRef<HTMLSelectElement>(null);
    const canEdit = hasPermission("addons:edit");

    // Check if this field should use a dropdown (currency fields)
    const isCurrencyField =
      field === "selling_currency" || field === "cost_currency";

    // Auto-focus when entering edit mode
    useEffect(() => {
      if (editing) {
        // Small delay to ensure component is fully rendered
        setTimeout(() => {
          if (isCurrencyField && selectRef.current) {
            selectRef.current.focus();
          } else if (inputRef.current) {
            inputRef.current.focus();
            // Move cursor to end of text instead of selecting all
            const length = inputRef.current.value.length;
            inputRef.current.setSelectionRange(length, length);
          }
        }, 0);
      }
    }, [editing, isCurrencyField]);

    if (editing) {
      return (
        <div className="flex items-center gap-1">
          {isCurrencyField ? (
            <Select
              value={getEditingValue(addOn.id, field) ?? String(value)}
              onValueChange={(newValue) =>
                handleEditingValueChange(addOn.id, field, newValue)
              }
              disabled={isUpdatingThis}
            >
              <Select.Trigger className="w-24 h-8">
                <Select.Value />
              </Select.Trigger>
              <Select.Content>
                {currencyOptions.map((option) => (
                  <Select.Item key={option.value} value={option.value}>
                    {option.value}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
          ) : (
            <Input
              ref={inputRef}
              type="text"
              value={getEditingValue(addOn.id, field) ?? String(value)}
              onChange={(e) =>
                handleEditingValueChange(addOn.id, field, e.target.value)
              }
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  saveEdit(addOn.id, field);
                } else if (e.key === "Escape") {
                  cancelEditing();
                }
              }}
              size="small"
              className="w-24"
              disabled={isUpdatingThis}
              placeholder="Enter value"
            />
          )}
          <IconButton
            variant="transparent"
            size="small"
            onClick={() => saveEdit(addOn.id, field)}
            disabled={isUpdatingThis}
          >
            <Check className="h-3 w-3 text-ui-fg-success" />
          </IconButton>
          <IconButton
            variant="transparent"
            size="small"
            onClick={cancelEditing}
            disabled={isUpdatingThis}
          >
            <X className="h-3 w-3 text-ui-fg-error" />
          </IconButton>
        </div>
      );
    }

    return (
      <div
        className={`flex items-center gap-1 group ${
          canEdit ? "cursor-pointer hover:bg-ui-bg-subtle-hover" : ""
        } p-1 rounded`}
        onClick={
          canEdit ? () => startEditing(addOn.id, field, value) : undefined
        }
      >
        <Text className="font-medium">{displayValue}</Text>
        {canEdit && (
          <Edit2 className="h-3 w-3 text-ui-fg-subtle opacity-0 group-hover:opacity-100 transition-opacity" />
        )}
      </div>
    );
  };

  // Check permissions
  if (!hasPermission("addons:view")) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <Package className="h-12 w-12 text-ui-fg-muted mx-auto mb-4" />
            <Heading level="h1">Access Denied</Heading>
            <Text className="mt-2">
              You don't have permission to view add-ons inventory.
            </Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <Heading level="h1">Add-ons Inventory</Heading>
            <Text className="text-ui-fg-subtle mt-1">
              Manage add-on products synced from suppliers
            </Text>
          </div>

          <div className="flex items-center gap-2">
            {hasPermission("addons:export") && (
              <Button variant="secondary" size="small">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            )}
          </div>
        </div>

        {/* Summary Cards */}
        {/* {summary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="border border-ui-border-base rounded-lg p-4">
              <Text className="text-ui-fg-subtle text-sm">Total Add-ons</Text>
              <Heading level="h3" className="mt-1">
                {summary.total_add_ons}
              </Heading>
            </div>
            <div className="border border-ui-border-base rounded-lg p-4">
              <Text className="text-ui-fg-subtle text-sm">Active Add-ons</Text>
              <Heading level="h3" className="mt-1">
                {summary.active_add_ons}
              </Heading>
            </div>
            <div className="border border-ui-border-base rounded-lg p-4">
              <Text className="text-ui-fg-subtle text-sm">Suppliers</Text>
              <Heading level="h3" className="mt-1">
                {summary.unique_suppliers}
              </Heading>
            </div>
            <div className="border border-ui-border-base rounded-lg p-4">
              <Text className="text-ui-fg-subtle text-sm">Categories</Text>
              <Heading level="h3" className="mt-1">
                {summary.unique_categories}
              </Heading>
            </div>
          </div>
        )} */}

        {/* Filters */}
        <div className="flex flex-wrap items-center gap-4 mb-6 rounded-lg">
          <div className="flex-1 min-w-[200px]">
            <Input
              placeholder="Search add-ons..."
              value={filters.search}
              onChange={(e) => handleFilterChange("search", e.target.value)}
              prefix={<Search className="h-4 w-4" />}
            />
          </div>

          <Select
            value={filters.is_active?.toString() || "all"}
            onValueChange={(value) =>
              handleFilterChange(
                "is_active",
                value === "all" ? undefined : value === "true"
              )
            }
          >
            <Select.Trigger className="w-[150px]">
              <Select.Value placeholder="All Status" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="all">All Status</Select.Item>
              <Select.Item value="true">Active</Select.Item>
              <Select.Item value="false">Inactive</Select.Item>
            </Select.Content>
          </Select>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-8">
            <Text>Loading add-ons...</Text>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-8">
            <Text className="text-ui-fg-error">
              Error loading add-ons: {error}
            </Text>
            <Button
              variant="secondary"
              size="small"
              className="mt-2"
              onClick={refetch}
            >
              Retry
            </Button>
          </div>
        )}

        {/* Add-ons Table */}
        {!loading && !error && addOns && (
          <div className="border border-ui-border-base rounded-lg overflow-hidden">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell
                    className="cursor-pointer hover:bg-ui-bg-subtle"
                    onClick={() => handleSortChange("title")}
                  >
                    <div className="flex items-center gap-1">
                      Add-on Name
                      {filters.sort_by === "title" && (
                        <span className="text-xs">
                          {filters.sort_order === "asc" ? "↑" : "↓"}
                        </span>
                      )}
                    </div>
                  </Table.HeaderCell>
                  <Table.HeaderCell>Cost</Table.HeaderCell>
                  <Table.HeaderCell>Cost Currency</Table.HeaderCell>
                  <Table.HeaderCell>Selling Margin</Table.HeaderCell>
                  <Table.HeaderCell>
                    Selling Price (Cost Currency)
                  </Table.HeaderCell>
                  <Table.HeaderCell>Selling Price</Table.HeaderCell>
                  <Table.HeaderCell>Selling Currency</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {addOns.map((addOn) => (
                  <Table.Row key={addOn.id}>
                    <Table.Cell>
                      <div>
                        <Text className="font-medium">{addOn.title}</Text>
                        {addOn.description && (
                          <Text className="text-ui-fg-subtle text-sm mt-1">
                            {addOn.description.length > 60
                              ? `${addOn.description.substring(0, 60)}...`
                              : addOn.description}
                          </Text>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <EditableCell
                        key={`${addOn.id}-cost`}
                        addOn={addOn}
                        field="cost"
                        value={addOn.cost}
                        displayValue={formatCurrency(
                          addOn.cost,
                          addOn.cost_currency
                        )}
                      />
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="text-sm">{addOn.cost_currency}</Text>
                    </Table.Cell>
                    <Table.Cell>
                      <EditableCell
                        key={`${addOn.id}-selling_margin`}
                        addOn={addOn}
                        field="selling_margin"
                        value={addOn.selling_margin}
                        displayValue={formatPercentage(addOn.selling_margin)}
                      />
                    </Table.Cell>
                    <Table.Cell>
                      <EditableCell
                        key={`${addOn.id}-selling_price_cost_currency`}
                        addOn={addOn}
                        field="selling_price_cost_currency"
                        value={addOn.selling_price_cost_currency}
                        displayValue={formatCurrency(
                          addOn.selling_price_cost_currency,
                          addOn.cost_currency
                        )}
                      />
                    </Table.Cell>
                    <Table.Cell>
                      <EditableCell
                        key={`${addOn.id}-selling_price`}
                        addOn={addOn}
                        field="selling_price"
                        value={addOn.selling_price}
                        displayValue={addOn.selling_price.toFixed(2)}
                      />
                    </Table.Cell>
                    <Table.Cell>
                      <EditableCell
                        key={`${addOn.id}-selling_currency`}
                        addOn={addOn}
                        field="selling_currency"
                        value={addOn.selling_currency}
                        displayValue={addOn.selling_currency}
                      />
                    </Table.Cell>
                    <Table.Cell>
                      <Badge color={addOn.is_active ? "green" : "grey"}>
                        {addOn.status}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        <Tooltip content="View Details">
                          <IconButton
                            variant="transparent"
                            size="small"
                            onClick={() => navigate(`/add-ons/${addOn.id}`)}
                          >
                            <Eye className="h-4 w-4" />
                          </IconButton>
                        </Tooltip>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>

            {/* Pagination */}
            {addOns.length > 0 && (
              <div className="flex items-center justify-between p-4 border-t border-ui-border-base">
                <div className="flex items-center gap-4">
                  <Text className="text-sm text-ui-fg-subtle">
                    Showing {pagination.offset + 1} to{" "}
                    {Math.min(
                      pagination.offset + pagination.limit,
                      summary?.total_add_ons || 0
                    )}{" "}
                    of {summary?.total_add_ons || 0} add-ons
                    {summary?.total_add_ons &&
                      summary.total_add_ons > pagination.limit && (
                        <span className="ml-2 text-ui-fg-muted">
                          (Page{" "}
                          {Math.floor(pagination.offset / pagination.limit) + 1}{" "}
                          of{" "}
                          {Math.ceil(summary.total_add_ons / pagination.limit)})
                        </span>
                      )}
                  </Text>

                  <div className="flex items-center gap-2">
                    <Text className="text-sm text-ui-fg-subtle">Show</Text>
                    <Select
                      value={pagination.limit.toString()}
                      onValueChange={(value) =>
                        handleLimitChange(parseInt(value))
                      }
                    >
                      <Select.Trigger className="w-[70px]">
                        <Select.Value />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="10">10</Select.Item>
                        <Select.Item value="25">25</Select.Item>
                        <Select.Item value="100">100</Select.Item>
                      </Select.Content>
                    </Select>
                    <Text className="text-sm text-ui-fg-subtle">per page</Text>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="secondary"
                    size="small"
                    disabled={pagination.offset === 0}
                    onClick={() => {
                      const newOffset = Math.max(
                        0,
                        pagination.offset - pagination.limit
                      );
                      handlePageChange(newOffset);
                    }}
                  >
                    Previous
                  </Button>
                  <Text className="text-sm text-ui-fg-subtle px-2">
                    Page {Math.floor(pagination.offset / pagination.limit) + 1}
                  </Text>
                  <Button
                    variant="secondary"
                    size="small"
                    disabled={
                      pagination.offset + pagination.limit >=
                      (summary?.total_add_ons || 0)
                    }
                    onClick={() => {
                      const newOffset = pagination.offset + pagination.limit;
                      handlePageChange(newOffset);
                    }}
                  >
                    Next
                  </Button>
                  {/* <Button
                    variant="secondary"
                    size="small"
                    onClick={() => refetch()}
                  >
                    Refresh
                  </Button> */}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && addOns && addOns.length === 0 && (
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-ui-fg-muted mx-auto mb-4" />
            <Heading level="h3" className="mb-2">
              No Add-ons Found
            </Heading>
            <Text className="text-ui-fg-subtle">
              {filters.search || filters.supplier_id || filters.category
                ? "No add-ons match your current filters."
                : "No add-ons have been synced from suppliers yet."}
            </Text>
          </div>
        )}
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Add-ons",
  icon: AddOnsIcon,
});

export default InventoryAddOnsPage;
