import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import SimpleBookingDetail from "../../../../components/booking/simple-booking-detail";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";

const BookingDetailPage = () => {
  const { id } = useParams();

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <RoleGuard
          requirePermission="bookings:view"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view booking details.
              </Text>
            </div>
          }
        >
          <SimpleBookingDetail bookingId={id} />
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Booking Details",
});

export default BookingDetailPage;
