import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import BookingList from "../../../components/booking/booking-list";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import ErrorBoundary from "../../../components/shared/error-boundary";
import { useLocation } from "react-router-dom";
import { RoleGuard } from "../../../components/rbac/RoleGuard";

const BookingsPage = () => {
  // Get hotel_id from URL query parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const hotelId = queryParams.get("hotel_id");
  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <RoleGuard
          requirePermission="bookings:view"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view bookings.
              </Text>
            </div>
          }
        >
          <ErrorBoundary>
            <BookingList hotelId={hotelId} />
          </ErrorBoundary>
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Bookings",
});

export default BookingsPage;
