import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import CartList from "../../../components/cart/cart-list";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";

const CartsPage = () => {
  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <RoleGuard
          requirePermission="carts:view"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view carts.
              </Text>
            </div>
          }
        >
          <CartList />
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Carts",
});

export default CartsPage;
