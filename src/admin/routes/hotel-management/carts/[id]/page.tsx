import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import CartDetail from "../../../../components/cart/cart-detail";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";

const CartDetailPage = () => {
  const { id } = useParams();

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <RoleGuard
          requirePermission="carts:view"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view cart details.
              </Text>
            </div>
          }
        >
          <CartDetail cartId={id} />
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Cart Details",
});

export default CartDetailPage;
