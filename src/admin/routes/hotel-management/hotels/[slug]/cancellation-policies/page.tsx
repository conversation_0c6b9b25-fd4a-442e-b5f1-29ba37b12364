import { useParams, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  IconButton,
} from "@camped-ai/ui";
import { ArrowLeft, ChevronLeft } from "@camped-ai/icons";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Clock, Hotel, Shield } from "lucide-react";
import { HotelData } from "../../../../../types";
import CancellationPolicyList from "../../../../../components/hotel/cancellation-policy/cancellation-policy-list";

const HotelCancellationPoliciesPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [hotel, setHotel] = useState<HotelData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchHotelDetails = async () => {
      try {
        const hotelResponse = await fetch(
          `/admin/hotel-management/hotels/${slug}`,
          {
            credentials: "include",
          }
        );
        const hotelData = await hotelResponse.json();
        const processedHotelData = Array.isArray(hotelData?.hotel)
          ? hotelData?.hotel[0]
          : hotelData?.hotel || hotelData;

        setHotel(processedHotelData);
        setIsLoading(false);
      } catch (error) {
        console.error("Failed to fetch hotel details:", error);
        toast.error("Error", {
          description: "Failed to load hotel details",
        });
        setIsLoading(false);
      }
    };

    fetchHotelDetails();
  }, [slug]);

  if (isLoading) {
    return (
      <Container>
        <Text>Loading hotel details...</Text>
      </Container>
    );
  }

  if (!hotel) {
    return (
      <Container>
        <Text>Hotel not found</Text>
        <Button
          variant="secondary"
          onClick={() => navigate("/hotel-management/hotels")}
          className="mt-4"
        >
          Back to Hotels
        </Button>
      </Container>
    );
  }

  return (
    <>
      <Toaster />
      <div className="flex items-center gap-4 mb-2">
        <IconButton
          onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
        >
          <ArrowLeft className="w-4 h-4" />
        </IconButton>
        <Heading level="h1">{hotel.name}</Heading>
      </div>

      <CancellationPolicyList hotelId={hotel.id} />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Cancellation Policies",
  icon: Shield,
});

export default HotelCancellationPoliciesPage;
