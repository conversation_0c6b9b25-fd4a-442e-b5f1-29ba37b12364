import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  Input,
  Tabs,
  IconButton,
} from "@camped-ai/ui";
import { ArrowLeft, ChevronLeft } from "@camped-ai/icons";
import { Calendar, Settings, Hotel, Upload, Download } from "lucide-react";
import BulkImportModal from "../../../../../components/room-inventory/bulk-import-modal";
import ExportModal from "../../../../../components/room-inventory/export-modal";
import RoomInventoryManager from "../../../../../components/hotel/room-inventory-manager";
import SimpleRoomCalendar from "../../../../../components/hotel/simple-room-calendar";
import { format, addDays, startOfDay, endOfDay } from "date-fns";

const HotelAvailabilityPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [hotel, setHotel] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [startDate, setStartDate] = useState(startOfDay(new Date()));
  const [endDate, setEndDate] = useState(endOfDay(addDays(new Date(), 14)));
  const [activeTab, setActiveTab] = useState("calendar");

  const [importModalOpen, setImportModalOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);

  // State for real data
  const [realRoomConfigs, setRealRoomConfigs] = useState<any[]>([]);
  const [realRooms, setRealRooms] = useState<any[]>([]);
  const [usingMockData, setUsingMockData] = useState(true); // Start with mock data by default

  // Use real data if available, otherwise fall back to mock data
  const roomConfigs = realRoomConfigs;
  const rooms = realRooms;
  // State for real availability datausingMockData
  const [realAvailability, setRealAvailability] = useState<any[]>([]);
  const availability = realAvailability;

  // Function to fetch room configurations and rooms separately
  const fetchRoomConfigsAndRooms = async (hotelId: string) => {
    try {
      // Fetch room configurations
      const roomConfigEndpoints = [
        `/admin/direct-room-configs?hotel_id=${hotelId}`,
        `/admin/hotel-room-configs?hotel_id=${hotelId}`,
        `/admin/simple-room-configs?hotel_id=${hotelId}`,
      ];

      let roomConfigsData = [];
      let roomConfigsSuccess = false;

      for (const endpoint of roomConfigEndpoints) {
        try {
          console.log(`Trying to fetch room configurations from ${endpoint}`);
          const response = await fetch(endpoint);

          if (response.ok) {
            const data = await response.json();

            if (data.roomConfigs && data.roomConfigs.length > 0) {
              roomConfigsData = data.roomConfigs;
              roomConfigsSuccess = true;
              console.log(
                `Found ${roomConfigsData.length} room configurations from ${endpoint}`
              );
              break;
            } else if (data.room_configs && data.room_configs.length > 0) {
              roomConfigsData = data.room_configs;
              roomConfigsSuccess = true;
              console.log(
                `Found ${roomConfigsData.length} room configurations from ${endpoint}`
              );
              break;
            }
          }
        } catch (error) {
          console.log(`Error fetching from ${endpoint}:`, error);
        }
      }

      if (roomConfigsSuccess) {
        setRealRoomConfigs(roomConfigsData);

        // Fetch rooms for each room configuration
        let allRooms: any[] = [];

        for (const config of roomConfigsData) {
          const roomEndpoints = [
            `/admin/room-configs/${config.id}/rooms`,
            `/admin/hotel-management/room-configs/${config.id}/rooms`,
            `/admin/products/${config.id}/variants`,
          ];

          for (const endpoint of roomEndpoints) {
            try {
              console.log(`Trying to fetch rooms from ${endpoint}`);
              const response = await fetch(endpoint);

              if (response.ok) {
                const data = await response.json();

                if (data.rooms && data.rooms.length > 0) {
                  allRooms = [...allRooms, ...data.rooms];
                  console.log(
                    `Found ${data.rooms.length} rooms from ${endpoint}`
                  );
                  break;
                } else if (data.variants && data.variants.length > 0) {
                  const roomsFromVariants = data.variants.map(
                    (variant: any) => ({
                      id: variant.id,
                      name: variant.title || `Room ${variant.id.slice(-4)}`,
                      title: variant.title,
                      room_number:
                        variant.metadata?.room_number || variant.title,
                      status: variant.metadata?.status || "available",
                      floor: variant.metadata?.floor || "",
                      room_config_id: config.id,
                    })
                  );
                  allRooms = [...allRooms, ...roomsFromVariants];
                  console.log(
                    `Found ${roomsFromVariants.length} variants/rooms from ${endpoint}`
                  );
                  break;
                }
              }
            } catch (error) {
              console.log(`Error fetching from ${endpoint}:`, error);
            }
          }
        }

        console.log(
          `Found a total of ${allRooms.length} rooms across all configurations`
        );
        setRealRooms(allRooms);
        setUsingMockData(allRooms.length === 0); // Use mock data only if we couldn't find any real rooms
      } else {
        console.error("Could not fetch room configurations from any endpoint");
        setUsingMockData(true);
      }
    } catch (error) {
      console.error("Error fetching room configurations and rooms:", error);
      setUsingMockData(true);
    }
  };

  // Fetch hotel data and availability
  useEffect(() => {
    const fetchHotelAndAvailability = async () => {
      try {
        setIsLoading(true);
        const hotelResponse = await fetch(
          `/admin/hotel-management/hotels/${slug}`
        );
        if (!hotelResponse.ok) {
          throw new Error("Failed to fetch hotel details");
        }

        const hotelData = await hotelResponse.json();
        // Check if hotel data is in the expected format
        const hotel =
          hotelData.hotel && hotelData.hotel[0]
            ? hotelData.hotel[0]
            : hotelData.hotel;
        setHotel(hotel);
      } finally {
        setIsLoading(false);
      }
    };
    if (slug) {
      fetchHotelAndAvailability();
    }
  }, [slug, startDate, endDate]);

  // Handle date range change and refetch availability
  const handleDateRangeChange = async () => {
    if (!hotel && !slug) return;

    try {
      setIsLoading(true);
      const formattedStartDate = format(startDate, "yyyy-MM-dd");
      const formattedEndDate = format(endDate, "yyyy-MM-dd");

      // Make sure we have a valid hotel ID
      const hotelId = hotel?.id || slug;

      // Try different endpoints to fetch availability data
      const availabilityEndpoints = [
        `/admin/hotel-management/availability?hotel_id=${hotelId}&start_date=${formattedStartDate}&end_date=${formattedEndDate}&consolidate=true`,
      ];

      let availabilityData = null;
      let availabilitySuccess = false;

      for (const endpoint of availabilityEndpoints) {
        try {
          console.log(`Trying to fetch availability data from ${endpoint}`);
          const response = await fetch(endpoint);

          if (response.ok) {
            availabilityData = await response.json();
            availabilitySuccess = true;
            console.log(
              `Successfully fetched availability data from ${endpoint}`
            );
            break;
          }
        } catch (error) {
          console.log(`Error fetching from ${endpoint}:`, error);
        }
      }

      if (availabilitySuccess && availabilityData) {
        // Try to extract room configurations, rooms, and availability from the response
        const roomConfigsData =
          availabilityData.room_configs || availabilityData.roomConfigs || [];
        const roomsData = availabilityData.rooms || [];
        const availabilityDataArray = availabilityData.availability || [];

        console.log(
          `Found ${roomConfigsData.length} room configurations, ${roomsData.length} rooms, and ${availabilityDataArray.length} availability records`
        );

        if (roomConfigsData.length > 0) {
          setRealRoomConfigs(roomConfigsData);
          setRealRooms(roomsData);
          setRealAvailability(availabilityDataArray);
          setUsingMockData(false);
        } else {
          console.log(
            "No room configurations found in availability data, trying to fetch separately"
          );
          await fetchRoomConfigsAndRooms(hotelId);
        }
      } else {
        console.log(
          "Could not fetch availability data from any endpoint, trying to fetch room configurations and rooms separately"
        );
        await fetchRoomConfigsAndRooms(hotelId);
      }
    } catch (error) {
      console.error("Error fetching availability:", error);
      toast.error("Error", {
        description:
          error instanceof Error
            ? error.message
            : "Failed to fetch availability data",
      });
      setUsingMockData(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Debug information
  useEffect(() => {
    console.log("Current state:", {
      isLoading,
      usingMockData,
      hotelId: hotel?.id,
      roomConfigsCount: roomConfigs.length,
      roomsCount: rooms.length,
      availabilityCount: availability.length,
    });
  }, [isLoading, usingMockData, hotel, roomConfigs, rooms, availability]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        <div className="ml-4 text-foreground">
          Loading room availability data...
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-background">
      <div className="w-full flex flex-col">
        {/* Header */}

        <div className="mb-6">
          <div className="flex items-center gap-4 ">
            <IconButton
              onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
            >
              <ArrowLeft className="w-4 h-4" />
            </IconButton>
            <Heading level="h1">
              {hotel?.name || "Hotel"} - Room Availability
            </Heading>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 pb-8 bg-background">
          {/* Availability Management */}
          <Container className="p-0 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <Tabs.List className="px-4 pt-4">
                <Tabs.Trigger value="calendar">
                  <Calendar className="w-4 h-4 mr-2" />
                  Calendar View
                </Tabs.Trigger>

                <Tabs.Trigger value="configure">
                  <Settings className="w-4 h-4 mr-2" />
                  Configure Availability
                </Tabs.Trigger>
              </Tabs.List>

              <Tabs.Content value="calendar" className="p-0">
                {hotel ? (
                  <SimpleRoomCalendar hotelId={hotel.id} />
                ) : (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                    <div className="ml-4 text-foreground">
                      Loading hotel information...
                    </div>
                  </div>
                )}
              </Tabs.Content>

              <Tabs.Content value="configure" className="p-6">
                <div className="space-y-6">
                  {/* Room Inventory Manager */}
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-4">
                      <div>
                        <Heading level="h3" className="text-lg text-foreground">
                          Configure Room Inventory
                        </Heading>
                        <Text className="text-muted-foreground">
                          Update room availability and pricing for specific date
                          ranges
                        </Text>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => setExportModalOpen(true)}
                          className="flex items-center gap-2"
                        >
                          <Download className="w-4 h-4" />
                          Export
                        </Button>
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => setImportModalOpen(true)}
                          className="flex items-center gap-2"
                        >
                          <Upload className="w-4 h-4" />
                          Import
                        </Button>
                      </div>
                    </div>

                    <div className="mt-4">
                      {hotel ? (
                        <RoomInventoryManager
                          hotelId={hotel.id}
                          onInventoryUpdated={handleDateRangeChange}
                        />
                      ) : (
                        <div className="flex items-center justify-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                          <div className="ml-4 text-foreground">
                            Loading hotel information...
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Tabs.Content>
            </Tabs>
          </Container>
        </div>
      </div>
      <Toaster />

      {/* Bulk Import Modal */}
      <BulkImportModal
        open={importModalOpen}
        onClose={() => {
          setImportModalOpen(false);
          // Refresh data when modal closes with a slight delay to ensure server has processed everything
          setTimeout(() => {
            console.log("Refreshing inventory after import");
            handleDateRangeChange();
          }, 500);
        }}
        hotelId={slug}
      />

      {/* Export Modal */}
      <ExportModal
        open={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
        hotelId={slug}
      />
    </div>
  );
};

export default HotelAvailabilityPage;
