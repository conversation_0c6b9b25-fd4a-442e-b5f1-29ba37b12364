import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { ArrowLeft, Tags } from "lucide-react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import HotelPricingManager from "../../../../../components/hotel/pricing/hotel-pricing-manager";
import { useRbac } from "../../../../../hooks/use-rbac";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";

interface Hotel {
  id: string;
  name: string;
}

interface RoomConfig {
  id: string;
  title: string;
  handle?: string;
  description?: string;
}

const HotelPricingPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [hotel, setHotel] = useState<Hotel | null>(null);
  const [roomConfigs, setRoomConfigs] = useState<RoomConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  // No need for activeTab state anymore as we're showing DetailedPricing directly

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch hotel details
        const hotelResponse = await fetch(
          `/admin/hotel-management/hotels/${slug}`,
          {
            credentials: "include",
          }
        );

        if (!hotelResponse.ok) {
          throw new Error("Failed to fetch hotel details");
        }

        const hotelData = await hotelResponse.json();
        const hotel = Array.isArray(hotelData?.hotel)
          ? hotelData?.hotel[0]
          : hotelData?.hotel;

        setHotel(hotel);

        // Fetch room configurations
        const roomConfigsResponse = await fetch(
          `/admin/direct-room-configs?hotel_id=${hotel.id}`,
          {
            credentials: "include",
          }
        );

        if (!roomConfigsResponse.ok) {
          throw new Error("Failed to fetch room configurations");
        }

        const roomConfigsData = await roomConfigsResponse.json();

        setRoomConfigs(roomConfigsData.roomConfigs || []);

        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Error", {
          description: "Failed to load hotel data",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [slug]);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Check permissions first
  if (!hasPermission("pricing:view")) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            You don't have permission to view pricing information
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  if (!hotel) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">Hotel not found</Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  const canEdit = hasPermission("pricing:edit");
  const canCreate = hasPermission("pricing:create");
  const canDelete = hasPermission("pricing:delete");

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <HotelPricingManager
        hotelId={hotel.id}
        hotelName={hotel.name}
        roomConfigs={roomConfigs}
        onBack={() => navigate(`/hotel-management/hotels/${slug}`)}
        canEdit={canEdit}
        canCreate={canCreate}
        canDelete={canDelete}
      />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Hotel Pricing",
  icon: Tags,
});

export default HotelPricingPage;
