/* Custom styles for text truncation with line clamping */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Ensure smooth transitions for show more/less functionality */
.line-clamp-3 {
  transition: all 0.2s ease-in-out;
}

/* Support for browsers that don't support -webkit-line-clamp */
@supports not (-webkit-line-clamp: 3) {
  .line-clamp-3 {
    display: block;
    max-height: calc(1.5em * 3); /* Assuming line-height of 1.5 */
    overflow: hidden;
    position: relative;
  }
  
  .line-clamp-3::after {
    content: "...";
    position: absolute;
    bottom: 0;
    right: 0;
    background: inherit;
    padding-left: 0.5em;
  }
}
