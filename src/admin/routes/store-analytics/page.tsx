import { useState } from "react";
import { useMemo } from "react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Tabs, Text } from "@camped-ai/ui";
import { ShoppingBag } from "@camped-ai/icons";
import { Box } from "@mui/material";
import OverviewTab from "../../../ui-components/tabs/overview";
import OrdersTab from "../../../ui-components/tabs/orders";
import ProductsTab from "../../../ui-components/tabs/products";
import SalesTab from "../../../ui-components/tabs/sales";
import CustomersTab from "../../../ui-components/tabs/customers";
import {
  DateLasts,
  OrderStatus,
  convertDateLastsToComparedDateRange,
  convertDateLastsToDateRange,
} from "../../../ui-components";
import { Grid } from "@mui/material";
import {
  ComparedDate,
  SelectDateLasts,
  SwitchComparison,
} from "../../../ui-components/common/overview-components";
import { AbandadCard } from "../../../ui-components/carts/funnel-chart";
import { RegionSelect } from "../../../ui-components/common/Region-component";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";

const AnalyticsPage = () => {
  const [dateLast, setDateLasts] = useState<DateLasts>(DateLasts.LastWeek);
  const [compareEnabled, setCompare] = useState<boolean>(true);
  const [orderStatuses, setOrderStatuses] = useState<OrderStatus[]>([
    OrderStatus.COMPLETED,
    OrderStatus.PENDING,
  ]);

  const dateRange = useMemo(
    () => convertDateLastsToDateRange(dateLast),
    [dateLast]
  );
  const dateRangeComparedTo = useMemo(
    () => convertDateLastsToComparedDateRange(dateLast),
    [dateLast]
  );

  function setDateLastsString(select: string) {
    switch (select) {
      case DateLasts.LastWeek:
        setDateLasts(DateLasts.LastWeek);
        break;
      case DateLasts.LastMonth:
        setDateLasts(DateLasts.LastMonth);
        break;
      case DateLasts.LastYear:
        setDateLasts(DateLasts.LastYear);
        break;
      case DateLasts.All:
        setDateLasts(DateLasts.All);
        break;
    }
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Grid container spacing={2}>
        <Grid item xs={12} md={12}>
          <Grid container spacing={2}>
            <Grid item>
              <SelectDateLasts
                dateLast={dateLast}
                onSelectChange={setDateLastsString}
              />
            </Grid>
            <Grid item>
              <RegionSelect />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={12} xl={12}>
          <Grid container alignItems="center" columnSpacing={6}></Grid>
        </Grid>
        <Grid item xs={12} md={12}>
          <Tabs defaultValue="overview">
            <Tabs.List style={{ justifyContent: "center" }}>
              <Tabs.Trigger value="overview">Overview</Tabs.Trigger>
              <Tabs.Trigger value="sales">Sales</Tabs.Trigger>
              <Tabs.Trigger value="orders">Orders</Tabs.Trigger>
              <Tabs.Trigger value="customers">Customers</Tabs.Trigger>
              <Tabs.Trigger value="products">Products</Tabs.Trigger>
              <Tabs.Trigger value="abandoned-carts">Carts</Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content value="overview">
              <Box height={20}></Box>
              <OverviewTab
                orderStatuses={orderStatuses}
                dateRange={dateRange}
                dateRangeCompareTo={dateRangeComparedTo}
                compareEnabled={compareEnabled}
              />
            </Tabs.Content>
            <Tabs.Content value="sales">
              <Box height={20}></Box>
              <SalesTab
                orderStatuses={orderStatuses}
                dateRange={dateRange}
                dateRangeCompareTo={dateRangeComparedTo}
                compareEnabled={compareEnabled}
              />
            </Tabs.Content>
            <Tabs.Content value="orders">
              <Box height={20}></Box>
              <OrdersTab
                orderStatuses={orderStatuses}
                dateRange={dateRange}
                dateRangeCompareTo={dateRangeComparedTo}
                compareEnabled={compareEnabled}
              />
            </Tabs.Content>
            <Tabs.Content value="customers">
              <Box height={20}></Box>
              <CustomersTab
                orderStatuses={orderStatuses}
                dateRange={dateRange}
                dateRangeCompareTo={dateRangeComparedTo}
                compareEnabled={compareEnabled}
              />
            </Tabs.Content>
            <Tabs.Content value="products">
              <Box height={20}></Box>
              <ProductsTab
                orderStatuses={orderStatuses}
                dateRange={dateRange}
                dateRangeCompareTo={dateRangeComparedTo}
                compareEnabled={compareEnabled}
              />
            </Tabs.Content>
            <Tabs.Content value="abandoned-carts">
              <Box height={20}></Box>
              <AbandadCard dateRange={dateRange} />
            </Tabs.Content>
          </Tabs>
        </Grid>
      </Grid>
    </>
  );
};

export default AnalyticsPage;

export const config = defineRouteConfig({
  label: "Store Analytics",
  icon: ShoppingBag,
});
