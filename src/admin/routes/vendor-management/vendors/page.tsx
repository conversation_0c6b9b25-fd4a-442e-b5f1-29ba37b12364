import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Buildings,
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import { Edit, Phone, Mail, MapPin, Filter } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
  Select,
  Drawer,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useVendors } from "../../../hooks/vendor-management";
import BulkOperations from "../../../components/vendor-management/bulk-operations";

interface Vendor {
  id: string;
  name: string;
  handle: string;
  description?: string;
  business_type: string;
  status: string;
  verification_status: string;
  primary_contact_name: string;
  primary_contact_email: string;
  primary_contact_phone?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  rating?: number;
  total_orders: number;
  completed_orders: number;
  created_at: string;
  updated_at: string;
  products?: any[];
  services?: any[];
  orders?: any[];
  contracts?: any[];
}

interface VendorFilters {
  status?: string;
  business_type?: string;
  verification_status?: string;
  search?: string;
}

const VendorsPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<VendorFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedVendors, setSelectedVendors] = useState<string[]>([]);

  // Use React Query hook for data fetching
  const { data: vendorsData, isLoading, error, refetch } = useVendors({
    search: searchTerm || undefined,
    ...filters,
    limit: 20,
  });

  // Force a refetch to bypass cache
  React.useEffect(() => {
    refetch();
  }, [refetch]);

  const vendors = vendorsData?.vendors || [];
  const totalCount = vendorsData?.count || 0;

  // Debug logging
  console.log("VendorsPage Debug:", {
    isLoading,
    error,
    vendorsData,
    vendors,
    totalCount,
    vendorsLength: vendors.length,
    vendorsDataType: typeof vendorsData,
    vendorsDataKeys: vendorsData ? Object.keys(vendorsData) : null,
    firstVendor: vendors[0] || null
  });

  const handleSelectVendor = (vendorId: string, checked: boolean) => {
    if (checked) {
      setSelectedVendors(prev => [...prev, vendorId]);
    } else {
      setSelectedVendors(prev => prev.filter(id => id !== vendorId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVendors(vendors.map(v => v.id));
    } else {
      setSelectedVendors([]);
    }
  };

  const clearSelection = () => {
    setSelectedVendors([]);
  };

  const refreshData = () => {
    // The React Query hook will automatically refetch
    // when dependencies change
  };



  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handleFilterChange = (key: keyof VendorFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm("");
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: "green" as const, label: "Active" },
      inactive: { color: "red" as const, label: "Inactive" },
      pending: { color: "orange" as const, label: "Pending" },
      suspended: { color: "red" as const, label: "Suspended" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: "grey" as const,
      label: status,
    };

    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const getVerificationBadge = (status: string) => {
    const statusConfig = {
      verified: { color: "green" as const, label: "Verified" },
      unverified: { color: "grey" as const, label: "Unverified" },
      in_review: { color: "orange" as const, label: "In Review" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: "grey" as const,
      label: status,
    };

    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const getBusinessTypeLabel = (type: string) => {
    const typeLabels = {
      housekeeping: "Housekeeping",
      maintenance: "Maintenance",
      catering: "Catering",
      transportation: "Transportation",
      laundry: "Laundry",
      security: "Security",
      landscaping: "Landscaping",
      supplies: "Supplies",
      other: "Other",
    };

    return typeLabels[type as keyof typeof typeLabels] || type;
  };

  const handleCreateVendor = () => {
    navigate("/supplier-management/suppliers/create");
  };

  const handleEditVendor = (vendorId: string) => {
    navigate(`/supplier-management/suppliers/${vendorId}`);
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Suppliers</Heading>
            <Text className="text-ui-fg-subtle">
              Manage supplier profiles and their service offerings
            </Text>
          </div>
          <div className="flex items-center gap-x-2">
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowFilters(true)}
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
            <Button
              size="small"
              onClick={handleCreateVendor}
            >
              <PlusMini />
              Add Supplier
            </Button>
          </div>
        </div>

        {/* Bulk Operations */}
        <div className="px-6">
          <BulkOperations
            selectedVendors={selectedVendors}
            onClearSelection={clearSelection}
            onRefresh={refreshData}
          />
        </div>

        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-2">
            <div className="relative">
              <MagnifyingGlass className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-ui-fg-muted" />
              <Input
                placeholder="Search vendors by name, email, or handle..."
                className="pl-8 w-80"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowFilters(true)}
            >
              <Adjustments className="h-4 w-4" />
              Filters
            </Button>
          </div>
          <div className="flex items-center gap-x-4">
            <Text className="text-ui-fg-subtle text-sm">
              {totalCount} vendor{totalCount !== 1 ? 's' : ''} total
            </Text>
          </div>
        </div>

        {/* Active Filters Display */}
        {(Object.keys(filters).length > 0 || searchTerm) && (
          <div className="px-6 pb-4">
            <div className="flex items-center gap-2">
              <Text size="small" className="text-ui-fg-subtle">Active filters:</Text>
              {searchTerm && (
                <Badge color="blue">Search: {searchTerm}</Badge>
              )}
              {filters.status && (
                <Badge color="blue">Status: {filters.status}</Badge>
              )}
              {filters.business_type && (
                <Badge color="blue">Type: {getBusinessTypeLabel(filters.business_type)}</Badge>
              )}
              {filters.verification_status && (
                <Badge color="blue">Verification: {filters.verification_status}</Badge>
              )}
              <Button
                variant="transparent"
                size="small"
                onClick={clearFilters}
              >
                Clear all
              </Button>
            </div>
          </div>
        )}

        <div className="px-6">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedVendors.length === vendors.length && vendors.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                </Table.HeaderCell>
                <Table.HeaderCell>Vendor Details</Table.HeaderCell>
                <Table.HeaderCell>Contact Information</Table.HeaderCell>
                <Table.HeaderCell>Business Type</Table.HeaderCell>
                <Table.HeaderCell>Activity</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell>Verification</Table.HeaderCell>
                <Table.HeaderCell className="w-8"></Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading ? (
                <Table.Row>
                  <Table.Cell colSpan={8} className="text-center py-8">
                    Loading vendors...
                  </Table.Cell>
                </Table.Row>
              ) : vendors.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={8} className="text-center py-8">
                    {searchTerm ? "No suppliers found matching your search" : "No suppliers found"}
                  </Table.Cell>
                </Table.Row>
              ) : (
                vendors.map((vendor) => (
                  <Table.Row
                    key={vendor.id}
                    className="hover:bg-ui-bg-subtle"
                  >
                    <Table.Cell onClick={(e) => e.stopPropagation()}>
                      <input
                        type="checkbox"
                        checked={selectedVendors.includes(vendor.id)}
                        onChange={(e) => handleSelectVendor(vendor.id, e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </Table.Cell>
                    <Table.Cell
                      className="cursor-pointer"
                      onClick={() => handleEditVendor(vendor.id)}
                    >
                      <div>
                        <Text weight="plus" size="small">
                          {vendor.name}
                        </Text>
                        <Text className="text-ui-fg-subtle" size="small">
                          {vendor.handle}
                        </Text>
                        {vendor.description && (
                          <Text className="text-ui-fg-subtle" size="small">
                            {vendor.description.length > 50
                              ? `${vendor.description.substring(0, 50)}...`
                              : vendor.description}
                          </Text>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3 text-ui-fg-muted" />
                          <Text size="small">{vendor.primary_contact_email}</Text>
                        </div>
                        <Text className="text-ui-fg-subtle" size="small">
                          {vendor.primary_contact_name}
                        </Text>
                        {vendor.primary_contact_phone && (
                          <div className="flex items-center gap-1">
                            <Phone className="h-3 w-3 text-ui-fg-muted" />
                            <Text size="small">{vendor.primary_contact_phone}</Text>
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-ui-fg-muted" />
                          <Text className="text-ui-fg-subtle" size="small">
                            {vendor.city}, {vendor.state}
                          </Text>
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge size="small" color="blue">
                        {getBusinessTypeLabel(vendor.business_type)}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="space-y-1">
                        <Text size="small">
                          {vendor.total_orders} orders
                        </Text>
                        <Text className="text-ui-fg-subtle" size="small">
                          {vendor.completed_orders} completed
                        </Text>
                        {vendor.rating && (
                          <Text className="text-ui-fg-subtle" size="small">
                            Rating: {vendor.rating.toFixed(1)}/5
                          </Text>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {getStatusBadge(vendor.status)}
                    </Table.Cell>
                    <Table.Cell>
                      {getVerificationBadge(vendor.verification_status)}
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        variant="transparent"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditVendor(vendor.id);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>

      {/* Filter Drawer */}
      <Drawer open={showFilters} onOpenChange={setShowFilters}>
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Filter Vendors</Drawer.Title>
          </Drawer.Header>
          <Drawer.Body className="space-y-4">
            <div>
              <Text size="small" weight="plus" className="mb-2">Status</Text>
              <Select
                value={filters.status || ""}
                onValueChange={(value) => handleFilterChange('status', value)}
              >
                <Select.Trigger>
                  <Select.Value placeholder="All statuses" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="">All statuses</Select.Item>
                  <Select.Item value="active">Active</Select.Item>
                  <Select.Item value="inactive">Inactive</Select.Item>
                  <Select.Item value="pending">Pending</Select.Item>
                  <Select.Item value="suspended">Suspended</Select.Item>
                </Select.Content>
              </Select>
            </div>

            <div>
              <Text size="small" weight="plus" className="mb-2">Business Type</Text>
              <Select
                value={filters.business_type || ""}
                onValueChange={(value) => handleFilterChange('business_type', value)}
              >
                <Select.Trigger>
                  <Select.Value placeholder="All business types" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="">All business types</Select.Item>
                  <Select.Item value="housekeeping">Housekeeping</Select.Item>
                  <Select.Item value="maintenance">Maintenance</Select.Item>
                  <Select.Item value="catering">Catering</Select.Item>
                  <Select.Item value="transportation">Transportation</Select.Item>
                  <Select.Item value="laundry">Laundry</Select.Item>
                  <Select.Item value="security">Security</Select.Item>
                  <Select.Item value="landscaping">Landscaping</Select.Item>
                  <Select.Item value="supplies">Supplies</Select.Item>
                  <Select.Item value="other">Other</Select.Item>
                </Select.Content>
              </Select>
            </div>

            <div>
              <Text size="small" weight="plus" className="mb-2">Verification Status</Text>
              <Select
                value={filters.verification_status || ""}
                onValueChange={(value) => handleFilterChange('verification_status', value)}
              >
                <Select.Trigger>
                  <Select.Value placeholder="All verification statuses" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="">All verification statuses</Select.Item>
                  <Select.Item value="verified">Verified</Select.Item>
                  <Select.Item value="unverified">Unverified</Select.Item>
                  <Select.Item value="in_review">In Review</Select.Item>
                </Select.Content>
              </Select>
            </div>
          </Drawer.Body>
          <Drawer.Footer>
            <div className="flex gap-2">
              <Button variant="secondary" onClick={clearFilters}>
                Clear Filters
              </Button>
              <Button onClick={() => setShowFilters(false)}>
                Apply Filters
              </Button>
            </div>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>

      <Toaster />
    </>
  );
};

// Disabled - replaced by new supplier-management routes
// export const config = defineRouteConfig({
//   label: "Vendors",
// });

export default VendorsPage;
