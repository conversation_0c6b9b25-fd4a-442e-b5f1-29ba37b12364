import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Toaster,
  toast,
  Label,
  Switch,
} from "@camped-ai/ui";
import React, { useEffect, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Plus, Trash2, Save } from "lucide-react";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { MultiSelect } from "../../../../../components/common/MultiSelect";
import { useRbac } from "../../../../../hooks/use-rbac";
import {
  useSupplier,
  useUpdateSupplier,
} from "../../../../../hooks/vendor-management/use-suppliers";
import { Supplier } from "../../../../../hooks/supplier-management/use-suppliers-list";
import { useBusinessTypes } from "../../../../../hooks/supplier-products-services/use-business-types";
import {
  REGIONS,
  TIMEZONES,
  LANGUAGES,
  PAYMENT_METHODS,
  PAYOUT_TERMS,
  CURRENCIES,
  SUPPLIER_STATUSES,
  SUPPLIER_TYPES,
} from "../../../../../constants/supplier-form-options";
import { DocumentUpload } from "../../../../../components/vendor-management";

// Contact interface
interface Contact {
  id: string;
  name: string;
  email: string;
  phone_number?: string;
  is_whatsapp: boolean;
  is_primary: boolean;
}

// Form validation schema
const supplierSchema = z.object({
  // Basic Info
  name: z.string().min(1, "Supplier name is required"),
  supplier_type: z.enum(["Company", "Individual"], {
    required_error: "Supplier type is required",
  }),
  phone: z.string().optional(),
  email: z.string().email("Valid email is required"),
  website: z.string().url().optional().or(z.literal("")),
  handle: z.string().optional(),
  status: z
    .enum(["Active", "Inactive", "Pending Approval", "Suspended", "Terminated"])
    .default("Active"),
  business_type: z.string().min(1, "Business type is required"),
  region: z.string().optional(),
  timezone: z.string().optional(),
  language_preference: z.array(z.string()).optional(),
  payment_method: z.string().optional(),
  payout_terms: z.string().optional(),
  tax_id: z.string().optional(),
  default_currency: z.string().default("CHF"),
  bank_account_details: z.string().optional(),
  categories: z.array(z.string()).optional(),

  // Address
  address_line_1: z.string().optional(),
  address_line_2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),

  // Contacts
  contacts: z
    .array(
      z.object({
        id: z.string(),
        name: z.string().min(1, "Contact name is required"),
        email: z.string().email("Valid email is required"),
        phone_number: z.string().optional(),
        is_whatsapp: z.boolean().default(false),
        is_primary: z.boolean(),
      })
    )
    .min(1, "At least one contact is required"),
});

type SupplierFormData = z.infer<typeof supplierSchema>;

const EditSupplierPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();

  // API hooks
  const updateSupplier = useUpdateSupplier();
  const { data: businessTypesData, isLoading: businessTypesLoading } =
    useBusinessTypes();
  const { data: supplierData, isLoading, error } = useSupplier(id!);
  const vendor = supplierData?.supplier as unknown as Supplier;

  // Form setup
  const form = useForm<SupplierFormData>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: "",
      supplier_type: "Company",
      phone: "",
      email: "",
      website: "",
      handle: "",
      status: "Active",
      business_type: "",
      region: "",
      timezone: "",
      language_preference: [],
      payment_method: "",
      payout_terms: "",
      tax_id: "",
      default_currency: "CHF",
      bank_account_details: "",
      categories: [],
      address_line_1: "",
      address_line_2: "",
      city: "",
      state: "",
      postal_code: "",
      country: "",
      contacts: [
        {
          id: "1",
          name: "",
          email: "",
          phone_number: "",
          is_whatsapp: false,
          is_primary: true,
        },
      ],
    },
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting },
  } = form;
  const watchedContacts = watch("contacts");
  const watchedName = watch("name");
  const watchedBusinessType = watch("business_type");
  const hasInitialized = useRef(false);

  // Generate handle from name
  useEffect(() => {
    if (watchedName) {
      const handle = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setValue("handle", handle);
    }
  }, [watchedName, setValue]);

  // Check permission and redirect if not authorized
  useEffect(() => {
    if (!hasPermission("supplier_management:edit")) {
      navigate("/supplier-management");
    }
  }, [hasPermission, navigate]);

  // Clear categories when business type changes
  useEffect(() => {
    setValue("categories", []);
  }, [watchedBusinessType, setValue]);

  // Load supplier data into form
  useEffect(() => {
    if (!vendor || hasInitialized.current) return;
    // Transform contacts from API format to form format
    const transformedContacts = vendor.contacts?.length
      ? vendor.contacts.map((contact: any, index: number) => ({
          id: contact.id || (index + 1).toString(),
          name: contact.name || "",
          email: contact.email || "",
          phone_number: contact.phone_number || "",
          is_whatsapp: contact.is_whatsapp || false,
          is_primary: contact.is_primary || index === 0,
        }))
      : [
          {
            id: "1",
            name: "",
            email: vendor.email || "",
            phone_number: vendor.phone || "",
            is_whatsapp: false,
            is_primary: true,
          },
        ];

    reset({
      name: vendor.name || "",
      supplier_type:
        (vendor.supplier_type as "company" | "individual") || "company",
      phone: vendor.phone || "",
      email: vendor.email || "",
      website: vendor.website || "",
      handle: vendor.handle || "",
      status: (vendor.status as "active" | "inactive") || "active",
      business_type: vendor.business_type || "",
      region: vendor.region || "",
      timezone: vendor.timezone || "",
      language_preference: Array.isArray(vendor.language_preference)
        ? vendor.language_preference
        : [],
      payment_method: vendor.payment_method || "",
      payout_terms: vendor.payout_terms || "",
      tax_id: vendor.tax_id || "",
      default_currency: vendor.default_currency || "CHF",
      bank_account_details: vendor.bank_account_details || "",
      categories: Array.isArray(vendor.categories) ? vendor.categories : [],
      address_line_1: vendor.address_line_1 || "",
      address_line_2: vendor.address_line_2 || "",
      city: vendor.city || "",
      state: vendor.state || "",
      postal_code: vendor.postal_code || "",
      country: vendor.country || "",
      contacts: transformedContacts,
    });
    hasInitialized.current = true;
  }, [vendor, reset]);

  // Handle error state
  useEffect(() => {
    if (error) {
      toast.error("Failed to load supplier details");
      navigate("/supplier-management/suppliers");
    }
  }, [error, navigate]);

  // Contact management functions
  const addContact = () => {
    const newContact: Contact = {
      id: Date.now().toString(),
      name: "",
      email: "",
      phone_number: "",
      is_whatsapp: false,
      is_primary: false,
    };
    setValue("contacts", [...watchedContacts, newContact]);
  };

  const removeContact = (contactId: string) => {
    if (watchedContacts.length <= 1) {
      toast.error("At least one contact is required");
      return;
    }
    const updatedContacts = watchedContacts.filter((c) => c.id !== contactId);
    setValue("contacts", updatedContacts);
  };

  const setPrimaryContact = (contactId: string) => {
    const updatedContacts = watchedContacts.map((contact) => ({
      ...contact,
      is_primary: contact.id === contactId,
    }));
    setValue("contacts", updatedContacts);
  };

  // Form submission
  const onSubmit = async (data: SupplierFormData) => {
    try {
      // Ensure only one primary contact
      const primaryContacts = data.contacts.filter((c) => c.is_primary);
      if (primaryContacts.length !== 1) {
        toast.error("Exactly one contact must be marked as primary");
        return;
      }

      // Transform contacts data - ensure phone_number is string
      const transformedContacts = data.contacts.map((contact) => ({
        name: contact.name,
        email: contact.email,
        phone_number: contact.phone_number
          ? String(contact.phone_number)
          : undefined,
        is_whatsapp: contact.is_whatsapp,
        is_primary: contact.is_primary,
      }));

      // Transform data for API - use new format with contacts array
      const supplierData = {
        id: id!,
        // Basic Info
        name: data.name,
        supplier_type: data.supplier_type,
        phone: data.phone,
        email: data.email,
        website: data.website,
        handle: data.handle,
        status: data.status,
        business_type: data.business_type,

        // Business Info
        region: data.region,
        timezone: data.timezone,
        language_preference: data.language_preference,
        payment_method: data.payment_method,
        payout_terms: data.payout_terms,
        tax_id: data.tax_id,
        default_currency: data.default_currency,
        bank_account_details: data.bank_account_details
          ? String(data.bank_account_details)
          : undefined,
        categories: data.categories,

        // Address
        address_line_1: data.address_line_1,
        address_line_2: data.address_line_2,
        city: data.city,
        state: data.state,
        postal_code: data.postal_code,
        country: data.country,

        // Contacts with proper string conversion
        contacts: transformedContacts,
      };

      await updateSupplier.mutateAsync(supplierData);
      navigate(`/supplier-management/suppliers/${id}`);
    } catch (error) {
      console.error("Error updating supplier:", error);
    }
  };

  // Generate categories from selected business type
  const categories = React.useMemo(() => {
    if (!businessTypesData?.lookups || !watchedBusinessType) return [];

    // Find the selected business type
    const selectedBusinessType = businessTypesData.lookups.find(
      (businessType) => businessType.parsed_value?.name === watchedBusinessType
    );

    if (!selectedBusinessType?.parsed_value?.description) return [];

    // Get categories from the selected business type's description
    const serviceCategories = selectedBusinessType.parsed_value.description
      .split(",")
      .map((cat) => cat.trim())
      .filter((cat) => cat.length > 0);

    return serviceCategories.sort().map((category) => ({
      value: category.toLowerCase().replace(/[^a-z0-9]/g, "_"),
      label: category,
    }));
  }, [businessTypesData, watchedBusinessType]);

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-center py-8">
            <Text>Loading supplier details...</Text>
          </div>
        </Container>
      </>
    );
  }

  if (!vendor) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-center py-8">
            <Text>Supplier not found</Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />

      <Container>
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate(`/supplier-management/suppliers/${id}`)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <Heading level="h1">Edit Supplier</Heading>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          {/* Basic Information Section */}
          <div className="bg-white rounded-lg border p-6">
            <Heading level="h2" className="mb-4">
              Basic Information
            </Heading>

            <div className="space-y-4">
              {/* Supplier Type */}
              <div>
                <Label>Supplier Type *</Label>
                <Controller
                  name="supplier_type"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select supplier type" />
                      </Select.Trigger>
                      <Select.Content>
                        {SUPPLIER_TYPES.map((type) => (
                          <Select.Item key={type.value} value={type.value}>
                            {type.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.supplier_type && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.supplier_type.message}
                  </Text>
                )}
              </div>

              {/* Basic Fields Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="name"
                        placeholder="Enter supplier name"
                        className={errors.name ? "border-red-500" : ""}
                      />
                    )}
                  />
                  {errors.name && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.name.message}
                    </Text>
                  )}
                </div>

                <div>
                  <Label htmlFor="handle">Handle</Label>
                  <Controller
                    name="handle"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="handle"
                        placeholder="Auto-generated from name"
                        readOnly
                        className="bg-gray-50"
                      />
                    )}
                  />
                </div>

                <div>
                  <Label htmlFor="email">Email *</Label>
                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className={errors.email ? "border-red-500" : ""}
                      />
                    )}
                  />
                  {errors.email && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.email.message}
                    </Text>
                  )}
                </div>

                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Controller
                    name="phone"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="phone"
                        placeholder="+****************"
                      />
                    )}
                  />
                </div>

                <div>
                  <Label htmlFor="website">Website</Label>
                  <Controller
                    name="website"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="website"
                        type="url"
                        placeholder="https://www.example.com"
                        className={errors.website ? "border-red-500" : ""}
                      />
                    )}
                  />
                  {errors.website && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.website.message}
                    </Text>
                  )}
                </div>

                <div>
                  <Label>Status</Label>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <Select.Trigger>
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {SUPPLIER_STATUSES.map((status) => (
                            <Select.Item
                              key={status.value}
                              value={status.value}
                            >
                              {status.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Business Information Section */}
          <div className="bg-white rounded-lg border p-6">
            <Heading level="h2" className="mb-4">
              Business Information
            </Heading>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Business Type *</Label>
                <Controller
                  name="business_type"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={businessTypesLoading}
                    >
                      <Select.Trigger>
                        <Select.Value
                          placeholder={
                            businessTypesLoading
                              ? "Loading..."
                              : "Select business type"
                          }
                        />
                      </Select.Trigger>
                      <Select.Content>
                        {businessTypesData?.lookups
                          ?.filter(
                            (businessType) =>
                              businessType.parsed_value?.is_active
                          )
                          ?.map((businessType) => (
                            <Select.Item
                              key={businessType.id}
                              value={
                                businessType.parsed_value?.name ||
                                businessType.value
                              }
                            >
                              {businessType.parsed_value?.name ||
                                businessType.value}
                            </Select.Item>
                          ))}
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.business_type && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.business_type.message}
                  </Text>
                )}
              </div>

              <div>
                <Label>Region</Label>
                <Controller
                  name="region"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select region" />
                      </Select.Trigger>
                      <Select.Content>
                        {REGIONS.map((region) => (
                          <Select.Item key={region.value} value={region.value}>
                            {region.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label>Timezone</Label>
                <Controller
                  name="timezone"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select timezone" />
                      </Select.Trigger>
                      <Select.Content>
                        {TIMEZONES.map((timezone) => (
                          <Select.Item
                            key={timezone.value}
                            value={timezone.value}
                          >
                            {timezone.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label>Language Preference</Label>
                <Controller
                  name="language_preference"
                  control={control}
                  render={({ field }) => (
                    <MultiSelect
                      options={LANGUAGES}
                      selectedValues={field.value || []}
                      onChange={field.onChange}
                      placeholder="Select languages"
                    />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="payment_method">Payment Method</Label>
                <Controller
                  name="payment_method"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select payment method" />
                      </Select.Trigger>
                      <Select.Content>
                        {PAYMENT_METHODS.map((method) => (
                          <Select.Item key={method.value} value={method.value}>
                            {method.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label>Payment Terms</Label>
                <Controller
                  name="payout_terms"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select payment terms" />
                      </Select.Trigger>
                      <Select.Content>
                        {PAYOUT_TERMS.map((term) => (
                          <Select.Item key={term.value} value={term.value}>
                            {term.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label htmlFor="tax_id">Tax ID</Label>
                <Controller
                  name="tax_id"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="tax_id"
                      placeholder="Enter tax identification number"
                    />
                  )}
                />
              </div>

              <div>
                <Label>Default Currency</Label>
                <Controller
                  name="default_currency"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value />
                      </Select.Trigger>
                      <Select.Content>
                        {CURRENCIES.map((currency) => (
                          <Select.Item
                            key={currency.value}
                            value={currency.value}
                          >
                            {currency.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>
            </div>

            <div className="mt-4">
              <Label htmlFor="bank_account_details">Bank Account Details</Label>
              <Controller
                name="bank_account_details"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    id="bank_account_details"
                    placeholder="Enter bank account information"
                    rows={3}
                  />
                )}
              />
            </div>

            <div className="mt-4">
              <Label>Categories</Label>
              <Controller
                name="categories"
                control={control}
                render={({ field }) => (
                  <MultiSelect
                    options={categories}
                    selectedValues={field.value || []}
                    onChange={field.onChange}
                    placeholder="Select categories"
                  />
                )}
              />
            </div>
          </div>

          {/* Address Section */}
          <div className="bg-white rounded-lg border p-6">
            <Heading level="h2" className="mb-4">
              Address
            </Heading>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="address_line_1">Address Line 1</Label>
                <Controller
                  name="address_line_1"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="address_line_1"
                      placeholder="Street address"
                    />
                  )}
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="address_line_2">Address Line 2</Label>
                <Controller
                  name="address_line_2"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="address_line_2"
                      placeholder="Apartment, suite, etc. (optional)"
                    />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="city">City</Label>
                <Controller
                  name="city"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} id="city" placeholder="City" />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="state">State/Province</Label>
                <Controller
                  name="state"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="state"
                      placeholder="State or Province"
                    />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="postal_code">Postal Code</Label>
                <Controller
                  name="postal_code"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="postal_code"
                      placeholder="Postal/ZIP code"
                    />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="country">Country</Label>
                <Controller
                  name="country"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} id="country" placeholder="Country" />
                  )}
                />
              </div>
            </div>
          </div>

          {/* Contacts Section */}
          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center justify-between mb-4">
              <Heading level="h2">Contacts</Heading>
              <Button
                type="button"
                variant="secondary"
                size="small"
                onClick={addContact}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Contact
              </Button>
            </div>

            <div className="space-y-4">
              {watchedContacts.map((contact, index) => (
                <div key={contact.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <Text weight="plus">Contact {index + 1}</Text>
                    <div className="flex items-center gap-2">
                      <Controller
                        name={`contacts.${index}.is_primary`}
                        control={control}
                        render={({ field }) => (
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              name="primary_contact"
                              checked={field.value}
                              onChange={() => setPrimaryContact(contact.id)}
                              className="text-blue-600"
                            />
                            <Text size="small">Primary</Text>
                          </label>
                        )}
                      />
                      {watchedContacts.length > 1 && (
                        <Button
                          type="button"
                          variant="danger"
                          size="small"
                          onClick={() => removeContact(contact.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`contact_name_${contact.id}`}>
                        Name *
                      </Label>
                      <Controller
                        name={`contacts.${index}.name`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_name_${contact.id}`}
                            placeholder="Contact name"
                            className={
                              errors.contacts?.[index]?.name
                                ? "border-red-500"
                                : ""
                            }
                          />
                        )}
                      />
                      {errors.contacts?.[index]?.name && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.contacts[index].name?.message}
                        </Text>
                      )}
                    </div>

                    <div>
                      <Label htmlFor={`contact_email_${contact.id}`}>
                        Email *
                      </Label>
                      <Controller
                        name={`contacts.${index}.email`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_email_${contact.id}`}
                            type="email"
                            placeholder="<EMAIL>"
                            className={
                              errors.contacts?.[index]?.email
                                ? "border-red-500"
                                : ""
                            }
                          />
                        )}
                      />
                      {errors.contacts?.[index]?.email && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.contacts[index].email?.message}
                        </Text>
                      )}
                    </div>
                  </div>

                  {/* Phone Number and WhatsApp */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <Label htmlFor={`contact_phone_${contact.id}`}>
                        Phone Number
                      </Label>
                      <Controller
                        name={`contacts.${index}.phone_number`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_phone_${contact.id}`}
                            type="tel"
                            placeholder="+****************"
                          />
                        )}
                      />
                    </div>

                    <div className="flex items-center space-x-3 mt-6">
                      <Controller
                        name={`contacts.${index}.is_whatsapp`}
                        control={control}
                        render={({ field }) => (
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        )}
                      />
                      <Label htmlFor={`contact_whatsapp_${contact.id}`}>
                        WhatsApp Available
                      </Label>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Documents Section - now safely inside the form */}
          <div className="bg-white rounded-lg border p-6 mt-8">
            <Heading level="h2" className="mb-4">
              Documents
            </Heading>
            <DocumentUpload supplierId={id!} />
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => navigate(`/supplier-management/suppliers/${id}`)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              {isSubmitting ? "Updating..." : "Update Supplier"}
            </Button>
          </div>
        </form>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Supplier",
});

export default EditSupplierPage;
