import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Buildings,
  ArrowLeft,
  PencilSquare,
  Trash,
} from "@camped-ai/icons";
import { Edit, Phone, Mail, MapPin, Globe, Building, Calendar, Star } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  Tabs,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useVendor } from "../../../../hooks/vendor-management";
import VendorAnalyticsDashboard from "../../../../components/vendor-management/vendor-analytics-dashboard";
import DocumentUpload from "../../../../components/vendor-management/document-upload";

interface Vendor {
  id: string;
  name: string;
  handle: string;
  description?: string;
  business_type: string;
  status: string;
  verification_status: string;
  primary_contact_name: string;
  primary_contact_email: string;
  primary_contact_phone?: string;
  secondary_contact_name?: string;
  secondary_contact_email?: string;
  secondary_contact_phone?: string;
  business_registration_number?: string;
  tax_id?: string;
  website?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  rating?: number;
  total_orders: number;
  completed_orders: number;
  created_at: string;
  updated_at: string;
  products?: any[];
  services?: any[];
  orders?: any[];
  contracts?: any[];
}

const VendorDetailPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState("overview");

  // Use React Query hook for data fetching
  const { data: vendorData, isLoading, error } = useVendor(id!);
  const vendor = vendorData?.vendor;

  // Handle error state with useEffect to avoid setState during render
  useEffect(() => {
    if (error) {
      toast.error("Failed to load supplier details");
      navigate("/supplier-management/suppliers");
    }
  }, [error, navigate]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: "green" as const, label: "Active" },
      inactive: { color: "red" as const, label: "Inactive" },
      pending: { color: "orange" as const, label: "Pending" },
      suspended: { color: "red" as const, label: "Suspended" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: "grey" as const,
      label: status,
    };

    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const getVerificationBadge = (status: string) => {
    const statusConfig = {
      verified: { color: "green" as const, label: "Verified" },
      unverified: { color: "grey" as const, label: "Unverified" },
      in_review: { color: "orange" as const, label: "In Review" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: "grey" as const,
      label: status,
    };

    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const getBusinessTypeLabel = (type: string) => {
    const typeLabels = {
      housekeeping: "Housekeeping",
      maintenance: "Maintenance",
      catering: "Catering",
      transportation: "Transportation",
      laundry: "Laundry",
      security: "Security",
      landscaping: "Landscaping",
      supplies: "Supplies",
      other: "Other",
    };

    return typeLabels[type as keyof typeof typeLabels] || type;
  };

  const handleEdit = () => {
    navigate(`/supplier-management/suppliers/${id}/edit`);
  };

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this vendor?")) {
      return;
    }

    try {
      const response = await fetch("/admin/vendor_management/vendors", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids: id }),
      });

      if (!response.ok) {
        throw new Error("Failed to delete vendor");
      }

      toast.success("Vendor deleted successfully");
      navigate("/supplier-management/suppliers");
    } catch (error) {
      console.error("Error deleting vendor:", error);
      toast.error("Failed to delete vendor");
    }
  };

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-center py-8">
            <Text>Loading supplier details...</Text>
          </div>
        </Container>
      </>
    );
  }

  if (!vendor) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-center py-8">
            <Text>Vendor not found</Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-4">
            <Button
              variant="transparent"
              onClick={() => navigate("/supplier-management/suppliers")}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <div className="flex items-center gap-x-3">
                <Heading level="h2">{vendor.name}</Heading>
                {getStatusBadge(vendor.status)}
                {getVerificationBadge(vendor.verification_status)}
              </div>
              <Text className="text-ui-fg-subtle">
                {vendor.handle} • {getBusinessTypeLabel(vendor.business_type)}
              </Text>
            </div>
          </div>
          <div className="flex items-center gap-x-2">
            <Button
              variant="secondary"
              size="small"
              onClick={handleEdit}
            >
              <PencilSquare className="h-4 w-4" />
              Edit
            </Button>
            <Button
              variant="secondary"
              size="small"
              onClick={handleDelete}
            >
              <Trash className="h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Trigger value="overview">Overview</Tabs.Trigger>
              <Tabs.Trigger value="analytics">Analytics</Tabs.Trigger>
              <Tabs.Trigger value="products">Products</Tabs.Trigger>
              <Tabs.Trigger value="services">Services</Tabs.Trigger>
              <Tabs.Trigger value="orders">Orders</Tabs.Trigger>
              <Tabs.Trigger value="documents">Documents</Tabs.Trigger>
              <Tabs.Trigger value="contracts">Contracts</Tabs.Trigger>
            </Tabs.List>

            <Tabs.Content value="overview" className="space-y-6">
              {/* Basic Information */}
              <Container className="p-6">
                <Heading level="h3" className="mb-4">Basic Information</Heading>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Name</Text>
                    <Text>{vendor.name}</Text>
                  </div>
                  <div>
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Handle</Text>
                    <Text>{vendor.handle}</Text>
                  </div>
                  <div className="md:col-span-2">
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Description</Text>
                    <Text>{vendor.description || "No description provided"}</Text>
                  </div>
                  <div>
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Business Type</Text>
                    <Text>{getBusinessTypeLabel(vendor.business_type)}</Text>
                  </div>
                  <div>
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Created</Text>
                    <Text>{new Date(vendor.created_at).toLocaleDateString()}</Text>
                  </div>
                </div>
              </Container>

              {/* Contact Information */}
              <Container className="p-6">
                <Heading level="h3" className="mb-4">Contact Information</Heading>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Primary Contact</Text>
                    <div className="space-y-2">
                      <Text>{vendor.primary_contact_name}</Text>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-ui-fg-muted" />
                        <Text>{vendor.primary_contact_email}</Text>
                      </div>
                      {vendor.primary_contact_phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-ui-fg-muted" />
                          <Text>{vendor.primary_contact_phone}</Text>
                        </div>
                      )}
                    </div>
                  </div>
                  {(vendor.secondary_contact_name || vendor.secondary_contact_email) && (
                    <div>
                      <Text size="small" weight="plus" className="text-ui-fg-subtle">Secondary Contact</Text>
                      <div className="space-y-2">
                        {vendor.secondary_contact_name && <Text>{vendor.secondary_contact_name}</Text>}
                        {vendor.secondary_contact_email && (
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-ui-fg-muted" />
                            <Text>{vendor.secondary_contact_email}</Text>
                          </div>
                        )}
                        {vendor.secondary_contact_phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-ui-fg-muted" />
                            <Text>{vendor.secondary_contact_phone}</Text>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </Container>

              {/* Business Details */}
              <Container className="p-6">
                <Heading level="h3" className="mb-4">Business Details</Heading>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {vendor.business_registration_number && (
                    <div>
                      <Text size="small" weight="plus" className="text-ui-fg-subtle">Registration Number</Text>
                      <Text>{vendor.business_registration_number}</Text>
                    </div>
                  )}
                  {vendor.tax_id && (
                    <div>
                      <Text size="small" weight="plus" className="text-ui-fg-subtle">Tax ID</Text>
                      <Text>{vendor.tax_id}</Text>
                    </div>
                  )}
                  {vendor.website && (
                    <div>
                      <Text size="small" weight="plus" className="text-ui-fg-subtle">Website</Text>
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-ui-fg-muted" />
                        <a
                          href={vendor.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {vendor.website}
                        </a>
                      </div>
                    </div>
                  )}
                </div>
              </Container>

              {/* Address Information */}
              <Container className="p-6">
                <Heading level="h3" className="mb-4">Address Information</Heading>
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-ui-fg-muted mt-1" />
                  <div>
                    <Text>{vendor.address_line_1}</Text>
                    {vendor.address_line_2 && <Text>{vendor.address_line_2}</Text>}
                    <Text>{vendor.city}, {vendor.state} {vendor.postal_code}</Text>
                    <Text>{vendor.country}</Text>
                  </div>
                </div>
              </Container>

              {/* Performance Metrics */}
              <Container className="p-6">
                <Heading level="h3" className="mb-4">Performance Metrics</Heading>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Total Orders</Text>
                    <Text size="large" weight="plus">{vendor.total_orders}</Text>
                  </div>
                  <div>
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Completed Orders</Text>
                    <Text size="large" weight="plus">{vendor.completed_orders}</Text>
                  </div>
                  <div>
                    <Text size="small" weight="plus" className="text-ui-fg-subtle">Success Rate</Text>
                    <Text size="large" weight="plus">
                      {vendor.total_orders > 0
                        ? `${Math.round((vendor.completed_orders / vendor.total_orders) * 100)}%`
                        : "N/A"
                      }
                    </Text>
                  </div>
                  {vendor.rating && (
                    <div>
                      <Text size="small" weight="plus" className="text-ui-fg-subtle">Rating</Text>
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        <Text size="large" weight="plus">{vendor.rating.toFixed(1)}/5</Text>
                      </div>
                    </div>
                  )}
                </div>
              </Container>
            </Tabs.Content>

            <Tabs.Content value="analytics">
              <VendorAnalyticsDashboard vendorId={vendor.id} />
            </Tabs.Content>

            <Tabs.Content value="products">
              <Container className="p-6">
                <div className="text-center py-8">
                  <Building className="h-12 w-12 text-ui-fg-muted mx-auto mb-4" />
                  <Heading level="h3" className="mb-2">Products</Heading>
                  <Text className="text-ui-fg-subtle mb-4">
                    Manage vendor products and inventory
                  </Text>
                  <Button variant="secondary">
                    Add Product
                  </Button>
                </div>
              </Container>
            </Tabs.Content>

            <Tabs.Content value="services">
              <Container className="p-6">
                <Text>Services functionality coming soon</Text>
              </Container>
            </Tabs.Content>

            <Tabs.Content value="orders">
              <Container className="p-6">
                <Text>Orders functionality coming soon</Text>
              </Container>
            </Tabs.Content>

            <Tabs.Content value="documents">
              <Container className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <Heading level="h3">Documents</Heading>
                    <Text className="text-ui-fg-subtle">
                      Manage vendor documents and certifications
                    </Text>
                  </div>
                  <DocumentUpload vendorId={vendor.id} />
                </div>
                <div className="text-center py-8">
                  <Text className="text-ui-fg-subtle">
                    Document management functionality is ready. Upload documents using the button above.
                  </Text>
                </div>
              </Container>
            </Tabs.Content>

            <Tabs.Content value="contracts">
              <Container className="p-6">
                <Text>Contracts functionality coming soon</Text>
              </Container>
            </Tabs.Content>
          </Tabs>
        </div>
      </Container>
      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Details",
});

export default VendorDetailPage;
