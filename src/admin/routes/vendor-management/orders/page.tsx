import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  ShoppingCart,
  PlusMini,
  MagnifyingGlass,
} from "@camped-ai/icons";
import { Eye, Package, Calendar, DollarSign } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";

interface VendorOrder {
  id: string;
  order_number: string;
  vendor_name: string;
  vendor_id: string;
  order_type: string;
  status: string;
  total_amount: number;
  currency: string;
  order_date: string;
  delivery_date: string;
  items_count: number;
}

const VendorOrdersPage = () => {
  const [orders, setOrders] = useState<VendorOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call to vendor management module
      // const response = await fetch("/admin/vendor-management/orders");
      // const data = await response.json();
      
      // Mock data for demonstration
      const mockOrders: VendorOrder[] = [
        {
          id: "vorder_01HJQK1JMQZX8TGQR5VWXYZ123",
          order_number: "VO-2024-001",
          vendor_name: "Premium Catering Co.",
          vendor_id: "vendor_01HJQK1JMQZX8TGQR5VWXYZ123",
          order_type: "mixed",
          status: "confirmed",
          total_amount: 2500.00,
          currency: "USD",
          order_date: "2024-03-15T10:30:00Z",
          delivery_date: "2024-03-20T14:00:00Z",
          items_count: 5,
        },
        {
          id: "vorder_01HJQK1JMQZX8TGQR5VWXYZ456",
          order_number: "VO-2024-002",
          vendor_name: "Swift Transportation LLC",
          vendor_id: "vendor_01HJQK1JMQZX8TGQR5VWXYZ456",
          order_type: "service",
          status: "pending",
          total_amount: 800.00,
          currency: "USD",
          order_date: "2024-03-16T09:15:00Z",
          delivery_date: "2024-03-18T08:00:00Z",
          items_count: 2,
        },
        {
          id: "vorder_01HJQK1JMQZX8TGQR5VWXYZ789",
          order_number: "VO-2024-003",
          vendor_name: "Premium Catering Co.",
          vendor_id: "vendor_01HJQK1JMQZX8TGQR5VWXYZ123",
          order_type: "product",
          status: "delivered",
          total_amount: 1200.00,
          currency: "USD",
          order_date: "2024-03-10T16:45:00Z",
          delivery_date: "2024-03-12T12:00:00Z",
          items_count: 3,
        },
      ];
      
      setOrders(mockOrders);
    } catch (error) {
      console.error("Error fetching vendor orders:", error);
      toast.error("Failed to load vendor orders");
    } finally {
      setLoading(false);
    }
  };

  const filteredOrders = orders.filter(order =>
    order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.vendor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "orange" as const, label: "Pending" },
      confirmed: { color: "blue" as const, label: "Confirmed" },
      in_progress: { color: "purple" as const, label: "In Progress" },
      delivered: { color: "green" as const, label: "Delivered" },
      cancelled: { color: "red" as const, label: "Cancelled" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const getOrderTypeBadge = (type: string) => {
    const typeConfig = {
      product: { color: "blue" as const, label: "Product" },
      service: { color: "green" as const, label: "Service" },
      mixed: { color: "purple" as const, label: "Mixed" },
    };
    
    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.mixed;
    return <Badge color={config.color} size="small">{config.label}</Badge>;
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Supplier Orders</Heading>
            <Text className="text-ui-fg-subtle">
              Track and manage orders placed with suppliers
            </Text>
          </div>
          <div className="flex items-center gap-x-2">
            <Button
              size="small"
              onClick={() => {
                toast.info("Create supplier order form coming soon");
              }}
            >
              <PlusMini />
              Create Order
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-2">
            <div className="relative">
              <MagnifyingGlass className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-ui-fg-muted" />
              <Input
                placeholder="Search by order number, vendor, or status..."
                className="pl-8 w-80"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center gap-x-4">
            <Text className="text-ui-fg-subtle text-sm">
              {filteredOrders.length} order{filteredOrders.length !== 1 ? 's' : ''}
            </Text>
          </div>
        </div>

        <div className="px-6">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Order Details</Table.HeaderCell>
                <Table.HeaderCell>Vendor</Table.HeaderCell>
                <Table.HeaderCell>Type & Items</Table.HeaderCell>
                <Table.HeaderCell>Dates</Table.HeaderCell>
                <Table.HeaderCell>Amount</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell className="w-8"></Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {loading ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center py-8">
                    Loading vendor orders...
                  </Table.Cell>
                </Table.Row>
              ) : filteredOrders.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center py-8">
                    {searchTerm ? "No orders found matching your search" : "No vendor orders found"}
                  </Table.Cell>
                </Table.Row>
              ) : (
                filteredOrders.map((order) => (
                  <Table.Row
                    key={order.id}
                    className="cursor-pointer hover:bg-ui-bg-subtle"
                    onClick={() => {
                      toast.info(`Opening order: ${order.order_number}`);
                    }}
                  >
                    <Table.Cell>
                      <div>
                        <Text weight="plus" size="small">
                          {order.order_number}
                        </Text>
                        <Text className="text-ui-fg-subtle" size="small">
                          {order.id}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div>
                        <Text size="small">{order.vendor_name}</Text>
                        <Text className="text-ui-fg-subtle" size="small">
                          {order.vendor_id}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="space-y-1">
                        {getOrderTypeBadge(order.order_type)}
                        <div className="flex items-center gap-1">
                          <Package className="h-3 w-3 text-ui-fg-muted" />
                          <Text className="text-ui-fg-subtle" size="small">
                            {order.items_count} items
                          </Text>
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 text-ui-fg-muted" />
                          <Text size="small">
                            Ordered: {new Date(order.order_date).toLocaleDateString()}
                          </Text>
                        </div>
                        <Text className="text-ui-fg-subtle" size="small">
                          Delivery: {new Date(order.delivery_date).toLocaleDateString()}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3 text-ui-fg-muted" />
                        <Text weight="plus" size="small">
                          {formatCurrency(order.total_amount, order.currency)}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {getStatusBadge(order.status)}
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        variant="transparent"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          toast.info(`View order details: ${order.order_number}`);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>
      <Toaster />
    </>
  );
};

// Disabled - replaced by new supplier-management routes
// export const config = defineRouteConfig({
//   label: "Vendor Orders",
// });

export default VendorOrdersPage;
