import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Buildings,
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import { Edit, Upload as UploadIcon, Download } from "lucide-react";
import OutlineButton from "../../components/shared/OutlineButton";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  FocusModal,
  Toaster,
  toast,
  Drawer,
  Table,
  Badge,
} from "@camped-ai/ui";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import { useVendors } from "../../hooks/vendor-management";

interface Vendor {
  id: string;
  name: string;
  handle: string;
  description?: string;
  business_type: string;
  status: string;
  verification_status: string;
  primary_contact_name: string;
  primary_contact_email: string;
  primary_contact_phone?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  rating?: number;
  total_orders: number;
  completed_orders: number;
  created_at: string;
  updated_at: string;
  products?: any[];
  services?: any[];
  orders?: any[];
}

const VendorManagementPage = () => {
  const [testData, setTestData] = React.useState(null);
  const [testLoading, setTestLoading] = React.useState(true);
  const [testError, setTestError] = React.useState(null);

  // Direct API test without React Query
  React.useEffect(() => {
    const fetchData = async () => {
      try {
        setTestLoading(true);
        const response = await fetch('/admin/vendor_management/vendors?limit=20');
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Direct fetch result:', data);
        setTestData(data);
        setTestError(null);
      } catch (err) {
        console.error('Direct fetch error:', err);
        setTestError(err.message);
      } finally {
        setTestLoading(false);
      }
    };

    fetchData();
  }, []);

  // Use React Query hook for data fetching
  console.log("Component Debug - About to call useVendors hook");
  const { data: vendorsData, isLoading, error } = useVendors({
    limit: 20,
  });
  console.log("Component Debug - useVendors result:", { vendorsData, isLoading, error });

  // For now, use the direct fetch data since React Query is having issues
  const actualVendorsData = vendorsData || testData;
  const actualIsLoading = isLoading && testLoading;
  const actualError = error || testError;

  const vendors = vendorsData?.vendors || [];
  const testVendors = testData?.vendors || [];

  // Show loading state
  if (actualIsLoading) {
    return (
      <Container className="divide-y p-0">
        <div className="px-6 py-4">
          <Text>Loading vendors...</Text>
        </div>
      </Container>
    );
  }

  return (
    <div style={{ padding: '20px', backgroundColor: 'white', minHeight: '100vh' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '20px' }}>
        🎯 Vendor Management Debug - {new Date().toLocaleTimeString()}
      </h1>

      <div style={{ border: '2px solid #000', padding: '15px', marginBottom: '20px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>✅ Component Status</h2>
        <p>✓ React component is rendering successfully</p>
        <p>✓ JavaScript is executing</p>
        <p>✓ Timestamp updates: {new Date().toLocaleString()}</p>
      </div>

      <div style={{ border: '2px solid #007bff', padding: '15px', marginBottom: '20px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>📡 Direct Fetch Results</h2>
        <p>Loading: {testLoading ? '🔄 Yes' : '✅ No'}</p>
        <p>Error: {testError ? `❌ ${testError}` : '✅ None'}</p>
        <p>Vendors found: <strong>{testVendors.length}</strong></p>
        {testVendors.length > 0 && (
          <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#e7f3ff' }}>
            <p><strong>First vendor:</strong> {testVendors[0].name}</p>
            <p><strong>Business type:</strong> {testVendors[0].business_type}</p>
            <p><strong>Email:</strong> {testVendors[0].primary_contact_email}</p>
          </div>
        )}
      </div>

      <div style={{ border: '2px solid #28a745', padding: '15px', marginBottom: '20px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>⚛️ React Query Results</h2>
        <p>Loading: {isLoading ? '🔄 Yes' : '✅ No'}</p>
        <p>Error: {error ? `❌ ${error.message}` : '✅ None'}</p>
        {error && (
          <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#ffe6e6' }}>
            <p><strong>Error details:</strong></p>
            <p>Message: {error.message}</p>
            <p>Name: {error.name}</p>
            <p>Stack: {error.stack?.substring(0, 200)}...</p>
            <p>Full error: {JSON.stringify(error, null, 2)}</p>
          </div>
        )}
        <p>Data: {vendorsData ? JSON.stringify(Object.keys(vendorsData)) : 'null'}</p>
        <p>Vendors found: <strong>{vendors.length}</strong></p>
        {vendors.length > 0 && (
          <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#e8f5e8' }}>
            <p><strong>First vendor:</strong> {vendors[0].name}</p>
            <p><strong>Business type:</strong> {vendors[0].business_type}</p>
            <p><strong>Email:</strong> {vendors[0].primary_contact_email}</p>
          </div>
        )}
      </div>

      <div style={{ border: '2px solid #ffc107', padding: '15px', marginBottom: '20px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>🔧 Working Solution</h2>
        <p>Using: {actualVendorsData === testData ? 'Direct Fetch' : 'React Query'}</p>
        <p>Loading: {actualIsLoading ? '🔄 Yes' : '✅ No'}</p>
        <p>Error: {actualError ? `❌ ${actualError}` : '✅ None'}</p>
        <p>Vendors found: <strong>{actualVendorsData?.vendors?.length || 0}</strong></p>
        {actualVendorsData?.vendors?.length > 0 && (
          <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#fff9e6' }}>
            <p><strong>First vendor:</strong> {actualVendorsData.vendors[0].name}</p>
            <p><strong>Business type:</strong> {actualVendorsData.vendors[0].business_type}</p>
            <p><strong>Email:</strong> {actualVendorsData.vendors[0].primary_contact_email}</p>
          </div>
        )}
      </div>

      {actualVendorsData?.vendors?.length > 0 && (
        <div style={{ border: '2px solid #17a2b8', padding: '15px' }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>📋 All Vendors ({actualVendorsData.vendors.length})</h2>
          {actualVendorsData.vendors.map((vendor, index) => (
            <div key={vendor.id} style={{
              border: '1px solid #ddd',
              padding: '10px',
              margin: '10px 0',
              backgroundColor: '#fff9e6'
            }}>
              <p><strong>{index + 1}. {vendor.name}</strong></p>
              <p>Type: {vendor.business_type}</p>
              <p>Status: {vendor.status}</p>
              <p>Email: {vendor.primary_contact_email}</p>
              <p>City: {vendor.city}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const VendorManagementPageOriginal = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();
  const [filters, setFilters] = useState({
    status: "",
    business_type: "",
    verification_status: "",
  });

  // Use React Query hook for data fetching
  const { data: vendorsData, isLoading, error, refetch } = useVendors({
    search: searchTerm || undefined,
    status: filters.status || undefined,
    business_type: filters.business_type || undefined,
    verification_status: filters.verification_status || undefined,
    limit: 20,
  });

  // Force a refetch to bypass cache
  React.useEffect(() => {
    refetch();
  }, [refetch]);

  const vendors = vendorsData?.vendors || [];
  const totalCount = vendorsData?.count || 0;

  // Temporary debug logging
  console.log("Component Debug - vendorsData:", vendorsData);
  console.log("Component Debug - vendors array:", vendors);
  console.log("Component Debug - vendors length:", vendors.length);
  console.log("Component Debug - isLoading:", isLoading);
  console.log("Component Debug - error:", error);

  // Temporary hardcoded data for testing
  const testVendors = [
    {
      id: 'test-1',
      name: 'Test Vendor 1',
      business_type: 'transportation',
      status: 'active',
      verification_status: 'verified',
      primary_contact_name: 'John Doe',
      primary_contact_email: '<EMAIL>',
      city: 'Test City',
      created_at: new Date().toISOString(),
    }
  ];

  const filteredVendors = vendors.length > 0 ? vendors : testVendors;

  // Track data changes
  useEffect(() => {
    console.log("useEffect - Data changed:", { vendorsData, vendors, isLoading, error });
  }, [vendorsData, vendors, isLoading, error]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: "green" as const, label: "Active" },
      inactive: { color: "red" as const, label: "Inactive" },
      pending: { color: "orange" as const, label: "Pending" },
      suspended: { color: "red" as const, label: "Suspended" },
    };

    const statusInfo = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge color={statusInfo.color}>{statusInfo.label}</Badge>;
  };

  const getBusinessTypeLabel = (type: string) => {
    const typeLabels = {
      housekeeping: "Housekeeping",
      maintenance: "Maintenance",
      catering: "Catering",
      transportation: "Transportation",
      laundry: "Laundry",
      security: "Security",
      landscaping: "Landscaping",
      supplies: "Supplies",
      other: "Other",
    };

    return typeLabels[type as keyof typeof typeLabels] || type;
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Supplier Management</Heading>
            <Text className="text-ui-fg-subtle">
              Manage your suppliers and their services
            </Text>
          </div>
          <div className="flex items-center gap-x-2">
            <OutlineButton
              variant="secondary"
              size="small"
              onClick={() => {
                // TODO: Implement export functionality
                toast.info("Export functionality coming soon");
              }}
            >
              <UploadIcon className="h-4 w-4" />
              Export
            </OutlineButton>
            <Button
              size="small"
              onClick={() => navigate("/supplier-management/suppliers/create")}
            >
              <PlusMini />
              Add Supplier
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-2">
            <div className="relative">
              <MagnifyingGlass className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-ui-fg-muted" />
              <Input
                placeholder="Search vendors..."
                className="pl-8 w-64"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center gap-x-2">
            <Text className="text-ui-fg-subtle text-sm">
              {filteredVendors.length} vendor{filteredVendors.length !== 1 ? 's' : ''}
            </Text>
          </div>
        </div>

        <div className="px-6">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Vendor</Table.HeaderCell>
                <Table.HeaderCell>Contact</Table.HeaderCell>
                <Table.HeaderCell>Business Type</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell>Location</Table.HeaderCell>
                <Table.HeaderCell>Created</Table.HeaderCell>
                <Table.HeaderCell className="w-8"></Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center py-8">
                    Loading vendors...
                  </Table.Cell>
                </Table.Row>
              ) : !vendors || vendors.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center py-8">
                    {searchTerm ? "No vendors found matching your search" : `No vendors found (Debug: vendors=${JSON.stringify(vendors)}, vendorsData=${JSON.stringify(vendorsData)}, isLoading=${isLoading})`}
                  </Table.Cell>
                </Table.Row>
              ) : (
                vendors.map((vendor) => (
                  <Table.Row
                    key={vendor.id}
                    className="cursor-pointer hover:bg-ui-bg-subtle"
                    onClick={() => navigate(`/vendor-management/vendors/${vendor.id}`)}
                  >
                    <Table.Cell>
                      <div>
                        <Text weight="plus" size="small">
                          {vendor.name}
                        </Text>
                        <Text className="text-ui-fg-subtle" size="small">
                          {vendor.handle}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div>
                        <Text size="small">{vendor.primary_contact_name}</Text>
                        <Text className="text-ui-fg-subtle" size="small">
                          {vendor.primary_contact_email}
                        </Text>
                        {vendor.primary_contact_phone && (
                          <Text className="text-ui-fg-subtle" size="small">
                            {vendor.primary_contact_phone}
                          </Text>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge size="small" color="blue">
                        {getBusinessTypeLabel(vendor.business_type)}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      {getStatusBadge(vendor.status)}
                    </Table.Cell>
                    <Table.Cell>
                      <div>
                        <Text size="small">{vendor.city}, {vendor.state}</Text>
                        <Text className="text-ui-fg-subtle" size="small">
                          {vendor.country}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Text size="small">
                        {new Date(vendor.created_at).toLocaleDateString()}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        variant="transparent"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Show actions menu
                          toast.info("Actions menu coming soon");
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>
      <Toaster />
    </>
  );
};

// Disabled - replaced by new supplier-management routes
// export const config = defineRouteConfig({
//   label: "Supplier Management",
//   icon: Buildings,
// });

export default VendorManagementPage;
