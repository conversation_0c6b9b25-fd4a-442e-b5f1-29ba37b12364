import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  DocumentText,
  PlusMini,
  MagnifyingGlass,
} from "@camped-ai/icons";
import { Eye, FileText, Calendar, DollarSign, AlertCircle } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";

interface VendorContract {
  id: string;
  contract_number: string;
  vendor_name: string;
  vendor_id: string;
  contract_type: string;
  status: string;
  total_value: number;
  currency: string;
  start_date: string;
  end_date: string;
  payment_terms: string;
  auto_renewal: boolean;
  payments_count: number;
}

const VendorContractsPage = () => {
  const [contracts, setContracts] = useState<VendorContract[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    fetchContracts();
  }, []);

  const fetchContracts = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call to vendor management module
      // const response = await fetch("/admin/vendor-management/contracts");
      // const data = await response.json();
      
      // Mock data for demonstration
      const mockContracts: VendorContract[] = [
        {
          id: "vcontract_01HJQK1JMQZX8TGQR5VWXYZ123",
          contract_number: "VC-2024-001",
          vendor_name: "Premium Catering Co.",
          vendor_id: "vendor_01HJQK1JMQZX8TGQR5VWXYZ123",
          contract_type: "service_agreement",
          status: "active",
          total_value: 50000.00,
          currency: "USD",
          start_date: "2024-01-01T00:00:00Z",
          end_date: "2024-12-31T23:59:59Z",
          payment_terms: "monthly",
          auto_renewal: true,
          payments_count: 3,
        },
        {
          id: "vcontract_01HJQK1JMQZX8TGQR5VWXYZ456",
          contract_number: "VC-2024-002",
          vendor_name: "Swift Transportation LLC",
          vendor_id: "vendor_01HJQK1JMQZX8TGQR5VWXYZ456",
          contract_type: "master_agreement",
          status: "pending_signature",
          total_value: 25000.00,
          currency: "USD",
          start_date: "2024-04-01T00:00:00Z",
          end_date: "2025-03-31T23:59:59Z",
          payment_terms: "quarterly",
          auto_renewal: false,
          payments_count: 0,
        },
        {
          id: "vcontract_01HJQK1JMQZX8TGQR5VWXYZ789",
          contract_number: "VC-2023-015",
          vendor_name: "Elite Cleaning Services",
          vendor_id: "vendor_01HJQK1JMQZX8TGQR5VWXYZ789",
          contract_type: "service_agreement",
          status: "expired",
          total_value: 18000.00,
          currency: "USD",
          start_date: "2023-01-01T00:00:00Z",
          end_date: "2023-12-31T23:59:59Z",
          payment_terms: "monthly",
          auto_renewal: false,
          payments_count: 12,
        },
      ];
      
      setContracts(mockContracts);
    } catch (error) {
      console.error("Error fetching supplier contracts:", error);
      toast.error("Failed to load supplier contracts");
    } finally {
      setLoading(false);
    }
  };

  const filteredContracts = contracts.filter(contract =>
    contract.contract_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.vendor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: "grey" as const, label: "Draft" },
      pending_signature: { color: "orange" as const, label: "Pending Signature" },
      active: { color: "green" as const, label: "Active" },
      expired: { color: "red" as const, label: "Expired" },
      terminated: { color: "red" as const, label: "Terminated" },
      suspended: { color: "orange" as const, label: "Suspended" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const getContractTypeBadge = (type: string) => {
    const typeConfig = {
      service_agreement: { color: "blue" as const, label: "Service Agreement" },
      master_agreement: { color: "purple" as const, label: "Master Agreement" },
      purchase_order: { color: "green" as const, label: "Purchase Order" },
      nda: { color: "grey" as const, label: "NDA" },
    };
    
    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.service_agreement;
    return <Badge color={config.color} size="small">{config.label}</Badge>;
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const isExpiringSoon = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Supplier Contracts</Heading>
            <Text className="text-ui-fg-subtle">
              Manage supplier contracts and agreements
            </Text>
          </div>
          <div className="flex items-center gap-x-2">
            <Button
              size="small"
              onClick={() => {
                toast.info("Create contract form coming soon");
              }}
            >
              <PlusMini />
              New Contract
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-2">
            <div className="relative">
              <MagnifyingGlass className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-ui-fg-muted" />
              <Input
                placeholder="Search by contract number, vendor, or status..."
                className="pl-8 w-80"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center gap-x-4">
            <Text className="text-ui-fg-subtle text-sm">
              {filteredContracts.length} contract{filteredContracts.length !== 1 ? 's' : ''}
            </Text>
          </div>
        </div>

        <div className="px-6">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Contract Details</Table.HeaderCell>
                <Table.HeaderCell>Vendor</Table.HeaderCell>
                <Table.HeaderCell>Type & Terms</Table.HeaderCell>
                <Table.HeaderCell>Duration</Table.HeaderCell>
                <Table.HeaderCell>Value</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell className="w-8"></Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {loading ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center py-8">
                    Loading supplier contracts...
                  </Table.Cell>
                </Table.Row>
              ) : filteredContracts.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center py-8">
                    {searchTerm ? "No contracts found matching your search" : "No supplier contracts found"}
                  </Table.Cell>
                </Table.Row>
              ) : (
                filteredContracts.map((contract) => (
                  <Table.Row
                    key={contract.id}
                    className="cursor-pointer hover:bg-ui-bg-subtle"
                    onClick={() => {
                      toast.info(`Opening contract: ${contract.contract_number}`);
                    }}
                  >
                    <Table.Cell>
                      <div>
                        <div className="flex items-center gap-2">
                          <Text weight="plus" size="small">
                            {contract.contract_number}
                          </Text>
                          {isExpiringSoon(contract.end_date) && (
                            <AlertCircle className="h-3 w-3 text-orange-500" />
                          )}
                        </div>
                        <Text className="text-ui-fg-subtle" size="small">
                          {contract.id}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div>
                        <Text size="small">{contract.vendor_name}</Text>
                        <Text className="text-ui-fg-subtle" size="small">
                          {contract.vendor_id}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="space-y-1">
                        {getContractTypeBadge(contract.contract_type)}
                        <div className="flex items-center gap-1">
                          <FileText className="h-3 w-3 text-ui-fg-muted" />
                          <Text className="text-ui-fg-subtle" size="small">
                            {contract.payment_terms} payments
                          </Text>
                        </div>
                        {contract.auto_renewal && (
                          <Text className="text-ui-fg-subtle" size="small">
                            Auto-renewal
                          </Text>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 text-ui-fg-muted" />
                          <Text size="small">
                            {new Date(contract.start_date).toLocaleDateString()}
                          </Text>
                        </div>
                        <Text className="text-ui-fg-subtle" size="small">
                          to {new Date(contract.end_date).toLocaleDateString()}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3 text-ui-fg-muted" />
                          <Text weight="plus" size="small">
                            {formatCurrency(contract.total_value, contract.currency)}
                          </Text>
                        </div>
                        <Text className="text-ui-fg-subtle" size="small">
                          {contract.payments_count} payments made
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {getStatusBadge(contract.status)}
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        variant="transparent"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          toast.info(`View contract details: ${contract.contract_number}`);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>
      <Toaster />
    </>
  );
};

// Disabled - replaced by new supplier-management routes
// export const config = defineRouteConfig({
//   label: "Supplier Contracts",
// });

export default VendorContractsPage;
