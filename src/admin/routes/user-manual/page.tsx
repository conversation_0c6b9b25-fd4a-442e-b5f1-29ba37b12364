import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLongUp, DocumentText } from "@camped-ai/icons";
import { Container, Heading } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";

const CustomPage = () => {
  const USER_MANUAL_URL = import.meta.env.VITE_USER_MANUAL_URL;

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center justify-between w-full">
            <Heading level="h2">User Manual</Heading>
            <div
              className="flex items-center space-x-2 cursor-pointer"
              onClick={() => window.open(USER_MANUAL_URL, "_blank")}
            >
              <p className="text-xs">Open in new tab</p>
              <ArrowLongUp className="rotate-45" />
            </div>
          </div>
        </div>
        <iframe
          src={USER_MANUAL_URL}
          title="description"
          style={{ width: "100%", height: "100vh" }}
        ></iframe>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "User Manual",
  icon: DocumentText,
});

export default CustomPage;
