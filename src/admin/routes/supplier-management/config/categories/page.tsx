import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Tag, PlusMini } from "@camped-ai/icons";
import {
  Edit,
  MoreHorizontal,
  Trash,
  Download,
  Upload,
  FileText,
  FileSpreadsheet,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
  FocusModal,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import {
  useCategories,
  useDeleteCategory,
  useCategoryImportExport,
  type Category,
  type ImportValidationError,
  exportToCSV,
  exportToExcel,
} from "../../../../hooks/supplier-products-services/use-categories";

// Simple Progress Bar Component
const ProgressBarComponent = ({
  value,
  className = "",
}: {
  value: number;
  className?: string;
}) => {
  return (
    <div className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
      <div
        className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
        style={{ width: `${Math.min(100, Math.max(0, value))}%` }}
      />
    </div>
  );
};

const CategoriesConfigPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");

  // Import/Export state
  const [showImportModal, setShowImportModal] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<ImportValidationError[]>([]);
  const [importStep, setImportStep] = useState<
    "upload" | "preview" | "importing" | "complete"
  >("upload");

  // Hooks
  const { data: categoriesData, isLoading } = useCategories({
    name: searchTerm || undefined,
    limit: 50,
  });
  const deleteCategory = useDeleteCategory();

  // Use the import/export hook
  const {
    parseImportFile,
    importCategories,
    isImporting,
    importProgress,
    generateTemplate,
    exportToCSV: exportCSV,
    exportToExcel: exportExcel,
  } = useCategoryImportExport();

  const categories = categoriesData?.categories || [];
  const { hasPermission } = useRbac();

  const handleEdit = (category: Category) => {
    navigate(`/supplier-management/config/categories/${category.id}/edit`);
  };

  const handleDelete = async (category: Category) => {
    if (window.confirm(`Are you sure you want to delete "${category.name}"?`)) {
      try {
        await deleteCategory.mutateAsync(category.id);
        toast.success("Category deleted successfully");
      } catch (error) {
        console.error("Error deleting category:", error);
        toast.error("Failed to delete category. Please try again.");
      }
    }
  };

  // Export functions
  const handleExport = async (format: "csv" | "excel") => {
    try {
      const exportData = categories.map((category) => ({
        name: category.name,
        description: category.description || "",
        category_type: category.category_type,
        icon: category.icon || "",
        is_active: category.is_active,
        dynamic_field_schema: category.dynamic_field_schema
          ? JSON.stringify(category.dynamic_field_schema)
          : "",
      }));

      const filename = `categories_export`;

      if (format === "csv") {
        exportCSV(exportData, filename);
      } else {
        exportExcel(exportData, filename);
      }

      toast.success(
        `Categories exported as ${format.toUpperCase()} successfully`
      );
    } catch (error) {
      console.error("Export error:", error);
      toast.error(`Failed to export as ${format.toUpperCase()}`);
    }
  };

  // Template export function
  const exportCategoryTemplate = (format: "csv" | "excel") => {
    const templateData = generateTemplate();
    const filename = `category_import_template`;

    if (format === "csv") {
      exportCSV(templateData, filename);
      toast.success("Template exported as CSV successfully");
    } else {
      exportExcel(templateData, filename);
      toast.success("Template exported as Excel (.xlsx) successfully");
    }
  };

  // Import functions
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setImportFile(file);
    setImportStep("preview");

    try {
      const { data, errors } = await parseImportFile(file);
      setImportData(data);
      setImportErrors(errors);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to parse file"
      );
      setImportStep("upload");
    }
  };

  const handleImport = async () => {
    if (importErrors.length > 0) {
      toast.error("Please fix validation errors before importing");
      return;
    }

    setImportStep("importing");

    try {
      await importCategories(importData);
      setImportStep("complete");
      setTimeout(() => {
        setShowImportModal(false);
        resetImportState();
      }, 2000);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Import failed");
      setImportStep("preview");
    }
  };

  const resetImportState = () => {
    setImportFile(null);
    setImportData([]);
    setImportErrors([]);
    setImportStep("upload");
  };

  const handleCloseImportModal = () => {
    setShowImportModal(false);
    resetImportState();
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Breadcrumb Navigation */}
        <div className="px-6 py-3 bg-gray-50 border-b">
          <div className="flex items-center gap-2 text-sm">
            <button
              onClick={() => navigate("/supplier-management")}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Supplier Management
            </button>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">Configuration</span>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">Categories</span>
          </div>
        </div>

        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between px-6 py-4 gap-4">
          <div>
            <Heading level="h2">Categories Configuration</Heading>
            <Text className="text-ui-fg-subtle">
              Manage product and service categories for supplier offerings
            </Text>
          </div>
          <div className="flex items-center gap-x-2 flex-wrap">
            {hasPermission("supplier_management:view") && (
              <>
                <DropdownMenu>
                  <DropdownMenu.Trigger asChild>
                    <Button variant="secondary" size="small">
                      <Download className="h-4 w-4" />
                      Export
                    </Button>
                  </DropdownMenu.Trigger>
                  <DropdownMenu.Content>
                    <DropdownMenu.Item onClick={() => handleExport("csv")}>
                      Export Data as CSV
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onClick={() => handleExport("excel")}>
                      Export Data as Excel
                    </DropdownMenu.Item>
                    <DropdownMenu.Separator />
                    <DropdownMenu.Item
                      onClick={() => exportCategoryTemplate("csv")}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Export Template (CSV)
                    </DropdownMenu.Item>
                    <DropdownMenu.Item
                      onClick={() => exportCategoryTemplate("excel")}
                    >
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      Export Template (Excel .xlsx)
                    </DropdownMenu.Item>
                  </DropdownMenu.Content>
                </DropdownMenu>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => setShowImportModal(true)}
                >
                  <Upload className="h-4 w-4" />
                  Import Data
                </Button>
              </>
            )}
            {hasPermission("supplier_management:create") && (
              <Button
                size="small"
                onClick={() =>
                  navigate("/supplier-management/config/categories/create")
                }
              >
                <PlusMini />
                Add Category
              </Button>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="px-6 py-4">
          <Input
            placeholder="Search categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Name</Table.HeaderCell>
                <Table.HeaderCell>Type</Table.HeaderCell>
                <Table.HeaderCell>Description</Table.HeaderCell>
                <Table.HeaderCell>Dynamic Fields</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell>Actions</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <Table.Row key={`skeleton-${index}`}>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-32" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-8" />
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : categories.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={6} className="text-center py-8">
                    <Text className="text-ui-fg-subtle">
                      {searchTerm
                        ? "No categories found matching your search"
                        : "No categories found"}
                    </Text>
                  </Table.Cell>
                </Table.Row>
              ) : (
                categories.map((category) => (
                  <Table.Row key={category.id}>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        {(category as any).icon && (
                          <span className="text-lg">
                            {(category as any).icon}
                          </span>
                        )}
                        <Text weight="plus">{category.name}</Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge variant="secondary" size="small">
                        {(category as any).category_type || "Both"}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="text-ui-fg-subtle">
                        {category.description || "—"}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        {(category as any).dynamic_field_schema?.length > 0 ? (
                          <>
                            <Badge variant="default" size="small">
                              {(category as any).dynamic_field_schema.length}{" "}
                              fields
                            </Badge>
                            <Text size="small" className="text-ui-fg-subtle">
                              (
                              {
                                (category as any).dynamic_field_schema.filter(
                                  (f: any) => f.required
                                ).length
                              }{" "}
                              required)
                            </Text>
                          </>
                        ) : (
                          <Text size="small" className="text-ui-fg-subtle">
                            No custom fields
                          </Text>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge variant={category.is_active ? "green" : "grey"}>
                        {category.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <DropdownMenu>
                        <DropdownMenu.Trigger asChild>
                          <Button variant="transparent" size="small">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Item
                            onClick={() => handleEdit(category)}
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </DropdownMenu.Item>
                          <DropdownMenu.Item
                            onClick={() => handleDelete(category)}
                            className="text-red-600"
                          >
                            <Trash className="h-4 w-4" />
                            Delete
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>

      {/* Import Modal */}
      <FocusModal open={showImportModal} onOpenChange={handleCloseImportModal}>
        <FocusModal.Content className="max-w-4xl max-h-[90vh]">
          <FocusModal.Header>
            <div className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              <Heading level="h2">Import Categories</Heading>
            </div>
          </FocusModal.Header>

          <FocusModal.Body className="overflow-y-auto space-y-6">
            {importStep === "upload" && (
              <div className="space-y-4">
                <div>
                  <Text className="text-ui-fg-subtle mb-4">
                    Upload a CSV or Excel file containing category data. Make
                    sure your file follows the template format.
                  </Text>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                    />
                    <label htmlFor="file-upload" className="cursor-pointer">
                      <div className="space-y-2">
                        <Upload className="h-8 w-8 mx-auto text-gray-400" />
                        <Text>Click to upload or drag and drop</Text>
                        <Text className="text-sm text-ui-fg-subtle">
                          CSV, Excel (.xlsx, .xls)
                        </Text>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <Text className="font-medium text-blue-900 mb-2">
                    💡 Tips for successful import:
                  </Text>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>
                      • Download the template first to see the required format
                    </li>
                    <li>• Category names must be unique</li>
                    <li>• Dynamic field schema should be valid JSON</li>
                    <li>
                      • Use "Product", "Service", or "Both" for category_type
                    </li>
                  </ul>
                </div>
              </div>
            )}

            {importStep === "preview" && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Heading level="h3">Preview Import Data</Heading>
                  <Text className="text-sm text-ui-fg-subtle">
                    {importData.length} categories found
                  </Text>
                </div>

                {importErrors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <Text className="font-medium text-red-900 mb-2">
                      ⚠️ Validation Errors ({importErrors.length})
                    </Text>
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {importErrors.map((error, index) => (
                        <Text key={index} className="text-sm text-red-800">
                          Row {error.row}: {error.field} - {error.message}
                        </Text>
                      ))}
                    </div>
                  </div>
                )}

                <div className="border rounded-lg overflow-hidden">
                  <div className="max-h-64 overflow-y-auto">
                    <Table>
                      <Table.Header>
                        <Table.Row>
                          <Table.HeaderCell>Name</Table.HeaderCell>
                          <Table.HeaderCell>Type</Table.HeaderCell>
                          <Table.HeaderCell>Description</Table.HeaderCell>
                          <Table.HeaderCell>Dynamic Fields</Table.HeaderCell>
                          <Table.HeaderCell>Status</Table.HeaderCell>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {importData.slice(0, 10).map((category, index) => (
                          <Table.Row key={index}>
                            <Table.Cell>{category.name}</Table.Cell>
                            <Table.Cell>
                              <Badge variant="grey">
                                {category.category_type || "Both"}
                              </Badge>
                            </Table.Cell>
                            <Table.Cell className="max-w-xs truncate">
                              {category.description || "-"}
                            </Table.Cell>
                            <Table.Cell>
                              {category.dynamic_field_schema
                                ? `${
                                    Array.isArray(category.dynamic_field_schema)
                                      ? category.dynamic_field_schema.length
                                      : JSON.parse(
                                          category.dynamic_field_schema || "[]"
                                        ).length
                                  } fields`
                                : "None"}
                            </Table.Cell>
                            <Table.Cell>
                              <Badge
                                variant={category.is_active ? "green" : "grey"}
                              >
                                {category.is_active ? "Active" : "Inactive"}
                              </Badge>
                            </Table.Cell>
                          </Table.Row>
                        ))}
                      </Table.Body>
                    </Table>
                  </div>
                  {importData.length > 10 && (
                    <div className="p-2 bg-gray-50 text-center">
                      <Text className="text-sm text-ui-fg-subtle">
                        ... and {importData.length - 10} more categories
                      </Text>
                    </div>
                  )}
                </div>
              </div>
            )}

            {importStep === "importing" && (
              <div className="space-y-4 text-center">
                <div className="space-y-2">
                  <Text className="font-medium">Importing Categories...</Text>
                  <Text className="text-sm text-ui-fg-subtle">
                    {importProgress.currentItem ||
                      `Processing ${importProgress.processed} of ${importProgress.total}`}
                  </Text>
                </div>
                <ProgressBarComponent value={importProgress.percentage} />
                <Text className="text-sm text-ui-fg-subtle">
                  {importProgress.percentage.toFixed(0)}% Complete
                </Text>
              </div>
            )}

            {importStep === "complete" && (
              <div className="text-center space-y-4">
                <div className="text-green-600">
                  <div className="text-4xl mb-2">✅</div>
                  <Text className="font-medium">
                    Import Completed Successfully!
                  </Text>
                  <Text className="text-sm text-ui-fg-subtle">
                    Categories have been imported and are now available.
                  </Text>
                </div>
              </div>
            )}
          </FocusModal.Body>

          <FocusModal.Footer>
            <div className="flex items-center gap-2">
              {importStep === "upload" && (
                <Button variant="secondary" onClick={handleCloseImportModal}>
                  Cancel
                </Button>
              )}

              {importStep === "preview" && (
                <>
                  <Button
                    variant="secondary"
                    onClick={() => setImportStep("upload")}
                  >
                    Back
                  </Button>
                  <Button
                    onClick={handleImport}
                    disabled={importErrors.length > 0}
                  >
                    Import {importData.length} Categories
                  </Button>
                </>
              )}

              {importStep === "importing" && (
                <Button disabled>Importing...</Button>
              )}

              {importStep === "complete" && (
                <Button onClick={handleCloseImportModal}>Close</Button>
              )}
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>

      <Toaster />
    </>
  );
};

export default CategoriesConfigPage;
