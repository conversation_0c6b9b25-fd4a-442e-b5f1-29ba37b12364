import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Tag, PlusMini, Pencil, Trash } from "@camped-ai/icons";
import { Edit, MoreHorizontal, Hash } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
  FocusModal,
  Label,
  DropdownMenu,
  Prompt,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import {
  useTags,
  useCreateTag,
  useUpdateTag,
  useDeleteTag,
  type Tag as TagType,
  type CreateTagInput,
  type UpdateTagInput,
} from "../../../../hooks/supplier-products-services/use-tags";

interface FormData {
  name: string;
  color: string;
  is_active: boolean;
}

const TagsConfigPage = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<TagType | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [formData, setFormData] = useState<FormData>({
    name: "",
    color: "#3B82F6",
    is_active: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Delete confirmation modal state
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [tagToDelete, setTagToDelete] = useState<TagType | null>(null);

  // Hooks
  const { data: tagsData, isLoading } = useTags({
    name: searchTerm || undefined,
    limit: 50,
  });
  const createTag = useCreateTag();
  const updateTag = useUpdateTag();
  const deleteTag = useDeleteTag();

  const tags = tagsData?.tags || [];

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error("Please fill all the required fields");
      return;
    }

    try {
      const data = {
        name: formData.name.trim(),
        color: formData.color || undefined,
        is_active: formData.is_active,
      };

      if (editingTag) {
        await updateTag.mutateAsync({
          id: editingTag.id,
          data: data as UpdateTagInput,
        });
        toast.success("Tag updated successfully");
      } else {
        await createTag.mutateAsync(data as CreateTagInput);
        toast.success("Tag created successfully");
      }

      closeModal();
    } catch (error) {
      console.error("Error saving tag:", error);
    }
  };

  const handleEdit = (tag: TagType) => {
    setEditingTag(tag);
    setFormData({
      name: tag.name,
      color: tag.color || "#3B82F6",
      is_active: tag.is_active,
    });
    setIsModalOpen(true);
  };

  const handleDelete = (tag: TagType) => {
    setTagToDelete(tag);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!tagToDelete) return;

    try {
      await deleteTag.mutateAsync(tagToDelete.id);
      toast.success("Tag deleted successfully");
      setDeleteConfirmOpen(false);
      setTagToDelete(null);
    } catch (error) {
      console.error("Error deleting tag:", error);
      toast.error("Failed to delete tag");
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmOpen(false);
    setTagToDelete(null);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingTag(null);
    setFormData({
      name: "",
      color: "#3B82F6",
      is_active: true,
    });
    setErrors({});
  };

  const openCreateModal = () => {
    setEditingTag(null);
    setFormData({
      name: "",
      color: "#3B82F6",
      is_active: true,
    });
    setIsModalOpen(true);
  };

  const predefinedColors = [
    "#3B82F6", // Blue
    "#10B981", // Green
    "#F59E0B", // Yellow
    "#EF4444", // Red
    "#8B5CF6", // Purple
    "#F97316", // Orange
    "#06B6D4", // Cyan
    "#84CC16", // Lime
    "#EC4899", // Pink
    "#6B7280", // Gray
  ];

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Breadcrumb Navigation */}
        <div className="px-6 py-3 bg-gray-50 border-b">
          <div className="flex items-center gap-2 text-sm">
            <button
              onClick={() => navigate("/supplier-management")}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Supplier Management
            </button>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">Configuration</span>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">Tags</span>
          </div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Tags Configuration</Heading>
            <Text className="text-ui-fg-subtle">
              Manage tags for grouping and filtering products and services
            </Text>
          </div>
          <Button size="small" onClick={openCreateModal}>
            <PlusMini />
            Add Tag
          </Button>
        </div>

        {/* Search */}
        <div className="px-6 py-4">
          <Input
            placeholder="Search tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Name</Table.HeaderCell>
                <Table.HeaderCell>Color</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell>Actions</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <Table.Row key={`skeleton-${index}`}>
                    <Table.Cell><div className="h-4 bg-gray-200 rounded animate-pulse" /></Table.Cell>
                    <Table.Cell><div className="h-4 bg-gray-200 rounded animate-pulse w-16" /></Table.Cell>
                    <Table.Cell><div className="h-4 bg-gray-200 rounded animate-pulse w-16" /></Table.Cell>
                    <Table.Cell><div className="h-4 bg-gray-200 rounded animate-pulse w-8" /></Table.Cell>
                  </Table.Row>
                ))
              ) : tags.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={4} className="text-center py-8">
                    <Text className="text-ui-fg-subtle">
                      {searchTerm ? "No tags found matching your search" : "No tags found"}
                    </Text>
                  </Table.Cell>
                </Table.Row>
              ) : (
                tags.map((tag) => (
                  <Table.Row key={tag.id}>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: tag.color || "#3B82F6" }}
                        />
                        <Text weight="plus">{tag.name}</Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="text-ui-fg-subtle font-mono text-sm">
                        {tag.color || "#3B82F6"}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge variant={tag.is_active ? "green" : "grey"}>
                        {tag.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <DropdownMenu>
                        <DropdownMenu.Trigger asChild>
                          <Button variant="transparent" size="small">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Item onClick={() => handleEdit(tag)}>
                            <Edit className="h-4 w-4" />
                            Edit
                          </DropdownMenu.Item>
                          <DropdownMenu.Item 
                            onClick={() => handleDelete(tag)}
                            className="text-red-600"
                          >
                            <Trash className="h-4 w-4" />
                            Delete
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>

      {/* Create/Edit Modal */}
      <FocusModal open={isModalOpen} onOpenChange={setIsModalOpen}>
        <FocusModal.Content>
          <FocusModal.Header>
            <Heading level="h2">
              {editingTag ? "Edit Tag" : "Add New Tag"}
            </Heading>
          </FocusModal.Header>
          
          <form onSubmit={handleSubmit}>
            <FocusModal.Body className="space-y-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="e.g., Kids, Winter, Luxury"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.name}
                  </Text>
                )}
              </div>

              <div>
                <Label htmlFor="color">Color</Label>
                <div className="space-y-3">
                  <Input
                    id="color"
                    type="color"
                    value={formData.color}
                    onChange={(e) => handleInputChange("color", e.target.value)}
                    className="w-20 h-10"
                  />
                  <div className="flex flex-wrap gap-2">
                    <Text size="small" className="text-ui-fg-subtle w-full">
                      Quick colors:
                    </Text>
                    {predefinedColors.map((color) => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => handleInputChange("color", color)}
                        className={`w-6 h-6 rounded-full border-2 ${
                          formData.color === color ? "border-gray-800" : "border-gray-300"
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => handleInputChange("is_active", e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </FocusModal.Body>

            <FocusModal.Footer>
              <div className="flex items-center gap-2">
                <Button type="button" variant="secondary" onClick={closeModal}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={createTag.isPending || updateTag.isPending}
                >
                  {editingTag ? "Update" : "Create"}
                </Button>
              </div>
            </FocusModal.Footer>
          </form>
        </FocusModal.Content>
      </FocusModal>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Tag</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{tagToDelete?.name}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={cancelDelete}>Cancel</Prompt.Cancel>
            <Prompt.Action
              onClick={confirmDelete}
              disabled={deleteTag.isPending}
            >
              {deleteTag.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

// Note: No route config export to prevent this from appearing as a top-level menu item
// This page is accessible through the Supplier Management dashboard

export default TagsConfigPage;
