import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@camped-ai/icons";
import { Edit, MoreHorizontal } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Toaster,
  toast,
  Drawer,
  Label,
  DropdownMenu,
  Badge,
  IconButton,
  Prompt,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import {
  useBusinessTypes,
  useCreateBusinessType,
  useUpdateBusinessType,
  useDeleteBusinessType,
  type BusinessType,
} from "../../../../hooks/supplier-products-services/use-business-types";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import { MultiSelect } from "../../../../components/common/MultiSelect";

interface FormData {
  name: string;
  service_categories: string[]; // Store category IDs
  is_active: boolean;
}

interface ShowMoreDrawerData {
  businessType: BusinessType;
  categories: string[];
}

const BusinessTypesConfigPage = () => {
  const navigate = useNavigate();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isShowMoreDrawerOpen, setIsShowMoreDrawerOpen] = useState(false);
  const [editingBusinessType, setEditingBusinessType] =
    useState<BusinessType | null>(null);
  const [showMoreData, setShowMoreData] = useState<ShowMoreDrawerData | null>(
    null
  );
  const [formData, setFormData] = useState<FormData>({
    name: "",
    service_categories: [],
    is_active: true,
  });
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [businessTypeToDelete, setBusinessTypeToDelete] =
    useState<BusinessType | null>(null);

  // API hooks
  const { data: businessTypesData, isLoading, error } = useBusinessTypes();
  const { data: categoriesData, isLoading: categoriesLoading } = useCategories({
    is_active: true,
  });
  const createBusinessTypeMutation = useCreateBusinessType();
  const updateBusinessTypeMutation = useUpdateBusinessType();
  const deleteBusinessTypeMutation = useDeleteBusinessType();

  const businessTypes = businessTypesData?.lookups || [];

  const resetForm = () => {
    setFormData({
      name: "",
      service_categories: [],
      is_active: true,
    });
    setEditingBusinessType(null);
  };

  const openCreateDrawer = () => {
    resetForm();
    setIsDrawerOpen(true);
  };

  const openEditDrawer = (businessType: BusinessType) => {
    const parsedValue = businessType.parsed_value || {
      name: "Unknown",
      description: "",
      service_categories: [],
      is_active: true,
    };
    setFormData({
      name: parsedValue.name,
      service_categories: parsedValue.service_categories || [],
      is_active: parsedValue.is_active,
    });
    setEditingBusinessType(businessType);
    setIsDrawerOpen(true);
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
    resetForm();
  };

  const openShowMoreDrawer = (businessType: BusinessType) => {
    const parsedValue = businessType.parsed_value || {
      name: "Unknown",
      description: "",
      service_categories: [],
      is_active: true,
    };
    // Get category names from IDs for display
    const categoryNames = getCategoryNames(parsedValue.service_categories || []);
    setShowMoreData({
      businessType,
      categories: categoryNames,
    });
    setIsShowMoreDrawerOpen(true);
  };

  const closeShowMoreDrawer = () => {
    setIsShowMoreDrawerOpen(false);
    setShowMoreData(null);
  };

  // Helper function to handle category selection
  const handleCategoryChange = (selectedCategoryIds: string[]) => {
    setFormData((prev) => ({
      ...prev,
      service_categories: selectedCategoryIds,
    }));
  };

  // Helper function to get category names from IDs
  const getCategoryNames = (categoryIds: string[]): string[] => {
    if (!categoriesData?.categories) return [];
    return categoryIds
      .map((id) => {
        const category = categoriesData.categories.find((cat) => cat.id === id);
        return category ? category.name : null;
      })
      .filter((name): name is string => name !== null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Business type name is required");
      return;
    }

    if (formData.service_categories.length === 0) {
      toast.error("At least one service category is required");
      return;
    }

    try {
      if (editingBusinessType) {
        await updateBusinessTypeMutation.mutateAsync({
          id: editingBusinessType.id,
          data: {
            name: formData.name.trim(),
            description: "", // Keep empty for now, can be used for additional description
            service_categories: formData.service_categories,
            is_active: formData.is_active,
          },
        });
      } else {
        await createBusinessTypeMutation.mutateAsync({
          name: formData.name.trim(),
          description: "", // Keep empty for now, can be used for additional description
          service_categories: formData.service_categories,
          is_active: formData.is_active,
        });
      }
      closeDrawer();
    } catch (error) {
      console.error("Error saving business type:", error);
    }
  };

  const handleDelete = (businessType: BusinessType) => {
    setBusinessTypeToDelete(businessType);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!businessTypeToDelete) return;

    try {
      await deleteBusinessTypeMutation.mutateAsync(businessTypeToDelete.id);
      setDeleteConfirmOpen(false);
      setBusinessTypeToDelete(null);
    } catch (error) {
      console.error("Error deleting business type:", error);
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmOpen(false);
    setBusinessTypeToDelete(null);
  };

  const isSubmitting =
    createBusinessTypeMutation.isPending ||
    updateBusinessTypeMutation.isPending;

  return (
    <>
      <PermissionBasedSidebarHider />
      <div className="flex items-center justify-between">
        <IconButton onClick={() => navigate("/supplier-management")}>
          <ArrowLeft className="w-4 h-4" />
        </IconButton>
        <Button onClick={openCreateDrawer}>
          <PlusMini className="h-4 w-4" />
          Add Business Type
        </Button>
      </div>
      <Container>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div>
              <Heading level="h1">Business Types</Heading>
              <Text className="text-ui-fg-subtle">
                Manage business types for supplier products and services
              </Text>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="">
          {error && (
            <div className="p-6 mb-4">
              <Text className="text-red-600">
                Error loading business types: {error.message}
              </Text>
            </div>
          )}

          {/* Table */}
          <div className="overflow-x-auto  rounded-lg">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Business Type</Table.HeaderCell>
                  <Table.HeaderCell>Service Categories</Table.HeaderCell>
                  <Table.HeaderCell>Created</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <Table.Row key={`skeleton-${index}`}>
                      <Table.Cell>
                        <div className="h-4  rounded animate-pulse" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4  rounded animate-pulse w-24" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4  rounded animate-pulse w-24" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4  rounded animate-pulse w-16" />
                      </Table.Cell>
                    </Table.Row>
                  ))
                ) : businessTypes.length === 0 ? (
                  <Table.Row>
                    <Table.Cell
                      className="text-center py-8"
                      style={{ gridColumn: "1 / -1" }}
                    >
                      <Text className="text-ui-fg-subtle">
                        No business types found
                      </Text>
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={openCreateDrawer}
                        className="mt-2"
                      >
                        <PlusMini className="h-4 w-4" />
                        Add First Business Type
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  businessTypes.map((businessType) => {
                    const parsedValue = businessType.parsed_value || {
                      name: "Unknown",
                      description: "",
                      service_categories: [],
                      is_active: true,
                    };
                    // Get category names from IDs
                    const categoryNames = getCategoryNames(parsedValue.service_categories || []);
                    const displayCategories = categoryNames.slice(0, 4);
                    const hasMore = categoryNames.length > 4;

                    return (
                      <Table.Row key={businessType.id}>
                        <Table.Cell>
                          <Text className="font-medium">
                            {parsedValue.name}
                          </Text>
                        </Table.Cell>
                        <Table.Cell>
                          <div className="flex flex-wrap gap-1 items-center">
                            {displayCategories.map((category, index) => (
                              <Badge
                                key={index}
                                className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border transition-colors bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                              >
                                {category}
                              </Badge>
                            ))}
                            {hasMore && (
                              <Button
                                variant="transparent"
                                size="small"
                                onClick={() => openShowMoreDrawer(businessType)}
                                className="text-xs text-ui-fg-subtle hover:text-ui-fg-base"
                              >
                                Show More ({categoryNames.length - 4})
                              </Button>
                            )}
                          </div>
                        </Table.Cell>
                        <Table.Cell>
                          <Text className="text-ui-fg-subtle">
                            {new Date(
                              businessType.created_at
                            ).toLocaleDateString()}
                          </Text>
                        </Table.Cell>
                        <Table.Cell>
                          <DropdownMenu>
                            <DropdownMenu.Trigger asChild>
                              <IconButton size="small">
                                <MoreHorizontal className="h-4 w-4" />
                              </IconButton>
                            </DropdownMenu.Trigger>
                            <DropdownMenu.Content>
                              <DropdownMenu.Item
                                onClick={() => openEditDrawer(businessType)}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenu.Item>
                              <DropdownMenu.Item
                                onClick={() => handleDelete(businessType)}
                                className="text-red-600"
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenu.Item>
                            </DropdownMenu.Content>
                          </DropdownMenu>
                        </Table.Cell>
                      </Table.Row>
                    );
                  })
                )}
              </Table.Body>
            </Table>
          </div>
        </div>
      </Container>

      {/* Create/Edit Drawer */}
      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>
              {editingBusinessType ? "Edit Business Type" : "Add Business Type"}
            </Drawer.Title>
            <Drawer.Description>
              {editingBusinessType
                ? "Update the business type value"
                : "Add a new business type for supplier products and services"}
            </Drawer.Description>
          </Drawer.Header>

          <form onSubmit={handleSubmit}>
            <Drawer.Body className="space-y-4">
              {/* Business Type Name Field */}
              <div>
                <Label htmlFor="name">Business Type Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder="e.g., Transportation, Maintenance"
                  required
                  className="my-1"
                />
                <Text className="text-ui-fg-subtle text-sm mt-1">
                  Name for this business type category
                </Text>
              </div>

              {/* Service Categories Multi-Select */}
              <div>
                <Label htmlFor="service_categories">Service Categories *</Label>
                <MultiSelect
                  options={
                    categoriesData?.categories.map((category) => ({
                      value: category.id,
                      label: category.name,
                    })) || []
                  }
                  selectedValues={formData.service_categories}
                  onChange={handleCategoryChange}
                  placeholder="Select service categories..."
                  disabled={categoriesLoading}
                  showSelectAll={true}
                  showSelectedTags={true}
                  className="my-1"
                />
                <Text className="text-ui-fg-subtle text-sm mt-1">
                  Select one or more service categories for this business type.
                </Text>
              </div>
            </Drawer.Body>

            <Drawer.Footer>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={closeDrawer}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  isLoading={isSubmitting}
                >
                  {editingBusinessType ? "Update" : "Create"} Business Type
                </Button>
              </div>
            </Drawer.Footer>
          </form>
        </Drawer.Content>
      </Drawer>

      {/* Show More Categories Drawer */}
      <Drawer
        open={isShowMoreDrawerOpen}
        onOpenChange={setIsShowMoreDrawerOpen}
      >
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>
              Service Categories -{" "}
              {showMoreData?.businessType.parsed_value?.name || "Unknown"}
            </Drawer.Title>
            <Drawer.Description>
              All service categories for this business type
            </Drawer.Description>
          </Drawer.Header>

          <Drawer.Body>
            {showMoreData && (
              <div className="flex flex-wrap gap-2">
                {showMoreData.categories.map((category, index) => (
                  <Badge
                    key={index}
                    className="inline-flex items-center px-3 py-1 text-sm font-medium rounded-full border transition-colors bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                  >
                    {category}
                  </Badge>
                ))}
              </div>
            )}
          </Drawer.Body>

          <Drawer.Footer>
            <Button
              type="button"
              variant="secondary"
              onClick={closeShowMoreDrawer}
            >
              Close
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Business Type</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{businessTypeToDelete?.parsed_value?.name || businessTypeToDelete?.value}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={cancelDelete}>Cancel</Prompt.Cancel>
            <Prompt.Action
              onClick={confirmDelete}
              disabled={deleteBusinessTypeMutation.isPending}
            >
              {deleteBusinessTypeMutation.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

// Note: No route config export to prevent this from appearing as a top-level menu item
// This page is accessible through the Supplier Management dashboard

export default BusinessTypesConfigPage;
