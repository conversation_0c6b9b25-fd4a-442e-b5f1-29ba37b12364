import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
  
  try {
    logger.info("Creating supplier products services tables...");
    
    // Get the database manager
    const manager = req.scope.resolve("manager");
    
    // Create product_service_category table
    await manager.query(`
      CREATE TABLE IF NOT EXISTS "product_service_category" (
        "id" text NOT NULL,
        "name" text NOT NULL,
        "description" text NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "product_service_category_pkey" PRIMARY KEY ("id")
      );
    `);
    
    // Create product_service_unit_type table
    await manager.query(`
      CREATE TABLE IF NOT EXISTS "product_service_unit_type" (
        "id" text NOT NULL,
        "name" text NOT NULL,
        "description" text NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "product_service_unit_type_pkey" PRIMARY KEY ("id")
      );
    `);
    
    // Create product_service_tag table
    await manager.query(`
      CREATE TABLE IF NOT EXISTS "product_service_tag" (
        "id" text NOT NULL,
        "name" text NOT NULL,
        "description" text NULL,
        "color" text NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "product_service_tag_pkey" PRIMARY KEY ("id")
      );
    `);
    
    // Create product_service_custom_field table
    await manager.query(`
      CREATE TABLE IF NOT EXISTS "product_service_custom_field" (
        "id" text NOT NULL,
        "name" text NOT NULL,
        "type" text NOT NULL,
        "required" boolean NOT NULL DEFAULT false,
        "options" jsonb NULL,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "product_service_custom_field_pkey" PRIMARY KEY ("id")
      );
    `);
    
    // Create product_service table
    await manager.query(`
      CREATE TABLE IF NOT EXISTS "product_service" (
        "id" text NOT NULL,
        "name" text NOT NULL,
        "type" text NOT NULL,
        "description" text NULL,
        "status" text NOT NULL DEFAULT 'active',
        "category_id" text NOT NULL,
        "unit_type_id" text NOT NULL,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "product_service_pkey" PRIMARY KEY ("id")
      );
    `);
    
    // Create product_service_tag_junction table
    await manager.query(`
      CREATE TABLE IF NOT EXISTS "product_service_tag_junction" (
        "id" text NOT NULL,
        "product_service_id" text NOT NULL,
        "tag_id" text NOT NULL,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "product_service_tag_junction_pkey" PRIMARY KEY ("id")
      );
    `);
    
    // Create product_service_custom_field_junction table
    await manager.query(`
      CREATE TABLE IF NOT EXISTS "product_service_custom_field_junction" (
        "id" text NOT NULL,
        "product_service_id" text NOT NULL,
        "custom_field_id" text NOT NULL,
        "value" text NULL,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "product_service_custom_field_junction_pkey" PRIMARY KEY ("id")
      );
    `);
    
    // Create indexes for better performance
    await manager.query(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_category_name" ON "product_service_category" ("name") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_category_is_active" ON "product_service_category" ("is_active") WHERE "deleted_at" IS NULL;`);
    
    await manager.query(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_unit_type_name" ON "product_service_unit_type" ("name") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_unit_type_is_active" ON "product_service_unit_type" ("is_active") WHERE "deleted_at" IS NULL;`);
    
    await manager.query(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_tag_name" ON "product_service_tag" ("name") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_is_active" ON "product_service_tag" ("is_active") WHERE "deleted_at" IS NULL;`);
    
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_category_id" ON "product_service" ("category_id") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_unit_type_id" ON "product_service" ("unit_type_id") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_status" ON "product_service" ("status") WHERE "deleted_at" IS NULL;`);
    
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_product_service_id" ON "product_service_tag_junction" ("product_service_id") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_tag_id" ON "product_service_tag_junction" ("tag_id") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_unique" ON "product_service_tag_junction" ("product_service_id", "tag_id") WHERE "deleted_at" IS NULL;`);
    
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_product_service_id" ON "product_service_custom_field_junction" ("product_service_id") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_custom_field_id" ON "product_service_custom_field_junction" ("custom_field_id") WHERE "deleted_at" IS NULL;`);
    await manager.query(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_unique" ON "product_service_custom_field_junction" ("product_service_id", "custom_field_id") WHERE "deleted_at" IS NULL;`);
    
    logger.info("✅ Supplier products services tables created successfully");
    
    res.json({
      success: true,
      message: "Supplier products services tables created successfully"
    });
    
  } catch (error) {
    logger.error("❌ Failed to create supplier products services tables:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create tables",
      error: error.message
    });
  }
};
