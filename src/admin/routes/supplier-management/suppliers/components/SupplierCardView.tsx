import { useNavigate } from "react-router-dom";
import { Edit, Eye, MoreVertical, Mail, Phone, MapPin, User, Trash } from "lucide-react";
import { Buildings, PlusMini } from "@camped-ai/icons";
import { Text, Button, Badge, DropdownMenu, Prompt } from "@camped-ai/ui";
import { useRbac } from "../../../../hooks/use-rbac";
import { useDeleteSupplier } from "../../../../hooks/vendor-management/use-suppliers";
import { useBusinessTypes } from "../../../../hooks/supplier-products-services/use-business-types";

import type { Supplier } from "../../../../hooks/supplier-management/use-suppliers-list";
import { useState } from "react";

interface SupplierCardViewProps {
  suppliers: Supplier[];
  isLoading: boolean;
  onSupplierClick?: (id: string) => void;
  onEditClick?: (id: string) => void;
  onCreateClick?: () => void;
  onDeleteClick?: (id: string) => void;
  hasCreatePermission?: boolean;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
}

const SupplierCardView: React.FC<SupplierCardViewProps> = ({
  suppliers,
  isLoading,
  onSupplierClick,
  onEditClick,
  onCreateClick,
  onDeleteClick,
  hasCreatePermission = true,
  hasEditPermission = true,
  hasDeletePermission = true,
}) => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const deleteSupplier = useDeleteSupplier();
  const { data: businessTypesData } = useBusinessTypes();
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
    const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);
  // Helper functions
  const getBusinessTypeBadge = (businessTypeId: string | null | undefined): string => {
    if (!businessTypeId) return "General";

    // Find business type by ID and return its name
    const businessType = businessTypesData?.lookups?.find(
      (bt) => bt.id === businessTypeId
    );

    if (businessType?.parsed_value?.name) {
      return businessType.parsed_value.name;
    }

    // Fallback to formatted ID if business type not found
    return businessTypeId.charAt(0).toUpperCase() + businessTypeId.slice(1);
  };

    const handleDeleteClick = (supplier: Supplier, event: React.MouseEvent) => {
      event.stopPropagation();
      setSupplierToDelete(supplier);
      setDeleteConfirmOpen(true);
    };

  const handleConfirmDelete = async () => {
    if (!supplierToDelete) return;

    try {
      if (onDeleteClick) {
        onDeleteClick(supplierToDelete.id);
      } else {
        await deleteSupplier.mutateAsync(supplierToDelete.id);
      }
    } catch (error) {
      console.error("Error deleting supplier:", error);
    } finally {
      setDeleteConfirmOpen(false);
      setSupplierToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setSupplierToDelete(null);
  };

  const getStatusBadgeVariant = (status: string): "green" | "red" | "orange" | "grey" => {
    switch (status?.toLowerCase()) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      case "pending":
        return "orange";
      default:
        return "grey";
    }
  };



  const getPrimaryContact = (supplier: Supplier) => {
    return supplier.contacts?.find(contact => contact.is_primary) || supplier.contacts?.[0];
  };

  // Helper function to get initials from company name (standardized)
  const getInitials = (name: string): string => {
    return name.charAt(0).toUpperCase();
  };

  // Helper function to get avatar background color (standardized with list view)
  const getAvatarColor = (name: string): string => {
    const colors = [
      "bg-teal-500",
      "bg-blue-500",
      "bg-green-500",
      "bg-orange-500",
      "bg-purple-500",
      "bg-pink-500",
      "bg-indigo-500",
      "bg-cyan-500",
    ];
    return colors[name.charCodeAt(0) % colors.length];
  };





  // Supplier Card Component
  const SupplierCard = ({ supplier }: { supplier: Supplier }) => {
    const primaryContact = getPrimaryContact(supplier);
    const initials = getInitials(supplier.name);
    const avatarColor = getAvatarColor(supplier.name);

    return (
      <div
        className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all duration-200 cursor-pointer"
        onClick={() => {
          if (onSupplierClick) {
            onSupplierClick(supplier.id);
          } else {
            navigate(`/supplier-management/suppliers/${supplier.id}`);
          }
        }}
      >
        {/* Header with Avatar, Name, Status and Actions */}
        <div className="flex items-start gap-4 mb-3">
          {/* Avatar - Standardized with list view */}
          <div className={`w-10 h-10 ${avatarColor} rounded-full flex items-center justify-center flex-shrink-0`}>
            <span className="text-white font-medium text-sm">{initials}</span>
          </div>

          {/* Name and Actions */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-gray-900 text-sm truncate">
                  {supplier.name}
                </h3>
              </div>

              {/* Three-dots menu */}
              <div
                className="flex items-center gap-1"
                onClick={(e) => e.stopPropagation()}
              >
                <DropdownMenu>
                  <DropdownMenu.Trigger asChild>
                    <Button variant="transparent" size="small" className="text-gray-400 hover:text-gray-600">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenu.Trigger>
                  <DropdownMenu.Content align="end">
                    <DropdownMenu.Item
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onSupplierClick) {
                          onSupplierClick(supplier.id);
                        } else {
                          navigate(`/supplier-management/suppliers/${supplier.id}`);
                        }
                      }}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </DropdownMenu.Item>
                    {(hasEditPermission || hasPermission("supplier_management:edit")) && (
                      <DropdownMenu.Item
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onEditClick) {
                            onEditClick(supplier.id);
                          } else {
                            navigate(`/supplier-management/suppliers/${supplier.id}/edit`);
                          }
                        }}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenu.Item>
                    )}
                    {(hasDeletePermission || hasPermission("supplier_management:delete")) && (
                      <DropdownMenu.Item
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteClick(supplier, e);
                        }}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenu.Item>
                    )}
                  </DropdownMenu.Content>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-2 mb-3">
          {/* Primary Contact */}
          {primaryContact?.name && (
            <div className="flex items-center gap-3">
              <User className="h-4 w-4 text-gray-400 flex-shrink-0" />
              <Text size="small" className="text-gray-900 font-medium">
                {primaryContact.name}
              </Text>
            </div>
          )}

          {/* Email */}
          <div className="flex items-center gap-3">
            <Mail className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <Text size="small" className="text-gray-500 truncate">
              {primaryContact?.email || supplier.email || "—"}
            </Text>
          </div>

          {/* Phone */}
          <div className="flex items-center gap-3">
            <Phone className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <Text size="small" className="text-gray-500">
              {primaryContact?.phone_number || "—"}
            </Text>
          </div>

          {/* Location */}
          <div className="flex items-center gap-3">
            <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <Text size="small" className="text-gray-500">
              {supplier.region ||
                (supplier.city && supplier.state
                  ? `${supplier.city}, ${supplier.state}`
                  : supplier.city || supplier.state || "—")}
            </Text>
          </div>
        </div>

        {/* Categories/Tags - Standardized with list view */}
        <div className="flex flex-wrap gap-2 mb-3">
          {/* Status Badge - moved from header */}
          <Badge
            color={getStatusBadgeVariant(supplier.status)}
            className="rounded-full text-xs px-3 py-1.5 font-medium"
          >
            {supplier.status}
          </Badge>

          {/* Business Type */}
          <Badge color="grey" className="rounded-full text-xs px-3 py-1.5 font-medium">
            {getBusinessTypeBadge(supplier.business_type)}
          </Badge>

          {/* Preference Badge */}
          {(supplier as any).preference && (
            <Badge
              color={
                (supplier as any).preference === "Preferred"
                  ? "blue"
                  : "orange"
              }
              className="rounded-full text-xs px-3 py-1.5 font-medium"
            >
              {(supplier as any).preference}
            </Badge>
          )}



        </div>
      </div>
    );
  };

  return (
    <div className="px-5 py-2">
      {isLoading ? (
        // Loading skeleton cards
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={`skeleton-card-${index}`}
              className="bg-white border border-gray-200 rounded-xl p-4"
            >
              <div className="space-y-3">
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-16" />
                  </div>
                  <div className="h-6 w-6 bg-gray-200 rounded animate-pulse" />
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-full" />
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3" />
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4" />
                </div>
                <div className="flex gap-2">
                  <div className="h-5 bg-gray-200 rounded animate-pulse w-16" />
                  <div className="h-5 bg-gray-200 rounded animate-pulse w-12" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : suppliers.length === 0 ? (
        <div className="text-center py-16">
          <Buildings className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <Text size="large" weight="plus" className="text-gray-900">
            No suppliers found
          </Text>
          <Text className="text-gray-500 mt-2">
            Try adjusting your filters or get started by adding your first supplier
          </Text>
          {(hasCreatePermission || hasPermission("supplier_management:create")) && (
            <Button
              size="small"
              className="mt-6"
              onClick={() => {
                if (onCreateClick) {
                  onCreateClick();
                } else {
                  navigate("/supplier-management/suppliers-new/create");
                }
              }}
            >
              <PlusMini />
              Add your first supplier
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {suppliers.map((supplier) => (
            <SupplierCard key={supplier.id} supplier={supplier} />
          ))}
        </div>
      )}

            {/* Delete Confirmation Prompt */}
            <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
              <Prompt.Content>
                <Prompt.Header>
                  <Prompt.Title>Delete Supplier</Prompt.Title>
                  <Prompt.Description>
                    Are you sure you want to delete "{supplierToDelete?.name}"? This action cannot be undone.
                  </Prompt.Description>
                </Prompt.Header>
                <Prompt.Footer>
                  <Prompt.Cancel onClick={handleCancelDelete}>
                    Cancel
                  </Prompt.Cancel>
                  <Prompt.Action
                    onClick={handleConfirmDelete}
                    disabled={deleteSupplier.isPending}
                  >
                    {deleteSupplier.isPending ? "Deleting..." : "Delete"}
                  </Prompt.Action>
                </Prompt.Footer>
              </Prompt.Content>
            </Prompt>
    </div>
  );
};

export default SupplierCardView;
