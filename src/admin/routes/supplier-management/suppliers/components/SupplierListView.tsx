import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Edit, Eye, MoreHorizontal, Mail, Trash } from "lucide-react";
import { Buildings, PlusMini } from "@camped-ai/icons";
import {
  Text,
  Button,
  Badge,
  DropdownMenu,
  IconButton,
  Table,
  Prompt,
} from "@camped-ai/ui";
import { useRbac } from "../../../../hooks/use-rbac";
import { useDeleteSupplier } from "../../../../hooks/vendor-management/use-suppliers";
import { useBusinessTypes } from "../../../../hooks/supplier-products-services/use-business-types";
import { getCurrencyDisplayName } from "../../../../constants/supplier-form-options";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";

import type { Supplier } from "../../../../hooks/supplier-management/use-suppliers-list";

interface SupplierListViewProps {
  suppliers: Supplier[];
  isLoading: boolean;
  onSupplierClick?: (id: string) => void;
  onEditClick?: (id: string) => void;
  onCreateClick?: () => void;
  onDeleteClick?: (id: string) => void;
  hasCreatePermission?: boolean;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
}

const SupplierListView: React.FC<SupplierListViewProps> = ({
  suppliers,
  isLoading,
  onSupplierClick,
  onEditClick,
  onCreateClick,
  onDeleteClick,
  hasCreatePermission = true,
  hasEditPermission = true,
  hasDeletePermission = true,
}) => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const deleteSupplier = useDeleteSupplier();
  const { data: businessTypesData } = useBusinessTypes();
  const { data: categoriesData } = useCategories({ is_active: true, limit: 100 });
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);

  // Helper functions (standardized with card view)
  const getBusinessTypeBadge = (
    businessTypeId: string | null | undefined
  ): string => {
    if (!businessTypeId) return "General";

    // Find business type by ID and return its name
    const businessType = businessTypesData?.lookups?.find(
      (bt) => bt.id === businessTypeId
    );

    if (businessType?.parsed_value?.name) {
      return businessType.parsed_value.name;
    }

    // Fallback to formatted ID if business type not found
    return businessTypeId.charAt(0).toUpperCase() + businessTypeId.slice(1);
  };

  const getStatusBadgeVariant = (
    status: string
  ): "green" | "red" | "orange" | "grey" => {
    switch (status?.toLowerCase()) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      case "pending":
        return "orange";
      default:
        return "grey";
    }
  };

  const getPrimaryContact = (supplier: Supplier) => {
    return (
      supplier.contacts?.find((contact) => contact.is_primary) ||
      supplier.contacts?.[0]
    );
  };

  // Helper function to get category names from IDs
  const getCategoryNames = (categoryIds: string[]): string[] => {
    if (!categoriesData?.categories || !Array.isArray(categoryIds)) return [];
    return categoryIds
      .map((id) => {
        const category = categoriesData.categories.find((cat) => cat.id === id);
        return category ? category.name : null;
      })
      .filter((name): name is string => name !== null);
  };

  // Helper function to render categories
  const renderCategories = (categories: string[]) => {
    if (!categories || categories.length === 0) {
      return (
        <Text size="small" className="text-ui-fg-subtle">
          —
        </Text>
      );
    }

    const categoryNames = getCategoryNames(categories);
    if (categoryNames.length === 0) {
      return (
        <Text size="small" className="text-ui-fg-subtle">
          —
        </Text>
      );
    }

    // If only one category, show as a badge
    if (categoryNames.length === 1) {
      return (
        <Badge
          color="blue"
          className="rounded-full text-xs px-3 py-1.5 font-medium"
        >
          {categoryNames[0]}
        </Badge>
      );
    }

    // If multiple categories, show first one as badge with count
    return (
      <div className="flex items-center gap-1">
        <Badge
          color="blue"
          className="rounded-full text-xs px-3 py-1.5 font-medium"
        >
          {categoryNames[0]}
        </Badge>
        {categoryNames.length > 1 && (
          <Badge
            color="grey"
            className="rounded-full text-xs px-2 py-1 font-medium"
          >
            +{categoryNames.length - 1}
          </Badge>
        )}
      </div>
    );
  };

  // Helper function to render currency
  const renderCurrency = (currencyCode?: string) => {
    if (!currencyCode) {
      return (
        <Text size="small" className="text-ui-fg-subtle">
          Not set
        </Text>
      );
    }

    return (
      <Badge
        color="green"
        className="rounded-full text-xs px-3 py-1.5 font-medium"
      >
        {getCurrencyDisplayName(currencyCode)}
      </Badge>
    );
  };

  const handleDeleteClick = (supplier: Supplier, event: React.MouseEvent) => {
    event.stopPropagation();
    setSupplierToDelete(supplier);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!supplierToDelete) return;

    try {
      if (onDeleteClick) {
        onDeleteClick(supplierToDelete.id);
      } else {
        await deleteSupplier.mutateAsync(supplierToDelete.id);
      }
    } catch (error) {
      console.error("Error deleting supplier:", error);
    } finally {
      setDeleteConfirmOpen(false);
      setSupplierToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setSupplierToDelete(null);
  };

  // Standardized avatar color function (same as card view)
  const getAvatarColor = (name: string) => {
    const colors = [
      "bg-teal-500",
      "bg-blue-500",
      "bg-green-500",
      "bg-orange-500",
      "bg-purple-500",
      "bg-pink-500",
      "bg-indigo-500",
      "bg-cyan-500",
    ];
    return colors[name.charCodeAt(0) % colors.length];
  };

  const renderLoadingSkeleton = () =>
    Array.from({ length: 5 }).map((_, index) => (
      <Table.Row key={`skeleton-${index}`}>
        {/* Supplier column skeleton */}
        <Table.Cell>
          <div className="flex items-center">
            <div className="w-8 h-8 bg-ui-bg-subtle rounded-full animate-pulse mr-3" />
            <div className="space-y-1 flex-1">
              <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-24" />
              <div className="h-3 bg-ui-bg-subtle rounded animate-pulse w-16" />
            </div>
          </div>
        </Table.Cell>
        {/* Business Type skeleton */}
        <Table.Cell>
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-20" />
        </Table.Cell>
        {/* Categories skeleton */}
        <Table.Cell>
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-24" />
        </Table.Cell>
        {/* Currency skeleton */}
        <Table.Cell>
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-16" />
        </Table.Cell>
        {/* Status skeleton */}
        <Table.Cell>
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-16" />
        </Table.Cell>
        {/* Preference skeleton */}
        <Table.Cell>
          <div className="h-6 bg-ui-bg-subtle rounded animate-pulse w-16" />
        </Table.Cell>
        {/* Region skeleton */}
        <Table.Cell>
          <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-20" />
        </Table.Cell>
        {/* Contact skeleton */}
        <Table.Cell>
          <div className="space-y-1">
            <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-24" />
            <div className="h-3 bg-ui-bg-subtle rounded animate-pulse w-32" />
          </div>
        </Table.Cell>
        {/* Actions skeleton */}
        <Table.Cell>
          <div className="h-8 w-8 bg-ui-bg-subtle rounded animate-pulse" />
        </Table.Cell>
      </Table.Row>
    ));

  const renderEmptyState = () => (
    <div className="py-16 text-center">
      <div className="max-w-sm mx-auto">
        <Buildings className="h-16 w-16 text-ui-fg-muted mx-auto mb-4" />
        <Text size="large" weight="plus" className="text-ui-fg-base">
          No suppliers found
        </Text>
        <Text className="text-ui-fg-subtle mt-2">
          Try adjusting your filters or get started by adding your first
          supplier
        </Text>
        {(hasCreatePermission ||
          hasPermission("supplier_management:create")) && (
            <Button
              size="small"
              className="mt-6"
              onClick={() => {
                if (onCreateClick) {
                  onCreateClick();
                } else {
                  navigate("/supplier-management/suppliers-new/create");
                }
              }}
            >
              <PlusMini />
              Add your first supplier
            </Button>
          )}
      </div>
    </div>
  );

  const renderActionDropdown = (supplier: Supplier) => (
    <div onClick={(e) => e.stopPropagation()}>
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          {/* <Button variant="transparent" size="small">
            <MoreHorizontal className="h-4 w-4" />
          </Button> */}
          <IconButton size="small">
            <MoreHorizontal className="h-4 w-4" />
          </IconButton>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content align="end">
          <DropdownMenu.Item
            onClick={(e) => {
              e.stopPropagation();
              if (onSupplierClick) {
                onSupplierClick(supplier.id);
              } else {
                navigate(`/supplier-management/suppliers/${supplier.id}`);
              }
            }}
          >
            <Eye className="h-4 w-4 mr-2" />
            View Details
          </DropdownMenu.Item>
          {(hasEditPermission || hasPermission("supplier_management:edit")) && (
            <DropdownMenu.Item
              onClick={(e) => {
                e.stopPropagation();
                if (onEditClick) {
                  onEditClick(supplier.id);
                } else {
                  navigate(
                    `/supplier-management/suppliers/${supplier.id}/edit`
                  );
                }
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenu.Item>
          )}
          {(hasDeletePermission ||
            hasPermission("supplier_management:delete")) && (
              <DropdownMenu.Item
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteClick(supplier, e);
                }}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenu.Item>
            )}
        </DropdownMenu.Content>
      </DropdownMenu>
    </div>
  );

  return (
    <>
      <div className="mx-0 mt-6">
        {suppliers.length === 0 && !isLoading ? (
          renderEmptyState()
        ) : (
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Supplier</Table.HeaderCell>
                <Table.HeaderCell>Business Type</Table.HeaderCell>
                <Table.HeaderCell>Categories</Table.HeaderCell>
                <Table.HeaderCell>Currency</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell>Preference</Table.HeaderCell>
                <Table.HeaderCell>Region</Table.HeaderCell>
                <Table.HeaderCell>Primary Contact</Table.HeaderCell>
                <Table.HeaderCell className="text-right">
                  Actions
                </Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading
                ? renderLoadingSkeleton()
                : suppliers.map((supplier) => {
                  const primaryContact = getPrimaryContact(supplier);
                  return (
                    <Table.Row
                      key={supplier.id}
                      className="cursor-pointer hover:bg-ui-bg-subtle transition-colors"
                      onClick={() => {
                        if (onSupplierClick) {
                          onSupplierClick(supplier.id);
                        } else {
                          navigate(
                            `/supplier-management/suppliers/${supplier.id}`
                          );
                        }
                      }}
                    >
                      {/* Supplier Info */}
                      <Table.Cell>
                        <div className="flex items-center">
                          <div
                            className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-medium mr-3 ${getAvatarColor(
                              supplier.name
                            )}`}
                          >
                            {supplier.name.charAt(0).toUpperCase()}
                          </div>
                          <div className="min-w-0 flex-1">
                            <div
                              className="font-medium text-ui-fg-base truncate text-sm"
                              title={supplier.name}
                            >
                              {supplier.name}
                            </div>
                            {supplier.website && (
                              <div className="flex items-center text-sm text-ui-fg-subtle mt-0.5">
                                <span className="truncate">
                                  {supplier.website}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </Table.Cell>

                      {/* Business Type */}
                      <Table.Cell>
                        <Badge
                          color="grey"
                          className="rounded-full text-xs px-3 py-1.5 font-medium"
                        >
                          {getBusinessTypeBadge(supplier.business_type)}
                        </Badge>
                      </Table.Cell>

                      {/* Categories */}
                      <Table.Cell>
                        {renderCategories(supplier.categories)}
                      </Table.Cell>

                      {/* Currency */}
                      <Table.Cell>
                        {renderCurrency(supplier.default_currency)}
                      </Table.Cell>

                      {/* Status */}
                      <Table.Cell>
                        <Badge
                          color={getStatusBadgeVariant(supplier.status)}
                          className="rounded-full text-xs px-3 py-1.5 font-medium"
                        >
                          {supplier.status}
                        </Badge>
                      </Table.Cell>

                      {/* Preference */}
                      <Table.Cell>
                        {supplier.preference ? (
                          <Badge
                            color={
                              supplier.preference === "Preferred"
                                ? "blue"
                                : "orange"
                            }
                            className="rounded-full text-xs px-3 py-1.5 font-medium"
                          >
                            {supplier.preference}
                          </Badge>
                        ) : (
                          <Text size="small" className="text-ui-fg-subtle">
                            —
                          </Text>
                        )}
                      </Table.Cell>

                      {/* Region */}
                      <Table.Cell>
                        <Text size="small">{supplier.region || "—"}</Text>
                      </Table.Cell>

                      {/* Primary Contact */}
                      <Table.Cell>
                        <div className="space-y-0.5">
                          <Text
                            size="small"
                            weight="plus"
                            className="text-ui-fg-base"
                          >
                            {primaryContact?.name || "No primary contact"}
                          </Text>
                          <div className="flex items-center gap-1 text-ui-fg-subtle">
                            <Mail className="h-3 w-3" />
                            <Text size="small" className="text-ui-fg-subtle">
                              {primaryContact?.email || supplier.email || "—"}
                            </Text>
                          </div>
                        </div>
                      </Table.Cell>

                      {/* Actions */}
                      <Table.Cell className="text-right">
                        <div onClick={(e) => e.stopPropagation()}>
                          {renderActionDropdown(supplier)}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  );
                })}
            </Table.Body>
          </Table>
        )}
      </div>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{supplierToDelete?.name}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleConfirmDelete}
              disabled={deleteSupplier.isPending}
            >
              {deleteSupplier.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default SupplierListView;
