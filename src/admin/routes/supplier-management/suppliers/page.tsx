import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Buildings, PlusMini, Adjustments } from "@camped-ai/icons";
import { Download, Upload } from "lucide-react";
import OutlineButton from "../../../components/shared/OutlineButton";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Toaster,
  toast,
  Select,
} from "@camped-ai/ui";
import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  useSuppliersList,
  useRefreshSuppliersList,
  type Supplier,
} from "../../../hooks/supplier-management/use-suppliers-list";
import { useDeleteSupplier } from "../../../hooks/vendor-management/use-suppliers";
import { useRbac } from "../../../hooks/use-rbac";
import { useBusinessTypes } from "../../../hooks/supplier-products-services/use-business-types";
import { useDebouncedValue } from "../../../hooks/common/use-debounced-value";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { SupplierListView, SupplierCardView } from "./components";
import SupplierExportModal from "../../../components/supplier-management/supplier-export-modal";
import SupplierImportModal from "../../../components/supplier-management/supplier-import-modal";

// Filter interface
interface SupplierFilters {
  search: string; // Consolidated search for supplier name, contact name, and contact email
  status: string;
  business_type: string;
  preference: string;
}

const SupplierListPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [searchParams, setSearchParams] = useSearchParams();
  const refreshSuppliersList = useRefreshSuppliersList();
  const deleteSupplier = useDeleteSupplier();

  // Filter state management with URL synchronization
  const [filters, setFilters] = useState<SupplierFilters>(() => ({
    search: searchParams.get("search") || "",
    status: searchParams.get("status") || "",
    business_type: searchParams.get("business_type") || "",
    preference: searchParams.get("preference") || "",
  }));

  const [currentPage, setCurrentPage] = useState(() => {
    const page = searchParams.get("page");
    return page ? parseInt(page, 10) : 1;
  });

  const [pageSize, setPageSize] = useState(() => {
    const size = searchParams.get("pageSize");
    return size ? parseInt(size, 10) : 25;
  });

  const [viewMode, setViewMode] = useState<"list" | "card">(() => {
    // Remember user's preferred view from localStorage
    const savedView = localStorage.getItem("supplier-list-view-mode");
    return (savedView as "list" | "card") || "list";
  });

  const [showFilters, setShowFilters] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);

  // Debounce search input to improve performance (300ms delay)
  const debouncedSearch = useDebouncedValue(filters.search, 300);

  // Reset to page 1 when search changes
  useEffect(() => {
    if (debouncedSearch !== filters.search) {
      setCurrentPage(1);
    }
  }, [debouncedSearch, filters.search]);

  // Fetch business types for filter dropdown
  const {
    data: businessTypesData,
    isLoading: businessTypesLoading,
    error: businessTypesError,
  } = useBusinessTypes({
    is_active: true,
    limit: 100,
  });

  // Process business types to use IDs as values and names as labels
  const businessTypeOptions = React.useMemo(() => {
    if (!businessTypesData?.lookups) return [];

    const options: { value: string; label: string }[] = [];

    businessTypesData.lookups.forEach((businessType) => {
      if (businessType.parsed_value?.is_active) {
        options.push({
          value: businessType.id, // Use business type ID as value
          label: businessType.parsed_value.name, // Display business type name as label
        });
      }
    });

    return options.sort((a, b) => a.label.localeCompare(b.label));
  }, [businessTypesData]);

  // Debug logging for business types
  React.useEffect(() => {
    if (businessTypesData) {
      console.log("Business types loaded:", businessTypesData);
      console.log("Business type options:", businessTypeOptions);
    }
  }, [businessTypesData, businessTypeOptions]);

  // Use the suppliers list hook with comprehensive filters
  const {
    data: suppliersData,
    isLoading,
    error,
  } = useSuppliersList({
    search: debouncedSearch || undefined,
    status: filters.status || undefined,
    business_type: filters.business_type || undefined,
    preference: filters.preference || undefined,
    sort_by: "updated_at", // Changed to updated_at for "latest modified first"
    sort_order: "desc",
    limit: pageSize,
    offset: (currentPage - 1) * pageSize,
  });

  const suppliers = suppliersData?.suppliers || [];
  const totalCount = suppliersData?.count || 0;

  // Handle error states
  useEffect(() => {
    if (error) {
      toast.error("Failed to load suppliers");
    }
  }, [error]);

  useEffect(() => {
    if (businessTypesError) {
      toast.error("Failed to load business types");
    }
  }, [businessTypesError]);

  // URL synchronization effect
  useEffect(() => {
    const params = new URLSearchParams();

    if (filters.search) params.set("search", filters.search);
    if (filters.status) params.set("status", filters.status);
    if (filters.business_type)
      params.set("business_type", filters.business_type);
    if (filters.preference) params.set("preference", filters.preference);
    if (currentPage > 1) params.set("page", currentPage.toString());
    if (pageSize !== 25) params.set("pageSize", pageSize.toString());

    setSearchParams(params);
  }, [filters, currentPage, pageSize, setSearchParams]);

  // Auto-refresh data when page becomes visible (user returns from other pages)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, refresh the suppliers list
        refreshSuppliersList();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Also refresh when the component mounts (user navigates to this page)
    refreshSuppliersList();

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []); // Empty dependency array - only run on mount/unmount

  // Filter management functions
  const updateFilter = (
    key: keyof SupplierFilters,
    value: string | string[]
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const clearFilters = () => {
    setFilters({
      search: "",
      status: "",
      business_type: "",
      preference: "",
    });
    setCurrentPage(1);
  };

  const hasActiveFilters = () => {
    return filters.search || filters.status || filters.business_type || filters.preference;
  };



  // Pagination
  const totalPages = Math.ceil(totalCount / pageSize);
  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalCount);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleViewModeChange = (mode: "list" | "card") => {
    setViewMode(mode);
    localStorage.setItem("supplier-list-view-mode", mode);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Delete handler
  const handleDeleteSupplier = async (supplierId: string) => {
    try {
      await deleteSupplier.mutateAsync(supplierId);
      // The useDeleteSupplier hook already handles the success toast and cache invalidation
      // No need to manually refresh the list as it's handled by the hook
    } catch (error) {
      console.error("Error deleting supplier:", error);
      // Error toast is already handled by the hook
    }
  };

  // Check permission and redirect if not authorized
  if (!hasPermission("supplier_management:view")) {
    return null;
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container className="space-t-6 p-0">
        <div className="pt-4 px-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <Heading level="h1" className="text-2xl">
                Suppliers
              </Heading>
              <Text className="text-muted-foreground">
                Manage your supplier directory and partnerships
              </Text>
            </div>

            <div className="flex gap-2">
              <OutlineButton
                size="small"
                onClick={() => setShowExportModal(true)}
                className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </OutlineButton>
              {hasPermission("supplier_management:create") && (
                <OutlineButton
                  size="small"
                  onClick={() => setShowImportModal(true)}
                  className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
                >
                  <Upload className="w-4 h-4" />
                  <span>Import</span>
                </OutlineButton>
              )}
              {hasPermission("supplier_management:create") && (
                <Button
                  variant="primary"
                  size="small"
                  onClick={() =>
                    navigate("/supplier-management/suppliers/create")
                  }
                  className="shadow-sm flex items-center gap-2 px-4 py-2 rounded-md transition-all"
                >
                  <PlusMini className="w-4 h-4" />
                  <span>Add Supplier</span>
                </Button>
              )}
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <Input
                placeholder="Search suppliers, contacts, or emails..."
                value={filters.search}
                onChange={(e) => updateFilter("search", e.target.value)}
                className="h-9 pl-2"
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => setShowFilters(!showFilters)}
                className="whitespace-nowrap bg-background border border-border shadow-sm hover:bg-muted flex items-center gap-2 px-3 py-2 rounded-md transition-all"
              >
                <Adjustments className="w-4 h-4 text-muted-foreground" />
                <span>{showFilters ? "Hide Filters" : "Show Filters"}</span>
              </Button>

              <div className="flex gap-2">
                <Button
                  variant={viewMode === "list" ? "primary" : "secondary"}
                  size="small"
                  onClick={() => handleViewModeChange("list")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-5 h-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </Button>

                <Button
                  variant={viewMode === "card" ? "primary" : "secondary"}
                  size="small"
                  onClick={() => handleViewModeChange("card")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-5 h-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>

          {showFilters && (
            <div className="bg-card border border-border rounded-lg p-4 shadow-sm mt-4">
              <div className="flex items-center justify-between mb-3 border-b border-border pb-2">
                <Text className="font-medium text-card-foreground">
                  Filter Suppliers
                </Text>
                <button
                  onClick={() => setShowFilters(false)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-1">
                  <Text className="font-medium text-sm text-card-foreground">
                    Status
                  </Text>
                  <Select
                    value={filters.status || "all"}
                    onValueChange={(value) => {
                      if (value === "all") {
                        updateFilter("status", "");
                      } else {
                        updateFilter("status", value);
                      }
                    }}
                    disabled={isLoading}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="All Statuses" />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="all">All Statuses</Select.Item>
                      <Select.Item value="Active">Active</Select.Item>
                      <Select.Item value="Inactive">Inactive</Select.Item>
                    </Select.Content>
                  </Select>
                </div>

                <div className="space-y-1">
                  <Text className="font-medium text-sm text-card-foreground">
                    Business Type
                  </Text>
                  <Select
                    value={filters.business_type || "all"}
                    onValueChange={(value) => {
                      if (value === "all") {
                        updateFilter("business_type", "");
                      } else {
                        updateFilter("business_type", value);
                      }
                    }}
                    disabled={isLoading || businessTypesLoading}
                  >
                    <Select.Trigger>
                      <Select.Value
                        placeholder={
                          businessTypesLoading
                            ? "Loading business types..."
                            : "All business types"
                        }
                      />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="all">All business types</Select.Item>
                      {businessTypeOptions.map((businessType) => (
                        <Select.Item
                          key={businessType.value}
                          value={businessType.value}
                        >
                          {businessType.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>

                <div className="space-y-1">
                  <Text className="font-medium text-sm text-card-foreground">
                    Preference
                  </Text>

                  <Select
                    value={filters.preference || "all"}
                    onValueChange={(value) => {
                      if (value === "all") {
                        updateFilter("preference", "");
                      } else {
                        updateFilter("preference", value);
                      }
                    }}
                    disabled={isLoading}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="All Preferences" />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="all">All Preferences</Select.Item>
                      <Select.Item value="Preferred">Preferred</Select.Item>
                      <Select.Item value="Backup">Backup</Select.Item>
                    </Select.Content>
                  </Select>

                </div>
              </div>

              <div className="flex justify-between mt-3 pt-3 border-t border-border">
                <div>
                  {hasActiveFilters() && (
                    <div className="text-sm text-muted-foreground">
                      <span className="font-medium">{suppliers.length}</span>{" "}
                      suppliers match your filters
                    </div>
                  )}
                </div>
                <div className="flex gap-3">
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={clearFilters}
                    className="bg-background border border-border shadow-sm hover:bg-muted flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-sm"
                    disabled={!hasActiveFilters() || isLoading}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-3.5 h-3.5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span>Clear</span>
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Suppliers Content - List or Card View */}
        {viewMode === "list" ? (
          <SupplierListView
            suppliers={suppliers}
            isLoading={isLoading}
            onSupplierClick={(id) => navigate(`/supplier-management/suppliers/${id}`)}
            onEditClick={(id) => navigate(`/supplier-management/suppliers/${id}/edit`)}
            onCreateClick={() => navigate("/supplier-management/suppliers/create")}
            onDeleteClick={handleDeleteSupplier}
            hasCreatePermission={hasPermission("supplier_management:create")}
            hasEditPermission={hasPermission("supplier_management:edit")}
            hasDeletePermission={hasPermission("supplier_management:delete")}
          />
        ) : (
          <SupplierCardView
            suppliers={suppliers}
            isLoading={isLoading}
            onSupplierClick={(id) => navigate(`/supplier-management/suppliers/${id}`)}
            onEditClick={(id) => navigate(`/supplier-management/suppliers/${id}/edit`)}
            onCreateClick={() => navigate("/supplier-management/suppliers/create")}
            onDeleteClick={handleDeleteSupplier}
            hasCreatePermission={hasPermission("supplier_management:create")}
            hasEditPermission={hasPermission("supplier_management:edit")}
            hasDeletePermission={hasPermission("supplier_management:delete")}
          />
        )}

        {/* Pagination */}
        {totalCount > 0 && (
          <div className="flex flex-col sm:flex-row items-center justify-between px-6 py-4 border-t gap-4">
            <div className="flex items-center gap-4">
              <Text size="small" className="text-ui-fg-subtle">
                Showing {startIndex} to {endIndex} of {totalCount} suppliers
              </Text>

              {/* Page Size Selector */}
              <div className="flex items-center gap-2">
                <Text size="small" className="text-ui-fg-subtle">
                  Show:
                </Text>
                <Select
                  value={pageSize.toString()}
                  onValueChange={(value) =>
                    handlePageSizeChange(parseInt(value))
                  }
                >
                  <Select.Trigger className="w-20">
                    <Select.Value />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="10">10</Select.Item>
                    <Select.Item value="25">25</Select.Item>
                    <Select.Item value="50">50</Select.Item>
                    <Select.Item value="100">100</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "primary" : "secondary"}
                      size="small"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Export Modal */}
        <SupplierExportModal
          open={showExportModal}
          onClose={() => setShowExportModal(false)}
        />

        {/* Import Modal */}
        <SupplierImportModal
          open={showImportModal}
          onClose={() => setShowImportModal(false)}
        />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Suppliers",
  icon: Buildings,
});

export default SupplierListPage;