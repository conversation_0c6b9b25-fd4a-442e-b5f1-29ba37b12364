import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Toaster,
  toast,
  Label,
  Switch,
} from "@camped-ai/ui";
import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Plus,
  Trash2,
  Save,
  Upload,
  CheckCircle,
  AlertCircle,
  File,
} from "lucide-react";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { MultiSelect } from "../../../../components/common/MultiSelect";
import { useRbac } from "../../../../hooks/use-rbac";
import { useCreateSupplier } from "../../../../hooks/vendor-management/use-suppliers";
import { useBusinessTypes } from "../../../../hooks/supplier-products-services/use-business-types";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import {
  REGIONS,
  TIMEZONES,
  LANGUAGES,
  PAYMENT_METHODS,
  PAYOUT_TERMS,
  CURRENCIES,
  SUPPLIER_STATUSES,
  SUPPLIER_TYPES,
} from "../../../../constants/supplier-form-options";


// Contact interface
interface Contact {
  id: string;
  name: string;
  email: string;
  phone_number?: string;
  is_whatsapp: boolean;
  is_primary: boolean;
}

// Form validation schema
const supplierSchema = z.object({
  // Basic Info
  name: z.string().min(1, "Supplier name is required"),
  supplier_type: z.enum(["Company", "Individual"], {
    required_error: "Supplier type is required",
  }),
  phone: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[\+]?[1-9][\d]{0,15}$/.test(val.replace(/[\s\-\(\)]/g, "")),
      "Please enter a valid phone number"
    ),
  email: z.string().email("Valid email is required"),
  website: z.string().url().optional().or(z.literal("")),
  handle: z.string().optional(),
  status: z
    .enum(["Active", "Inactive", "Pending Approval", "Suspended", "Terminated"])
    .default("Active"),
  business_type: z.string().min(1, "Business type is required"), // Now stores business type ID
  preference: z.enum(["Preferred", "Backup"]).optional(),
  region: z.string().optional(),
  timezone: z.string().optional(),
  language_preference: z.array(z.string()).optional(),
  payment_method: z.string().optional(),
  payout_terms: z.string().optional(),
  tax_id: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[A-Z0-9\-]{5,20}$/.test(val.replace(/[\s]/g, "")),
      "Please enter a valid tax ID (5-20 alphanumeric characters)"
    ),
  default_currency: z.string().default("CHF"),
  bank_account_details: z.string().optional(),
  categories: z.array(z.string()).optional(),

  // Address
  address_line_1: z.string().optional(),
  address_line_2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),

  // Contacts
  contacts: z
    .array(
      z.object({
        id: z.string(),
        name: z.string().min(1, "Contact name is required"),
        email: z.string().email("Valid email is required"),
        phone_number: z
          .string()
          .optional()
          .refine(
            (val) => !val || /^[\+]?[1-9][\d]{0,15}$/.test(val.replace(/[\s\-\(\)]/g, "")),
            "Please enter a valid phone number"
          ),
        is_whatsapp: z.boolean().default(false),
        is_primary: z.boolean(),
      })
    )
    .min(1, "At least one contact is required"),
});

type SupplierFormData = z.infer<typeof supplierSchema>;

interface PreUploadFile {
  file: File;
  id: string;
  progress: number;
  status: "pending" | "uploading" | "success" | "error";
  error?: string;
}

const CreateSupplierPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();

  // API hooks
  const createSupplier = useCreateSupplier();
  const { data: businessTypesData, isLoading: businessTypesLoading } =
    useBusinessTypes();
  const { data: categoriesData, isLoading: categoriesLoading } = useCategories({
    is_active: true,
  });

  // Form setup
  const form = useForm<SupplierFormData>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: "",
      supplier_type: "Company",
      phone: "",
      email: "",
      website: "",
      handle: "",
      status: "Active",
      business_type: "",
      preference: undefined,
      region: "",
      timezone: "",
      language_preference: [],
      payment_method: "",
      payout_terms: "",
      tax_id: "",
      default_currency: "CHF",
      bank_account_details: "",
      categories: [],
      address_line_1: "",
      address_line_2: "",
      city: "",
      state: "",
      postal_code: "",
      country: "",
      contacts: [
        {
          id: "1",
          name: "",
          email: "",
          phone_number: "",
          is_whatsapp: false,
          is_primary: true,
        },
      ],
    },
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = form;
  const watchedContacts = watch("contacts");
  const watchedName = watch("name");
  const watchedBusinessType = watch("business_type");

  const [newSupplierId, setNewSupplierId] = useState<string | null>(null);
  const [preUploadFiles, setPreUploadFiles] = useState<PreUploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [formDisabled, setFormDisabled] = useState(false);


  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ];

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  // For pending document uploads
  // const [pendingFiles, setPendingFiles] = useState<File[]>([]);
  // const [uploading, setUploading] = useState(false);
  // const [uploadError, setUploadError] = useState<string | null>(null);

  const handlePreFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handlePreFiles(Array.from(e.target.files));
    }
  };
  const handlePreFiles = (fileList: File[]) => {
    const newFiles: PreUploadFile[] = [];
    fileList.forEach((file) => {
      if (!allowedTypes.includes(file.type)) {
        toast.error(`File type not supported: ${file.name}`);
        return;
      }
      if (file.size > maxFileSize) {
        toast.error(`File too large: ${file.name} (max 10MB)`);
        return;
      }
      newFiles.push({
        file,
        id: Math.random().toString(36).substring(2, 11),
        progress: 0,
        status: "pending",
      });
    });
    setPreUploadFiles((prev) => [...prev, ...newFiles]);
  };
  const removePreFile = (id: string) => {
    setPreUploadFiles((prev) => prev.filter((f) => f.id !== id));
  };
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "uploading":
        return (
          <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <File className="h-4 w-4 text-gray-500" />;
    }
  };

  // Generate handle from name
  useEffect(() => {
    if (watchedName) {
      const handle = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setValue("handle", handle);
    }
  }, [watchedName, setValue]);

  // Check permission and redirect if not authorized
  useEffect(() => {
    if (!hasPermission("supplier_management:create")) {
      navigate("/supplier-management");
    }
  }, [hasPermission, navigate]);

  // Remove any remaining references to 'categories' in form setup, useEffect, and UI.

  // Contact management functions
  const addContact = () => {
    const newContact: Contact = {
      id: Date.now().toString(),
      name: "",
      email: "",
      phone_number: "",
      is_whatsapp: false,
      is_primary: false,
    };
    setValue("contacts", [...watchedContacts, newContact]);
  };

  const removeContact = (contactId: string) => {
    if (watchedContacts.length <= 1) {
      toast.error("At least one contact is required");
      return;
    }
    const updatedContacts = watchedContacts.filter((c) => c.id !== contactId);
    setValue("contacts", updatedContacts);
  };

  const setPrimaryContact = (contactId: string) => {
    const updatedContacts = watchedContacts.map((contact) => ({
      ...contact,
      is_primary: contact.id === contactId,
    }));
    setValue("contacts", updatedContacts);
  };



  // Create supplier
  const createSupplierData = async (data: SupplierFormData) => {
    try {
      // Transform contacts data - ensure proper structure and types
      const transformedContacts = data.contacts.map((contact) => ({
        name: contact.name,
        email: contact.email,
        phone_number: contact.phone_number && contact.phone_number.trim()
          ? String(contact.phone_number).trim()
          : undefined,
        is_whatsapp: Boolean(contact.is_whatsapp),
        is_primary: Boolean(contact.is_primary),
      }));

      // Helper function to only include non-empty values
      const cleanValue = (value: any) => {
        if (value === null || value === undefined || value === "") {
          return undefined;
        }
        return value;
      };

      // Transform data for API - use new format with contacts array
      const supplierData = {
        // Basic Info
        name: data.name,
        supplier_type: data.supplier_type,
        phone: cleanValue(data.phone),
        email: data.email,
        website: cleanValue(data.website),
        handle: cleanValue(data.handle),
        status: data.status,
        business_type: data.business_type,
        preference: data.preference,

        // Business Info
        region: cleanValue(data.region),
        timezone: cleanValue(data.timezone),
        language_preference: data.language_preference && data.language_preference.length > 0 ? data.language_preference : undefined,
        payment_method: cleanValue(data.payment_method),
        payout_terms: cleanValue(data.payout_terms),
        tax_id: cleanValue(data.tax_id),
        default_currency: data.default_currency,
        bank_account_details: cleanValue(data.bank_account_details),

        // Categories
        categories: data.categories && data.categories.length > 0 ? data.categories : undefined,

        // Address
        address_line_1: cleanValue(data.address_line_1),
        address_line_2: cleanValue(data.address_line_2),
        city: cleanValue(data.city),
        state: cleanValue(data.state),
        postal_code: cleanValue(data.postal_code),
        country: cleanValue(data.country),

        // Contacts with proper string conversion
        contacts: transformedContacts,
      };

      const result = await createSupplier.mutateAsync(supplierData);
      const newSupplierId = result?.supplier?.id;

      if (newSupplierId) {
        setNewSupplierId(newSupplierId);
        toast.success("Supplier created successfully!");

        // Upload pre-selected files after supplier creation
        if (preUploadFiles.length > 0) {
          setUploading(true);
          try {
            for (const preFile of preUploadFiles) {
              setPreUploadFiles((prev) =>
                prev.map((f) =>
                  f.id === preFile.id ? { ...f, status: "uploading" } : f
                )
              );

              const formData = new FormData();
              formData.append("files", preFile.file);

              const uploadResponse = await fetch(
                `/admin/supplier-management/suppliers/${newSupplierId}/documents`,
                {
                  method: "POST",
                  body: formData,
                  credentials: "include",
                }
              );

              if (uploadResponse.ok) {
                setPreUploadFiles((prev) =>
                  prev.map((f) =>
                    f.id === preFile.id
                      ? { ...f, status: "success", progress: 100 }
                      : f
                  )
                );
              } else {
                throw new Error(`Failed to upload ${preFile.file.name}`);
              }
            }
            toast.success("All documents uploaded successfully!");
          } catch (uploadError: any) {
            console.error("Upload error:", uploadError);
            toast.error(uploadError.message || "Failed to upload some documents");
          } finally {
            setUploading(false);
          }
        }

        // Navigate to supplier list after successful creation
        setTimeout(() => {
          navigate("/supplier-management/suppliers");
        }, 2000);
      }
    } catch (error: any) {
      console.error("Error creating supplier:", error);
      toast.error(error.message || "Failed to create supplier");
    } finally {
      setFormDisabled(false);
    }
  };

  // Form submission
  const onSubmit = async (data: SupplierFormData) => {
    setFormDisabled(true);
    try {
      // Ensure only one primary contact
      const primaryContacts = data.contacts.filter((c) => c.is_primary);
      if (primaryContacts.length !== 1) {
        toast.error("Exactly one contact must be marked as primary");
        setFormDisabled(false);
        return;
      }

      // Proceed with creation
      await createSupplierData(data);
    } catch (error: any) {
      console.error("Error in form submission:", error);
      toast.error(error.message || "Failed to create supplier");
      setFormDisabled(false);
    }
  };

  // Generate available categories from selected business type
  const availableCategories = React.useMemo(() => {
    if (!businessTypesData?.lookups || !watchedBusinessType || !categoriesData?.categories) return [];

    // Find the selected business type by ID (watchedBusinessType now contains the ID)
    const selectedBusinessType = businessTypesData.lookups.find(
      (businessType) => businessType.id === watchedBusinessType
    );

    if (!selectedBusinessType?.parsed_value?.service_categories) return [];

    // Get category IDs from the selected business type
    const serviceCategoryIds = selectedBusinessType.parsed_value.service_categories || [];

    // Filter categories to only show those available for this business type
    const filteredCategories = categoriesData.categories.filter(
      (category) => serviceCategoryIds.includes(category.id)
    );

    return filteredCategories.map((category) => ({
      value: category.id, // Store category ID as value
      label: category.name, // Display category name as label
    }));
  }, [businessTypesData, watchedBusinessType, categoriesData]);

  // Helper function to handle category selection change
  const handleCategoryChange = (selectedCategoryIds: string[]) => {
    setValue("categories", selectedCategoryIds);
  };



  // Clear categories when business type changes
  React.useEffect(() => {
    setValue("categories", []);
  }, [watchedBusinessType, setValue]);

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />

      <Container>
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate("/supplier-management/suppliers")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <Heading level="h1">Create Supplier</Heading>
        </div>

        {/* Form */}
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="space-y-8"
          style={{
            pointerEvents: formDisabled || uploading ? "none" : "auto",
            opacity: formDisabled || uploading ? 0.6 : 1,
          }}
        >
          {/* Basic Information Section */}
          <div className="bg-white rounded-lg border p-6">
            <Heading level="h2" className="mb-4">
              Basic Information
            </Heading>

            <div className="space-y-4">
              {/* Supplier Type */}
              <div>
                <Label>Supplier Type *</Label>
                <Controller
                  name="supplier_type"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select supplier type" />
                      </Select.Trigger>
                      <Select.Content>
                        {SUPPLIER_TYPES.map((type) => (
                          <Select.Item key={type.value} value={type.value}>
                            {type.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.supplier_type && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.supplier_type.message}
                  </Text>
                )}
              </div>

              {/* Basic Fields Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="name"
                        placeholder="Enter supplier name"
                        className={errors.name ? "border-red-500" : ""}
                      />
                    )}
                  />
                  {errors.name && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.name.message}
                    </Text>
                  )}
                </div>

                <div>
                  <Label htmlFor="handle">Handle</Label>
                  <Controller
                    name="handle"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="handle"
                        placeholder="Auto-generated from name"
                        readOnly
                        className="bg-gray-50"
                      />
                    )}
                  />
                </div>

                <div>
                  <Label htmlFor="email">Email *</Label>
                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className={errors.email ? "border-red-500" : ""}
                      />
                    )}
                  />
                  {errors.email && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.email.message}
                    </Text>
                  )}
                </div>

                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Controller
                    name="phone"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="phone"
                        type="tel"
                        placeholder="+****************"
                      />
                    )}
                  />
                  {errors.phone && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.phone.message}
                    </Text>
                  )}
                </div>

                <div>
                  <Label htmlFor="website">Website</Label>
                  <Controller
                    name="website"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="website"
                        type="url"
                        placeholder="https://www.example.com"
                        className={errors.website ? "border-red-500" : ""}
                      />
                    )}
                  />
                  {errors.website && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.website.message}
                    </Text>
                  )}
                </div>

                <div>
                  <Label>Status</Label>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <Select.Trigger>
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {SUPPLIER_STATUSES.map((status) => (
                            <Select.Item
                              key={status.value}
                              value={status.value}
                            >
                              {status.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Business Information Section */}
          <div className="bg-white rounded-lg border p-6">
            <Heading level="h2" className="mb-4">
              Business Information
            </Heading>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Business Type *</Label>
                <Controller
                  name="business_type"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={businessTypesLoading}
                    >
                      <Select.Trigger>
                        <Select.Value
                          placeholder={
                            businessTypesLoading
                              ? "Loading..."
                              : "Select business type"
                          }
                        />
                      </Select.Trigger>
                      <Select.Content>
                        {businessTypesData?.lookups
                          ?.filter(
                            (businessType) =>
                              businessType.parsed_value?.is_active
                          )
                          ?.map((businessType) => (
                            <Select.Item
                              key={businessType.id}
                              value={businessType.id}
                            >
                              {businessType.parsed_value?.name ||
                                businessType.value}
                            </Select.Item>
                          ))}
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.business_type && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.business_type.message}
                  </Text>
                )}
              </div>

              <div>
                <Label>Preference</Label>
                <Controller
                  name="preference"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select preference" />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="Preferred">Preferred</Select.Item>
                        <Select.Item value="Backup">Backup</Select.Item>
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>
            </div>

            {/* Categories Field - Full Width */}
            <div>
              <Label>Service Categories</Label>
              <Controller
                name="categories"
                control={control}
                render={({ field }) => (
                  <MultiSelect
                    options={availableCategories}
                    selectedValues={field.value || []}
                    onChange={(selectedValues) => {
                      field.onChange(selectedValues);
                      handleCategoryChange(selectedValues);
                    }}
                    placeholder={
                      !watchedBusinessType
                        ? "Select a business type first"
                        : categoriesLoading
                        ? "Loading categories..."
                        : availableCategories.length === 0
                        ? "No categories available for this business type"
                        : "Select service categories..."
                    }
                    disabled={!watchedBusinessType || categoriesLoading}
                    showSelectAll={true}
                    showSelectedTags={true}
                    className="my-1"
                  />
                )}
              />
              <Text className="text-ui-fg-subtle text-sm mt-1">
                Select the service categories this supplier can provide. Categories are filtered based on the selected business type.
              </Text>
              {errors.categories && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.categories.message}
                </Text>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Region</Label>
                <Controller
                  name="region"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select region" />
                      </Select.Trigger>
                      <Select.Content>
                        {REGIONS.map((region) => (
                          <Select.Item key={region.value} value={region.value}>
                            {region.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label>Timezone</Label>
                <Controller
                  name="timezone"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select timezone" />
                      </Select.Trigger>
                      <Select.Content>
                        {TIMEZONES.map((timezone) => (
                          <Select.Item
                            key={timezone.value}
                            value={timezone.value}
                          >
                            {timezone.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label>Language Preference</Label>
                <Controller
                  name="language_preference"
                  control={control}
                  render={({ field }) => (
                    <MultiSelect
                      options={LANGUAGES}
                      selectedValues={field.value || []}
                      onChange={field.onChange}
                      placeholder="Select languages"
                    />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="payment_method">Payment Method</Label>
                <Controller
                  name="payment_method"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select payment method" />
                      </Select.Trigger>
                      <Select.Content>
                        {PAYMENT_METHODS.map((method) => (
                          <Select.Item key={method.value} value={method.value}>
                            {method.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label>Payment Terms</Label>
                <Controller
                  name="payout_terms"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select payment terms" />
                      </Select.Trigger>
                      <Select.Content>
                        {PAYOUT_TERMS.map((term) => (
                          <Select.Item key={term.value} value={term.value}>
                            {term.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div>
                <Label htmlFor="tax_id">Tax ID</Label>
                <Controller
                  name="tax_id"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="tax_id"
                      placeholder="Enter tax identification number"
                    />
                  )}
                />
                {errors.tax_id && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.tax_id.message}
                  </Text>
                )}
              </div>

              <div>
                <Label>Default Currency</Label>
                <Controller
                  name="default_currency"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value />
                      </Select.Trigger>
                      <Select.Content>
                        {CURRENCIES.map((currency) => (
                          <Select.Item
                            key={currency.value}
                            value={currency.value}
                          >
                            {currency.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>
            </div>

            <div className="mt-4">
              <Label htmlFor="bank_account_details">Bank Account Details</Label>
              <Controller
                name="bank_account_details"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    id="bank_account_details"
                    placeholder="Enter bank account information"
                    rows={3}
                  />
                )}
              />
            </div>

            {/* Remove all logic and UI related to categories */}
          </div>

          {/* Address Section */}
          <div className="bg-white rounded-lg border p-6">
            <Heading level="h2" className="mb-4">
              Address
            </Heading>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="address_line_1">Address Line 1</Label>
                <Controller
                  name="address_line_1"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="address_line_1"
                      placeholder="Street address"
                    />
                  )}
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="address_line_2">Address Line 2</Label>
                <Controller
                  name="address_line_2"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="address_line_2"
                      placeholder="Apartment, suite, etc. (optional)"
                    />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="city">City</Label>
                <Controller
                  name="city"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} id="city" placeholder="City" />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="state">State/Province</Label>
                <Controller
                  name="state"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="state"
                      placeholder="State or Province"
                    />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="postal_code">Postal Code</Label>
                <Controller
                  name="postal_code"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="postal_code"
                      placeholder="Postal/ZIP code"
                    />
                  )}
                />
              </div>

              <div>
                <Label htmlFor="country">Country</Label>
                <Controller
                  name="country"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} id="country" placeholder="Country" />
                  )}
                />
              </div>
            </div>
          </div>

          {/* Contacts Section */}
          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center justify-between mb-4">
              <Heading level="h2">Contacts</Heading>
              <Button
                type="button"
                variant="secondary"
                size="small"
                onClick={addContact}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Contact
              </Button>
            </div>

            <div className="space-y-4">
              {watchedContacts.map((contact, index) => (
                <div key={contact.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <Text weight="plus">Contact {index + 1}</Text>
                    <div className="flex items-center gap-2">
                      <Controller
                        name={`contacts.${index}.is_primary`}
                        control={control}
                        render={({ field }) => (
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              name="primary_contact"
                              checked={field.value}
                              onChange={() => setPrimaryContact(contact.id)}
                              className="text-blue-600"
                            />
                            <Text size="small">Primary</Text>
                          </label>
                        )}
                      />
                      {watchedContacts.length > 1 && (
                        <Button
                          type="button"
                          variant="danger"
                          size="small"
                          onClick={() => removeContact(contact.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`contact_name_${contact.id}`}>
                        Name *
                      </Label>
                      <Controller
                        name={`contacts.${index}.name`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_name_${contact.id}`}
                            placeholder="Contact name"
                            className={
                              errors.contacts?.[index]?.name
                                ? "border-red-500"
                                : ""
                            }
                          />
                        )}
                      />
                      {errors.contacts?.[index]?.name && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.contacts[index].name?.message}
                        </Text>
                      )}
                    </div>

                    <div>
                      <Label htmlFor={`contact_email_${contact.id}`}>
                        Email *
                      </Label>
                      <Controller
                        name={`contacts.${index}.email`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_email_${contact.id}`}
                            type="email"
                            placeholder="<EMAIL>"
                            className={
                              errors.contacts?.[index]?.email
                                ? "border-red-500"
                                : ""
                            }
                          />
                        )}
                      />
                      {errors.contacts?.[index]?.email && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.contacts[index].email?.message}
                        </Text>
                      )}
                    </div>
                  </div>

                  {/* Phone Number and WhatsApp */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <Label htmlFor={`contact_phone_${contact.id}`}>
                        Phone Number
                      </Label>
                      <Controller
                        name={`contacts.${index}.phone_number`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            id={`contact_phone_${contact.id}`}
                            type="tel"
                            placeholder="+****************"
                          />
                        )}
                      />
                      {errors.contacts?.[index]?.phone_number && (
                        <Text size="small" className="text-red-600 mt-1">
                          {errors.contacts[index]?.phone_number?.message}
                        </Text>
                      )}
                    </div>

                    <div className="flex items-center space-x-3 mt-6">
                      <Controller
                        name={`contacts.${index}.is_whatsapp`}
                        control={control}
                        render={({ field }) => (
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        )}
                      />
                      <Label htmlFor={`contact_whatsapp_${contact.id}`}>
                        WhatsApp Available
                      </Label>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Documents Section - match edit form UI */}
          <div className="bg-white rounded-lg border p-6 mt-8">
            <Heading level="h2" className="mb-4">
              Documents
            </Heading>
            <Text weight="plus">Supplier Documents</Text>
            <div className="mt-2">
              <label className="text-blue-600 hover:text-blue-700 cursor-pointer">
                <Button
                  variant="secondary"
                  size="small"
                  type="button"
                  className="mr-2"
                  disabled={formDisabled || uploading}
                  onClick={() => {
                    if (!formDisabled && !uploading && fileInputRef.current) {
                      fileInputRef.current.click();
                    }
                  }}
                >
                  <Upload className="h-4 w-4" /> Upload Documents
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                  onChange={handlePreFileInput}
                  className="hidden"
                  disabled={formDisabled || uploading}
                />
              </label>
            </div>
            {preUploadFiles.length > 0 ? (
              <div className="space-y-2 mt-4">
                <Text weight="plus">Selected Files</Text>
                {preUploadFiles.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(file.status)}
                      <div>
                        <Text size="small" weight="plus">
                          {file.file.name}
                        </Text>
                        <Text size="small" className="text-gray-500">
                          {formatFileSize(file.file.size)}
                        </Text>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="transparent"
                        size="small"
                        onClick={() => removePreFile(file.id)}
                        disabled={uploading || file.status === "uploading"}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                      {file.status === "error" && (
                        <Text size="small" className="text-red-600">
                          {file.error}
                        </Text>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Text className="text-gray-500 mt-2">
                No documents uploaded yet.
              </Text>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => navigate("/supplier-management/suppliers")}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || formDisabled || uploading}
              className="flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              {isSubmitting ? "Creating..." : "Create Supplier"}
            </Button>
          </div>
        </form>


      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Create Supplier",
});

export default CreateSupplierPage;
