import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Edit,
  MapPin,
  Building,
  ArrowLeft as ArrowLeftLucide,
  FileText,
  Package,
  DollarSign,
  User,
  Clock,
  Trash2,
  ChevronDown,
  ChevronUp,
  Star,
  Phone,
  Mail,
  CreditCard,
  Globe,
  Copy,
  Calendar,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  IconButton,
  Tabs,
  Prompt,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import {
  useSupplier,
  useDeleteSupplier,
} from "../../../../hooks/vendor-management/use-suppliers";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import { useRbac } from "../../../../hooks/use-rbac";
import { useBusinessTypes } from "../../../../hooks/supplier-products-services/use-business-types";
import { DocumentUpload } from "../../../../components/vendor-management";
import {
  REGIONS,
  PAYMENT_METHODS,
  LANGUAGES,
} from "../../../../constants/supplier-form-options";
import {
  findOptionLabelWithFallback,
  getPayoutTermsLabel,
} from "../../../../utils/form-helpers";

const SupplierDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const { data: businessTypesData } = useBusinessTypes();
  const [activeTab, setActiveTab] = useState("details");
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    company: true,
    contact: false,
    payment: false,
    additional: false,
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // Copy to clipboard functionality
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied", {
        description: `${type} copied to clipboard`,
      });
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy", {
        description: "Unable to copy to clipboard",
      });
    }
  };

  // Use the supplier management hooks
  const { data: supplierData, isLoading } = useSupplier(id!);
  const { data: categoriesData } = useCategories({ is_active: true });
  const deleteSupplier = useDeleteSupplier();
  const supplier : any = supplierData?.supplier;

  // Helper function to get status badge variant
  const getStatusBadgeVariant = (status: string): "green" | "red" | "grey" => {
    const normalizedStatus = status?.toLowerCase();
    switch (normalizedStatus) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      default:
        return "grey";
    }
  };

  // Helper function to get preference badge variant
  const getPreferenceBadgeVariant = (preference: string): "blue" | "grey" => {
    switch (preference) {
      case "Preferred":
        return "blue";
      case "Backup":
        return "grey";
      default:
        return "grey";
    }
  };

  // Helper function to get business type name from ID
  const getBusinessTypeName = (businessTypeId: string | null | undefined): string => {
    if (!businessTypeId || !businessTypesData?.lookups) return businessTypeId || "Unknown";

    const businessType = businessTypesData.lookups.find(
      (bt) => bt.id === businessTypeId
    );

    return businessType?.parsed_value?.name || businessTypeId;
  };

  // Helper function to get primary contact
  const getPrimaryContact = (supplier: any) => {
    if (!supplier?.contacts || supplier.contacts.length === 0) {
      return null;
    }
    return (
      supplier.contacts.find((contact: any) => contact.is_primary) ||
      supplier.contacts[0]
    );
  };

  // Helper function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Helper function to get category names from IDs
  const getCategoryNames = (categoryIds: string[]): string[] => {
    if (!categoriesData?.categories || !Array.isArray(categoryIds)) return [];
    return categoryIds
      .map((id) => {
        const category = categoriesData.categories.find((cat) => cat.id === id);
        return category ? category.name : null;
      })
      .filter((name): name is string => name !== null);
  };

  // Collapsible Section Component
  const CollapsibleSection = ({
    title,
    icon: Icon,
    sectionKey,
    children,
  }: {
    title: string;
    icon: any;
    sectionKey: keyof typeof expandedSections;
    children: React.ReactNode;
  }) => {
    const isExpanded = expandedSections[sectionKey];

    return (
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <button
          onClick={() => toggleSection(sectionKey)}
          className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center gap-3">
            <Icon className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          </div>
          {isExpanded ? (
            <ChevronUp className="h-5 w-5 text-gray-400" />
          ) : (
            <ChevronDown className="h-5 w-5 text-gray-400" />
          )}
        </button>
        {isExpanded && (
          <div className="px-6 pb-6 border-t border-gray-100">{children}</div>
        )}
      </div>
    );
  };

  const handleDeleteSupplier = () => {
    if (!supplier) return;
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteSupplier = () => {
    if (!supplier) return;

    deleteSupplier.mutate(supplier.id, {
      onSuccess: () => {
        setDeleteConfirmOpen(false);
        // Navigate back to suppliers list
        navigate("/supplier-management/suppliers");
      },
    });
  };

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <div className="h-screen flex items-center justify-center bg-background">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </>
    );
  }

  if (!supplier) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="py-8">
          <div className="text-center py-12 bg-muted rounded-lg">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <Text className="text-muted-foreground mb-4">
              Supplier not found
            </Text>
            <Button
              variant="primary"
              size="small"
              onClick={() => navigate("/supplier-management/suppliers")}
            >
              Back to Suppliers
            </Button>
          </div>
        </Container>
      </>
    );
  }

  const primaryContact = getPrimaryContact(supplier);

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <div className="flex flex-row items-center justify-between">
        <IconButton onClick={() => navigate("/supplier-management/suppliers")}>
          <ArrowLeftLucide className="w-4 h-4" />
        </IconButton>

        <div className="flex flex-row items-center gap-2">
          {hasPermission("supplier_management:edit") && (
            <Button
              variant="secondary"
              size="small"
              onClick={() =>
                navigate(`/supplier-management/suppliers/${supplier.id}/edit`)
              }
            >
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
          )}
          {hasPermission("supplier_management:delete") && (
            <Button
              variant="secondary"
              size="small"
              onClick={handleDeleteSupplier}
              className="text-red-600"
            >
              <Trash2 className="w-4 h-4 mr-2 " />
              Delete
            </Button>
          )}
        </div>
      </div>

      <Container className="py-6">
        {/* Header Section */}
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-2">
            <Heading level="h1" className="text-3xl font-bold text-gray-900">
              {supplier.name}
            </Heading>
            <Badge
              color={getStatusBadgeVariant(supplier.status)}
              className="px-3 py-1 text-sm font-medium rounded-full capitalize"
            >
              {supplier.status}
            </Badge>
          </div>
          <div className="flex items-center gap-2 text-gray-500">
            <Calendar className="w-4 h-4" />
            <Text className="text-sm">
              Last updated: {formatDate(supplier.updated_at)}
            </Text>
          </div>
        </div>

        {/* Key Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center gap-2 mb-2">
              <Building className="w-4 h-4 text-blue-600" />
              <Text className="text-sm font-medium text-muted-foreground">
                Business Type
              </Text>
            </div>
            <Text className="font-semibold">{getBusinessTypeName(supplier.business_type)}</Text>
          </div>



          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center gap-2 mb-2">
              <Star className="w-4 h-4 text-blue-600" />
              <Text className="text-sm font-medium text-muted-foreground">
                Preference
              </Text>
            </div>
            <div>
              {(supplier as any).preference ? (
                <Text className="font-semibold">
                  {(supplier as any).preference}
                </Text>
              ) : (
                <Text className="font-semibold text-gray-400">Not Set</Text>
              )}
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center gap-2 mb-2">
              <User className="w-4 h-4 text-purple-600" />
              <Text className="text-sm font-medium text-muted-foreground">
                Primary Contact
              </Text>
            </div>
            <Text className="font-semibold">{primaryContact?.name || "—"}</Text>
          </div>

          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="w-4 h-4 text-yellow-600" />
              <Text className="text-sm font-medium text-muted-foreground">
                Default Currency
              </Text>
            </div>
            <Text className="font-semibold">
              {(supplier as any).default_currency || "—"}
            </Text>
          </div>

          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-red-600" />
              <Text className="text-sm font-medium text-muted-foreground">
                Payment Terms
              </Text>
            </div>
            <Text className="font-semibold">
              {getPayoutTermsLabel((supplier as any).payout_terms || "")}
            </Text>
          </div>
        </div>

        {/* Tabs Section */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <Tabs.List className="mb-6">
            <Tabs.Trigger value="details">
              <FileText className="w-4 h-4 mr-2" />
              Details
            </Tabs.Trigger>
            <Tabs.Trigger value="documents">
              <FileText className="w-4 h-4 mr-2" />
              Documents
            </Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content value="details">
            <div className="space-y-6">
              {/* Company Information Section */}
              <CollapsibleSection
                title="Company Information"
                icon={Building}
                sectionKey="company"
              >
                <div className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Supplier Type
                      </Text>
                      <Text>
                        {(supplier as any).supplier_type === "Company"
                          ? "Company"
                          : "Individual"}
                      </Text>
                    </div>
                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Name
                      </Text>
                      <Text>
                        {supplier?.name || "—"}
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Handle
                      </Text>
                      <Text>
                        {supplier?.handle || "—"}
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Email
                      </Text>
                      <Text>
                        {supplier?.email ? supplier?.email : "—"}
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Phone
                      </Text>
                      <div className="flex gap-2 items-center">
                        <Text>
                        {supplier?.phone ? supplier?.phone : "—"}
                      </Text>
                      { supplier?.phone && (
                        <Copy
                        className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                        onClick={() =>
                          copyToClipboard(
                            (supplier as any)?.phone!,
                            "Phone Number"
                          )
                        }
                      />)}
                      </div>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Business Category
                      </Text>
                      <Text>{getBusinessTypeName(supplier.business_type) || "—"}</Text>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Preference
                      </Text>
                      <div className="flex items-center gap-2">

                        <Text>
                          {(supplier as any).preference || "—"}
                        </Text>
                      </div>
                    </div>
                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Website
                      </Text>
                      {supplier.website ? (
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-blue-600" />
                          <a
                            href={supplier.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {supplier.website}
                          </a>
                        </div>
                      ) : (
                        <Text>—</Text>
                      )}
                    </div>

                    <div className="md:col-span-2">
                      <Text className="text-sm font-medium text-muted-foreground mb-3">
                        Address
                      </Text>
                      <div className="flex items-start gap-3">
                        <MapPin className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                        <div className="space-y-1">
                          {supplier.address_line_1 ? (
                            <>
                              <Text className="font-medium text-gray-900">
                                {supplier.address_line_1}
                              </Text>
                              {supplier.address_line_2 && (
                                <Text className="font-medium text-gray-900">
                                  {supplier.address_line_2}
                                </Text>
                              )}
                              <Text className="font-medium text-gray-900">
                                {[
                                  supplier.city,
                                  supplier.state && supplier.postal_code
                                    ? `${supplier.state} ${supplier.postal_code}`
                                    : supplier.state || supplier.postal_code
                                ]
                                  .filter(Boolean)
                                  .join(", ")}
                              </Text>
                              {supplier.country && (
                                <Text className="font-medium text-gray-900">
                                  {supplier.country}
                                </Text>
                              )}
                            </>
                          ) : (
                            <Text className="text-gray-500">No address provided</Text>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CollapsibleSection>

              {/* Contact Information Section */}
              <CollapsibleSection
                title="Contact Information"
                icon={User}
                sectionKey="contact"
              >
                <div className="pt-4 space-y-6">
                  {(supplier as any).contacts &&
                    (supplier as any).contacts.length > 0 ? (
                    (supplier as any).contacts.map(
                      (contact: any, index: number) => (
                        <div key={contact.id || index}>
                          <div className="flex items-center gap-2 mb-3">
                            <Text className="font-medium">{contact.name}</Text>
                            {contact.is_primary && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                Primary
                              </span>
                            )}
                            {contact.is_whatsapp && (
                              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                WhatsApp
                              </span>
                            )}
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <Text className="text-sm">{contact.email}</Text>
                              <Copy
                                className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                                onClick={() =>
                                  copyToClipboard(contact.email, "Email")
                                }
                              />
                            </div>
                            {contact.phone_number && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 text-gray-400" />
                                <Text className="text-sm">
                                  {contact.phone_number}
                                </Text>
                                <Copy
                                  className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                                  onClick={() =>
                                    copyToClipboard(
                                      contact.phone_number,
                                      "Phone number"
                                    )
                                  }
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    )
                  ) : (
                    <div className="text-center py-8">
                      <Text className="text-muted-foreground">
                        No contact information available
                      </Text>
                    </div>
                  )}
                </div>
              </CollapsibleSection>

              {/* Payment Information Section */}
              <CollapsibleSection
                title="Payment Information"
                icon={CreditCard}
                sectionKey="payment"
              >
                <div className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Payment Method
                      </Text>
                      <Text>
                        {findOptionLabelWithFallback(
                          PAYMENT_METHODS,
                          (supplier as any).payment_method || ""
                        )}
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Payment Terms
                      </Text>
                      <Text>
                        {getPayoutTermsLabel(
                          (supplier as any).payout_terms || ""
                        )}
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Tax ID
                      </Text>
                      <div className="flex items-center gap-2">
                        <Text>{supplier.tax_id || "—"}</Text>
                        {supplier.tax_id && (
                          <Copy
                            className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                            onClick={() =>
                              copyToClipboard(supplier.tax_id!, "Tax ID")
                            }
                          />
                        )}
                      </div>
                    </div>
                  </div>

                  {(supplier as any).bank_account_details && (
                    <div className="mt-6">
                      <Text className="text-sm font-medium text-muted-foreground mb-3">
                        Bank Account Details
                      </Text>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center gap-2 justify-between">
                          <Text className="text-sm">
                            {(supplier as any).bank_account_details}
                          </Text>
                          <Copy
                            className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                            onClick={() =>
                              copyToClipboard(
                                (supplier as any).bank_account_details!,
                                "Account details"
                              )
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CollapsibleSection>

              {/* Additional Information Section */}
              <CollapsibleSection
                title="Additional Information"
                icon={FileText}
                sectionKey="additional"
              >
                <div className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Timezone
                      </Text>
                      <Text>{(supplier as any).timezone || "—"}</Text>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-2">
                        Language
                      </Text>
                      <div className="flex flex-wrap gap-2">
                        {(supplier as any).language_preference &&
                          (supplier as any).language_preference.length > 0 ? (
                          (supplier as any).language_preference.map((langCode: string) => {
                            const language = LANGUAGES.find(
                              (lang) =>
                                lang.value.toLowerCase() === langCode.toLowerCase() ||
                                lang.label.toLowerCase() === langCode.toLowerCase()
                            );
                            const displayName = language ? language.label : langCode;

                            return (
                              <Badge
                                key={langCode}
                                color="blue"
                                className="rounded-full text-xs px-2 py-1"
                              >
                                {displayName}
                              </Badge>
                            );
                          })
                        ) : (
                          <Text className="text-gray-500">No languages specified</Text>
                        )}
                      </div>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Region
                      </Text>
                      <Text>
                        {findOptionLabelWithFallback(
                          REGIONS,
                          (supplier as any).region || ""
                        )}
                      </Text>
                    </div>

                    <div>
                      <Text className="text-sm font-medium text-muted-foreground mb-1">
                        Default Currency
                      </Text>
                      <Text>{(supplier as any).default_currency || "—"}</Text>
                    </div>
                  </div>

                  {(supplier as any).categories &&
                    (supplier as any).categories.length > 0 && (
                      <div className="mt-6">
                        <Text className="text-sm font-medium text-muted-foreground mb-3">
                          Service Categories
                        </Text>
                        <div className="flex flex-wrap gap-2">
                          {(() => {
                            const categoryNames = getCategoryNames((supplier as any).categories || []);
                            return categoryNames.map((categoryName, index) => (
                              <Badge
                                key={index}
                                color="blue"
                                className="rounded-full text-sm px-3 py-1.5 font-medium"
                              >
                                {categoryName}
                              </Badge>
                            ));
                          })()}
                          {getCategoryNames((supplier as any).categories || []).length === 0 && (
                            <Text className="text-gray-400 text-sm">No categories assigned</Text>
                          )}
                        </div>
                      </div>
                    )}
                </div>
              </CollapsibleSection>
            </div>
          </Tabs.Content>

          <Tabs.Content value="documents">
            <div className="bg-card rounded-lg border p-6">
              <DocumentUpload supplierId={id!} />
            </div>
          </Tabs.Content>
        </Tabs>
      </Container>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{supplier?.name}"? This action
              cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setDeleteConfirmOpen(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={confirmDeleteSupplier}
              disabled={deleteSupplier.isPending}
            >
              {deleteSupplier.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Details",
});

export default SupplierDetailPage;
