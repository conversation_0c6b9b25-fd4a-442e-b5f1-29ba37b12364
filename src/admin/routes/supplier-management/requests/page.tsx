import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Calendar } from "lucide-react";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import CalendarComponent from "../../../components/supplier-management/calendar/calendar";

const AvailabilityPage = () => {
  return (
    <>
      <PermissionBasedSidebarHider />
      <CalendarComponent />
    </>
  );
};

export const config = defineRouteConfig({
  label: "On Requests",
  icon: Calendar,
});

export default AvailabilityPage;
