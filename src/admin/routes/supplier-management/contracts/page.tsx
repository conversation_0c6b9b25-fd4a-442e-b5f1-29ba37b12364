import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  DocumentText,
  PlusMini,
  MagnifyingGlass,
} from "@camped-ai/icons";
import { Eye, FileText, Calendar, DollarSign, AlertCircle } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";

interface SupplierContract {
  id: string;
  contract_number: string;
  supplier_name: string;
  supplier_id: string;
  contract_type: string;
  status: string;
  total_value: number;
  currency: string;
  start_date: string;
  end_date: string;
  payment_terms: string;
  auto_renewal: boolean;
  payments_count: number;
}

const SupplierContractsPage = () => {
  const [contracts, setContracts] = useState<SupplierContract[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    fetchContracts();
  }, []);

  const fetchContracts = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call to supplier management module
      // const response = await fetch("/admin/supplier-management/contracts");
      // const data = await response.json();
      
      // Mock data for demonstration
      const mockContracts: SupplierContract[] = [
        {
          id: "contract_01HJQK1JMQZX8TGQR5VWXYZ123",
          contract_number: "SC-2024-001",
          supplier_name: "Premium Catering Co.",
          supplier_id: "supplier_01HJQK1JMQZX8TGQR5VWXYZ123",
          contract_type: "service_agreement",
          status: "active",
          total_value: 50000.00,
          currency: "USD",
          start_date: "2024-01-01T00:00:00Z",
          end_date: "2024-12-31T23:59:59Z",
          payment_terms: "net_30",
          auto_renewal: true,
          payments_count: 12,
        },
        {
          id: "contract_01HJQK1JMQZX8TGQR5VWXYZ456",
          contract_number: "SC-2024-002",
          supplier_name: "Swift Transportation LLC",
          supplier_id: "supplier_01HJQK1JMQZX8TGQR5VWXYZ456",
          contract_type: "master_agreement",
          status: "pending",
          total_value: 25000.00,
          currency: "USD",
          start_date: "2024-04-01T00:00:00Z",
          end_date: "2025-03-31T23:59:59Z",
          payment_terms: "net_15",
          auto_renewal: false,
          payments_count: 0,
        },
        {
          id: "contract_01HJQK1JMQZX8TGQR5VWXYZ789",
          contract_number: "SC-2024-003",
          supplier_name: "Mountain Adventure Tours",
          supplier_id: "supplier_01HJQK1JMQZX8TGQR5VWXYZ789",
          contract_type: "product_supply",
          status: "expired",
          total_value: 15000.00,
          currency: "USD",
          start_date: "2023-06-01T00:00:00Z",
          end_date: "2024-05-31T23:59:59Z",
          payment_terms: "net_45",
          auto_renewal: false,
          payments_count: 8,
        },
      ];

      setContracts(mockContracts);
    } catch (error) {
      console.error("Error fetching supplier contracts:", error);
      toast.error("Failed to load supplier contracts");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    // TODO: Implement actual search functionality
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "green";
      case "pending":
        return "orange";
      case "expired":
        return "red";
      case "terminated":
        return "red";
      case "draft":
        return "grey";
      default:
        return "grey";
    }
  };

  const getContractTypeBadgeVariant = (type: string) => {
    switch (type.toLowerCase()) {
      case "service_agreement":
        return "blue";
      case "master_agreement":
        return "purple";
      case "product_supply":
        return "green";
      default:
        return "grey";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const isContractExpiringSoon = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Supplier Contracts</Heading>
            <Text className="text-ui-fg-subtle">
              Manage contracts and agreements with suppliers
            </Text>
          </div>
          <div className="flex items-center gap-x-2">
            <Button
              size="small"
              onClick={() => {
                toast.info("Create contract form coming soon");
              }}
            >
              <PlusMini />
              Create Contract
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="px-6 py-4">
          <Input
            placeholder="Search contracts by number, supplier, or type..."
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            prefix={<MagnifyingGlass className="h-4 w-4" />}
          />
        </div>

        {/* Contracts Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Contract</Table.HeaderCell>
                <Table.HeaderCell>Supplier</Table.HeaderCell>
                <Table.HeaderCell>Type</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell>Value</Table.HeaderCell>
                <Table.HeaderCell>Start Date</Table.HeaderCell>
                <Table.HeaderCell>End Date</Table.HeaderCell>
                <Table.HeaderCell>Payment Terms</Table.HeaderCell>
                <Table.HeaderCell>Actions</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {loading ? (
                <Table.Row>
                  <Table.Cell colSpan={9} className="text-center py-8">
                    Loading supplier contracts...
                  </Table.Cell>
                </Table.Row>
              ) : contracts.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={9} className="text-center py-8">
                    <div className="flex flex-col items-center gap-2">
                      <DocumentText className="h-12 w-12 text-gray-400" />
                      <Text>No supplier contracts found</Text>
                      <Button
                        size="small"
                        onClick={() => {
                          toast.info("Create contract form coming soon");
                        }}
                      >
                        Create your first contract
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ) : (
                contracts.map((contract) => (
                  <Table.Row key={contract.id}>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <div>
                          <div className="font-medium">{contract.contract_number}</div>
                          <div className="text-sm text-ui-fg-subtle">
                            {contract.id}
                          </div>
                        </div>
                        {isContractExpiringSoon(contract.end_date) && (
                          <AlertCircle className="h-4 w-4 text-orange-500" />
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="font-medium">{contract.supplier_name}</div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge variant={getContractTypeBadgeVariant(contract.contract_type)}>
                        {contract.contract_type.replace('_', ' ')}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge variant={getStatusBadgeVariant(contract.status)}>
                        {contract.status}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="font-medium">
                        {formatCurrency(contract.total_value, contract.currency)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(contract.start_date)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(contract.end_date)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge variant="grey">
                        {contract.payment_terms.replace('_', ' ')}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        variant="transparent"
                        size="small"
                        onClick={() => {
                          toast.info("Contract details view coming soon");
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>
      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Contracts",
});

export default SupplierContractsPage;
