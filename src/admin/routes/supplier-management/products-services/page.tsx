import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { PlusMini, MagnifyingGlass } from "@camped-ai/icons";
import {
  Package,
  Download,
  Upload,
  Edit,
  Eye,
  MoreHorizontal,
  Trash,
  X,
  Flag,
  TrendingUp,
  XCircle,
  CheckCircle,
  AlertCircle,
  Filter,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
  Select,
  DropdownMenu,
  FocusModal,
  Label,
  Prompt,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import {
  useProductsServices,
  useDeleteProductService,
  useUpdateProductService,
  useProductServiceImportExport,
} from "../../../hooks/supplier-products-services/use-products-services";

import { useCategories } from "../../../hooks/supplier-products-services/use-categories";
import { useUnitTypes } from "../../../hooks/supplier-products-services/use-unit-types";
import { useHotels } from "../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../hooks/supplier-products-services/use-destinations";

// Import types from hooks
import type { ProductService } from "../../../hooks/supplier-products-services/use-products-services";

const ProductsServicesPage = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // Initialize state from URL parameters
  const [searchTerm, setSearchTerm] = useState(
    () => searchParams.get("search") || ""
  );
  const [currentPage, setCurrentPage] = useState(() => {
    const page = searchParams.get("page");
    return page ? parseInt(page, 10) : 1;
  });
  const [pageSize, setPageSize] = useState(() => {
    const size = searchParams.get("pageSize");
    return size ? parseInt(size, 10) : 25;
  });
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<ProductService | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Check for mobile screen size
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Filters state - initialize from URL parameters
  const [filters, setFilters] = useState(() => ({
    type: searchParams.get("type") || "all",
    status: searchParams.get("status") || "all",
    category: searchParams.get("category") || "all",
    unit_type: searchParams.get("unit_type") || "all",
  }));

  // Import/Export state
  const [showImportModal, setShowImportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);

  // Fetch categories for import template generation
  const { data: importCategoriesData, isLoading: importCategoriesLoading } =
    useCategories({ limit: 100 });

  // State for selected category in import modal
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");

  // Export modal state
  const [exportCategoryId, setExportCategoryId] = useState<string>("");
  const [exportFilters, setExportFilters] = useState({
    status: "all",
    type: "all",
  });
  const [exportFormat, setExportFormat] = useState<"excel" | "csv">("excel");
  const [selectedExportFields, setSelectedExportFields] = useState<
    Record<string, boolean>
  >({
    category_name: true,
    type: true,
    description: true,
    unit_type_name: true,
    tag_names: true,
    base_cost: true,
    status: true,
  });
  const [isExporting, setIsExporting] = useState(false);

  // Import functionality
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<any[]>([]);

  // Import/Export hook
  const { parseImportFile, importProductServices, isImporting } =
    useProductServiceImportExport();

  // Function to handle downloading template for selected category
  const handleDownloadTemplate = async () => {
    if (!selectedCategoryId) {
      toast.error("Please select a category first");
      return;
    }

    const selectedCategory = importCategoriesData?.categories.find(
      (cat) => cat.id === selectedCategoryId
    );
    if (!selectedCategory) {
      toast.error("Selected category not found");
      return;
    }

    try {
      const response = await fetch(
        `/admin/supplier-management/products-services/template?category_id=${selectedCategoryId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to download template");
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `products_services_template_${selectedCategory.name}_${
        new Date().toISOString().split("T")[0]
      }.xlsx`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(
        `Template for ${selectedCategory.name} downloaded successfully`
      );
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error(`Failed to download template for ${selectedCategory.name}`);
    }
  };

  // Export functionality
  const handleExport = async () => {
    if (!exportCategoryId) {
      toast.error("Please select a category first");
      return;
    }

    const selectedCategory = importCategoriesData?.categories.find(
      (cat) => cat.id === exportCategoryId
    );
    if (!selectedCategory) {
      toast.error("Selected category not found");
      return;
    }

    setIsExporting(true);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append("category_id", exportCategoryId);
      queryParams.append("format", exportFormat);

      // Add selected fields to the query parameters
      const selectedFieldsList = Object.entries(selectedExportFields)
        .filter(([_, isSelected]) => isSelected)
        .map(([field, _]) => field);

      if (selectedFieldsList.length > 0) {
        queryParams.append("fields", selectedFieldsList.join(","));
      }

      // Add filters
      if (exportFilters.status !== "all") {
        queryParams.append("status", exportFilters.status);
      }

      if (exportFilters.type !== "all") {
        queryParams.append("type", exportFilters.type);
      }

      // Make export request
      const response = await fetch(
        `/admin/supplier-management/products-services/export?${queryParams.toString()}`,
        {
          method: "GET",
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `Export failed with status ${response.status}`
        );
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get("Content-Disposition");
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1]?.replace(/"/g, "")
        : `products_services_export_${selectedCategory.name}_${
            new Date().toISOString().split("T")[0]
          }.${exportFormat === "excel" ? "xlsx" : "csv"}`;

      // Create download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(
        `Export for ${selectedCategory.name} completed successfully`
      );
      setShowExportModal(false);
    } catch (error) {
      console.error("Error exporting data:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      toast.error(`Failed to export data: ${errorMessage}`);
    } finally {
      setIsExporting(false);
    }
  };

  // Reset export modal
  const resetExportModal = () => {
    setShowExportModal(false);
    setExportCategoryId("");
    setExportFilters({
      status: "all",
      type: "all",
    });
    setExportFormat("excel");
    setSelectedExportFields({
      category_name: true,
      type: true,
      description: true,
      unit_type_name: true,
      tag_names: true,
      base_cost: true,
      status: true,
    });
  };

  // Import handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Automatically parse the file when selected
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file: File) => {
    setImportFile(file);
    setImportData([]);
    setImportErrors([]);

    try {
      console.log(`Auto-parsing file: ${file.name}`);
      const { data, errors } = await parseImportFile(file, selectedCategoryId);
      setImportData(data);
      setImportErrors(errors);

      // Validation errors will be shown in the modal UI
      // No toast needed - errors are displayed in the validation errors section
    } catch (error) {
      toast.error("Failed to parse file");
      console.error("File parsing error:", error);
    }
  };

  const [importSuccess, setImportSuccess] = useState(false);
  const [importedCount, setImportedCount] = useState(0);

  const handleImport = async () => {
    if (importData.length === 0) return;

    try {
      const result = await importProductServices(importData);
      console.log("Import completed, result:", result);

      // Manually refetch the data to ensure it's updated
      console.log("Manually refetching data...");
      await refetch();

      // Set success state to show in modal instead of toast
      setImportSuccess(true);
      setImportedCount(importData.length);

      // Reset import state after a delay
      setTimeout(() => {
        resetImportModal();
      }, 3000); // Slightly longer delay to show success message
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Failed to import data");
    }
  };

  const resetImportModal = () => {
    setShowImportModal(false);
    setSelectedCategoryId("");
    setImportFile(null);
    setImportData([]);
    setImportErrors([]);
    setImportSuccess(false);
    setImportedCount(0);
  };

  // API integration for products/services
  const {
    data: apiData,
    isLoading: apiLoading,
    error,
    refetch,
  } = useProductsServices({
    name: searchTerm || undefined,
    type: (filters.type !== "all" ? filters.type as "Product" | "Service" : undefined),
    status: (filters.status !== "all" ? filters.status as "active" | "inactive" : undefined),
    category_id: filters.category !== "all" ? filters.category : undefined,
    unit_type_id: filters.unit_type !== "all" ? filters.unit_type : undefined,

    limit: pageSize,
    offset: (currentPage - 1) * pageSize,
  });

  // Debug logging for API data
  useEffect(() => {
    console.log("API Data received:", {
      data: apiData,
      isLoading: apiLoading,
      error: error,
      filters: filters,
      searchTerm: searchTerm,
      currentPage: currentPage,
      pageSize: pageSize,
    });
  }, [apiData, apiLoading, error, filters, searchTerm, currentPage, pageSize]);

  // API integration for filter options
  const { data: categoriesData, isLoading: categoriesLoading } = useCategories({
    is_active: true,
  });
  const { data: unitTypesData, isLoading: unitTypesLoading } = useUnitTypes({
    is_active: true,
  });

  // Fetch hotels and destinations for name resolution
  const { data: hotelsData } = useHotels({ is_active: true });
  const { data: destinationsData } = useDestinations({ is_active: true });

  // Delete mutation
  const deleteProductService = useDeleteProductService();

  // Update mutation for price updates
  const updateProductService = useUpdateProductService();

  // Function to update base price and clear price flag
  const handleUpdatePrice = async (item: any) => {
    if (!item.highest_price) {
      toast.error("No flagged price available to update");
      return;
    }

    try {
      await updateProductService.mutateAsync({
        id: item.id,
        data: {
          base_cost: item.highest_price, // Update base cost to the flagged price
          // Clear all price flag fields
          highest_price: null,
          highest_price_currency: null,
          price_flag_active: false,
          price_flag_created_at: null,
          price_flag_supplier_offering_id: null,
        },
      });

      toast.success(
        `Base price updated to ${formatCurrency(item.highest_price)} ${
          item.highest_price_currency || "CHF"
        }`
      );
    } catch (error) {
      toast.error("Failed to update price");
      console.error("Price update error:", error);
    }
  };

  // Function to dismiss price flag without updating base price
  const handleDismissFlag = async (item: any) => {
    try {
      await updateProductService.mutateAsync({
        id: item.id,
        data: {
          // Keep base_cost unchanged, just clear price flag fields
          highest_price: null,
          highest_price_currency: null,
          price_flag_active: false,
          price_flag_created_at: null,
          price_flag_supplier_offering_id: null,
        },
      });

      toast.success("Price flag dismissed");
    } catch (error) {
      toast.error("Failed to dismiss price flag");
      console.error("Price flag dismiss error:", error);
    }
  };

  const productsServices = apiData?.product_services || [];
  const totalCount = apiData?.count || 0;
  const categories = categoriesData?.categories || [];
  const unitTypes = unitTypesData?.unit_types || [];
  const hotels = hotelsData?.hotels || [];
  const destinations = destinationsData?.destinations || [];

  // Helper function to resolve hotel/destination IDs to names in product names
  const resolveProductName = (item: ProductService): string => {
    if (
      !item.name ||
      !item.category?.dynamic_field_schema ||
      !item.custom_fields
    ) {
      return item.name;
    }

    let resolvedName = item.name;

    // Find hotel and destination fields in the category schema
    const hotelFields = item.category.dynamic_field_schema.filter(
      (field: any) => field.type === "hotels" && field.used_in_product !== false
    );
    const destinationFields = item.category.dynamic_field_schema.filter(
      (field: any) =>
        field.type === "destinations" && field.used_in_product !== false
    );

    // Replace hotel IDs with names
    hotelFields.forEach((field: any) => {
      const hotelValue = item.custom_fields?.[field.key];
      if (hotelValue) {
        try {
          let hotelIds: string[] = [];
          if (Array.isArray(hotelValue)) {
            hotelIds = hotelValue;
          } else if (typeof hotelValue === "string") {
            try {
              const parsed = JSON.parse(hotelValue);
              hotelIds = Array.isArray(parsed) ? parsed : [parsed];
            } catch {
              hotelIds = [hotelValue];
            }
          }

          const hotelNames = hotelIds
            .map((id: string) => hotels.find((hotel) => hotel.id === id)?.name)
            .filter(Boolean);

          if (hotelNames.length > 0) {
            // For multiple hotels, we need to replace each ID individually
            // since the name might contain IDs in different formats
            hotelIds.forEach((id, index) => {
              if (hotelNames[index]) {
                resolvedName = resolvedName.replace(id, hotelNames[index]);
              }
            });
          }
        } catch (error) {
          console.warn("Error resolving hotel names:", error);
        }
      }
    });

    // Replace destination IDs with names
    destinationFields.forEach((field: any) => {
      const destinationValue = item.custom_fields?.[field.key];
      if (destinationValue) {
        try {
          let destinationIds: string[] = [];
          if (Array.isArray(destinationValue)) {
            destinationIds = destinationValue;
          } else if (typeof destinationValue === "string") {
            try {
              const parsed = JSON.parse(destinationValue);
              destinationIds = Array.isArray(parsed) ? parsed : [parsed];
            } catch {
              destinationIds = [destinationValue];
            }
          }

          const destinationNames = destinationIds
            .map(
              (id: string) => destinations.find((dest) => dest.id === id)?.name
            )
            .filter(Boolean);

          if (destinationNames.length > 0) {
            // For multiple destinations, we need to replace each ID individually
            destinationIds.forEach((id, index) => {
              if (destinationNames[index]) {
                resolvedName = resolvedName.replace(
                  id,
                  destinationNames[index]
                );
              }
            });
          }
        } catch (error) {
          console.warn("Error resolving destination names:", error);
        }
      }
    });

    return resolvedName;
  };

  // Error handling
  useEffect(() => {
    if (error) {
      toast.error("Failed to load products and services. Please try again.");
    }
  }, [error]);

  // Pagination - now handled by API
  const totalPages = Math.ceil(totalCount / pageSize);
  const paginatedItems = productsServices; // API already returns paginated results

  // Calculate display indices for pagination info (matching suppliers page)
  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalCount);

  // URL synchronization effect
  useEffect(() => {
    const params = new URLSearchParams();

    if (searchTerm) params.set("search", searchTerm);
    if (filters.type !== "all") params.set("type", filters.type);
    if (filters.status !== "all") params.set("status", filters.status);
    if (filters.category !== "all") params.set("category", filters.category);
    if (filters.unit_type !== "all") params.set("unit_type", filters.unit_type);
    if (currentPage > 1) params.set("page", currentPage.toString());
    if (pageSize !== 25) params.set("pageSize", pageSize.toString());

    setSearchParams(params);
  }, [searchTerm, filters, currentPage, pageSize, setSearchParams]);

  // Event handlers
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handleFilterChange = (filterKey: string, value: string | string[]) => {
    setFilters((prev) => ({ ...prev, [filterKey]: value }));
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const resetFilters = () => {
    setFilters({
      type: "all",
      status: "all",
      category: "all",
      unit_type: "all",
    });
    setSearchTerm("");
    setCurrentPage(1);
    setShowFilters(false);
  };

  const handleDeleteClick = (item: ProductService) => {
    setItemToDelete(item);
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (itemToDelete) {
      try {
        await deleteProductService.mutateAsync(itemToDelete.id);
        setDeleteModalOpen(false);
        setItemToDelete(null);
      } catch (error) {
        // Error is handled by the mutation
      }
    }
  };

  // Utility functions
  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      default:
        return "grey";
    }
  };

  const getTypeBadgeVariant = (type: string) => {
    switch (type.toLowerCase()) {
      case "product":
        return "blue";
      case "service":
        return "purple";
      default:
        return "grey";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to safely format currency
  const formatCurrency = (
    amount: number | string | null | undefined,
    currency: string = "CHF"
  ): string => {
    if (!amount) return "—";
    const numericAmount =
      typeof amount === "number" ? amount : parseFloat(amount.toString());
    if (isNaN(numericAmount)) return "—";
    return `${currency} ${numericAmount.toFixed(2)}`;
  };

  // Mobile Card Component
  const MobileCard = ({ item }: { item: ProductService }) => (
    <div
      className="bg-white border border-gray-200 rounded-lg p-4 space-y-3 cursor-pointer hover:bg-gray-50"
      onClick={() =>
        navigate(`/supplier-management/products-services/${item.id}`)
      }
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="font-medium text-blue-600 hover:text-blue-800">
            {item.name}
          </div>
          {item.description && (
            <div className="text-sm text-ui-fg-subtle mt-1 line-clamp-2">
              {item.description}
            </div>
          )}
        </div>
        <div className="flex items-center gap-2 ml-4">
          <Badge color={getStatusBadgeVariant(item.status)}>
            {item.status}
          </Badge>
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button
                variant="transparent"
                size="small"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/supplier-management/products-services/${item.id}`);
                }}
              >
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    `/supplier-management/products-services/${item.id}/edit`
                  );
                }}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenu.Item>
              <DropdownMenu.Separator />
              <DropdownMenu.Item
                className="text-red-600"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteClick(item);
                }}
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 text-sm">
        <div className="flex items-center gap-1">
          <Badge color={getTypeBadgeVariant(item.type)} size="small">
            {item.type}
          </Badge>
        </div>
        <div className="text-ui-fg-subtle">{item.category?.name || "—"}</div>
        <div className="text-ui-fg-subtle">{item.unit_type?.name || "—"}</div>
      </div>

      <div className="text-xs text-ui-fg-subtle">
        Updated {formatDate(item.updated_at)}
      </div>
    </div>
  );

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between px-6 py-4 gap-4">
          <div>
            <Heading level="h2">Products & Services</Heading>
            <Text className="text-ui-fg-subtle">
              Manage your standardized products and services catalog
            </Text>
          </div>
          <div className="flex items-center gap-x-2 flex-wrap">
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowExportModal(true)}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowImportModal(true)}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Import
            </Button>
            <Button
              size="small"
              onClick={() =>
                navigate("/supplier-management/products-services/create")
              }
            >
              <PlusMini />
              Add Product/Service
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="px-6 py-4">
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Input
                  placeholder="Search by name, description, or category..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => setShowFilters(!showFilters)}
                className="flex-shrink-0"
              >
                <Filter className="h-4 w-4" />
                Filters
                {Object.values(filters).some(f => f !== "all") && (
                  <Badge color="blue" className="ml-2">
                    {Object.values(filters).filter(f => f !== "all").length}
                  </Badge>
                )}
              </Button>
              {(Object.values(filters).some((f) => f !== "all") ||
                searchTerm) && (
                <Button
                  variant="secondary"
                  size="small"
                  onClick={resetFilters}
                  className="flex-shrink-0"
                >
                  <X className="h-4 w-4" />
                  Clear
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="px-6 py-4 bg-ui-bg-subtle border-b">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Type
                </Text>
                <Select
                  value={filters.type}
                  onValueChange={(value) => handleFilterChange("type", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="All types" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All types</Select.Item>
                    <Select.Item value="Product">Product</Select.Item>
                    <Select.Item value="Service">Service</Select.Item>
                  </Select.Content>
                </Select>
              </div>

              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Status
                </Text>
                <Select
                  value={filters.status}
                  onValueChange={(value) => handleFilterChange("status", value)}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="All statuses" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All statuses</Select.Item>
                    <Select.Item value="active">Active</Select.Item>
                    <Select.Item value="inactive">Inactive</Select.Item>
                  </Select.Content>
                </Select>
              </div>

              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Category
                </Text>
                <Select
                  value={filters.category}
                  onValueChange={(value) =>
                    handleFilterChange("category", value)
                  }
                  disabled={categoriesLoading}
                >
                  <Select.Trigger>
                    <Select.Value
                      placeholder={
                        categoriesLoading
                          ? "Loading categories..."
                          : categories.length === 0
                          ? "No categories available"
                          : "All categories"
                      }
                    />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All categories</Select.Item>
                    {categories.map((category) => (
                      <Select.Item key={category.id} value={category.id}>
                        {category.name}
                      </Select.Item>
                    ))}
                    {categories.length === 0 && !categoriesLoading && (
                      <Select.Item value="no-categories" disabled>
                        No categories found
                      </Select.Item>
                    )}
                  </Select.Content>
                </Select>
              </div>

              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Unit Type
                </Text>
                <Select
                  value={filters.unit_type}
                  onValueChange={(value) =>
                    handleFilterChange("unit_type", value)
                  }
                  disabled={unitTypesLoading}
                >
                  <Select.Trigger>
                    <Select.Value
                      placeholder={
                        unitTypesLoading
                          ? "Loading unit types..."
                          : unitTypes.length === 0
                          ? "No unit types available"
                          : "All unit types"
                      }
                    />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All unit types</Select.Item>
                    {unitTypes.map((unitType) => (
                      <Select.Item key={unitType.id} value={unitType.id}>
                        {unitType.name}
                      </Select.Item>
                    ))}
                    {unitTypes.length === 0 && !unitTypesLoading && (
                      <Select.Item value="no-unit-types" disabled>
                        No unit types found
                      </Select.Item>
                    )}
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div className="flex items-center justify-between mt-4">
              <Text size="small" className="text-ui-fg-subtle">
                {totalCount} items found
                {Object.values(filters).some((f) => f !== "all") && (
                  <span className="ml-2">
                    (
                    {Object.values(filters).filter((f) => f !== "all").length}{" "}
                    filters applied)
                  </span>
                )}
              </Text>
              <Button variant="secondary" size="small" onClick={resetFilters}>
                Reset Filters
              </Button>
            </div>
          </div>
        )}

        {/* Content - Mobile Cards or Desktop Table */}
        {isMobile ? (
          <div className="px-6 py-4">
            {apiLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div
                    key={`skeleton-${index}`}
                    className="bg-white border border-gray-200 rounded-lg p-4 space-y-3"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                        <div className="h-3 bg-gray-200 rounded animate-pulse w-full" />
                      </div>
                      <div className="h-6 bg-gray-200 rounded animate-pulse w-16 ml-4" />
                    </div>
                    <div className="flex gap-2">
                      <div className="h-5 bg-gray-200 rounded animate-pulse w-16" />
                      <div className="h-5 bg-gray-200 rounded animate-pulse w-20" />
                      <div className="h-5 bg-gray-200 rounded animate-pulse w-24" />
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="flex flex-col items-center gap-4">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                    <X className="h-8 w-8 text-red-600" />
                  </div>
                  <div>
                    <Text size="large" weight="plus">
                      Failed to load products and services
                    </Text>
                    <Text className="text-ui-fg-subtle mt-1">
                      There was an error loading the data. Please try again.
                    </Text>
                  </div>
                  <Button size="small" onClick={() => window.location.reload()}>
                    Try Again
                  </Button>
                </div>
              </div>
            ) : paginatedItems.length === 0 ? (
              <div className="text-center py-12">
                <div className="flex flex-col items-center gap-4">
                  <Package className="h-16 w-16 text-gray-400" />
                  <div>
                    <Text size="large" weight="plus">
                      No products or services found
                    </Text>
                    <Text className="text-ui-fg-subtle mt-1">
                      {searchTerm || Object.values(filters).some((f) => f !== "all")
                        ? "Try adjusting your search or filters"
                        : "Get started by adding your first product or service"}
                    </Text>
                  </div>
                  <Button
                    size="small"
                    onClick={() =>
                      navigate("/supplier-management/products-services/create")
                    }
                  >
                    <PlusMini />
                    Add your first item
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {paginatedItems.map((item) => (
                  <MobileCard key={item.id} item={item} />
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Name</Table.HeaderCell>
                  <Table.HeaderCell>Type</Table.HeaderCell>
                  <Table.HeaderCell>Category</Table.HeaderCell>
                  <Table.HeaderCell>Unit Type</Table.HeaderCell>
                  <Table.HeaderCell>Base Cost</Table.HeaderCell>
                  <Table.HeaderCell>Price Flags</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Last Updated</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {apiLoading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <Table.Row key={`skeleton-${index}`}>
                      {/* <Table.Cell><div className="h-4 bg-gray-200 rounded animate-pulse" /></Table.Cell> */}
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-32" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-24" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-32" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
                      </Table.Cell>
                      <Table.Cell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-8" />
                      </Table.Cell>
                    </Table.Row>
                  ))
                ) : error ? (
                  <Table.Row>
                    <Table.Cell
                      className="text-center py-12"
                      style={{ gridColumn: "1 / -1" }}
                    >
                      <div className="flex flex-col items-center gap-4">
                        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                          <X className="h-8 w-8 text-red-600" />
                        </div>
                        <div>
                          <Text size="large" weight="plus">
                            Failed to load products and services
                          </Text>
                          <Text className="text-ui-fg-subtle mt-1">
                            There was an error loading the data. Please try
                            again.
                          </Text>
                        </div>
                        <Button
                          size="small"
                          onClick={() => window.location.reload()}
                        >
                          Try Again
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ) : paginatedItems.length === 0 ? (
                  <Table.Row>
                    <Table.Cell
                      className="text-center py-12"
                      style={{ gridColumn: "1 / -1" }}
                    >
                      <div className="flex flex-col items-center gap-4">
                        <Package className="h-16 w-16 text-gray-400" />
                        <div>
                          <Text size="large" weight="plus">
                            No products or services found
                          </Text>
                          <Text className="text-ui-fg-subtle mt-1">
                            {searchTerm || Object.values(filters).some((f) => f !== "all")
                              ? "Try adjusting your search or filters"
                              : "Get started by adding your first product or service"}
                          </Text>
                        </div>
                        <Button
                          size="small"
                          onClick={() =>
                            navigate(
                              "/supplier-management/products-services/create"
                            )
                          }
                        >
                          <PlusMini />
                          Add your first item
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  paginatedItems.map((item) => (
                    <Table.Row
                      key={item.id}
                      className="hover:bg-ui-bg-subtle cursor-pointer"
                      onClick={() =>
                        navigate(
                          `/supplier-management/products-services/${item.id}`
                        )
                      }
                    >
                      <Table.Cell>
                        <div className="font-medium text-blue-600 hover:text-blue-800">
                          {resolveProductName(item)}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color={getTypeBadgeVariant(item.type)}>
                          {item.type}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="text-sm">
                          {item.category?.name || "—"}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="text-sm">
                          {item.unit_type?.name || "—"}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="text-sm font-medium">
                          {formatCurrency(item.base_cost)}
                        </div>
                      </Table.Cell>
                      <Table.Cell onClick={(e) => e.stopPropagation()}>
                        <div className="flex items-center justify-center">
                          {item.price_flag_active ? (
                            <DropdownMenu>
                              <DropdownMenu.Trigger asChild>
                                <button className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-ui-bg-subtle transition-colors">
                                  <Flag className="w-4 h-4 text-orange-500" />
                                </button>
                              </DropdownMenu.Trigger>
                              <DropdownMenu.Content
                                align="center"
                                className="w-80"
                              >
                                <div className="p-4 space-y-3">
                                  <div className="flex items-center gap-2">
                                    <Flag className="w-4 h-4 text-orange-500" />
                                    <Text className="font-medium">
                                      Price Flag Active
                                    </Text>
                                  </div>

                                  <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                      <span className="text-ui-fg-subtle">
                                        Current Base:
                                      </span>
                                      <span className="font-medium">
                                        {formatCurrency(item.base_cost)}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-ui-fg-subtle">
                                        Highest Price:
                                      </span>
                                      <span className="font-medium">
                                        {formatCurrency(item.highest_price)}{" "}
                                        {item.highest_price_currency || "CHF"}
                                      </span>
                                    </div>
                                    {item.base_cost && item.highest_price && (
                                      <div className="flex justify-between">
                                        <span className="text-ui-fg-subtle">
                                          Increase:
                                        </span>
                                        <span className="font-medium text-orange-600">
                                          +
                                          {(
                                            ((item.highest_price -
                                              item.base_cost) /
                                              item.base_cost) *
                                            100
                                          ).toFixed(1)}
                                          %
                                        </span>
                                      </div>
                                    )}
                                    {item.price_flag_created_at && (
                                      <div className="flex justify-between">
                                        <span className="text-ui-fg-subtle">
                                          Flagged:
                                        </span>
                                        <span className="text-xs">
                                          {new Date(
                                            item.price_flag_created_at
                                          ).toLocaleDateString()}
                                        </span>
                                      </div>
                                    )}
                                  </div>

                                  <div className="flex gap-2 pt-2 border-t">
                                    <Button
                                      size="small"
                                      variant="secondary"
                                      className="flex-1"
                                      onClick={() => {
                                        if (
                                          item.price_flag_supplier_offering_id
                                        ) {
                                          window.open(
                                            `/app/supplier-management/supplier-offerings/${item.price_flag_supplier_offering_id}`,
                                            "_blank"
                                          );
                                        } else {
                                          toast.error(
                                            "No supplier offering found for this price flag"
                                          );
                                        }
                                      }}
                                    >
                                      <Eye className="w-3 h-3 mr-1" />
                                      View
                                    </Button>
                                    <Button
                                      size="small"
                                      variant="secondary"
                                      className="flex-1"
                                      onClick={() => handleDismissFlag(item)}
                                      disabled={updateProductService.isPending}
                                    >
                                      <XCircle className="w-3 h-3 mr-1" />
                                      {updateProductService.isPending
                                        ? "Dismissing..."
                                        : "Dismiss"}
                                    </Button>
                                    <Button
                                      size="small"
                                      className="flex-1"
                                      onClick={() => handleUpdatePrice(item)}
                                      disabled={updateProductService.isPending}
                                    >
                                      <TrendingUp className="w-3 h-3 mr-1" />
                                      {updateProductService.isPending
                                        ? "Updating..."
                                        : "Update"}
                                    </Button>
                                  </div>
                                </div>
                              </DropdownMenu.Content>
                            </DropdownMenu>
                          ) : (
                            <div className="w-8 h-8 flex items-center justify-center">
                              <span className="text-ui-fg-muted text-xs">
                                —
                              </span>
                            </div>
                          )}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color={getStatusBadgeVariant(item.status)}>
                          {item?.status?.charAt(0).toUpperCase() + item?.status?.slice(1)}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="text-sm text-ui-fg-subtle">
                          {formatDate(item.updated_at)}
                        </div>
                      </Table.Cell>
                      <Table.Cell onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="transparent" size="small">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <DropdownMenu.Item
                              onClick={() =>
                                navigate(
                                  `/supplier-management/products-services/${item.id}`
                                )
                              }
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </DropdownMenu.Item>
                            <DropdownMenu.Item
                              onClick={() =>
                                navigate(
                                  `/supplier-management/products-services/${item.id}/edit`
                                )
                              }
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenu.Item>
                            <DropdownMenu.Separator />
                            <DropdownMenu.Item
                              className="text-red-600"
                              onClick={() => handleDeleteClick(item)}
                            >
                              <Trash className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenu.Item>
                          </DropdownMenu.Content>
                        </DropdownMenu>
                      </Table.Cell>
                    </Table.Row>
                  ))
                )}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination */}
        {totalCount > 0 && (
          <div className="flex flex-col sm:flex-row items-center justify-between px-6 py-4 border-t gap-4">
            <div className="flex items-center gap-4">
              <Text size="small" className="text-ui-fg-subtle">
                Showing {startIndex} to {endIndex} of {totalCount} items
              </Text>

              {/* Page Size Selector */}
              <div className="flex items-center gap-2">
                <Text size="small" className="text-ui-fg-subtle">
                  Show:
                </Text>
                <Select
                  value={pageSize.toString()}
                  onValueChange={(value) =>
                    handlePageSizeChange(parseInt(value))
                  }
                >
                  <Select.Trigger className="w-20">
                    <Select.Value />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="10">10</Select.Item>
                    <Select.Item value="25">25</Select.Item>
                    <Select.Item value="50">50</Select.Item>
                    <Select.Item value="100">100</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <div className="flex items-center gap-1">
                {(() => {
                  const maxVisiblePages = 5;
                  const startPage = Math.max(
                    1,
                    currentPage - Math.floor(maxVisiblePages / 2)
                  );
                  const endPage = Math.min(
                    totalPages,
                    startPage + maxVisiblePages - 1
                  );
                  const adjustedStartPage = Math.max(
                    1,
                    endPage - maxVisiblePages + 1
                  );

                  return Array.from(
                    { length: endPage - adjustedStartPage + 1 },
                    (_, i) => {
                      const page = adjustedStartPage + i;
                      return (
                        <Button
                          key={page}
                          variant={
                            currentPage === page ? "primary" : "secondary"
                          }
                          size="small"
                          onClick={() => handlePageChange(page)}
                          className="min-w-[32px]"
                        >
                          {page}
                        </Button>
                      );
                    }
                  );
                })()}
              </div>
              <Button
                variant="secondary"
                size="small"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </Container>

      <Prompt open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Are you sure?</Prompt.Title>
            <Prompt.Description className="mt-3">
              You are about to delete the product/service "{itemToDelete?.name}
              ". This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel
              onClick={() => setDeleteModalOpen(false)}
              disabled={deleteProductService.isPending}
            >
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleDeleteConfirm}
              disabled={deleteProductService.isPending}
            >
              {deleteProductService.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      {/* Export Modal */}
      <FocusModal open={showExportModal} onOpenChange={resetExportModal}>
        <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
          <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center w-full py-4 px-6">
              <div className="flex items-center gap-3">
                <Heading
                  level="h2"
                  className="text-xl font-semibold text-gray-900 dark:text-gray-100"
                >
                  Export Products & Services
                </Heading>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Step 1 */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                      1
                    </div>
                    <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">
                      Configure
                    </span>
                  </div>
                  <div className="w-8 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                  {/* Step 2 */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400 rounded-full flex items-center justify-center text-sm font-semibold">
                      2
                    </div>
                    <span className="ml-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                      Export
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </FocusModal.Header>

          <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
            <div className="flex-grow overflow-y-auto p-6 pb-0">
              <div className="flex flex-col gap-6">
                {/* Category Selection Section - Required */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading
                      level="h3"
                      className="text-lg font-semibold text-gray-900 dark:text-gray-100"
                    >
                      Select Category <span className="text-red-500">*</span>
                    </Heading>
                  </div>
                  <div className="p-4">
                    <select
                      value={exportCategoryId}
                      onChange={(e) => setExportCategoryId(e.target.value)}
                      className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      required
                    >
                      <option value="">Select a category...</option>
                      {importCategoriesData?.categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Fields Selection Section - Full Width Top Row */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex justify-between items-center">
                      <Heading
                        level="h3"
                        className="text-lg font-semibold text-gray-900 dark:text-gray-100"
                      >
                        Select Fields to Export
                      </Heading>
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() => {
                          const allSelected =
                            Object.values(selectedExportFields).every(Boolean);
                          const newState = allSelected
                            ? Object.keys(selectedExportFields).reduce(
                                (acc, key) => ({ ...acc, [key]: false }),
                                {}
                              )
                            : Object.keys(selectedExportFields).reduce(
                                (acc, key) => ({ ...acc, [key]: true }),
                                {}
                              );
                          setSelectedExportFields(newState);
                        }}
                        className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600"
                      >
                        {Object.values(selectedExportFields).every(Boolean)
                          ? "Deselect All"
                          : "Select All"}
                      </Button>
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="grid grid-cols-8 gap-3">
                      {Object.entries(selectedExportFields).map(
                        ([field, isSelected]) => {
                          const fieldLabels: Record<string, string> = {
                            category_name: "Category Name",
                            type: "Type",
                            description: "Description",
                            unit_type_name: "Unit Type Name",
                            tag_names: "Tag Names",
                            base_cost: "Base Cost (CHF)",
                            status: "Status",
                          };

                          return (
                            <div
                              key={field}
                              className="flex items-center gap-2 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600 h-12 min-w-0"
                            >
                              <input
                                type="checkbox"
                                id={`field-${field}`}
                                checked={isSelected}
                                onChange={(e) =>
                                  setSelectedExportFields((prev) => ({
                                    ...prev,
                                    [field]: e.target.checked,
                                  }))
                                }
                                className="h-4 w-4 flex-shrink-0 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                              />
                              <Label
                                htmlFor={`field-${field}`}
                                className="cursor-pointer text-gray-700 dark:text-gray-300 text-xs font-medium truncate min-w-0 flex-1"
                              >
                                {fieldLabels[field] || field}
                              </Label>
                            </div>
                          );
                        }
                      )}
                    </div>
                  </div>
                </div>

                {/* Bottom Row - Filters, Format, and Summary */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                  {/* Filters Section */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                      <Heading
                        level="h3"
                        className="text-base font-semibold text-gray-900 dark:text-gray-100"
                      >
                        Filters
                      </Heading>
                    </div>

                    <div className="p-4 space-y-4">
                      <div>
                        <Label
                          htmlFor="export_status"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                        >
                          Active Status
                        </Label>
                        <select
                          id="export_status"
                          value={exportFilters.status}
                          onChange={(e) =>
                            setExportFilters((prev) => ({
                              ...prev,
                              status: e.target.value,
                            }))
                          }
                          className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                        >
                          <option value="all">All Products & Services</option>
                          <option value="active">Active Only</option>
                          <option value="inactive">Inactive Only</option>
                        </select>
                      </div>

                      <div>
                        <Label
                          htmlFor="export_type"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                        >
                          Product/Service Type
                        </Label>
                        <select
                          id="export_type"
                          value={exportFilters.type}
                          onChange={(e) =>
                            setExportFilters((prev) => ({
                              ...prev,
                              type: e.target.value,
                            }))
                          }
                          className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                        >
                          <option value="all">All Types</option>
                          <option value="Product">Product</option>
                          <option value="Service">Service</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Format Section */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                      <Heading
                        level="h3"
                        className="text-base font-semibold text-gray-900 dark:text-gray-100"
                      >
                        Export Format
                      </Heading>
                    </div>

                    <div className="p-4 space-y-3">
                      <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                        <input
                          type="radio"
                          id="format-xlsx"
                          name="exportFormat"
                          value="excel"
                          checked={exportFormat === "excel"}
                          onChange={(e) =>
                            setExportFormat(e.target.value as "excel" | "csv")
                          }
                          className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                        />
                        <div className="flex-grow">
                          <Label
                            htmlFor="format-xlsx"
                            className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium"
                          >
                            Excel (.xlsx)
                          </Label>
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            Recommended for data analysis
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                        <input
                          type="radio"
                          id="format-csv"
                          name="exportFormat"
                          value="csv"
                          checked={exportFormat === "csv"}
                          onChange={(e) =>
                            setExportFormat(e.target.value as "excel" | "csv")
                          }
                          className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                        />
                        <div className="flex-grow">
                          <Label
                            htmlFor="format-csv"
                            className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium"
                          >
                            CSV (.csv)
                          </Label>
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            Compatible with most applications
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Export Summary Section */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                      <Heading
                        level="h3"
                        className="text-base font-semibold text-gray-900 dark:text-gray-100"
                      >
                        Export Summary
                      </Heading>
                    </div>

                    <div className="p-4 space-y-4">
                      {/* Category Selection Summary */}
                      <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 border border-orange-200 dark:border-orange-800">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-semibold text-orange-900 dark:text-orange-100">
                            Selected Category
                          </h4>
                          <span
                            className={`text-xs font-medium px-2 py-1 rounded-full ${
                              exportCategoryId
                                ? "bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200"
                                : "bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200"
                            }`}
                          >
                            {exportCategoryId ? "Selected" : "Required"}
                          </span>
                        </div>
                        <div className="text-xs text-orange-700 dark:text-orange-300">
                          {exportCategoryId
                            ? importCategoriesData?.categories.find(
                                (cat) => cat.id === exportCategoryId
                              )?.name || "Unknown"
                            : "Please select a category to continue"}
                        </div>
                      </div>

                      {/* Selected Fields Summary */}
                      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                            Selected Fields
                          </h4>
                          <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">
                            {
                              Object.values(selectedExportFields).filter(
                                Boolean
                              ).length
                            }{" "}
                            of {Object.keys(selectedExportFields).length}
                          </span>
                        </div>
                        <div className="text-xs text-blue-700 dark:text-blue-300 max-h-16 overflow-y-auto">
                          {Object.entries(selectedExportFields)
                            .filter(([_, selected]) => selected)
                            .slice(0, 4)
                            .map(([field]) => (
                              <div key={field} className="truncate">
                                •{" "}
                                {field
                                  .replace(/_/g, " ")
                                  .replace(/\b\w/g, (l) => l.toUpperCase())}
                              </div>
                            ))}
                          {Object.values(selectedExportFields).filter(Boolean)
                            .length > 4 && (
                            <div className="text-blue-600 dark:text-blue-400 font-medium">
                              +
                              {Object.values(selectedExportFields).filter(
                                Boolean
                              ).length - 4}{" "}
                              more...
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Filter & Export Details Combined */}
                      <div className="grid grid-cols-2 gap-3">
                        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-800">
                          <h4 className="text-sm font-semibold text-green-900 dark:text-green-100 mb-2">
                            Filters
                          </h4>
                          <div className="space-y-1 text-xs text-green-700 dark:text-green-300">
                            <div>
                              <span className="font-medium">Status: </span>
                              {exportFilters.status === "all"
                                ? "All"
                                : exportFilters.status}
                            </div>
                            <div>
                              <span className="font-medium">Type: </span>
                              <span className="truncate">
                                {exportFilters.type === "all"
                                  ? "All Types"
                                  : exportFilters.type}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800">
                          <h4 className="text-sm font-semibold text-purple-900 dark:text-purple-100 mb-2">
                            Export Details
                          </h4>
                          <div className="space-y-1 text-xs text-purple-700 dark:text-purple-300">
                            <div>
                              <span className="font-medium">Format: </span>
                              <span className="uppercase">
                                {exportFormat === "excel" ? "xlsx" : "csv"}
                              </span>
                            </div>
                            <div>
                              <span className="font-medium">Date: </span>
                              {new Date().toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                        <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
                          {exportCategoryId
                            ? 'Click "Export Data" to download the file to your downloads folder'
                            : "Select a category above to enable export"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Footer */}
            <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {exportCategoryId
                    ? `Ready to export ${
                        Object.values(selectedExportFields).filter(Boolean)
                          .length
                      } fields`
                    : "Select a category to continue"}
                </div>
                <div className="flex gap-4">
                  <Button
                    variant="secondary"
                    onClick={resetExportModal}
                    disabled={isExporting}
                    className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleExport}
                    disabled={
                      !exportCategoryId ||
                      isExporting ||
                      Object.values(selectedExportFields).filter(Boolean)
                        .length === 0
                    }
                    className="flex items-center gap-3 px-6 py-3 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Download className="w-5 h-5" />
                    {isExporting ? "Exporting..." : "Export Data"}
                  </Button>
                </div>
              </div>
            </div>
          </FocusModal.Body>
        </FocusModal.Content>
      </FocusModal>

      {/* Import Modal */}
      <FocusModal open={showImportModal} onOpenChange={resetImportModal}>
        <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
          <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center w-full py-4 px-6">
              <div className="flex items-center gap-3">
                <Heading
                  level="h2"
                  className="text-xl font-semibold text-gray-900 dark:text-gray-100"
                >
                  Import Products & Services
                </Heading>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Step 1 */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                      1
                    </div>
                    <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">
                      Download
                    </span>
                  </div>
                  <div className="w-12 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                  {/* Step 2 */}
                  <div className="flex items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                        importFile
                          ? "bg-green-600 dark:bg-green-500 text-white"
                          : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                      }`}
                    >
                      2
                    </div>
                    <span
                      className={`ml-2 text-sm font-medium ${
                        importFile
                          ? "text-green-600 dark:text-green-400"
                          : "text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      Upload
                    </span>
                  </div>
                  <div className="w-12 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                  {/* Step 3 */}
                  <div className="flex items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                        importSuccess
                          ? "bg-green-600 dark:bg-green-500 text-white"
                          : importData.length > 0 && importErrors.length === 0
                          ? "bg-purple-600 dark:bg-purple-500 text-white"
                          : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                      }`}
                    >
                      {importSuccess ? "✓" : "3"}
                    </div>
                    <span
                      className={`ml-2 text-sm font-medium ${
                        importSuccess
                          ? "text-green-600 dark:text-green-400"
                          : importData.length > 0 && importErrors.length === 0
                          ? "text-purple-600 dark:text-purple-400"
                          : "text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      {importSuccess ? "Complete" : "Import"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </FocusModal.Header>

          <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
            <div className="flex-grow overflow-y-auto p-6 space-y-6">
              <>
                {/* Step 1: Download Template */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex items-start gap-3 p-6">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">
                        1
                      </span>
                    </div>
                    <div className="flex-1">
                      <Heading
                        level="h3"
                        className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"
                      >
                        Download Import Template
                      </Heading>
                      <Text className="text-gray-600 dark:text-gray-400 mb-4">
                        Download category-specific Excel templates with
                        pre-configured fields for products & services. Each
                        category has its own template with custom fields
                        specific to that category.
                      </Text>

                      <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-700 mb-4">
                        <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Each template includes:
                        </Text>
                        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                          <li>
                            •{" "}
                            <strong className="text-gray-800 dark:text-gray-200">
                              Category-specific fields:
                            </strong>{" "}
                            Custom fields and validation for the selected
                            category
                          </li>
                          <li>
                            •{" "}
                            <strong className="text-gray-800 dark:text-gray-200">
                              User-friendly dropdowns:
                            </strong>{" "}
                            Product/Service types, unit type names, tag names
                          </li>
                          <li>
                            •{" "}
                            <strong className="text-gray-800 dark:text-gray-200">
                              Tags reference sheet:
                            </strong>{" "}
                            Easy lookup for tag names (supports comma-separated
                            values)
                          </li>
                          <li>
                            •{" "}
                            <strong className="text-gray-800 dark:text-gray-200">
                              Custom field dropdowns:
                            </strong>{" "}
                            Dynamic validation for category-specific fields
                          </li>
                          <li>
                            •{" "}
                            <strong className="text-gray-800 dark:text-gray-200">
                              Instructions sheet:
                            </strong>{" "}
                            Comprehensive field guide with all available options
                          </li>
                        </ul>
                      </div>

                      {/* Category Selection and Download */}
                      {importCategoriesLoading ? (
                        <div className="flex items-center justify-center py-4">
                          <Text className="text-gray-500 dark:text-gray-400">
                            Loading categories...
                          </Text>
                        </div>
                      ) : importCategoriesData?.categories &&
                        importCategoriesData.categories.length > 0 ? (
                        <div className="space-y-4">
                          <div>
                            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                              Select Category:
                            </Label>
                            <Select
                              value={selectedCategoryId}
                              onValueChange={setSelectedCategoryId}
                            >
                              <Select.Trigger className="w-80 max-w-full">
                                <Select.Value placeholder="Choose a category..." />
                              </Select.Trigger>
                              <Select.Content>
                                {importCategoriesData.categories.map(
                                  (category) => (
                                    <Select.Item
                                      key={category.id}
                                      value={category.id}
                                    >
                                      <div className="flex items-center gap-2">
                                        <span>{category.icon}</span>
                                        <span>{category.name}</span>
                                        <span className="text-xs text-gray-500">
                                          ({category.category_type})
                                        </span>
                                      </div>
                                    </Select.Item>
                                  )
                                )}
                              </Select.Content>
                            </Select>
                          </div>

                          <Button
                            variant="secondary"
                            onClick={handleDownloadTemplate}
                            disabled={!selectedCategoryId}
                            className="flex items-center gap-2 bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Download className="w-4 h-4" />
                            Download Excel Template
                          </Button>
                        </div>
                      ) : (
                        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-700">
                          <Text className="text-sm text-yellow-700 dark:text-yellow-300">
                            No categories found. Please create categories first
                            before generating templates.
                          </Text>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Step 2: Upload File */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex items-start gap-3 p-6">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                      <span className="text-green-600 dark:text-green-400 font-semibold text-sm">
                        2
                      </span>
                    </div>
                    <div className="flex-1">
                      <Heading
                        level="h3"
                        className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"
                      >
                        Upload & Parse Your Data File
                      </Heading>
                      <Text className="text-gray-600 dark:text-gray-400 mb-4">
                        Upload the completed Excel file with your products &
                        services data. The file will be automatically parsed and
                        validated.
                      </Text>

                      {/* File Upload Area */}
                      <div
                        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                          importFile
                            ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                            : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                        }`}
                      >
                        {importFile ? (
                          <>
                            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                            <Text className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                              File Selected: {importFile.name}
                            </Text>
                            <Text className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                              Size: {(importFile.size / 1024 / 1024).toFixed(2)}{" "}
                              MB
                            </Text>
                            <Button
                              variant="secondary"
                              onClick={() => setImportFile(null)}
                              className="text-sm"
                            >
                              Choose Different File
                            </Button>
                          </>
                        ) : (
                          <>
                            <Upload className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                            <Text className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                              Click to upload or drag and drop
                            </Text>
                            <Text className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                              Excel (.xlsx, .xls) and CSV files • Maximum 5MB
                            </Text>
                            <input
                              type="file"
                              accept=".xlsx,.xls,.csv"
                              onChange={handleFileSelect}
                              className="hidden"
                              id="file-upload"
                            />
                            <label
                              htmlFor="file-upload"
                              className="inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 cursor-pointer"
                            >
                              Select a file to import
                            </label>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Validation Errors Section */}
                {importErrors.length > 0 && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-red-200 dark:border-red-700 shadow-sm">
                    <div className="flex items-start gap-3 p-6">
                      <div className="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                        <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                      </div>
                      <div className="flex-1">
                        <Heading
                          level="h3"
                          className="text-lg font-medium text-red-900 dark:text-red-100 mb-2"
                        >
                          Validation Errors Found
                        </Heading>
                        <Text className="text-red-700 dark:text-red-300 mb-4">
                          Found {importErrors.length} validation error
                          {importErrors.length > 1 ? "s" : ""} in your data.
                          Please fix these issues and re-upload the file.
                        </Text>

                        {/* Error List */}
                        <div className="bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-700 max-h-64 overflow-y-auto">
                          <div className="p-4 space-y-3">
                            {importErrors.slice(0, 20).map((error, index) => (
                              <div
                                key={index}
                                className="flex items-start gap-3 p-3 bg-white dark:bg-gray-800 rounded border border-red-100 dark:border-red-800"
                              >
                                <div className="flex-shrink-0 w-6 h-6 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                  <span className="text-red-600 dark:text-red-400 text-xs font-medium">
                                    {error.row || index + 1}
                                  </span>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <Text className="text-sm font-medium text-red-900 dark:text-red-100">
                                      Row {error.row || index + 1}
                                    </Text>
                                    {error.field && (
                                      <Badge className="text-xs bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300">
                                        {error.field}
                                      </Badge>
                                    )}
                                  </div>
                                  <Text className="text-sm text-red-700 dark:text-red-300">
                                    {error.message}
                                  </Text>
                                  {error.value && (
                                    <Text className="text-xs text-red-600 dark:text-red-400 mt-1 font-mono bg-red-50 dark:bg-red-900/30 px-2 py-1 rounded">
                                      Value:{" "}
                                      {typeof error.value === "object"
                                        ? JSON.stringify(error.value)
                                        : String(error.value)}
                                    </Text>
                                  )}
                                </div>
                              </div>
                            ))}
                            {importErrors.length > 20 && (
                              <div className="text-center py-2">
                                <Text className="text-sm text-red-600 dark:text-red-400">
                                  ... and {importErrors.length - 20} more errors
                                </Text>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Error Summary */}
                        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-700">
                          <Text className="text-sm text-red-700 dark:text-red-300">
                            <strong>Next Steps:</strong>
                            <br />
                            1. Review the errors listed above
                            <br />
                            2. Fix the data in your Excel file
                            <br />
                            3. Re-upload the corrected file
                            <br />
                            4. Import will be enabled once all errors are
                            resolved
                          </Text>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Success Message */}
                {importSuccess && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700 shadow-sm">
                    <div className="flex items-start gap-3 p-6">
                      <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div className="flex-1">
                        <Heading
                          level="h3"
                          className="text-lg font-medium text-green-900 dark:text-green-100 mb-2"
                        >
                          Import Successful!
                        </Heading>
                        <Text className="text-green-700 dark:text-green-300 mb-4">
                          Successfully imported {importedCount}{" "}
                          products/services. The data has been added to your
                          inventory and is now available in the products &
                          services list.
                        </Text>
                        <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md border border-green-200 dark:border-green-700">
                          <Text className="text-sm text-green-700 dark:text-green-300">
                            ✓ Data imported and validated
                            <br />
                            ✓ Records added to database
                            <br />✓ Available in products & services list
                          </Text>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </>
            </div>

            {/* Footer */}
            <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {importSuccess ? (
                    <span className="text-green-600 dark:text-green-400 font-medium">
                      ✅ Successfully imported {importedCount} records
                    </span>
                  ) : importFile ? (
                    <>
                      <span className="text-green-600 dark:text-green-400">
                        ✓
                      </span>{" "}
                      {importFile.name}
                      {importData.length > 0 && importErrors.length === 0 && (
                        <span className="text-green-600 dark:text-green-400">
                          {" "}
                          • {importData.length} records ready
                        </span>
                      )}
                      {importErrors.length > 0 && (
                        <span className="text-red-600 dark:text-red-400">
                          {" "}
                          • {importErrors.length} errors found
                        </span>
                      )}
                    </>
                  ) : (
                    "Select a file to automatically parse and import"
                  )}
                </div>
                <div className="flex gap-4">
                  <Button
                    variant="secondary"
                    onClick={resetImportModal}
                    className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleImport}
                    disabled={
                      isImporting ||
                      importSuccess ||
                      !importFile ||
                      importData.length === 0 ||
                      importErrors.length > 0
                    }
                    className={`flex items-center gap-3 px-6 py-3 shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed ${
                      importSuccess
                        ? "bg-green-600 dark:bg-green-500 text-white"
                        : "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white"
                    }`}
                  >
                    {importSuccess ? (
                      <>
                        <CheckCircle className="w-5 h-5" />
                        Import Complete
                      </>
                    ) : (
                      <>
                        <Upload className="w-5 h-5" />
                        {isImporting
                          ? "Importing..."
                          : `Import Data${
                              importData.length > 0
                                ? ` (${importData.length} Records)`
                                : ""
                            }`}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </FocusModal.Body>
        </FocusModal.Content>
      </FocusModal>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Products & Services",
  icon: Package,
});

export default ProductsServicesPage;
