import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import { Edit, Trash2, Eye, MapPin, Calendar, Users } from "lucide-react";
import OutlineButton from "../../../components/shared/OutlineButton";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Badge,
  FocusModal,
  Toaster,
  toast,
  Table,
  DropdownMenu,
  IconButton,
  Select,
} from "@camped-ai/ui";
import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
// import ItineraryForm from "../../../components/fulfillment/itinerary-form";

// Simple inline itinerary form component
const ItineraryForm = ({ formData, setFormData, onSubmit, closeModal, isEditing = false }: any) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <Heading level="h2" className="text-xl font-semibold">
          {isEditing ? "Edit Itinerary" : "Create New Itinerary"}
        </Heading>
        <Button variant="secondary" onClick={closeModal}>
          Close
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="title">Itinerary Title *</Label>
          <Input
            id="title"
            placeholder="Enter itinerary title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            required
          />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Input
            id="description"
            placeholder="Itinerary description"
            value={formData.description || ""}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="booking_id">Booking ID *</Label>
            <Input
              id="booking_id"
              placeholder="Enter booking ID"
              value={formData.booking_id}
              onChange={(e) => setFormData({ ...formData, booking_id: e.target.value })}
              required
            />
          </div>

          <div>
            <Label htmlFor="customer_id">Customer ID *</Label>
            <Input
              id="customer_id"
              placeholder="Enter customer ID"
              value={formData.customer_id}
              onChange={(e) => setFormData({ ...formData, customer_id: e.target.value })}
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="destination">Destination *</Label>
            <Input
              id="destination"
              placeholder="Enter destination"
              value={formData.destination}
              onChange={(e) => setFormData({ ...formData, destination: e.target.value })}
              required
            />
          </div>

          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData({ ...formData, status: value })}
            >
              <Select.Item value="draft">Draft</Select.Item>
              <Select.Item value="pending_approval">Pending Approval</Select.Item>
              <Select.Item value="approved">Approved</Select.Item>
              <Select.Item value="in_progress">In Progress</Select.Item>
              <Select.Item value="completed">Completed</Select.Item>
              <Select.Item value="cancelled">Cancelled</Select.Item>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="start_date">Start Date *</Label>
            <Input
              id="start_date"
              type="date"
              value={formData.start_date}
              onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
              required
            />
          </div>

          <div>
            <Label htmlFor="end_date">End Date *</Label>
            <Input
              id="end_date"
              type="date"
              value={formData.end_date}
              onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
              required
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button type="button" variant="secondary" onClick={closeModal}>
            Cancel
          </Button>
          <Button type="submit">
            {isEditing ? "Update Itinerary" : "Create Itinerary"}
          </Button>
        </div>
      </form>
    </div>
  );
};
import Spinner from "../../../components/shared/spinner";

// Types
interface Itinerary {
  id: string;
  title: string;
  description?: string;
  booking_id: string;
  customer_id: string;
  status: "draft" | "pending_approval" | "approved" | "in_progress" | "completed" | "cancelled";
  start_date: string;
  end_date: string;
  destination: string;
  total_cost?: number;
  currency?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ItineraryFormData {
  title: string;
  description?: string;
  booking_id: string;
  customer_id: string;
  status: "draft" | "pending_approval" | "approved" | "in_progress" | "completed" | "cancelled";
  start_date: string;
  end_date: string;
  destination: string;
  total_cost?: number;
  currency?: string;
  metadata?: Record<string, any>;
}

// Status badge colors
const statusColors = {
  draft: "gray",
  pending_approval: "yellow",
  approved: "green",
  in_progress: "blue",
  completed: "green",
  cancelled: "red",
};

const ItinerariesPage = () => {
  const navigate = useNavigate();
  const [itineraries, setItineraries] = useState<Itinerary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [open, setOpen] = useState(false);
  const [editingItinerary, setEditingItinerary] = useState<Itinerary | null>(null);
  const [formData, setFormData] = useState<ItineraryFormData>({
    title: "",
    description: "",
    booking_id: "",
    customer_id: "",
    status: "draft",
    start_date: "",
    end_date: "",
    destination: "",
    total_cost: 0,
    currency: "USD",
    metadata: {},
  });

  // Fetch itineraries
  const fetchItineraries = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (searchQuery) params.append("search", searchQuery);
      if (statusFilter) params.append("status", statusFilter);
      
      const url = `/admin/fulfillment-operations/itineraries${params.toString() ? `?${params.toString()}` : ""}`;
      
      const response = await fetch(url, {
        credentials: "include",
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch itineraries: ${response.status}`);
      }
      
      const data = await response.json();
      setItineraries(data.itineraries || []);
    } catch (error) {
      console.error("Error fetching itineraries:", error);
      toast.error("Failed to fetch itineraries");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchItineraries();
  }, [searchQuery, statusFilter]);

  // Handle create itinerary
  const handleCreate = async (data: ItineraryFormData) => {
    try {
      const response = await fetch("/admin/fulfillment-operations/itineraries", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to create itinerary: ${response.status}`);
      }

      toast.success("Itinerary created successfully");
      setOpen(false);
      fetchItineraries();
      
      // Reset form
      setFormData({
        title: "",
        description: "",
        booking_id: "",
        customer_id: "",
        status: "draft",
        start_date: "",
        end_date: "",
        destination: "",
        total_cost: 0,
        currency: "USD",
        metadata: {},
      });
    } catch (error) {
      console.error("Error creating itinerary:", error);
      toast.error("Failed to create itinerary");
    }
  };

  // Handle edit itinerary
  const handleEdit = (itinerary: Itinerary) => {
    setEditingItinerary(itinerary);
    setFormData({
      title: itinerary.title,
      description: itinerary.description || "",
      booking_id: itinerary.booking_id,
      customer_id: itinerary.customer_id,
      status: itinerary.status,
      start_date: itinerary.start_date,
      end_date: itinerary.end_date,
      destination: itinerary.destination,
      total_cost: itinerary.total_cost || 0,
      currency: itinerary.currency || "USD",
      metadata: itinerary.metadata || {},
    });
    setOpen(true);
  };

  // Handle update itinerary
  const handleUpdate = async (data: ItineraryFormData) => {
    if (!editingItinerary) return;
    
    try {
      const response = await fetch(`/admin/fulfillment-operations/itineraries/${editingItinerary.id}`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to update itinerary: ${response.status}`);
      }

      toast.success("Itinerary updated successfully");
      setOpen(false);
      setEditingItinerary(null);
      fetchItineraries();
    } catch (error) {
      console.error("Error updating itinerary:", error);
      toast.error("Failed to update itinerary");
    }
  };

  // Handle delete itinerary
  const handleDelete = async (itineraryId: string) => {
    if (!confirm("Are you sure you want to delete this itinerary?")) return;
    
    try {
      const response = await fetch(`/admin/fulfillment-operations/itineraries/${itineraryId}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to delete itinerary: ${response.status}`);
      }

      toast.success("Itinerary deleted successfully");
      fetchItineraries();
    } catch (error) {
      console.error("Error deleting itinerary:", error);
      toast.error("Failed to delete itinerary");
    }
  };

  // Handle view itinerary details
  const handleViewDetails = (itineraryId: string) => {
    navigate(`/fulfillment-operations/itineraries/${itineraryId}`);
  };

  // Filter itineraries based on search query
  const filteredItineraries = useMemo(() => {
    return itineraries.filter((itinerary) =>
      itinerary.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      itinerary.destination.toLowerCase().includes(searchQuery.toLowerCase()) ||
      itinerary.booking_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      itinerary.customer_id.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [itineraries, searchQuery]);

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      
      {/* Create/Edit Itinerary Modal */}
      <FocusModal open={open} onOpenChange={setOpen}>
        <FocusModal.Content className="w-full h-full">
          <ItineraryForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={editingItinerary ? handleUpdate : handleCreate}
            closeModal={() => {
              setOpen(false);
              setEditingItinerary(null);
            }}
            isEditing={!!editingItinerary}
          />
        </FocusModal.Content>
      </FocusModal>

      <Container>
        <div className="flex items-center justify-between mb-6">
          <div>
            <Heading level="h1" className="text-2xl font-semibold text-gray-900">
              Itinerary Management
            </Heading>
            <Text className="text-gray-600 mt-1">
              Create and manage customer itineraries and travel plans
            </Text>
          </div>
          <Button
            onClick={() => {
              setEditingItinerary(null);
              setFormData({
                title: "",
                description: "",
                booking_id: "",
                customer_id: "",
                status: "draft",
                start_date: "",
                end_date: "",
                destination: "",
                total_cost: 0,
                currency: "USD",
                metadata: {},
              });
              setOpen(true);
            }}
            className="flex items-center gap-2"
          >
            <PlusMini />
            Create Itinerary
          </Button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Search itineraries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
            placeholder="Filter by status"
          >
            <Select.Item value="">All Statuses</Select.Item>
            <Select.Item value="draft">Draft</Select.Item>
            <Select.Item value="pending_approval">Pending Approval</Select.Item>
            <Select.Item value="approved">Approved</Select.Item>
            <Select.Item value="in_progress">In Progress</Select.Item>
            <Select.Item value="completed">Completed</Select.Item>
            <Select.Item value="cancelled">Cancelled</Select.Item>
          </Select>
        </div>

        {/* Itineraries Table */}
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Spinner size="medium" />
            <div className="ml-4">Loading itineraries...</div>
          </div>
        ) : (
          <div className="bg-white rounded-md shadow-sm overflow-hidden">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Itinerary</Table.HeaderCell>
                  <Table.HeaderCell>Destination</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Dates</Table.HeaderCell>
                  <Table.HeaderCell>Customer</Table.HeaderCell>
                  <Table.HeaderCell>Total Cost</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {filteredItineraries.length === 0 ? (
                  <Table.Row>
                    <Table.Cell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center">
                        <MapPin className="h-12 w-12 text-gray-400 mb-4" />
                        <Text className="text-gray-500">No itineraries found</Text>
                        <Text className="text-gray-400 text-sm">
                          {searchQuery || statusFilter
                            ? "Try adjusting your filters"
                            : "Get started by creating your first itinerary"}
                        </Text>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  filteredItineraries.map((itinerary) => (
                    <Table.Row key={itinerary.id} className="hover:bg-gray-50">
                      <Table.Cell>
                        <div>
                          <Text className="font-medium">{itinerary.title}</Text>
                          <Text className="text-sm text-gray-500">#{itinerary.booking_id}</Text>
                          {itinerary.description && (
                            <Text className="text-sm text-gray-500 truncate max-w-xs">
                              {itinerary.description}
                            </Text>
                          )}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <Text>{itinerary.destination}</Text>
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color={statusColors[itinerary.status]}>
                          {itinerary.status.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <div>
                            <Text className="text-sm">
                              {new Date(itinerary.start_date).toLocaleDateString()}
                            </Text>
                            <Text className="text-xs text-gray-500">
                              to {new Date(itinerary.end_date).toLocaleDateString()}
                            </Text>
                          </div>
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4 text-gray-400" />
                          <Text className="text-sm">{itinerary.customer_id}</Text>
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        {itinerary.total_cost ? (
                          <Text className="font-medium">
                            {itinerary.currency} {itinerary.total_cost.toLocaleString()}
                          </Text>
                        ) : (
                          <Text className="text-gray-400">-</Text>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        <DropdownMenu>
                          <DropdownMenu.Trigger asChild>
                            <IconButton>
                              <Adjustments />
                            </IconButton>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <DropdownMenu.Item onClick={() => handleViewDetails(itinerary.id)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenu.Item>
                            <DropdownMenu.Item onClick={() => handleEdit(itinerary)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenu.Item>
                            <DropdownMenu.Separator />
                            <DropdownMenu.Item
                              onClick={() => handleDelete(itinerary.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenu.Item>
                          </DropdownMenu.Content>
                        </DropdownMenu>
                      </Table.Cell>
                    </Table.Row>
                  ))
                )}
              </Table.Body>
            </Table>
          </div>
        )}
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Itineraries",
});

export default ItinerariesPage;
