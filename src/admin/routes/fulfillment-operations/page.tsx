import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Container,
  Heading,
  Text,
  Button,
} from "@camped-ai/ui";
import { Clock, MapPin, Users, Settings, ArrowRight, CheckCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import ErrorBoundary from "../../components/shared/error-boundary";

const FulfillmentOperationsPage = () => {
  const navigate = useNavigate();

  const quickActions = [
    {
      title: "Task Management",
      description: "Manage fulfillment tasks and operational workflows",
      icon: Clock,
      href: "/fulfillment-operations/tasks",
      color: "blue",
    },
    {
      title: "Itinerary Builder",
      description: "Create and manage customer itineraries and travel plans",
      icon: MapPin,
      href: "/fulfillment-operations/itineraries",
      color: "green",
    },
    {
      title: "Booking Assignments",
      description: "Assign bookings to vendors and team members",
      icon: Users,
      href: "/fulfillment-operations/assignments",
      color: "purple",
      disabled: true, // Will be implemented later
    },
    {
      title: "Workflow Management",
      description: "Configure and manage operational workflows",
      icon: Settings,
      href: "/fulfillment-operations/workflows",
      color: "orange",
      disabled: true, // Will be implemented later
    },
  ];

  const taskStats = [
    {
      label: "Pending Tasks",
      value: "-",
      icon: Clock,
      color: "yellow",
    },
    {
      label: "In Progress",
      value: "-",
      icon: Settings,
      color: "blue",
    },
    {
      label: "Completed Today",
      value: "-",
      icon: CheckCircle,
      color: "green",
    },
  ];

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <ErrorBoundary>
          <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Clock className="h-8 w-8 text-blue-600" />
              <Heading level="h1" className="text-3xl font-bold text-gray-900">
                Fulfillment Operations
              </Heading>
            </div>
            <Text className="text-lg text-gray-600">
              Manage tasks, itineraries, and operational workflows
            </Text>
          </div>

          {/* Task Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {taskStats.map((stat) => {
              const IconComponent = stat.icon;
              return (
                <Container key={stat.label} className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Text className="text-sm font-medium text-gray-500">{stat.label}</Text>
                      <Heading level="h2" className="text-2xl font-bold text-gray-900">
                        {stat.value}
                      </Heading>
                    </div>
                    <div
                      className={`p-2 rounded-full ${
                        stat.color === "yellow"
                          ? "bg-yellow-100 text-yellow-600"
                          : stat.color === "blue"
                          ? "bg-blue-100 text-blue-600"
                          : "bg-green-100 text-green-600"
                      }`}
                    >
                      <IconComponent className="h-6 w-6" />
                    </div>
                  </div>
                </Container>
              );
            })}
          </div>

          {/* Quick Actions Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action) => {
              const IconComponent = action.icon;
              return (
                <Container
                  key={action.title}
                  className={`p-6 hover:shadow-lg transition-shadow cursor-pointer ${
                    action.disabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  onClick={() => {
                    if (!action.disabled) {
                      navigate(action.href);
                    }
                  }}
                >
                  <div className="flex flex-col items-center text-center space-y-4">
                    <div
                      className={`p-3 rounded-full ${
                        action.color === "blue"
                          ? "bg-blue-100 text-blue-600"
                          : action.color === "green"
                          ? "bg-green-100 text-green-600"
                          : action.color === "purple"
                          ? "bg-purple-100 text-purple-600"
                          : "bg-orange-100 text-orange-600"
                      }`}
                    >
                      <IconComponent className="h-8 w-8" />
                    </div>
                    <div>
                      <Heading level="h3" className="text-lg font-semibold mb-2">
                        {action.title}
                      </Heading>
                      <Text className="text-sm text-gray-600">
                        {action.description}
                      </Text>
                    </div>
                    {!action.disabled && (
                      <div className="flex items-center text-blue-600 text-sm font-medium">
                        Get Started
                        <ArrowRight className="ml-1 h-4 w-4" />
                      </div>
                    )}
                    {action.disabled && (
                      <div className="text-xs text-gray-400">
                        Coming Soon
                      </div>
                    )}
                  </div>
                </Container>
              );
            })}
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Tasks */}
            <Container className="p-6">
              <div className="flex items-center justify-between mb-4">
                <Heading level="h3" className="text-lg font-semibold">
                  Recent Tasks
                </Heading>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => navigate("/fulfillment-operations/tasks")}
                >
                  View All
                </Button>
              </div>
              <div className="space-y-3">
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <Text className="text-gray-500">No recent tasks</Text>
                  <Button
                    size="small"
                    className="mt-2"
                    onClick={() => navigate("/fulfillment-operations/tasks")}
                  >
                    Create First Task
                  </Button>
                </div>
              </div>
            </Container>

            {/* Recent Itineraries */}
            <Container className="p-6">
              <div className="flex items-center justify-between mb-4">
                <Heading level="h3" className="text-lg font-semibold">
                  Recent Itineraries
                </Heading>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => navigate("/fulfillment-operations/itineraries")}
                >
                  View All
                </Button>
              </div>
              <div className="space-y-3">
                <div className="text-center py-8">
                  <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <Text className="text-gray-500">No recent itineraries</Text>
                  <Button
                    size="small"
                    className="mt-2"
                    onClick={() => navigate("/fulfillment-operations/itineraries")}
                  >
                    Create First Itinerary
                  </Button>
                </div>
              </div>
            </Container>
          </div>

          {/* Getting Started */}
          <Container className="p-6">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <Heading level="h3" className="text-lg font-semibold mb-2">
                  Getting Started with Fulfillment Operations
                </Heading>
                <Text className="text-gray-600 mb-4">
                  Start by creating your first task or itinerary to begin managing your fulfillment operations.
                </Text>
                <div className="flex gap-3">
                  <Button
                    onClick={() => navigate("/fulfillment-operations/tasks")}
                    className="flex items-center gap-2"
                  >
                    <Clock className="h-4 w-4" />
                    Manage Tasks
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => navigate("/fulfillment-operations/itineraries")}
                    className="flex items-center gap-2"
                  >
                    <MapPin className="h-4 w-4" />
                    Create Itinerary
                  </Button>
                </div>
              </div>
            </div>
          </Container>
          </div>
        </ErrorBoundary>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Fulfillment Operations",
  icon: Clock,
});

export default FulfillmentOperationsPage;
