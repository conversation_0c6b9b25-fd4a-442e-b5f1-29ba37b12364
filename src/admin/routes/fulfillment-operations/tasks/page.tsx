import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import { Edit, Trash2, Eye, CheckCircle, Clock, AlertTriangle, User } from "lucide-react";
import OutlineButton from "../../../components/shared/OutlineButton";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Badge,
  FocusModal,
  Toaster,
  toast,
  Table,
  DropdownMenu,
  IconButton,
  Select,
} from "@camped-ai/ui";
import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
// import TaskForm from "../../../components/fulfillment/task-form";

// Simple inline task form component
const TaskForm = ({ formData, setFormData, onSubmit, closeModal, isEditing = false }: any) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <Heading level="h2" className="text-xl font-semibold">
          {isEditing ? "Edit Task" : "Create New Task"}
        </Heading>
        <Button variant="secondary" onClick={closeModal}>
          Close
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="title">Task Title *</Label>
          <Input
            id="title"
            placeholder="Enter task title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            required
          />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Input
            id="description"
            placeholder="Task description"
            value={formData.description || ""}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="type">Type</Label>
            <Select
              value={formData.type}
              onValueChange={(value) => setFormData({ ...formData, type: value })}
            >
              <Select.Item value="booking_confirmation">Booking Confirmation</Select.Item>
              <Select.Item value="vendor_assignment">Vendor Assignment</Select.Item>
              <Select.Item value="itinerary_creation">Itinerary Creation</Select.Item>
              <Select.Item value="customer_communication">Customer Communication</Select.Item>
              <Select.Item value="payment_processing">Payment Processing</Select.Item>
              <Select.Item value="document_generation">Document Generation</Select.Item>
              <Select.Item value="quality_check">Quality Check</Select.Item>
              <Select.Item value="follow_up">Follow Up</Select.Item>
            </Select>
          </div>

          <div>
            <Label htmlFor="priority">Priority</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData({ ...formData, priority: value })}
            >
              <Select.Item value="low">Low</Select.Item>
              <Select.Item value="medium">Medium</Select.Item>
              <Select.Item value="high">High</Select.Item>
              <Select.Item value="urgent">Urgent</Select.Item>
            </Select>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button type="button" variant="secondary" onClick={closeModal}>
            Cancel
          </Button>
          <Button type="submit">
            {isEditing ? "Update Task" : "Create Task"}
          </Button>
        </div>
      </form>
    </div>
  );
};
import Spinner from "../../../components/shared/spinner";

// Types
interface FulfillmentTask {
  id: string;
  title: string;
  description?: string;
  type: "booking_confirmation" | "vendor_assignment" | "itinerary_creation" | "customer_communication" | "payment_processing" | "document_generation" | "quality_check" | "follow_up";
  status: "pending" | "in_progress" | "completed" | "failed" | "cancelled" | "on_hold";
  priority: "low" | "medium" | "high" | "urgent";
  assigned_to?: string;
  booking_id?: string;
  vendor_id?: string;
  due_date?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface TaskFormData {
  title: string;
  description?: string;
  type: "booking_confirmation" | "vendor_assignment" | "itinerary_creation" | "customer_communication" | "payment_processing" | "document_generation" | "quality_check" | "follow_up";
  status: "pending" | "in_progress" | "completed" | "failed" | "cancelled" | "on_hold";
  priority: "low" | "medium" | "high" | "urgent";
  assigned_to?: string;
  booking_id?: string;
  vendor_id?: string;
  due_date?: string;
  metadata?: Record<string, any>;
}

// Status badge colors
const statusColors = {
  pending: "yellow",
  in_progress: "blue",
  completed: "green",
  failed: "red",
  cancelled: "gray",
  on_hold: "orange",
};

// Priority badge colors
const priorityColors = {
  low: "gray",
  medium: "blue",
  high: "orange",
  urgent: "red",
};

// Type badge colors
const typeColors = {
  booking_confirmation: "green",
  vendor_assignment: "blue",
  itinerary_creation: "purple",
  customer_communication: "cyan",
  payment_processing: "yellow",
  document_generation: "pink",
  quality_check: "orange",
  follow_up: "indigo",
};

const TasksPage = () => {
  const navigate = useNavigate();
  const [tasks, setTasks] = useState<FulfillmentTask[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [priorityFilter, setPriorityFilter] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [open, setOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<FulfillmentTask | null>(null);
  const [formData, setFormData] = useState<TaskFormData>({
    title: "",
    description: "",
    type: "booking_confirmation",
    status: "pending",
    priority: "medium",
    assigned_to: "",
    booking_id: "",
    vendor_id: "",
    due_date: "",
    metadata: {},
  });

  // Fetch tasks
  const fetchTasks = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (searchQuery) params.append("search", searchQuery);
      if (statusFilter) params.append("status", statusFilter);
      if (priorityFilter) params.append("priority", priorityFilter);
      if (typeFilter) params.append("type", typeFilter);
      
      const url = `/admin/fulfillment-operations/tasks${params.toString() ? `?${params.toString()}` : ""}`;
      
      const response = await fetch(url, {
        credentials: "include",
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch tasks: ${response.status}`);
      }
      
      const data = await response.json();
      setTasks(data.tasks || []);
    } catch (error) {
      console.error("Error fetching tasks:", error);
      toast.error("Failed to fetch tasks");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, [searchQuery, statusFilter, priorityFilter, typeFilter]);

  // Handle create task
  const handleCreate = async (data: TaskFormData) => {
    try {
      const response = await fetch("/admin/fulfillment-operations/tasks", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to create task: ${response.status}`);
      }

      toast.success("Task created successfully");
      setOpen(false);
      fetchTasks();
      
      // Reset form
      setFormData({
        title: "",
        description: "",
        type: "booking_confirmation",
        status: "pending",
        priority: "medium",
        assigned_to: "",
        booking_id: "",
        vendor_id: "",
        due_date: "",
        metadata: {},
      });
    } catch (error) {
      console.error("Error creating task:", error);
      toast.error("Failed to create task");
    }
  };

  // Handle edit task
  const handleEdit = (task: FulfillmentTask) => {
    setEditingTask(task);
    setFormData({
      title: task.title,
      description: task.description || "",
      type: task.type,
      status: task.status,
      priority: task.priority,
      assigned_to: task.assigned_to || "",
      booking_id: task.booking_id || "",
      vendor_id: task.vendor_id || "",
      due_date: task.due_date || "",
      metadata: task.metadata || {},
    });
    setOpen(true);
  };

  // Handle update task
  const handleUpdate = async (data: TaskFormData) => {
    if (!editingTask) return;
    
    try {
      const response = await fetch(`/admin/fulfillment-operations/tasks/${editingTask.id}`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to update task: ${response.status}`);
      }

      toast.success("Task updated successfully");
      setOpen(false);
      setEditingTask(null);
      fetchTasks();
    } catch (error) {
      console.error("Error updating task:", error);
      toast.error("Failed to update task");
    }
  };

  // Handle delete task
  const handleDelete = async (taskId: string) => {
    if (!confirm("Are you sure you want to delete this task?")) return;
    
    try {
      const response = await fetch(`/admin/fulfillment-operations/tasks/${taskId}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to delete task: ${response.status}`);
      }

      toast.success("Task deleted successfully");
      fetchTasks();
    } catch (error) {
      console.error("Error deleting task:", error);
      toast.error("Failed to delete task");
    }
  };

  // Handle view task details
  const handleViewDetails = (taskId: string) => {
    navigate(`/fulfillment-operations/tasks/${taskId}`);
  };

  // Handle quick status update
  const handleStatusUpdate = async (taskId: string, newStatus: string) => {
    try {
      const response = await fetch(`/admin/fulfillment-operations/tasks/${taskId}`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update task status: ${response.status}`);
      }

      toast.success("Task status updated");
      fetchTasks();
    } catch (error) {
      console.error("Error updating task status:", error);
      toast.error("Failed to update task status");
    }
  };

  // Filter tasks based on search query
  const filteredTasks = useMemo(() => {
    return tasks.filter((task) =>
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.status.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [tasks, searchQuery]);

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "in_progress":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "failed":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      
      {/* Create/Edit Task Modal */}
      <FocusModal open={open} onOpenChange={setOpen}>
        <FocusModal.Content className="w-full h-full">
          <TaskForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={editingTask ? handleUpdate : handleCreate}
            closeModal={() => {
              setOpen(false);
              setEditingTask(null);
            }}
            isEditing={!!editingTask}
          />
        </FocusModal.Content>
      </FocusModal>

      <Container>
        <div className="flex items-center justify-between mb-6">
          <div>
            <Heading level="h1" className="text-2xl font-semibold text-gray-900">
              Task Management
            </Heading>
            <Text className="text-gray-600 mt-1">
              Manage fulfillment tasks and operational workflows
            </Text>
          </div>
          <Button
            onClick={() => {
              setEditingTask(null);
              setFormData({
                title: "",
                description: "",
                type: "booking_confirmation",
                status: "pending",
                priority: "medium",
                assigned_to: "",
                booking_id: "",
                vendor_id: "",
                due_date: "",
                metadata: {},
              });
              setOpen(true);
            }}
            className="flex items-center gap-2"
          >
            <PlusMini />
            Add Task
          </Button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
            placeholder="Filter by status"
          >
            <Select.Item value="">All Statuses</Select.Item>
            <Select.Item value="pending">Pending</Select.Item>
            <Select.Item value="in_progress">In Progress</Select.Item>
            <Select.Item value="completed">Completed</Select.Item>
            <Select.Item value="failed">Failed</Select.Item>
            <Select.Item value="cancelled">Cancelled</Select.Item>
            <Select.Item value="on_hold">On Hold</Select.Item>
          </Select>
          <Select
            value={priorityFilter}
            onValueChange={setPriorityFilter}
            placeholder="Filter by priority"
          >
            <Select.Item value="">All Priorities</Select.Item>
            <Select.Item value="low">Low</Select.Item>
            <Select.Item value="medium">Medium</Select.Item>
            <Select.Item value="high">High</Select.Item>
            <Select.Item value="urgent">Urgent</Select.Item>
          </Select>
          <Select
            value={typeFilter}
            onValueChange={setTypeFilter}
            placeholder="Filter by type"
          >
            <Select.Item value="">All Types</Select.Item>
            <Select.Item value="booking_confirmation">Booking Confirmation</Select.Item>
            <Select.Item value="vendor_assignment">Vendor Assignment</Select.Item>
            <Select.Item value="itinerary_creation">Itinerary Creation</Select.Item>
            <Select.Item value="customer_communication">Customer Communication</Select.Item>
            <Select.Item value="payment_processing">Payment Processing</Select.Item>
            <Select.Item value="document_generation">Document Generation</Select.Item>
            <Select.Item value="quality_check">Quality Check</Select.Item>
            <Select.Item value="follow_up">Follow Up</Select.Item>
          </Select>
        </div>

        {/* Tasks Table */}
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Spinner size="medium" />
            <div className="ml-4">Loading tasks...</div>
          </div>
        ) : (
          <div className="bg-white rounded-md shadow-sm overflow-hidden">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Task</Table.HeaderCell>
                  <Table.HeaderCell>Type</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Priority</Table.HeaderCell>
                  <Table.HeaderCell>Assigned To</Table.HeaderCell>
                  <Table.HeaderCell>Due Date</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {filteredTasks.length === 0 ? (
                  <Table.Row>
                    <Table.Cell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center">
                        <Clock className="h-12 w-12 text-gray-400 mb-4" />
                        <Text className="text-gray-500">No tasks found</Text>
                        <Text className="text-gray-400 text-sm">
                          {searchQuery || statusFilter || priorityFilter || typeFilter
                            ? "Try adjusting your filters"
                            : "Get started by creating your first task"}
                        </Text>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  filteredTasks.map((task) => (
                    <Table.Row key={task.id} className="hover:bg-gray-50">
                      <Table.Cell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(task.status)}
                          <div>
                            <Text className="font-medium">{task.title}</Text>
                            {task.description && (
                              <Text className="text-sm text-gray-500 truncate max-w-xs">
                                {task.description}
                              </Text>
                            )}
                          </div>
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color={typeColors[task.type]}>
                          {task.type.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color={statusColors[task.status]}>
                          {task.status.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color={priorityColors[task.priority]}>
                          {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        {task.assigned_to ? (
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4 text-gray-400" />
                            <Text className="text-sm">{task.assigned_to}</Text>
                          </div>
                        ) : (
                          <Text className="text-gray-400">Unassigned</Text>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {task.due_date ? (
                          <Text className="text-sm">
                            {new Date(task.due_date).toLocaleDateString()}
                          </Text>
                        ) : (
                          <Text className="text-gray-400">-</Text>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        <DropdownMenu>
                          <DropdownMenu.Trigger asChild>
                            <IconButton>
                              <Adjustments />
                            </IconButton>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <DropdownMenu.Item onClick={() => handleViewDetails(task.id)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenu.Item>
                            <DropdownMenu.Item onClick={() => handleEdit(task)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenu.Item>
                            {task.status !== "completed" && (
                              <DropdownMenu.Item onClick={() => handleStatusUpdate(task.id, "completed")}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Mark Complete
                              </DropdownMenu.Item>
                            )}
                            <DropdownMenu.Separator />
                            <DropdownMenu.Item
                              onClick={() => handleDelete(task.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenu.Item>
                          </DropdownMenu.Content>
                        </DropdownMenu>
                      </Table.Cell>
                    </Table.Row>
                  ))
                )}
              </Table.Body>
            </Table>
          </div>
        )}
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Tasks",
});

export default TasksPage;
