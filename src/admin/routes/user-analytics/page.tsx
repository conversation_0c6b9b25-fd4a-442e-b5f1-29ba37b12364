import { defineRouteConfig } from "@camped-ai/admin-sdk";
import Geo<PERSON>hart from "../../components/GeoChart";
import UserAcquisition<PERSON>hart from "../../components/session";
import { Container, Heading, Select } from "@camped-ai/ui";
import PageView from "../../components/PageView";
import AnalyticsCard from "../../components/AnalyticsCard";
import DevicePieChart from "../../components/DevicePieChart";
import BrowserBarChart from "../../components/BrowserChart";
import CityProgressChart from "../../components/CityCard";
import { UsersSolid } from "@camped-ai/icons";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";

function App() {
  return (
    <>
      <PermissionBasedSidebarHider />
      <div className="min-h-screen p-6">
        <div className="max-w-6xl mx-auto space-y-6">
          <div className="grid grid-cols-5 gap-6">
            <div className="col-span-3 flex flex-col h-full">
              <Container className="h-full">
                <AnalyticsCard />
              </Container>
            </div>
            <div className="col-span-2 flex flex-col h-full">
              <Container className="h-full">
                <PageView />
              </Container>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Container className="rounded-lg p-6 h-full flex flex-col ">
              <GeoChart />
            </Container>
            <Container className="rounded-lg p-6 h-full flex flex-col">
              <UserAcquisitionChart />
            </Container>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-[4fr_3fr_3fr] gap-6">
            <Container className=" p-6 h-full flex flex-col ">
              <BrowserBarChart />
            </Container>
            <Container className="p-6 h-full flex flex-col">
              <DevicePieChart />
            </Container>
            <Container className="p-6 h-full flex flex-col">
              <CityProgressChart />
            </Container>
          </div>
        </div>
      </div>
    </>
  );
}

export default App;

export const config = defineRouteConfig({
  label: "User Analytics",
  icon: UsersSolid,
});
