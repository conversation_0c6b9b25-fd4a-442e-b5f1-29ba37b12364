import { defineRouteConfig } from "@camped-ai/admin-sdk";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";

const SettingsPage = () => {
  return (
    <>
      <PermissionBasedSidebarHider />
      {/* The actual settings content will be rendered by Medusa's default settings page */}
    </>
  );
};

export const config = defineRouteConfig({
  label: "Settings",
});

export default SettingsPage;
