import React, { useState } from "react";
import {
  Container,
  <PERSON>ing,
  Text,
  Button,
  Input,
  Select,
  FocusModal,
  Toaster,
  toast,
  Badge,
  IconButton,
} from "@camped-ai/ui";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../../lib/sdk";
import { Link, useNavigate } from "react-router-dom";
import {
  Plus,
  Envelope,
  GridList,
  ListBullet,
  Eye,
  Pencil,
} from "@camped-ai/icons";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import NotificationTemplateForm from "../../../components/notification-template-form-simple";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";

const NotificationTemplatesPage = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [channelFilter, setChannelFilter] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"table" | "card">("table");

  // Fetch notification templates
  const { data: templates, isLoading } = useQuery({
    queryKey: ["notification-templates"],
    queryFn: async () => {
      const response = await sdk.client.fetch("/admin/notification-templates");
      return response.notification_templates;
    },
  });

  // Group templates by event_name
  const groupedTemplates = templates
    ? templates.reduce((acc, template) => {
        // Create a unique key for each event
        const eventKey = template.event_name;

        // If this event doesn't exist in our accumulator yet, create it
        if (!acc[eventKey]) {
          acc[eventKey] = {
            event_name: template.event_name,
            channels: {},
            // Track if any channel is active
            has_active_channel: false,
          };
        }

        // Add this template to the appropriate channel
        acc[eventKey].channels[template.channel] = template;

        // Update active status if any channel is active
        if (template.is_active) {
          acc[eventKey].has_active_channel = true;
        }

        return acc;
      }, {})
    : {};

  // Convert grouped templates to array and filter
  const filteredTemplates = Object.values(groupedTemplates).filter(
    (group: any) => {
      const matchesSearch =
        !searchQuery ||
        group.event_name.toLowerCase().includes(searchQuery.toLowerCase());

      // If channel filter is applied, check if the event has that channel
      const matchesChannel =
        !channelFilter || (group.channels && group.channels[channelFilter]);

      return matchesSearch && matchesChannel;
    }
  );

  // Get unique channels for filter dropdown
  const channels = templates
    ? [...new Set(templates.map((template) => template.channel))]
    : [];

  // Form submission handling removed

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="bg-gradient-to-b from-white to-ui-bg-subtle bg-opacity-30 py-6 rounded-lg">
        <Toaster />
        <div className="mb-6">
          <Heading level="h1">Notification Templates</Heading>
          <Text className="text-ui-fg-subtle">
            Manage notification templates for different events and channels
          </Text>
        </div>

        <div className="flex flex-wrap gap-4 mb-6 items-center bg-ui-bg-subtle p-4 rounded-lg shadow-sm">
          <div className="flex-1 min-w-[250px]">
            <div className="relative">
              <Input
                placeholder="Search by event name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 bg-white shadow-sm"
              />
              <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-ui-fg-subtle">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div className="w-64">
            <Select
              value={channelFilter || "all"}
              onValueChange={(value) =>
                setChannelFilter(value === "all" ? null : value)
              }
            >
              <Select.Content>
                <Select.Item value="all">All Channels</Select.Item>
                {channels.map((channel) => (
                  <Select.Item key={channel} value={channel}>
                    <div className="flex items-center">
                      <div
                        className={`w-2 h-2 rounded-full mr-2 ${
                          channel === "email"
                            ? "bg-ui-tag-blue-bg"
                            : "bg-ui-tag-green-bg"
                        }`}
                      ></div>
                      {channel.charAt(0).toUpperCase() + channel.slice(1)}
                    </div>
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
          </div>
          <div className="ml-auto flex border rounded-md overflow-hidden shadow-sm">
            <Button
              variant="transparent"
              size="small"
              onClick={() => setViewMode("table")}
              className={`rounded-none border-0 ${
                viewMode === "table"
                  ? "bg-ui-bg-interactive text-white"
                  : "hover:bg-ui-bg-base-hover"
              }`}
            >
              <ListBullet className="w-4 h-4" />
            </Button>
            <Button
              variant="transparent"
              size="small"
              onClick={() => setViewMode("card")}
              className={`rounded-none border-0 ${
                viewMode === "card"
                  ? "bg-ui-bg-interactive text-white"
                  : "hover:bg-ui-bg-base-hover"
              }`}
            >
              <GridList className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            Loading notification templates...
          </div>
        ) : filteredTemplates.length === 0 ? (
          <div className="text-center py-8 border rounded-lg">
            <Text className="text-ui-fg-subtle mb-4">
              No notification templates found
            </Text>
            <Button
              variant="secondary"
              onClick={() => setIsCreateModalOpen(true)}
            >
              Create your first template
            </Button>
          </div>
        ) : viewMode === "table" ? (
          <div className="border rounded-lg overflow-hidden shadow-sm">
            <table className="w-full">
              <thead className="bg-ui-bg-base-hover">
                <tr>
                  <th className="px-4 py-3 text-left font-medium text-ui-fg-subtle">
                    Event
                  </th>
                  <th className="px-4 py-3 text-left font-medium text-ui-fg-subtle">
                    Channels
                  </th>
                  <th className="px-4 py-3 text-left font-medium text-ui-fg-subtle">
                    Status
                  </th>
                  <th className="px-4 py-3 text-right font-medium text-ui-fg-subtle">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredTemplates.map((group: any, index: number) => (
                  <tr
                    key={group.event_name}
                    className={`border-t border-ui-border-base hover:bg-ui-bg-base-hover transition-colors duration-150 ${
                      index % 2 === 0
                        ? "bg-white"
                        : "bg-ui-bg-subtle bg-opacity-30"
                    }`}
                  >
                    <td className="px-4 py-3 font-normal">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full mr-2 bg-ui-tag-blue-bg"></div>
                        {group.event_name}
                      </div>
                    </td>
                    <td className="px-4 py-3 font-normal">
                      <div className="flex flex-wrap gap-1">
                        {Object.keys(group.channels).map((channel) => (
                          <span
                            key={channel}
                            className={`px-2 py-1 rounded-full text-xs ${
                              channel === "email"
                                ? "bg-ui-tag-blue-bg text-ui-tag-blue-text"
                                : "bg-ui-tag-green-bg text-ui-tag-green-text"
                            }`}
                          >
                            {channel.charAt(0).toUpperCase() + channel.slice(1)}
                            {group.channels[channel].is_active ? (
                              <span className="ml-1 inline-block w-1.5 h-1.5 rounded-full bg-ui-tag-green-text"></span>
                            ) : (
                              <span className="ml-1 inline-block w-1.5 h-1.5 rounded-full bg-ui-tag-red-text"></span>
                            )}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-4 py-3 font-normal">
                      {group.has_active_channel ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-ui-tag-green-bg text-ui-tag-green-text border border-ui-tag-green-border">
                          <span className="w-1.5 h-1.5 mr-1.5 rounded-full bg-ui-tag-green-text"></span>
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-ui-tag-red-bg text-ui-tag-red-text border border-ui-tag-red-border">
                          <span className="w-1.5 h-1.5 mr-1.5 rounded-full bg-ui-tag-red-text"></span>
                          Inactive
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-right">
                      <div className="flex justify-end gap-2">
                        {/* Use the first template ID for navigation */}
                        {Object.values(group.channels).length > 0 && (
                          <>
                            <IconButton
                              variant="transparent"
                              size="small"
                              className="hover:bg-ui-bg-base-hover rounded-full"
                              onClick={() =>
                                navigate(
                                  `/settings/notification-templates/${
                                    Object.values(group.channels)[0].id
                                  }`
                                )
                              }
                            >
                              <Eye className="text-ui-fg-subtle hover:text-ui-fg-base" />
                            </IconButton>
                            <IconButton
                              variant="transparent"
                              size="small"
                              className="hover:bg-ui-bg-base-hover rounded-full"
                              onClick={() =>
                                navigate(
                                  `/settings/notification-templates/${
                                    Object.values(group.channels)[0].id
                                  }?edit=true`
                                )
                              }
                            >
                              <Pencil className="text-ui-fg-subtle hover:text-ui-fg-base" />
                            </IconButton>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map((group: any) => (
              <div
                key={group.event_name}
                className={`border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer ${
                  group.has_active_channel
                    ? "border-l-4 border-l-ui-tag-green-border"
                    : "border-l-4 border-l-ui-tag-red-border"
                }`}
                onClick={() => {
                  // Navigate to the first template's detail page
                  if (Object.values(group.channels).length > 0) {
                    navigate(
                      `/settings/notification-templates/${
                        Object.values(group.channels)[0].id
                      }`
                    );
                  }
                }}
              >
                <div className="p-4 border-b border-ui-border-base bg-gradient-to-r from-white to-ui-bg-subtle bg-opacity-50">
                  <div className="flex justify-between items-start mb-2">
                    <div className="font-medium text-ui-fg-base">
                      {group.event_name}
                    </div>
                    <div className="flex gap-1">
                      {group.has_active_channel ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-ui-tag-green-bg text-ui-tag-green-text border border-ui-tag-green-border">
                          <span className="w-1.5 h-1.5 mr-1.5 rounded-full bg-ui-tag-green-text"></span>
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-ui-tag-red-bg text-ui-tag-red-text border border-ui-tag-red-border">
                          <span className="w-1.5 h-1.5 mr-1.5 rounded-full bg-ui-tag-red-text"></span>
                          Inactive
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-wrap items-center gap-1 mt-2">
                    {Object.keys(group.channels).map((channel) => (
                      <span
                        key={channel}
                        className={`px-2 py-1 rounded-full text-xs ${
                          channel === "email"
                            ? "bg-ui-tag-blue-bg text-ui-tag-blue-text"
                            : "bg-ui-tag-green-bg text-ui-tag-green-text"
                        }`}
                      >
                        {channel.charAt(0).toUpperCase() + channel.slice(1)}
                        {group.channels[channel].is_active ? (
                          <span className="ml-1 inline-block w-1.5 h-1.5 rounded-full bg-ui-tag-green-text"></span>
                        ) : (
                          <span className="ml-1 inline-block w-1.5 h-1.5 rounded-full bg-ui-tag-red-text"></span>
                        )}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="p-4 bg-white flex justify-between items-center">
                  <div className="text-ui-fg-subtle text-xs">
                    {/* Show the most recent update date */}
                    Updated:{" "}
                    {new Date(
                      Math.max(
                        ...Object.values(group.channels).map((t: any) =>
                          new Date(t.updated_at).getTime()
                        )
                      )
                    ).toLocaleDateString()}
                  </div>
                  <div className="flex gap-1">
                    {Object.values(group.channels).length > 0 && (
                      <>
                        <IconButton
                          variant="transparent"
                          size="small"
                          className="hover:bg-ui-bg-base-hover rounded-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(
                              `/settings/notification-templates/${
                                Object.values(group.channels)[0].id
                              }`
                            );
                          }}
                        >
                          <Eye className="text-ui-fg-subtle hover:text-ui-fg-base" />
                        </IconButton>
                        <IconButton
                          variant="transparent"
                          size="small"
                          className="hover:bg-ui-bg-base-hover rounded-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(
                              `/settings/notification-templates/${
                                Object.values(group.channels)[0].id
                              }?edit=true`
                            );
                          }}
                        >
                          <Pencil className="text-ui-fg-subtle hover:text-ui-fg-base" />
                        </IconButton>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Create Template modal removed */}
      </Container>
    </>
  );
};

export default NotificationTemplatesPage;

export const config = defineRouteConfig({
  label: "Notification Templates",
  icon: Envelope,
});
