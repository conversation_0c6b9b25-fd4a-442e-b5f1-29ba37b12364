// import { defineRouteConfig } from "@camped-ai/admin-sdk";
// import { Container, Heading } from "@camped-ai/ui";
// import CustomDataPage from "../../../../ui-components/common/components/ProductIndex";
// import HideSidebarItemsWidget from "../../../widgets/hide-sidebar-items-widget";
// import "../../../utils/navigation-aware-sidebar-hider";

// const CustomSettingPage = () => {
//   return (
//     <>
//       <HideSidebarItemsWidget />
//       <Container className="divide-y p-0">
//         <div>
//           <CustomDataPage />
//         </div>
//       </Container>
//     </>
//   );
// };

// export const config = defineRouteConfig({
//   label: "Custom Data",
// });

// export default CustomSettingPage;
