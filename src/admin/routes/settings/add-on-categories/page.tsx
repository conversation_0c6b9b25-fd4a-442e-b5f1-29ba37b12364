import { useState, useEffect } from "react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Container,
  Heading,
  Text,
  Button,
  Table,
  Badge,
  IconButton,
  FocusModal,
  Input,
  Label,
  Textarea,
  Switch,
  toast,
  Toaster,
} from "@camped-ai/ui";
import { Plus, Pencil, Trash, Tag } from "lucide-react";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  useAdminAddOnCategories,
  useAdminCreateAddOnCategory,
  useAdminUpdateAddOnCategory,
  useAdminDeleteAddOnCategory,
  AddOnCategory,
} from "../../../hooks/add-on-categories/use-admin-add-on-categories";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../hooks/use-rbac";
import { useAddOnCategoryTranslations } from "../../../hooks/translations/useAddOnCategoryTranslations";
import LanguageSelector from "../../../components/language-selector";
import AITranslateFieldButton from "../../../components/ai-translate-field-button";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { ScreenPermission } from "../../../../modules/rbac/types";

const categorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
});

type CategoryFormData = z.infer<typeof categorySchema>;

const AddOnCategoriesPage = () => {
  const { hasPermission, isAdmin } = useRbac();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<AddOnCategory | null>(
    null
  );
  const [selectedLanguage, setSelectedLanguage] = useState("en");

  // Local translation storage for both create and edit modes (stores all languages)
  const [localTranslations, setLocalTranslations] = useState<
    Record<string, Record<string, string>>
  >({});

  // Track which languages have been modified during this editing session
  const [modifiedLanguages, setModifiedLanguages] = useState<Set<string>>(
    new Set()
  );

  // Translation hooks - Always call hook to avoid hooks order violation
  const translationHook: ReturnType<typeof useAddOnCategoryTranslations> =
    useAddOnCategoryTranslations({
      categoryId: editingCategory?.id || "dummy",
    });

  // Queries and mutations
  const {
    data: categoriesData,
    isLoading,
    refetch,
  } = useAdminAddOnCategories();
  const createCategoryMutation = useAdminCreateAddOnCategory();
  const updateCategoryMutation = useAdminUpdateAddOnCategory(
    editingCategory?.id || ""
  );
  const deleteCategoryMutation = useAdminDeleteAddOnCategory();

  // Form setup
  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      description: "",
      is_active: true,
    },
  });

  const categories = (categoriesData as any)?.categories || [];

  // Check if current language is base language
  const isBaseLanguage = selectedLanguage === "en";

  // Translation state for current language
  const [translatedValues, setTranslatedValues] = useState<
    Record<string, string>
  >({});

  // Handle language change
  const handleLanguageChange = (language: string) => {
    // Save current translations to local storage before switching (both create and edit modes)
    if (selectedLanguage !== "en" && Object.keys(translatedValues).length > 0) {
      console.log("Saving current translations to local storage:", {
        language: selectedLanguage,
        translations: translatedValues,
      });
      setLocalTranslations((prev) => ({
        ...prev,
        [selectedLanguage]: translatedValues,
      }));

      // Track that this language has been modified
      setModifiedLanguages((prev) => new Set(prev).add(selectedLanguage));
    }

    setSelectedLanguage(language);
    // Translation loading is now handled by useEffect
  };

  const handleCreateCategory = async (data: CategoryFormData) => {
    try {
      // Collect all translations to save - build from current localTranslations state
      let allTranslationsToSave: Record<string, Record<string, string>> = {
        ...localTranslations,
      };

      // If we're currently on a non-base language and have pending translations, add them
      if (!isBaseLanguage && Object.keys(translatedValues).length > 0) {
        allTranslationsToSave[selectedLanguage] = translatedValues;
      }

      // Filter out English and empty translations
      const filteredTranslations: Record<string, Record<string, string>> = {};
      Object.entries(allTranslationsToSave).forEach(([lang, translations]) => {
        if (lang !== "en" && Object.keys(translations).length > 0) {
          filteredTranslations[lang] = translations;
        }
      });

      // Create the category first
      const result = await createCategoryMutation.mutateAsync(data);

      // Try different ways to extract the category ID
      let newCategoryId = (result as any)?.id;
      if (!newCategoryId) {
        newCategoryId = (result as any)?.category?.id;
      }
      if (!newCategoryId) {
        newCategoryId = (result as any)?.data?.id;
      }
      if (!newCategoryId) {
        newCategoryId = (result as any)?.data?.category?.id;
      }

      // Save translations if we have any and got a category ID
      if (
        Object.keys(filteredTranslations).length > 0 &&
        newCategoryId &&
        translationHook
      ) {
        for (const [language, translations] of Object.entries(
          filteredTranslations
        )) {
          if (Object.keys(translations).length > 0) {
            const translationsToSave: Record<string, string> = {};

            if (translations.name) {
              translationsToSave[`add_on_category.${newCategoryId}.name`] =
                translations.name;
            }
            if (translations.description) {
              translationsToSave[
                `add_on_category.${newCategoryId}.description`
              ] = translations.description;
            }

            if (Object.keys(translationsToSave).length > 0) {
              try {
                await translationHook.saveTranslations(
                  language,
                  translationsToSave
                );
              } catch (error) {
                console.error(`Error saving ${language} translations:`, error);
              }
            }
          }
        }
      }

      toast.success("Category created successfully");
      setIsCreateModalOpen(false);
      setSelectedLanguage("en"); // Reset to base language
      setLocalTranslations({}); // Clear local translations
      setModifiedLanguages(new Set()); // Clear modified languages
      setTranslatedValues({}); // Clear translated values
      form.reset();
      refetch();
    } catch (error) {
      toast.error("Failed to create category");
      console.error("Error creating category:", error);
    }
  };

  const handleUpdateCategory = async (data: CategoryFormData) => {
    if (!editingCategory) return;

    try {
      // Collect all translations to save - build from current localTranslations state
      let allTranslationsToSave: Record<string, Record<string, string>> = {
        ...localTranslations,
      };

      // If we're currently on a non-base language and have pending translations, add them
      if (!isBaseLanguage && Object.keys(translatedValues).length > 0) {
        allTranslationsToSave[selectedLanguage] = translatedValues;
      }

      // Filter out English and empty translations
      const filteredTranslations: Record<string, Record<string, string>> = {};
      Object.entries(allTranslationsToSave).forEach(([lang, translations]) => {
        if (lang !== "en" && Object.keys(translations).length > 0) {
          filteredTranslations[lang] = translations;
        }
      });

      if (isBaseLanguage) {
        // For base language, update the category with form data
        await updateCategoryMutation.mutateAsync(data);
      } else {
        // For non-base languages, update category with original English data only
        const originalCategoryData = {
          name: editingCategory.name,
          description: editingCategory.description || "",
          is_active: editingCategory.is_active, // Keep original is_active value
        };
        await updateCategoryMutation.mutateAsync(originalCategoryData);
      }

      // Save all modified language translations
      if (Object.keys(filteredTranslations).length > 0 && translationHook) {
        for (const [language, translations] of Object.entries(
          filteredTranslations
        )) {
          if (Object.keys(translations).length > 0) {
            const translationsToSave: Record<string, string> = {};

            if (translations.name) {
              translationsToSave[`add_on_category.${editingCategory.id}.name`] =
                translations.name;
            }
            if (translations.description) {
              translationsToSave[
                `add_on_category.${editingCategory.id}.description`
              ] = translations.description;
            }

            if (Object.keys(translationsToSave).length > 0) {
              try {
                await translationHook.saveTranslations(
                  language,
                  translationsToSave
                );
              } catch (error) {
                console.error(`Error saving ${language} translations:`, error);
              }
            }
          }
        }
      }

      toast.success("Category updated successfully");
      setEditingCategory(null);
      setSelectedLanguage("en"); // Reset to base language
      setLocalTranslations({}); // Clear local translations
      setModifiedLanguages(new Set()); // Clear modified languages
      setTranslatedValues({}); // Clear translated values
      form.reset();
      refetch();
    } catch (error) {
      toast.error("Failed to update category");
      console.error("Error updating category:", error);
    }
  };

  const handleDeleteCategory = async (id: string) => {
    if (!confirm("Are you sure you want to delete this category?")) return;

    try {
      await deleteCategoryMutation.mutateAsync(id);
      toast.success("Category deleted successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to delete category");
      console.error("Error deleting category:", error);
    }
  };

  const openEditModal = (category: AddOnCategory) => {
    setEditingCategory(category);
    setSelectedLanguage("en"); // Always start with base language
    form.reset({
      name: category.name,
      description: category.description || "",
      is_active: category.is_active,
    });
  };

  const closeModal = () => {
    setIsCreateModalOpen(false);
    setEditingCategory(null);
    setSelectedLanguage("en"); // Reset to base language
    setLocalTranslations({}); // Clear local translations
    setModifiedLanguages(new Set()); // Clear modified languages
    setTranslatedValues({}); // Clear translated values
    form.reset();
  };

  // Load translations when language changes
  useEffect(() => {
    if (editingCategory && translationHook && !isBaseLanguage) {
      translationHook.loadTranslations(selectedLanguage);
    }
  }, [editingCategory?.id, selectedLanguage, isBaseLanguage]); // Use editingCategory.id instead of the whole object

  // Update translated values when translations are loaded or language changes
  useEffect(() => {
    if (selectedLanguage === "en") {
      // Clear translated values for base language
      setTranslatedValues({});
    } else if (editingCategory) {
      // Edit mode: Prioritize local storage over translation hook for modified languages
      const localTranslation = localTranslations[selectedLanguage] || {};
      const hookTranslations: Record<string, string> = translationHook
        ? {
            name:
              translationHook.getTranslatedValue("name", selectedLanguage) ||
              "",
            description:
              translationHook.getTranslatedValue(
                "description",
                selectedLanguage
              ) || "",
          }
        : {};

      // If we have local translations (user has modified this language), use them
      // Otherwise, use translations from the hook (existing saved translations)
      const translationsToUse =
        Object.keys(localTranslation).length > 0
          ? localTranslation
          : hookTranslations;

      setTranslatedValues(translationsToUse);
    } else {
      // Create mode: Load from local storage
      const localTranslation = localTranslations[selectedLanguage] || {};
      setTranslatedValues(localTranslation);
    }
  }, [
    translationHook?.translations,
    selectedLanguage,
    localTranslations,
    editingCategory?.id,
  ]);

  // Update form fields when language changes or when translated values change
  useEffect(() => {
    if (editingCategory) {
      if (isBaseLanguage) {
        // For base language, use original category data
        form.setValue("name", editingCategory.name);
        form.setValue("description", editingCategory.description || "");
        form.setValue("is_active", editingCategory.is_active);
      } else {
        // For other languages, use translated values
        form.setValue("name", translatedValues.name || "");
        form.setValue("description", translatedValues.description || "");
        form.setValue("is_active", editingCategory.is_active); // is_active is not translatable
      }
    }
  }, [
    editingCategory,
    selectedLanguage,
    isBaseLanguage,
    translatedValues,
    form,
  ]);

  const isModalOpen = isCreateModalOpen || !!editingCategory;

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission={ScreenPermission.SETTINGS_ADDON_CATEGORIES}
        fallback={
          <Container className="p-8 text-center">
            <Heading level="h1">Access Denied</Heading>
            <Text className="mt-2">
              You don't have permission to manage add-on categories.
            </Text>
          </Container>
        }
      >
        <Container className="p-0">
          <Toaster />

          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <div>
              <Heading level="h1" className="text-2xl font-semibold">
                Add-On Categories
              </Heading>
              <Text className="text-gray-600 mt-1">
                Manage categories for organizing add-on services
              </Text>
            </div>
            {hasPermission(ScreenPermission.SETTINGS_ADDON_CATEGORIES) && (
              <Button
                onClick={() => setIsCreateModalOpen(true)}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Category
              </Button>
            )}
          </div>

          {/* Categories Table */}
          <div className="p-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Text>Loading categories...</Text>
              </div>
            ) : categories.length === 0 ? (
              <div className="text-center py-12">
                <div className="mb-4">
                  <Tag size={48} className="mx-auto text-gray-400" />
                </div>
                <Heading level="h3" className="mb-2">
                  No categories yet
                </Heading>
                <Text className="text-gray-600 mb-4">
                  Create your first add-on category to get started
                </Text>
                {hasPermission(ScreenPermission.SETTINGS_ADDON_CATEGORIES) && (
                  <Button onClick={() => setIsCreateModalOpen(true)}>
                    <Plus size={16} className="mr-2" />
                    Add Category
                  </Button>
                )}
              </div>
            ) : (
              <Table>
                <Table.Header>
                  <Table.Row>
                    <Table.HeaderCell>Name</Table.HeaderCell>
                    <Table.HeaderCell>Description</Table.HeaderCell>
                    <Table.HeaderCell>Status</Table.HeaderCell>
                    <Table.HeaderCell className="text-right">
                      Actions
                    </Table.HeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {categories.map((category: AddOnCategory) => (
                    <Table.Row key={category.id}>
                      <Table.Cell>
                        <div className="font-medium">{category.name}</div>
                      </Table.Cell>
                      <Table.Cell>
                        <Text className="text-gray-600">
                          {category.description || "—"}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge
                          color={category.is_active ? "green" : "red"}
                          size="small"
                        >
                          {category.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell className="text-right">
                        <div className="flex items-center justify-end gap-1">
                          {hasPermission(
                            ScreenPermission.SETTINGS_ADDON_CATEGORIES
                          ) && (
                            <IconButton
                              variant="transparent"
                              size="small"
                              onClick={() => openEditModal(category)}
                            >
                              <Pencil size={14} />
                            </IconButton>
                          )}
                          {hasPermission(
                            ScreenPermission.SETTINGS_ADDON_CATEGORIES
                          ) && (
                            <IconButton
                              variant="transparent"
                              size="small"
                              onClick={() => handleDeleteCategory(category.id)}
                            >
                              <Trash size={14} />
                            </IconButton>
                          )}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            )}
          </div>

          {/* Create/Edit Modal */}
          <FocusModal open={isModalOpen} onOpenChange={closeModal}>
            <FocusModal.Content>
              <FocusModal.Header>
                <div className="flex w-full items-center justify-between">
                  <Heading level="h2">
                    {editingCategory ? "Edit Category" : "Create Category"}
                  </Heading>

                  {/* Language Selector - Show for both create and edit modes */}
                  <LanguageSelector
                    selectedLanguage={selectedLanguage}
                    onLanguageChange={handleLanguageChange}
                  />
                </div>
              </FocusModal.Header>

              <form
                onSubmit={form.handleSubmit(
                  editingCategory ? handleUpdateCategory : handleCreateCategory
                )}
              >
                <FocusModal.Body className="space-y-4 p-6">
                  {/* Translation Mode Notice - Show for non-base languages in both create and edit modes */}
                  {!isBaseLanguage && (
                    <div className="bg-gray-50 border border-gray-200 rounded-md px-3 py-2 mb-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <svg
                            className="h-4 w-4 text-gray-500"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="ml-2">
                          <p className="text-sm text-gray-700">
                            <strong>Translation Mode:</strong> You are editing
                            in <strong>{selectedLanguage}</strong> language.
                            Only translatable fields (
                            <strong>name, description</strong>) can be edited.
                            Other fields show base language values and are
                            read-only.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                  <div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="name">Name *</Label>
                      {/* AI Translate button for name field - Show for non-base languages in both create and edit modes */}
                      {!isBaseLanguage && (
                        <AITranslateFieldButton
                          entityType="add_on_category"
                          entityId={editingCategory?.id || "dummy"}
                          fieldName="name"
                          fieldValue={
                            isBaseLanguage
                              ? form.watch("name") || ""
                              : translatedValues.name || ""
                          }
                          targetLanguage={selectedLanguage}
                          onTranslationComplete={(translatedValue) => {
                            // Update the form field with the translated value
                            form.setValue("name", translatedValue);

                            // Refresh translations after successful AI translation
                            if (editingCategory && translationHook) {
                              translationHook.loadTranslations(
                                selectedLanguage
                              );
                            }
                          }}
                          buttonVariant="transparent"
                          buttonSize="small"
                        />
                      )}
                    </div>
                    <Controller
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <Input
                          {...field}
                          id="name"
                          placeholder="Enter category name"
                          value={
                            isBaseLanguage
                              ? field.value || ""
                              : translatedValues.name || ""
                          }
                          onChange={(e) => {
                            if (isBaseLanguage) {
                              field.onChange(e.target.value);
                            } else {
                              // For non-base languages, update translated values
                              const newTranslatedValues = {
                                ...translatedValues,
                                name: e.target.value,
                              };
                              setTranslatedValues(newTranslatedValues);

                              // Save to local storage for both create and edit modes
                              setLocalTranslations((prev) => ({
                                ...prev,
                                [selectedLanguage]: newTranslatedValues,
                              }));

                              // Track that this language has been modified
                              setModifiedLanguages((prev) =>
                                new Set(prev).add(selectedLanguage)
                              );
                            }
                          }}
                        />
                      )}
                    />
                    {form.formState.errors.name && (
                      <Text className="text-red-500 text-sm mt-1">
                        {form.formState.errors.name.message}
                      </Text>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="description">Description</Label>
                      {/* AI Translate button for description field - Show for non-base languages in both create and edit modes */}
                      {!isBaseLanguage && (
                        <AITranslateFieldButton
                          entityType="add_on_category"
                          entityId={editingCategory?.id || "dummy"}
                          fieldName="description"
                          fieldValue={
                            isBaseLanguage
                              ? form.watch("description") || ""
                              : translatedValues.description || ""
                          }
                          targetLanguage={selectedLanguage}
                          onTranslationComplete={(translatedValue) => {
                            // Update the form field with the translated value
                            form.setValue("description", translatedValue);

                            // Refresh translations after successful AI translation
                            if (editingCategory && translationHook) {
                              translationHook.loadTranslations(
                                selectedLanguage
                              );
                            }
                          }}
                          buttonVariant="transparent"
                          buttonSize="small"
                        />
                      )}
                    </div>
                    <Controller
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <Textarea
                          {...field}
                          id="description"
                          placeholder="Enter category description (optional)"
                          rows={3}
                          value={
                            isBaseLanguage
                              ? field.value || ""
                              : translatedValues.description || ""
                          }
                          onChange={(e) => {
                            if (isBaseLanguage) {
                              field.onChange(e.target.value);
                            } else {
                              // For non-base languages, update translated values
                              const newTranslatedValues = {
                                ...translatedValues,
                                description: e.target.value,
                              };
                              setTranslatedValues(newTranslatedValues);

                              // Save to local storage for both create and edit modes
                              setLocalTranslations((prev) => ({
                                ...prev,
                                [selectedLanguage]: newTranslatedValues,
                              }));

                              // Track that this language has been modified
                              setModifiedLanguages((prev) =>
                                new Set(prev).add(selectedLanguage)
                              );
                            }
                          }}
                        />
                      )}
                    />
                  </div>

                  <div className="flex items-center gap-2">
                    <Controller
                      control={form.control}
                      name="is_active"
                      render={({ field }) => (
                        <Switch
                          id="is_active"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!isBaseLanguage} // Disable in non-base languages
                        />
                      )}
                    />
                    <Label
                      htmlFor="is_active"
                      className={!isBaseLanguage ? "text-gray-400" : ""}
                    >
                      Active
                    </Label>
                  </div>
                </FocusModal.Body>

                <FocusModal.Footer>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={closeModal}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      isLoading={
                        createCategoryMutation.isPending ||
                        updateCategoryMutation.isPending
                      }
                    >
                      {editingCategory ? "Update" : "Create"}
                    </Button>
                  </div>
                </FocusModal.Footer>
              </form>
            </FocusModal.Content>
          </FocusModal>
        </Container>
      </RoleGuard>
    </>
  );
};

export default AddOnCategoriesPage;
