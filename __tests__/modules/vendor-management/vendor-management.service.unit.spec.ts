import VendorManagementService from "../../../src/modules/vendor-management/service";
import { CreateVendorRequest, ListVendorsRequest } from "../../../src/modules/vendor-management/types";

// Mock the MedusaService
jest.mock("@camped-ai/framework/utils", () => ({
  MedusaService: jest.fn().mockImplementation((models) => {
    return class MockMedusaService {
      vendorRepository_ = {
        findOne: jest.fn(),
        createQueryBuilder: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        delete: jest.fn(),
      };
      vendorContactRepository_ = {
        create: jest.fn(),
        save: jest.fn(),
        delete: jest.fn(),
      };
      vendorContractRepository_ = {
        create: jest.fn(),
        save: jest.fn(),
        delete: jest.fn(),
      };
      vendorPerformanceRepository_ = {
        delete: jest.fn(),
      };
      vendorHotelMappingRepository_ = {
        delete: jest.fn(),
      };

      atomicPhase_ = jest.fn().mockImplementation((callback) => {
        const mockManager = {
          withRepository: jest.fn().mockImplementation((repo) => repo),
        };
        return callback(mockManager);
      });
    };
  }),
}));

describe("VendorManagementService", () => {
  let service: VendorManagementService;
  let mockVendorRepo: any;
  let mockContactRepo: any;
  let mockContractRepo: any;

  beforeEach(() => {
    service = new VendorManagementService({} as any);
    mockVendorRepo = service.vendorRepository_;
    mockContactRepo = service.vendorContactRepository_;
    mockContractRepo = service.vendorContractRepository_;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createVendor", () => {
    const mockVendorData: CreateVendorRequest = {
      vendor: {
        name: "Test Vendor",
        type: "hotel_partner",
        status: "active",
        description: "Test description",
        website: "https://test.com",
        tax_id: "TEST123",
        registration_number: "REG456",
        address: {
          street: "123 Test St",
          city: "Test City",
          state: "Test State",
          country: "Test Country",
          postal_code: "12345"
        },
        metadata: { test: true }
      },
      contacts: [
        {
          name: "John Doe",
          email: "<EMAIL>",
          phone: "+1234567890",
          role: "Manager",
          is_primary: true,
          metadata: { department: "Operations" }
        }
      ],
      contracts: [
        {
          contract_number: "CONTRACT-001",
          title: "Test Contract",
          description: "Test contract description",
          start_date: new Date("2024-01-01"),
          end_date: new Date("2024-12-31"),
          status: "active",
          terms_and_conditions: "Test terms",
          commission_rate: 15.0,
          payment_terms: "Net 30",
          metadata: { test_contract: true }
        }
      ]
    };

    it("should create a vendor with contacts and contracts", async () => {
      const mockCreatedVendor = { id: "vendor-123", ...mockVendorData.vendor };
      const mockRetrievedVendor = {
        ...mockCreatedVendor,
        contacts: [{ id: "contact-123", vendor_id: "vendor-123", ...mockVendorData.contacts![0] }],
        contracts: [{ id: "contract-123", vendor_id: "vendor-123", ...mockVendorData.contracts![0] }]
      };

      mockVendorRepo.create.mockReturnValue(mockCreatedVendor);
      mockContactRepo.create.mockReturnValue([{ id: "contact-123", vendor_id: "vendor-123" }]);
      mockContractRepo.create.mockReturnValue([{ id: "contract-123", vendor_id: "vendor-123" }]);

      // Mock retrieveVendor method
      jest.spyOn(service, 'retrieveVendor').mockResolvedValue(mockRetrievedVendor as any);

      const result = await service.createVendor(mockVendorData);

      expect(mockVendorRepo.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: mockVendorData.vendor.name,
          type: mockVendorData.vendor.type,
          status: mockVendorData.vendor.status,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
        })
      );

      expect(mockContactRepo.create).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            name: mockVendorData.contacts![0].name,
            email: mockVendorData.contacts![0].email,
            vendor_id: "vendor-123",
            created_at: expect.any(Date),
            updated_at: expect.any(Date),
          })
        ])
      );

      expect(mockContractRepo.create).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            contract_number: mockVendorData.contracts![0].contract_number,
            title: mockVendorData.contracts![0].title,
            vendor_id: "vendor-123",
            created_at: expect.any(Date),
            updated_at: expect.any(Date),
          })
        ])
      );

      expect(service.retrieveVendor).toHaveBeenCalledWith("vendor-123", [
        "contacts",
        "contracts",
        "performance_metrics",
        "hotel_mappings"
      ]);

      expect(result).toEqual(mockRetrievedVendor);
    });

    it("should create a vendor without contacts and contracts", async () => {
      const vendorOnlyData = { vendor: mockVendorData.vendor };
      const mockCreatedVendor = { id: "vendor-456", ...mockVendorData.vendor };

      mockVendorRepo.create.mockReturnValue(mockCreatedVendor);
      jest.spyOn(service, 'retrieveVendor').mockResolvedValue(mockCreatedVendor as any);

      const result = await service.createVendor(vendorOnlyData);

      expect(mockVendorRepo.create).toHaveBeenCalled();
      expect(mockContactRepo.create).not.toHaveBeenCalled();
      expect(mockContractRepo.create).not.toHaveBeenCalled();
      expect(result).toEqual(mockCreatedVendor);
    });
  });

  describe("retrieveVendor", () => {
    it("should retrieve a vendor by ID", async () => {
      const mockVendor = {
        id: "vendor-123",
        name: "Test Vendor",
        type: "hotel_partner",
        status: "active"
      };

      mockVendorRepo.findOne.mockResolvedValue(mockVendor);

      const result = await service.retrieveVendor("vendor-123", ["contacts"]);

      expect(mockVendorRepo.findOne).toHaveBeenCalledWith({
        where: { id: "vendor-123" },
        relations: ["contacts"]
      });
      expect(result).toEqual(mockVendor);
    });

    it("should throw error when vendor not found", async () => {
      mockVendorRepo.findOne.mockResolvedValue(null);

      await expect(service.retrieveVendor("non-existent")).rejects.toThrow(
        "Vendor with id non-existent not found"
      );
    });
  });

  describe("listVendors", () => {
    it("should list vendors with filters and pagination", async () => {
      const mockQueryBuilder = {
        andWhere: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([
          [
            { id: "vendor-1", name: "Vendor 1", type: "hotel_partner", status: "active" },
            { id: "vendor-2", name: "Vendor 2", type: "hotel_partner", status: "active" }
          ],
          2
        ])
      };

      mockVendorRepo.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const params: ListVendorsRequest = {
        limit: 10,
        offset: 0,
        type: "hotel_partner",
        status: "active",
        search: "Vendor"
      };

      const result = await service.listVendors(params);

      expect(mockVendorRepo.createQueryBuilder).toHaveBeenCalledWith("vendor");
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith("vendor.type = :type", { type: "hotel_partner" });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith("vendor.status = :status", { status: "active" });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "(vendor.name ILIKE :search OR vendor.description ILIKE :search)",
        { search: "%Vendor%" }
      );
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);

      expect(result).toEqual({
        vendors: expect.any(Array),
        count: 2,
        limit: 10,
        offset: 0
      });
    });

    it("should list vendors with default pagination", async () => {
      const mockQueryBuilder = {
        andWhere: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0])
      };

      mockVendorRepo.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.listVendors({});

      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(50);
      expect(result.limit).toBe(50);
      expect(result.offset).toBe(0);
    });
  });

  describe("deleteVendor", () => {
    it("should delete a vendor and related records", async () => {
      const mockVendor = { id: "vendor-123", name: "Test Vendor" };
      mockVendorRepo.findOne.mockResolvedValue(mockVendor);

      await service.deleteVendor("vendor-123");

      expect(mockVendorRepo.findOne).toHaveBeenCalledWith({ where: { id: "vendor-123" } });
      expect(mockContactRepo.delete).toHaveBeenCalledWith({ vendor_id: "vendor-123" });
      expect(mockContractRepo.delete).toHaveBeenCalledWith({ vendor_id: "vendor-123" });
      expect(service.vendorPerformanceRepository_.delete).toHaveBeenCalledWith({ vendor_id: "vendor-123" });
      expect(service.vendorHotelMappingRepository_.delete).toHaveBeenCalledWith({ vendor_id: "vendor-123" });
      expect(mockVendorRepo.delete).toHaveBeenCalledWith("vendor-123");
    });

    it("should throw error when vendor not found", async () => {
      mockVendorRepo.findOne.mockResolvedValue(null);

      await expect(service.deleteVendor("non-existent")).rejects.toThrow(
        "Vendor with id non-existent not found"
      );
    });
  });
});
