import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { vendorPerformanceEvaluationWorkflow } from '../vendor-performance-evaluation';

// Mock the workflow SDK
jest.mock('@camped-ai/workflows-sdk', () => ({
  createWorkflow: jest.fn().mockImplementation((name, fn) => fn),
  createStep: jest.fn().mockImplementation((name, fn) => fn),
  WorkflowResponse: jest.fn().mockImplementation((data) => data),
  StepResponse: jest.fn().mockImplementation((data) => data),
}));

// Mock the vendor management service
const mockVendorService = {
  retrieveVendor: jest.fn(),
  recordPerformanceMetric: jest.fn(),
  updateVendor: jest.fn(),
  createVendorCommunication: jest.fn(),
};

const mockContainer = {
  resolve: jest.fn().mockReturnValue(mockVendorService),
};

describe('Vendor Performance Evaluation Workflow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockEvaluationData = {
    vendor_id: 'vendor-1',
    evaluation_period: {
      start_date: new Date('2024-01-01'),
      end_date: new Date('2024-01-31'),
    },
    metrics: {
      booking_fulfillment_rate: 95.5,
      response_time: 2.5, // hours
      customer_satisfaction: 4.2, // 1-5 scale
      cancellation_rate: 3.2,
      revenue_generated: 50000,
    },
    notes: 'Excellent performance this month',
    evaluator: 'Performance Manager',
    auto_update_status: true,
  };

  const mockVendor = {
    id: 'vendor-1',
    name: 'Test Hotel Partner',
    status: 'active',
    metadata: {},
  };

  it('should complete performance evaluation successfully', async () => {
    mockVendorService.retrieveVendor.mockResolvedValue(mockVendor);
    mockVendorService.recordPerformanceMetric.mockResolvedValue({
      id: 'metric-1',
      vendor_id: 'vendor-1',
      metric_type: 'booking_fulfillment_rate',
      value: 95.5,
    });

    // Test vendor retrieval
    const vendor = await mockVendorService.retrieveVendor('vendor-1');
    expect(vendor).toEqual(mockVendor);
    expect(mockVendorService.retrieveVendor).toHaveBeenCalledWith('vendor-1');
  });

  it('should validate evaluation period', () => {
    const invalidPeriod = {
      start_date: new Date('2024-01-31'),
      end_date: new Date('2024-01-01'), // End before start
    };

    // Test validation logic
    expect(invalidPeriod.start_date > invalidPeriod.end_date).toBe(true);
    // In the real workflow, this would trigger a validation error
  });

  it('should record performance metrics', async () => {
    const mockMetric = {
      id: 'metric-1',
      vendor_id: 'vendor-1',
      metric_type: 'booking_fulfillment_rate',
      value: 95.5,
      period_start: mockEvaluationData.evaluation_period.start_date,
      period_end: mockEvaluationData.evaluation_period.end_date,
    };

    mockVendorService.recordPerformanceMetric.mockResolvedValue(mockMetric);

    const result = await mockVendorService.recordPerformanceMetric({
      vendor_id: 'vendor-1',
      metric_type: 'booking_fulfillment_rate',
      value: 95.5,
      period_start: mockEvaluationData.evaluation_period.start_date,
      period_end: mockEvaluationData.evaluation_period.end_date,
      notes: mockEvaluationData.notes,
      metadata: {
        evaluator: mockEvaluationData.evaluator,
        evaluation_date: expect.any(String),
      },
    });

    expect(result.id).toBe('metric-1');
    expect(result.value).toBe(95.5);
    expect(mockVendorService.recordPerformanceMetric).toHaveBeenCalled();
  });

  it('should calculate performance score correctly', () => {
    const metrics = mockEvaluationData.metrics;
    
    // Test scoring logic (simplified version of the actual calculation)
    let totalScore = 0;
    let metricCount = 0;

    // Booking fulfillment rate (0-100 scale)
    if (metrics.booking_fulfillment_rate !== undefined) {
      totalScore += Math.min(metrics.booking_fulfillment_rate, 100);
      metricCount++;
    }

    // Response time (lower is better)
    if (metrics.response_time !== undefined) {
      const responseScore = Math.max(0, 100 - (metrics.response_time - 1) * 2);
      totalScore += Math.min(responseScore, 100);
      metricCount++;
    }

    // Customer satisfaction (1-5 scale to 0-100)
    if (metrics.customer_satisfaction !== undefined) {
      totalScore += (metrics.customer_satisfaction - 1) * 25;
      metricCount++;
    }

    // Cancellation rate (lower is better)
    if (metrics.cancellation_rate !== undefined) {
      totalScore += Math.max(0, 100 - metrics.cancellation_rate);
      metricCount++;
    }

    // Revenue generated (simplified scoring)
    if (metrics.revenue_generated !== undefined) {
      totalScore += metrics.revenue_generated > 0 ? 80 : 20;
      metricCount++;
    }

    const averageScore = metricCount > 0 ? totalScore / metricCount : 0;

    expect(averageScore).toBeGreaterThan(80); // Should be high performance
    expect(metricCount).toBe(5); // All metrics provided
  });

  it('should generate appropriate recommendations based on score', () => {
    // Test high performance recommendations
    const highScore = 85;
    let recommendations = [];
    
    if (highScore >= 80) {
      recommendations.push("Excellent performance - consider for preferred vendor status");
      recommendations.push("Share best practices with other vendors");
      recommendations.push("Consider contract renewal or expansion");
    }

    expect(recommendations).toContain("Excellent performance - consider for preferred vendor status");
    expect(recommendations).toHaveLength(3);

    // Test low performance recommendations
    const lowScore = 45;
    recommendations = [];
    
    if (lowScore < 60) {
      recommendations.push("Performance below expectations - immediate attention required");
      recommendations.push("Schedule performance review meeting");
      recommendations.push("Consider additional training or support");
    }

    expect(recommendations).toContain("Performance below expectations - immediate attention required");
    expect(recommendations).toHaveLength(3);
  });

  it('should update vendor status based on performance', async () => {
    const lowPerformanceScore = 35;
    const updatedVendor = {
      ...mockVendor,
      status: 'suspended',
      metadata: {
        last_performance_evaluation: expect.any(String),
        last_performance_score: lowPerformanceScore,
      },
    };

    mockVendorService.updateVendor.mockResolvedValue(updatedVendor);

    // Test status update for low performance
    if (lowPerformanceScore < 40) {
      const result = await mockVendorService.updateVendor('vendor-1', {
        vendor: {
          status: 'suspended',
          metadata: {
            last_performance_evaluation: new Date().toISOString(),
            last_performance_score: lowPerformanceScore,
          },
        },
      });

      expect(result.status).toBe('suspended');
      expect(mockVendorService.updateVendor).toHaveBeenCalled();
    }
  });

  it('should log status change communication', async () => {
    const mockCommunication = {
      id: 'comm-1',
      vendor_id: 'vendor-1',
      subject: 'Vendor Status Updated Based on Performance',
      content: 'Vendor status changed from active to suspended based on performance evaluation (score: 35).',
    };

    mockVendorService.createVendorCommunication.mockResolvedValue(mockCommunication);

    const result = await mockVendorService.createVendorCommunication({
      vendor_id: 'vendor-1',
      communication_type: 'other',
      direction: 'outbound',
      subject: 'Vendor Status Updated Based on Performance',
      content: 'Vendor status changed from active to suspended based on performance evaluation (score: 35).',
      status: 'completed',
      priority: 'high',
      internal_participant: 'Performance Manager',
      completed_at: new Date(),
      tags: ['performance-evaluation', 'status-change'],
      metadata: {
        performance_score: 35,
        previous_status: 'active',
        new_status: 'suspended',
        automated: true,
      },
    });

    expect(result.id).toBe('comm-1');
    expect(result.subject).toBe('Vendor Status Updated Based on Performance');
    expect(mockVendorService.createVendorCommunication).toHaveBeenCalled();
  });

  it('should handle missing vendor error', async () => {
    mockVendorService.retrieveVendor.mockRejectedValue(new Error('Vendor with id non-existent not found'));

    try {
      await mockVendorService.retrieveVendor('non-existent');
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect((error as Error).message).toBe('Vendor with id non-existent not found');
    }
  });

  it('should validate metric values', () => {
    const invalidMetrics = {
      booking_fulfillment_rate: 150, // Should be 0-100
      customer_satisfaction: 6, // Should be 1-5
      response_time: -1, // Should be >= 0
      cancellation_rate: -5, // Should be >= 0
      revenue_generated: -1000, // Should be >= 0
    };

    // Test validation logic
    expect(invalidMetrics.booking_fulfillment_rate > 100).toBe(true);
    expect(invalidMetrics.customer_satisfaction > 5).toBe(true);
    expect(invalidMetrics.response_time < 0).toBe(true);
    expect(invalidMetrics.cancellation_rate < 0).toBe(true);
    expect(invalidMetrics.revenue_generated < 0).toBe(true);
  });

  it('should handle partial metrics', () => {
    const partialMetrics = {
      booking_fulfillment_rate: 90,
      customer_satisfaction: 4.5,
      // Missing other metrics
    };

    // Test that workflow can handle partial metrics
    let totalScore = 0;
    let metricCount = 0;

    if (partialMetrics.booking_fulfillment_rate !== undefined) {
      totalScore += partialMetrics.booking_fulfillment_rate;
      metricCount++;
    }

    if (partialMetrics.customer_satisfaction !== undefined) {
      totalScore += (partialMetrics.customer_satisfaction - 1) * 25;
      metricCount++;
    }

    const averageScore = metricCount > 0 ? totalScore / metricCount : 0;

    expect(metricCount).toBe(2);
    expect(averageScore).toBeGreaterThan(0);
  });
});
