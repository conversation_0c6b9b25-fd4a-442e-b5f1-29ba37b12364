# Supplier Offering Matrix - Module Specification

## Overview

The Supplier Offering Matrix enables admins to define **which supplier offers which product/service**, along with **route-specific or category-specific metadata** like locations, capacity, validity, etc.

This module dynamically adapts based on the **Category** of the Product/Service and supports **custom fields** configured at the Category level.

---

## Navigation & Routing

- **Main Menu**: Supplier Management → Supplier Offerings
- **URL**: `/supplier-management/supplier-offerings`
- **Sub-routes**:
  - `/supplier-offerings/list` — List view
  - `/supplier-offerings/create` — Create form
  - `/supplier-offerings/:id` — Detail/Edit view

---

## 1. List View

### ✅ Fields / Columns

| Column               | Type       |
|----------------------|------------|
| Supplier Name        | Text       |
| Product/Service Name | Text       |
| Category             | Text       |
| Status               | Badge (Active/Inactive) |
| Validity             | Date range (`Active From` → `Active To`) |
| Last Updated         | DateTime   |
| Actions              | View / Edit / Delete |

### 🔍 Filters

- Supplier (Dropdown)
- Category (Dropdown)
- Product/Service (Dropdown with search)
- Status (Active/Inactive)

---

## 2. Create Supplier Offering

### 🔁 Dynamic Logic

- Once a **Product/Service** is selected → auto-fill the **Category**
- Based on the Category → dynamically render **Custom Fields** (defined in `/config/categories`)
- Display “Required” field indicator where applicable

### 🧾 Form Fields

| Field Name          | Type          | Description |
|---------------------|---------------|-------------|
| Supplier            | Dropdown (Searchable) | Required |
| Product/Service     | Dropdown (Searchable) | Required |
| Category            | Auto-filled (Read-only) | Auto-linked from Product |
| Active From         | Date picker   | Optional (default: today) |
| Active To           | Date picker   | Optional |
| Availability Notes  | Textarea      | Optional notes shown in internal tools |
| Status              | Radio (Active / Inactive) | Default: Active |
| Custom Fields       | Dynamically rendered based on category | Varies |

> Example:  
> - **Category**: Taxi Transfer  
> - **Custom Fields**: From Location, To Location, Vehicle Type, Max Pax, etc.

### 🧠 UX Flow

- Select Supplier → Select Product → Custom Fields Load → Save

---

## 3. Detail/Edit View

- Read and edit all fields
- Show full audit log (Created by, Updated at)
- Option to **duplicate** offering (useful for bulk entries)

---

## 4. Associations

### ✅ Data Relationships

- Supplier Offering links:
  - 1 Supplier → Many Offerings
  - 1 Product → Many Offerings
- Category fields inherited from Product
- Offering contains Category-specific fields (from config)

---

## 5. Category Custom Field Config (Extension)

> Update Category Config UI to support this:

### Add Toggle:  
- **"Used in Supplier Offering?"** → Boolean toggle for each custom field

So, a field like `From Location` can be used in:
- ✅ Product/Service creation
- ✅ Supplier Offering
- ⬜️ Booking Add-on (future)

---

## Optional (Phase 2)

- Export Supplier Offering list as CSV
- Duplicate from existing Offering
- Attach documents/contracts to each Offering
- Use this to suggest offerings in Booking screen based on destination/transfer type

---

## Notes

- Use same component framework/UI design as Products & Services
- Make the dynamic form builder reusable
- For audit logging, capture:
  - Created by
  - Created at
  - Last modified by
  - Last modified at
