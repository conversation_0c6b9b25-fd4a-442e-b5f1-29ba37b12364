# 🛠 Product & Service Screens (Dynamic Fields via Category)

---

## 📍 Location
Module: Supplier Management  
Path: `/supplier-management/products-services`

---

## 🎯 Overview

This spec defines the enhancements needed for the Create, Edit, and List screens of the **Product & Services** module to support:

- **Dynamic fields** based on the selected category
- **Base cost** for pricing logic
- Clean form rendering and schema-driven flexibility

Custom fields are defined under `/supplier-management/config/categories`.

---

## 1️⃣ Create Product/Service Screen

Path: `/supplier-management/products-services/create`

### 🧾 Base Fields (Always Present)

| Field Name    | Type        | Notes                              |
|---------------|-------------|-------------------------------------|
| Name          | Text        | Required                            |
| Category      | Dropdown    | Required, pulls from configured categories |
| Unit Type     | Dropdown    | Required, also has `+` button to add |
| Base Cost     | Number (CHF)| Optional, used as pricing anchor (markup logic) |
| Tags          | Multi-select| Optional, allow `+ Add Tag`        |
| Description   | Textarea    | Optional                            |
| Status        | Radio       | Required – Active / Inactive        |

---

### 🧩 Dynamic Fields Section

- Titled: **“Custom Fields (Optional)”**
- Appears **after Category is selected**
- Renders fields based on the selected category’s schema
- Field types supported: Text, Number, Dropdown, Multi-select, Date, Boolean, Number Range
- All validations must follow the schema (`required: true`)

> 🔁 Dynamic form should re-render if Category is changed.

---

### 🔘 Example UX Flow

1. User opens Create Product screen
2. Selects Category: `Ski Pass`
3. The custom fields auto-appear below
   - Days of Validity
   - Region
   - Age Restriction
4. Fills fields → Submits

> ✅ Custom data is stored in `custom_fields` (JSON)  
> ✅ `base_cost` is used to calculate markup pricing in bookings

---

## 2️⃣ List View Screen

Path: `/supplier-management/products-services`

### 📋 Columns to Include

| Column         | Notes                              |
|----------------|-------------------------------------|
| Name           | Product/Service Name                |
| Category       | Display label from selected category|
| Unit Type      | Show unit type                      |
| Base Cost      | CHF format                          |
| Tags           | Show chips                          |
| Status         | Active / Inactive                   |
| Last Updated   | Date                                |
| Actions        | Edit / View / Delete                |

### 🔍 Filters

| Filter Type    | Source                             |
|----------------|------------------------------------|
| Category       | Dropdown                           |
| Unit Type      | Dropdown                           |
| Status         | Toggle                             |
| Custom Fields  | If marked “Used in Filtering” from Category Config, show these |

> Dynamic filters can appear when a Category is selected in the filter panel

---

## 3️⃣ Edit Product/Service Screen

Path: `/supplier-management/products-services/:id/edit`

- Same layout and flow as Create screen
- Populate **custom field values** based on saved `custom_fields` JSON
- If category was changed since creation, prompt user before discarding unmatched custom fields

> 🧠 Safe fallback: custom fields not matching current schema can be shown in “Archived Fields” (optional)

---

## 🔐 Validations & Edge Cases

- On Submit:
  - Validate all required custom fields
  - Validate `base_cost` is non-negative
  - Show errors inline per field
- Category change should **reset** dynamic field values unless user confirms retention
- Tags, Category, and Unit Type should support **on-the-fly creation**

---

## ✅ Acceptance Criteria

- [ ] Category selection triggers dynamic field rendering
- [ ] Fields are validated based on schema
- [ ] `base_cost` is included in the form and reflected in list
- [ ] List view supports filtering on dynamic fields
- [ ] Edit screen loads + saves custom field and cost data

---
