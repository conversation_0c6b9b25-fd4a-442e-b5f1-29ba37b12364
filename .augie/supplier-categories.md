# 📦 Product Categories with Dynamic Field Configuration

## 📍 Location
Path: `/supplier-management/config/categories`  
Module: Supplier Management

---

## 🎯 Overview

The Product Category module allows admins to define **dynamic form schemas** per category. When creating a product/service, the form fields should auto-render based on the **selected category's configuration**.

This removes hardcoding, increases flexibility, and enables intelligent filtering later.

---

## 🛠 Updated Functionality

### ✅ When Admin Creates a New Category:
1. They provide:
   - **Category Name**
   - **Category Type** (Product / Service / Both)
   - **Icon** (optional)

2. Then define a **Dynamic Field Schema** using a visual builder.

---

## 🧩 Dynamic Field Schema Configuration

Each category can define multiple **custom fields**. These fields will be shown during Product & Services creation based on the selected category.

| Field                | Type                | Required |
|----------------------|---------------------|----------|
| Field Label          | String              | ✅       |
| Field Key            | String (snake_case) | ✅       |
| Field Type           | Dropdown: `Text`, `Number`, `Dropdown`, `Multi-select`, `Date`, `Boolean`, `Number Range` | ✅ |
| Options (if needed)  | Array of strings    | Only if Dropdown/Multi-select |
| Required             | Boolean             | ✅       |
| Used in Filtering    | Boolean             | Optional – used in concierge filtering |

Store this configuration as a **JSON schema** under each category.

---

## ✏️ Sample JSON Schema (for “Kids Club”)

```json
[
  {
    "label": "Minimum Age",
    "key": "min_age",
    "type": "number",
    "required": true
  },
  {
    "label": "Duration Type",
    "key": "duration",
    "type": "dropdown",
    "options": ["Half-day", "Full-day"],
    "required": true
  },
  {
    "label": "Indoor / Outdoor",
    "key": "location_type",
    "type": "dropdown",
    "options": ["Indoor", "Outdoor"],
    "required": false
  }
]