# 📦 Supplier Management: Products & Services Module

This module defines the global catalog of products and services offered by Powder Byrne through various suppliers. It is used by internal operations and procurement teams. Each product/service is later mapped to one or more suppliers, with cost and availability.

---

## 🔧 Global Lookups Setup

The following lookups must be created and maintained from separate configuration screens:

### 1. ✅ Category List
Used to classify products/services (e.g., “Gear”, “Transport”, “Dining”).

- Screen: `/supplier-management/config/categories`
- Each category should have:
  - Name
  - Description (optional)
- Support inline creation from dropdown in create/edit screens
- Must be selectable in Product/Service creation

### 2. ✅ Unit Type List
Defines how products/services are measured or billed (e.g., “Per Day”, “Per Trip”).

- Screen: `/supplier-management/config/unit-types`
- Each unit type should have:
  - Name (e.g., “Per Person”, “Per Hour”)
- Support inline creation from dropdown in create/edit screens

### 3. ✅ Tags
Used to group or filter items (e.g., “Kids”, “Luxury”, “Winter”).

- Tags should be globally managed and selectable in:
  - Products & Services
  - Suppliers (optional)
- Inline tag creation should be supported (e.g., via a modal or +Add)

---

## 1️⃣ LIST VIEW – Products & Services

### 🧭 Route
`/supplier-management/products-services`

### 🖼️ Wireframe
Header: Products & Services      [ + Add Product/Service ]

Search & Filters Row:
[Search by name…] [Category ▼] [Tags ▼] [Status ▼] [Unit Type ▼]

Table:
┌────────────┬────────────┬────────────┬────────────┬────────────┐
│ Name       │ Category   │ Unit Type  │ Tags       │ Status     │
├────────────┼────────────┼────────────┼────────────┼────────────┤
│ Ski Pass   │ Gear       │ Per Person │ Kids, Snow │ Active     │
│ Kids Club  │ Activity   │ Per Day    │ Kids       │ Active     │
│ Spa Access │ Wellness   │ Per Entry  │ Relaxation │ Inactive   │
└────────────┴────────────┴────────────┴────────────┴────────────┘
### 📋 Fields Displayed

- **Name**
- **Category**
- **Unit Type**
- **Tags**
- **Status** (Active / Inactive)

### 🔍 Filters

- Name (text search)
- Category
- Unit Type
- Tags (multi-select)
- Status

---

## 2️⃣ CREATE VIEW – Add New Product / Service

### 🧭 Route
`/supplier-management/products-services/create`

### 🖼️ Wireframe
Header: Add New Product / Service

[ Name                            ]   ← Required
[ Category: Transport ▼          ]   ← Select or +Add (Required)
[ Unit Type: Per Trip ▼          ]   ← Select or +Add (Required)
[ Tags: +Kids +Winter +Luxury    ]   ← Multi-select with +Add support
[ Description                    ]   ← Optional
[ Status:  ● Active   ○ Inactive ]   ← Required

▾ Custom Fields (optional):
[ + Add Field ]
Field 1:  [ Label: Size         ] [ Field Type: Dropdown ▼ ]
Field 2:  [ Label: Age Group    ] [ Field Type: Dropdown ▼ ]

[💾 Save Product/Service] [❌ Cancel]
### 📋 Form Fields

| Field               | Type            | Required |
|---------------------|-----------------|----------|
| Name                | Text            | ✅ Yes    |
| Category            | Dropdown (+Add) | ✅ Yes    |
| Unit Type           | Dropdown (+Add) | ✅ Yes    |
| Tags                | Multi-select    | ❌ Optional |
| Description         | Textarea        | ❌ Optional |
| Status              | Toggle          | ✅ Yes    |
| Custom Fields       | Repeater        | ❌ Optional |

> Each custom field contains:
> - Field Name (e.g., "Size", "Pickup Time")
> - Field Type (Text, Dropdown, Number, Date, Time)

---

## 3️⃣ DETAIL VIEW – Product / Service

### 🧭 Route
`/supplier-management/products-services/:id`

### 🖼️ Wireframe
Header: Ski Pass                  [✏️ Edit]  [⋯ More Actions]

Section 1: Overview
	•	Category: Gear
	•	Unit Type: Per Person
	•	Tags: Kids, Snow
	•	Status: Active

Section 2: Description
Full-day ski pass valid for all Zermatt lifts.

Section 3: Custom Fields
	•	Duration: Dropdown
	•	Age Group: Dropdown

Section 4: Linked Suppliers
┌────────────┬────────────┬────────────┬──────────────┐
│ Vendor     │ Cost (CHF) │ Availability │ Season     │
├────────────┼────────────┼────────────┼──────────────┤
│ Swiss Gear │ 120        │ 10/day      │ Dec – Mar   │
│ Alps Rent  │ 100        │ Weekends    │ Jan – Feb   │
└────────────┴────────────┴────────────┴──────────────┘
---

## 🔄 Supplier Type Logic (Unified Design)

Suppliers can be either individuals or companies. One form should handle both.

### 📋 Supplier Type Field (at the top of Supplier Form)
[ Supplier Type: ● Company   ○ Individual ]
### 🔁 Behavior Based on Type:

#### If Supplier Type = **Company**:
Show:
- Company Name
- Business Address
- VAT Number
- Website
- Contact List (add multiple)
  - Each contact has: Name, Email, Phone, Role
  - Mark one as **Primary Contact**

#### If Supplier Type = **Individual**:
Show:
- Full Name
- Email
- Phone
- Address (optional)
- Auto-create a single contact using these fields

> The rest of the system (e.g. vendor notifications) should always reference the **Primary Contact** regardless of type.

---

## 🧠 Summary

- Remove “Product / Service Type” – not needed
- Add dynamic field management (Category, Unit Type, Tags)
- Unify Supplier form with conditional display logic for Company vs Individual
- Support inline creation of lookup items (Category, Unit Type, Tags)
- Each product/service can later be mapped to one or more suppliers with cost and seasonal availability (handled in a separate screen)
