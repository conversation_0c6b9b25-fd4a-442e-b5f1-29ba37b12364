/**
 * <PERSON><PERSON>t to update existing suppliers with default currency
 * This is a one-time script to set CHF as default currency for existing suppliers
 */

const { Client } = require('pg');

async function updateSupplierDefaultCurrency() {
  const client = new Client({
    host: process.env.DATABASE_HOST || 'localhost',
    port: process.env.DATABASE_PORT || 5432,
    database: process.env.DATABASE_NAME || 'medusa-store-v2',
    user: process.env.DATABASE_USERNAME || 'postgres',
    password: process.env.DATABASE_PASSWORD || 'postgres',
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Update all suppliers that don't have a default_currency set
    const updateQuery = `
      UPDATE supplier 
      SET default_currency = 'CHF' 
      WHERE default_currency IS NULL 
      AND deleted_at IS NULL;
    `;

    const result = await client.query(updateQuery);
    console.log(`Updated ${result.rowCount} suppliers with default currency CHF`);

    // Show the updated suppliers
    const selectQuery = `
      SELECT id, name, default_currency 
      FROM supplier 
      WHERE deleted_at IS NULL 
      ORDER BY name;
    `;

    const suppliers = await client.query(selectQuery);
    console.log('\nUpdated suppliers:');
    suppliers.rows.forEach(supplier => {
      console.log(`- ${supplier.name}: ${supplier.default_currency}`);
    });

  } catch (error) {
    console.error('Error updating suppliers:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

// Run the script
updateSupplierDefaultCurrency().catch(console.error);
