/**
 * Medusa script to update existing suppliers with default currency
 */

import { MedusaContainer } from "@medusajs/framework/types";
import { Modules } from "@medusajs/framework/utils";

export default async function updateSuppliers({ container }: { container: MedusaContainer }) {
  console.log("Starting supplier default currency update...");

  try {
    // Get the vendor management service (which includes supplier functionality)
    const supplierService = container.resolve("vendor_management");

    // Get all suppliers
    const suppliers = await supplierService.listSuppliers({});
    console.log(`Found ${suppliers.length} suppliers`);

    // Update each supplier that doesn't have a default currency
    let updatedCount = 0;
    for (const supplier of suppliers) {
      if (!supplier.default_currency) {
        await supplierService.updateSuppliers([{
          id: supplier.id,
          default_currency: "CHF"
        }]);
        console.log(`Updated supplier: ${supplier.name} -> CHF`);
        updatedCount++;
      } else {
        console.log(`Supplier ${supplier.name} already has currency: ${supplier.default_currency}`);
      }
    }

    console.log(`\nUpdate completed! Updated ${updatedCount} suppliers with default currency CHF`);

    // List all suppliers with their currencies
    const updatedSuppliers = await supplierService.listSuppliers({});
    console.log("\nAll suppliers with their default currencies:");
    updatedSuppliers.forEach((supplier: any) => {
      console.log(`- ${supplier.name}: ${supplier.default_currency || 'NOT SET'}`);
    });

  } catch (error) {
    console.error("Error updating suppliers:", error);
    throw error;
  }
}
